"""
🧪 SIMPLE TEST - Hardware & Browser Fingerprinting
🔍 تست ساده سیستم تشخیص سخت‌افزار (بدون وابستگی‌های اختیاری)
"""

import sys
import platform
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Check for required modules
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("❌ psutil not available - installing...")

def install_required_packages():
    """نصب کتابخانه‌های مورد نیاز"""
    import subprocess
    import sys
    
    packages = ['psutil']
    
    for package in packages:
        try:
            print(f"📦 Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def test_basic_system_detection():
    """🔍 تست تشخیص پایه سیستم"""
    print("🔍 Testing Basic System Detection...")
    print("=" * 50)
    
    try:
        # Basic system info
        print(f"🖥️ System: {platform.system()}")
        print(f"📋 Platform: {platform.platform()}")
        print(f"🏗️ Architecture: {platform.architecture()}")
        print(f"💻 Machine: {platform.machine()}")
        print(f"🔧 Processor: {platform.processor()}")
        print(f"🐍 Python: {platform.python_version()}")
        
        # Try psutil if available
        if PSUTIL_AVAILABLE:
            import psutil
            
            # CPU info
            cpu_count_physical = psutil.cpu_count(logical=False)
            cpu_count_logical = psutil.cpu_count(logical=True)
            print(f"💻 CPU Cores: {cpu_count_physical}P/{cpu_count_logical}L")
            
            # Memory info
            memory = psutil.virtual_memory()
            memory_gb = round(memory.total / (1024**3), 2)
            print(f"🧠 RAM: {memory_gb}GB total")
            print(f"🧠 RAM Usage: {memory.percent}%")
            
            # Disk info
            disk_usage = psutil.disk_usage('/')
            disk_total_gb = round(disk_usage.total / (1024**3), 2)
            print(f"💾 Disk: {disk_total_gb}GB total")
            
            # Network info
            network_interfaces = psutil.net_if_addrs()
            print(f"🌐 Network Interfaces: {len(network_interfaces)}")
            
        else:
            print("⚠️ psutil not available - limited system info")
        
        print("✅ Basic system detection completed")
        return True
        
    except Exception as e:
        print(f"❌ Basic system detection failed: {e}")
        return False

def test_browser_fingerprint_script():
    """🌐 تست اسکریپت fingerprinting مرورگر"""
    print("\n🌐 Testing Browser Fingerprint Script...")
    print("=" * 50)
    
    try:
        # Generate basic fingerprinting script
        script = """
        // 🔍 VIP BIG BANG Basic Browser Fingerprinting
        (function() {
            const fingerprint = {};
            
            // Hardware Detection
            fingerprint.deviceMemory = navigator.deviceMemory || 'unknown';
            fingerprint.hardwareConcurrency = navigator.hardwareConcurrency || 'unknown';
            
            // Screen Detection
            fingerprint.screenWidth = screen.width;
            fingerprint.screenHeight = screen.height;
            fingerprint.screenColorDepth = screen.colorDepth;
            
            // Browser Detection
            fingerprint.userAgent = navigator.userAgent;
            fingerprint.platform = navigator.platform;
            fingerprint.language = navigator.language;
            fingerprint.cookieEnabled = navigator.cookieEnabled;
            
            // Timezone Detection
            fingerprint.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            fingerprint.timezoneOffset = new Date().getTimezoneOffset();
            
            // Automation Detection
            fingerprint.webdriver = navigator.webdriver;
            
            console.log('🔍 VIP BIG BANG Browser Fingerprint:', fingerprint);
            return fingerprint;
        })();
        """
        
        print(f"📜 Fingerprint script generated ({len(script)} characters)")
        print("\n📋 Script Features:")
        print("   ✅ Hardware detection (RAM, CPU)")
        print("   ✅ Screen information")
        print("   ✅ Browser details")
        print("   ✅ Timezone detection")
        print("   ✅ WebDriver detection")
        
        # Save script to file
        with open("browser_fingerprint.js", "w", encoding="utf-8") as f:
            f.write(script)
        print("💾 Script saved to: browser_fingerprint.js")
        
        print("✅ Browser fingerprint script test completed")
        return True
        
    except Exception as e:
        print(f"❌ Browser fingerprint script test failed: {e}")
        return False

def test_vm_detection():
    """🔍 تست تشخیص محیط مجازی"""
    print("\n🔍 Testing VM Detection...")
    print("=" * 50)
    
    try:
        vm_indicators = []
        confidence = 0
        
        # Check processor name for VM indicators
        processor = platform.processor().lower()
        vm_cpu_indicators = ["virtual", "vmware", "virtualbox", "qemu", "xen"]
        
        for indicator in vm_cpu_indicators:
            if indicator in processor:
                vm_indicators.append(f"CPU: {indicator}")
                confidence += 20
        
        # Check platform for VM indicators
        platform_info = platform.platform().lower()
        vm_platform_indicators = ["vmware", "virtualbox", "qemu"]
        
        for indicator in vm_platform_indicators:
            if indicator in platform_info:
                vm_indicators.append(f"Platform: {indicator}")
                confidence += 25
        
        # Check if running in common VM configurations
        if PSUTIL_AVAILABLE:
            import psutil
            
            # Low CPU cores (common in VMs)
            cpu_cores = psutil.cpu_count(logical=False)
            if cpu_cores and cpu_cores <= 2:
                vm_indicators.append("Low CPU cores (≤2)")
                confidence += 10
            
            # Low RAM (common in VMs)
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            if memory_gb <= 4:
                vm_indicators.append("Low RAM (≤4GB)")
                confidence += 15
        
        # Results
        is_vm = confidence >= 50
        
        print(f"🔍 VM Detection Results:")
        print(f"   Is VM: {'Yes' if is_vm else 'No'}")
        print(f"   Confidence: {confidence}%")
        
        if vm_indicators:
            print("   Indicators:")
            for indicator in vm_indicators:
                print(f"     - {indicator}")
        else:
            print("   ✅ No VM indicators found")
        
        if is_vm:
            print("🚨 Virtual Machine detected!")
        else:
            print("✅ Physical machine confirmed")
        
        print("✅ VM detection test completed")
        return True
        
    except Exception as e:
        print(f"❌ VM detection test failed: {e}")
        return False

def generate_system_report():
    """📄 تولید گزارش سیستم"""
    print("\n📄 Generating System Report...")
    print("=" * 50)
    
    try:
        import json
        from datetime import datetime
        
        report = {
            "report_info": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0.0",
                "generator": "VIP BIG BANG Simple Test"
            },
            "system_info": {
                "system": platform.system(),
                "platform": platform.platform(),
                "architecture": platform.architecture(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version()
            }
        }
        
        # Add psutil info if available
        if PSUTIL_AVAILABLE:
            import psutil
            
            memory = psutil.virtual_memory()
            report["hardware_info"] = {
                "cpu_cores_physical": psutil.cpu_count(logical=False),
                "cpu_cores_logical": psutil.cpu_count(logical=True),
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "memory_usage_percent": memory.percent
            }
        
        # Save report
        report_file = "simple_system_report.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 System report saved to: {report_file}")
        
        # Show summary
        print("\n📊 Report Summary:")
        print(f"   System: {report['system_info']['system']}")
        print(f"   Processor: {report['system_info']['processor']}")
        
        if "hardware_info" in report:
            hw = report["hardware_info"]
            print(f"   CPU: {hw['cpu_cores_physical']}P/{hw['cpu_cores_logical']}L")
            print(f"   RAM: {hw['memory_total_gb']}GB")
        
        print("✅ System report generated")
        return True
        
    except Exception as e:
        print(f"❌ System report generation failed: {e}")
        return False

def main():
    """تابع اصلی تست"""
    print("🧪 VIP BIG BANG - Simple Fingerprinting Test")
    print("=" * 60)
    print("🔍 Testing basic hardware and browser detection")
    print("=" * 60)
    
    # Check if psutil is available
    if not PSUTIL_AVAILABLE:
        print("📦 Installing required packages...")
        if install_required_packages():
            print("✅ Packages installed successfully")
            print("🔄 Please restart the script to use new packages")
            return True
        else:
            print("❌ Package installation failed")
            print("⚠️ Continuing with limited functionality...")
    
    # Test results
    results = {
        "basic_system": False,
        "browser_script": False,
        "vm_detection": False,
        "system_report": False
    }
    
    try:
        # Test 1: Basic System Detection
        results["basic_system"] = test_basic_system_detection()
        
        # Test 2: Browser Fingerprint Script
        results["browser_script"] = test_browser_fingerprint_script()
        
        # Test 3: VM Detection
        results["vm_detection"] = test_vm_detection()
        
        # Test 4: System Report
        results["system_report"] = generate_system_report()
        
        # Final results
        print("\n🏆 Test Results Summary:")
        print("=" * 50)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name.upper().replace('_', ' ')}: {status}")
        
        all_passed = all(results.values())
        if all_passed:
            print("\n🎉 All tests passed! Basic system detection is working.")
        else:
            print("\n⚠️ Some tests failed. Check the logs above.")
        
        # Next steps
        print("\n📋 Next Steps:")
        print("1. Install optional packages: pip install wmi cpuinfo websockets")
        print("2. Run 'python quick_test_fingerprinting.py' for full test")
        print("3. Run 'python test_integrated_system.py' for UI test")
        print("4. Check generated files: browser_fingerprint.js, simple_system_report.json")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
