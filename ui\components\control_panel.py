"""
🎛️ VIP BIG BANG - Control Panel Component
Professional trading control panel with advanced features
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class ControlPanel(QFrame):
    """
    Professional control panel for trading operations
    
    Features:
    - Symbol selection with OTC filtering
    - Timeframe selection
    - Volume control
    - Emergency stop
    - Auto-filter settings
    - Confirm mode toggle
    """
    
    # Signals
    symbol_changed = Signal(str)
    timeframe_changed = Signal(str)
    volume_changed = Signal(int)
    emergency_stop_triggered = Signal()
    auto_filter_toggled = Signal(bool)
    confirm_mode_toggled = Signal(bool)
    
    def __init__(self):
        super().__init__()
        
        # Control states
        self.current_symbol = "EUR/USD"
        self.current_timeframe = "15s"
        self.current_volume = 10
        self.auto_filter_enabled = False
        self.confirm_mode_enabled = True
        
        # Setup panel
        self._setup_panel()
        self._apply_styling()
    
    def _setup_panel(self):
        """Setup control panel layout"""
        self.setObjectName("controlPanel")
        self.setFixedWidth(200)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Title
        title = QLabel("Trading Controls")
        title.setObjectName("panelTitle")
        layout.addWidget(title)
        
        # Symbol selection section
        symbol_section = self._create_symbol_section()
        layout.addWidget(symbol_section)
        
        # Timeframe selection section
        timeframe_section = self._create_timeframe_section()
        layout.addWidget(timeframe_section)
        
        # Volume control section
        volume_section = self._create_volume_section()
        layout.addWidget(volume_section)
        
        # Trading mode toggles
        mode_section = self._create_mode_section()
        layout.addWidget(mode_section)
        
        # Emergency controls
        emergency_section = self._create_emergency_section()
        layout.addWidget(emergency_section)
        
        layout.addStretch()
    
    def _create_symbol_section(self) -> QWidget:
        """Create symbol selection section"""
        section = QFrame()
        section.setObjectName("controlSection")
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Section title
        title = QLabel("📊 Symbol")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Symbol combo box
        self.symbol_combo = QComboBox()
        self.symbol_combo.setObjectName("symbolCombo")
        
        # Add major forex pairs
        symbols = [
            "EUR/USD", "GBP/USD", "USD/JPY", "AUD/USD",
            "USD/CAD", "EUR/GBP", "EUR/JPY", "GBP/JPY",
            "USD/CHF", "NZD/USD", "AUD/JPY", "CAD/JPY"
        ]
        
        for symbol in symbols:
            self.symbol_combo.addItem(symbol)
        
        self.symbol_combo.setCurrentText(self.current_symbol)
        self.symbol_combo.currentTextChanged.connect(self._on_symbol_changed)
        layout.addWidget(self.symbol_combo)
        
        # OTC filter checkbox
        self.otc_filter = QCheckBox("🌙 OTC Only")
        self.otc_filter.setObjectName("otcFilter")
        self.otc_filter.toggled.connect(self._on_otc_filter_toggled)
        layout.addWidget(self.otc_filter)
        
        return section
    
    def _create_timeframe_section(self) -> QWidget:
        """Create timeframe selection section"""
        section = QFrame()
        section.setObjectName("controlSection")
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Section title
        title = QLabel("⏱️ Timeframe")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Timeframe buttons
        timeframes = ["5s", "15s", "30s", "1m", "5m"]
        self.timeframe_buttons = []
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(5)
        
        for tf in timeframes:
            btn = QPushButton(tf)
            btn.setObjectName("timeframeBtn")
            btn.setCheckable(True)
            btn.setFixedSize(35, 30)
            btn.clicked.connect(lambda checked, timeframe=tf: self._on_timeframe_changed(timeframe))
            
            if tf == self.current_timeframe:
                btn.setChecked(True)
            
            self.timeframe_buttons.append(btn)
            buttons_layout.addWidget(btn)
        
        layout.addLayout(buttons_layout)
        
        return section
    
    def _create_volume_section(self) -> QWidget:
        """Create volume control section"""
        section = QFrame()
        section.setObjectName("controlSection")
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Section title
        title = QLabel("💰 Volume")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Volume slider
        volume_layout = QHBoxLayout()
        
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setObjectName("volumeSlider")
        self.volume_slider.setRange(1, 100)
        self.volume_slider.setValue(self.current_volume)
        self.volume_slider.valueChanged.connect(self._on_volume_changed)
        volume_layout.addWidget(self.volume_slider)
        
        # Volume display
        self.volume_label = QLabel(f"${self.current_volume}")
        self.volume_label.setObjectName("volumeLabel")
        self.volume_label.setFixedWidth(40)
        volume_layout.addWidget(self.volume_label)
        
        layout.addLayout(volume_layout)
        
        # Quick volume buttons
        quick_layout = QHBoxLayout()
        quick_volumes = [5, 10, 25, 50]
        
        for vol in quick_volumes:
            btn = QPushButton(f"${vol}")
            btn.setObjectName("quickVolumeBtn")
            btn.setFixedSize(35, 25)
            btn.clicked.connect(lambda checked, v=vol: self._set_quick_volume(v))
            quick_layout.addWidget(btn)
        
        layout.addLayout(quick_layout)
        
        return section
    
    def _create_mode_section(self) -> QWidget:
        """Create trading mode toggles section"""
        section = QFrame()
        section.setObjectName("controlSection")
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Section title
        title = QLabel("🎯 Trading Modes")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Auto Filter toggle
        self.auto_filter_toggle = QPushButton("🔍 Auto Filter")
        self.auto_filter_toggle.setObjectName("modeToggle")
        self.auto_filter_toggle.setCheckable(True)
        self.auto_filter_toggle.setChecked(self.auto_filter_enabled)
        self.auto_filter_toggle.toggled.connect(self._on_auto_filter_toggled)
        layout.addWidget(self.auto_filter_toggle)
        
        # Confirm Mode toggle
        self.confirm_mode_toggle = QPushButton("✅ Confirm Mode")
        self.confirm_mode_toggle.setObjectName("modeToggle")
        self.confirm_mode_toggle.setCheckable(True)
        self.confirm_mode_toggle.setChecked(self.confirm_mode_enabled)
        self.confirm_mode_toggle.toggled.connect(self._on_confirm_mode_toggled)
        layout.addWidget(self.confirm_mode_toggle)
        
        return section
    
    def _create_emergency_section(self) -> QWidget:
        """Create emergency controls section"""
        section = QFrame()
        section.setObjectName("emergencySection")
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Section title
        title = QLabel("🚨 Emergency")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Emergency stop button
        self.emergency_btn = QPushButton("🚨 EMERGENCY STOP")
        self.emergency_btn.setObjectName("emergencyBtn")
        self.emergency_btn.setFixedHeight(50)
        self.emergency_btn.clicked.connect(self._on_emergency_stop)
        layout.addWidget(self.emergency_btn)
        
        # Panic mode info
        info_label = QLabel("Stops all trading immediately")
        info_label.setObjectName("infoLabel")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        return section
    
    def _apply_styling(self):
        """Apply styling to the control panel"""
        self.setStyleSheet("""
            QFrame#controlPanel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(139, 92, 246, 0.1),
                    stop:1 rgba(139, 92, 246, 0.05));
                border: 2px solid rgba(139, 92, 246, 0.3);
                border-radius: 15px;
            }
            
            QLabel#panelTitle {
                font-size: 16px;
                font-weight: bold;
                color: white;
                padding: 5px;
            }
            
            QFrame#controlSection {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                padding: 5px;
            }
            
            QFrame#emergencySection {
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.3);
                border-radius: 8px;
                padding: 5px;
            }
            
            QLabel#sectionTitle {
                font-size: 12px;
                font-weight: bold;
                color: #8B5CF6;
            }
            
            QComboBox#symbolCombo {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(139, 92, 246, 0.5);
                border-radius: 5px;
                padding: 5px;
                color: white;
                font-weight: bold;
            }
            
            QPushButton#timeframeBtn {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(139, 92, 246, 0.5);
                border-radius: 5px;
                color: white;
                font-weight: bold;
            }
            
            QPushButton#timeframeBtn:checked {
                background: #8B5CF6;
                border-color: #8B5CF6;
            }
            
            QPushButton#quickVolumeBtn {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(139, 92, 246, 0.5);
                border-radius: 3px;
                color: white;
                font-size: 10px;
            }
            
            QPushButton#modeToggle {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(139, 92, 246, 0.5);
                border-radius: 5px;
                padding: 8px;
                color: white;
                font-weight: bold;
                text-align: left;
            }
            
            QPushButton#modeToggle:checked {
                background: rgba(139, 92, 246, 0.3);
                border-color: #8B5CF6;
            }
            
            QPushButton#emergencyBtn {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #EF4444, stop:1 #DC2626);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            
            QPushButton#emergencyBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:1 #B91C1C);
            }
            
            QLabel#volumeLabel {
                color: #8B5CF6;
                font-weight: bold;
                font-size: 12px;
            }
            
            QLabel#infoLabel {
                color: #9CA3AF;
                font-size: 10px;
            }
            
            QCheckBox#otcFilter {
                color: white;
                font-size: 11px;
            }
            
            QSlider#volumeSlider::groove:horizontal {
                background: rgba(255, 255, 255, 0.2);
                height: 6px;
                border-radius: 3px;
            }
            
            QSlider#volumeSlider::handle:horizontal {
                background: #8B5CF6;
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }
            
            QSlider#volumeSlider::sub-page:horizontal {
                background: #8B5CF6;
                border-radius: 3px;
            }
        """)
    
    # Event handlers
    def _on_symbol_changed(self, symbol: str):
        """Handle symbol change"""
        self.current_symbol = symbol
        self.symbol_changed.emit(symbol)
    
    def _on_timeframe_changed(self, timeframe: str):
        """Handle timeframe change"""
        self.current_timeframe = timeframe
        
        # Update button states
        for btn in self.timeframe_buttons:
            btn.setChecked(btn.text() == timeframe)
        
        self.timeframe_changed.emit(timeframe)
    
    def _on_volume_changed(self, volume: int):
        """Handle volume change"""
        self.current_volume = volume
        self.volume_label.setText(f"${volume}")
        self.volume_changed.emit(volume)
    
    def _set_quick_volume(self, volume: int):
        """Set volume using quick button"""
        self.volume_slider.setValue(volume)
        self._on_volume_changed(volume)
    
    def _on_otc_filter_toggled(self, enabled: bool):
        """Handle OTC filter toggle"""
        # Filter symbols based on OTC
        if enabled:
            # Add OTC symbols or filter existing ones
            pass
    
    def _on_auto_filter_toggled(self, enabled: bool):
        """Handle auto filter toggle"""
        self.auto_filter_enabled = enabled
        self.auto_filter_toggled.emit(enabled)
    
    def _on_confirm_mode_toggled(self, enabled: bool):
        """Handle confirm mode toggle"""
        self.confirm_mode_enabled = enabled
        self.confirm_mode_toggled.emit(enabled)
    
    def _on_emergency_stop(self):
        """Handle emergency stop"""
        # Show confirmation dialog
        reply = QMessageBox.question(
            self,
            "Emergency Stop",
            "Are you sure you want to activate emergency stop?\nThis will halt all trading activities.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.emergency_stop_triggered.emit()
    
    # Public methods
    def set_symbol(self, symbol: str):
        """Set current symbol"""
        if symbol in [self.symbol_combo.itemText(i) for i in range(self.symbol_combo.count())]:
            self.symbol_combo.setCurrentText(symbol)
    
    def set_timeframe(self, timeframe: str):
        """Set current timeframe"""
        self._on_timeframe_changed(timeframe)
    
    def set_volume(self, volume: int):
        """Set current volume"""
        self.volume_slider.setValue(volume)
    
    def enable_emergency_mode(self, enabled: bool):
        """Enable/disable emergency mode"""
        if enabled:
            self.emergency_btn.setText("🔴 EMERGENCY ACTIVE")
            self.emergency_btn.setEnabled(False)
        else:
            self.emergency_btn.setText("🚨 EMERGENCY STOP")
            self.emergency_btn.setEnabled(True)
