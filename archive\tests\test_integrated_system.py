"""
🧪 TEST INTEGRATED SYSTEM
🔍 آزمایش سیستم یکپارچه تشخیص سخت‌افزار و مرورگر
"""

import sys
import asyncio
import logging
from pathlib import Path
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton, QLabel
from PySide6.QtCore import QTimer

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.integrated_system_manager import IntegratedSystemManager
from utils.logger import setup_logger

class IntegratedSystemTestUI(QMainWindow):
    """رابط کاربری تست سیستم یکپارچه"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger("IntegratedSystemTest")
        
        # Initialize integrated system manager
        self.system_manager = IntegratedSystemManager()
        
        # Connect signals
        self.system_manager.system_ready.connect(self.on_system_ready)
        self.system_manager.security_alert.connect(self.on_security_alert)
        self.system_manager.vm_detected.connect(self.on_vm_detected)
        self.system_manager.fingerprint_updated.connect(self.on_fingerprint_updated)
        
        self.setup_ui()
        
        # Auto-update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(5000)  # Update every 5 seconds
        
        self.logger.info("🧪 Integrated System Test UI initialized")
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        self.setWindowTitle("🧪 VIP BIG BANG - Integrated System Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Title
        title_label = QLabel("🧪 VIP BIG BANG Integrated System Test")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2196F3;
                padding: 10px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # Status label
        self.status_label = QLabel("🔄 Initializing system...")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #4CAF50;
                padding: 5px;
                background: rgba(76, 175, 80, 0.1);
                border: 1px solid #4CAF50;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.status_label)
        
        # Log display
        self.log_display = QTextEdit()
        self.log_display.setStyleSheet("""
            QTextEdit {
                background: #1E1E1E;
                color: #FFFFFF;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                border: 1px solid #333;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.log_display)
        
        # Buttons
        button_layout = QVBoxLayout()
        
        # Test buttons
        self.test_hardware_btn = QPushButton("🔍 Test Hardware Detection")
        self.test_hardware_btn.clicked.connect(self.test_hardware_detection)
        button_layout.addWidget(self.test_hardware_btn)
        
        self.test_browser_btn = QPushButton("🌐 Test Browser Fingerprinting")
        self.test_browser_btn.clicked.connect(self.test_browser_fingerprinting)
        button_layout.addWidget(self.test_browser_btn)
        
        self.export_report_btn = QPushButton("📄 Export System Report")
        self.export_report_btn.clicked.connect(self.export_system_report)
        button_layout.addWidget(self.export_report_btn)
        
        self.show_summary_btn = QPushButton("📊 Show Complete Summary")
        self.show_summary_btn.clicked.connect(self.show_complete_summary)
        button_layout.addWidget(self.show_summary_btn)
        
        layout.addLayout(button_layout)
        
        # Style buttons
        button_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45A049);
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
                margin: 2px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45A049, stop:1 #4CAF50);
            }
            QPushButton:pressed {
                background: #3D8B40;
            }
        """
        
        for button in [self.test_hardware_btn, self.test_browser_btn, 
                      self.export_report_btn, self.show_summary_btn]:
            button.setStyleSheet(button_style)
    
    def log_message(self, message):
        """اضافه کردن پیام به نمایش لاگ"""
        self.log_display.append(f"[{asyncio.get_event_loop().time():.2f}] {message}")
        self.log_display.ensureCursorVisible()
    
    def on_system_ready(self, system_info):
        """پردازش آماده بودن سیستم"""
        self.status_label.setText("✅ System Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #4CAF50;
                padding: 5px;
                background: rgba(76, 175, 80, 0.1);
                border: 1px solid #4CAF50;
                border-radius: 5px;
            }
        """)
        self.log_message("✅ Integrated system is ready")
        self.log_message(f"📊 System components detected: {len(system_info)}")
    
    def on_security_alert(self, alert_info):
        """پردازش هشدار امنیتی"""
        self.status_label.setText("🚨 Security Alert")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #F44336;
                padding: 5px;
                background: rgba(244, 67, 54, 0.1);
                border: 1px solid #F44336;
                border-radius: 5px;
            }
        """)
        self.log_message(f"🚨 Security Alert: {alert_info}")
    
    def on_vm_detected(self, vm_info):
        """پردازش تشخیص VM"""
        vm_type = vm_info.get("vmType", "Unknown")
        confidence = vm_info.get("confidence", 0)
        
        self.status_label.setText(f"🔍 VM Detected: {vm_type}")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #FF9800;
                padding: 5px;
                background: rgba(255, 152, 0, 0.1);
                border: 1px solid #FF9800;
                border-radius: 5px;
            }
        """)
        self.log_message(f"🔍 Virtual Machine detected: {vm_type} (Confidence: {confidence}%)")
    
    def on_fingerprint_updated(self, fingerprint_info):
        """پردازش بروزرسانی fingerprint"""
        self.log_message("🌐 Browser fingerprint updated")
        
        # Show risk analysis
        analysis = fingerprint_info.get("analysis", {})
        risk_score = analysis.get("risk_score", 0)
        
        if risk_score >= 50:
            self.log_message(f"⚠️ High risk detected: {risk_score}/100")
        else:
            self.log_message(f"✅ Low risk: {risk_score}/100")
    
    def test_hardware_detection(self):
        """تست تشخیص سخت‌افزار"""
        self.log_message("🔍 Testing hardware detection...")
        
        if self.system_manager.hardware_detector:
            self.system_manager.hardware_detector.detect_all_hardware()
            self.log_message("✅ Hardware detection test completed")
        else:
            self.log_message("❌ Hardware detector not available")
    
    def test_browser_fingerprinting(self):
        """تست fingerprinting مرورگر"""
        self.log_message("🌐 Testing browser fingerprinting...")
        
        if self.system_manager.browser_manager:
            # Get fingerprint script
            script = self.system_manager.browser_manager.get_chrome_extension_script()
            self.log_message(f"📜 Fingerprint script generated ({len(script)} characters)")
            self.log_message("✅ Browser fingerprinting test completed")
        else:
            self.log_message("❌ Browser manager not available")
    
    def export_system_report(self):
        """صادرات گزارش سیستم"""
        self.log_message("📄 Exporting system report...")
        
        try:
            report_path = "system_report.json"
            report = self.system_manager.export_system_report(report_path)
            
            if report:
                self.log_message(f"✅ System report exported to: {report_path}")
                
                # Show summary
                security_status = report.get("security_analysis", {})
                risk_level = security_status.get("risk_level", "UNKNOWN")
                self.log_message(f"🛡️ Security Risk Level: {risk_level}")
                
                recommendations = report.get("recommendations", [])
                for rec in recommendations:
                    self.log_message(f"💡 {rec}")
            else:
                self.log_message("❌ Failed to export system report")
                
        except Exception as e:
            self.log_message(f"❌ Export error: {e}")
    
    def show_complete_summary(self):
        """نمایش خلاصه کامل"""
        self.log_message("📊 Generating complete system summary...")
        
        try:
            # Get complete system info
            complete_info = self.system_manager.get_complete_system_info()
            
            # Show summary
            system_info = complete_info.get("system_info", {})
            security_status = complete_info.get("security_status", {})
            
            self.log_message("=" * 50)
            self.log_message("📊 COMPLETE SYSTEM SUMMARY")
            self.log_message("=" * 50)
            
            # Hardware info
            if "advanced_hardware" in system_info:
                hw = system_info["advanced_hardware"]
                cpu = hw.get("cpu", {})
                ram = hw.get("ram", {})
                gpu = hw.get("gpu", {})
                
                self.log_message(f"💻 CPU: {cpu.get('brand', cpu.get('name', 'Unknown'))}")
                self.log_message(f"🧠 RAM: {ram.get('total_gb', 0)}GB")
                
                if gpu.get("detected", False):
                    primary_gpu = gpu.get("primary_gpu", {})
                    self.log_message(f"🎮 GPU: {primary_gpu.get('name', 'Unknown')}")
            
            # Security status
            self.log_message(f"🛡️ Security Risk: {security_status.get('risk_level', 'UNKNOWN')}")
            self.log_message(f"🔍 VM Detected: {'Yes' if security_status.get('vm_detected', False) else 'No'}")
            self.log_message(f"🤖 Bot Detected: {'Yes' if security_status.get('bot_detected', False) else 'No'}")
            
            # Indicators
            indicators = security_status.get("indicators", [])
            if indicators:
                self.log_message("🚨 Security Indicators:")
                for indicator in indicators:
                    self.log_message(f"   - {indicator}")
            
            self.log_message("=" * 50)
            
            # Log complete summary via system manager
            self.system_manager.log_complete_summary()
            
        except Exception as e:
            self.log_message(f"❌ Summary generation error: {e}")
    
    def update_display(self):
        """بروزرسانی نمایش"""
        try:
            # Update security status
            if self.system_manager.is_safe_environment():
                risk_level = self.system_manager.get_risk_level()
                if risk_level == "LOW":
                    self.status_label.setText("✅ System Secure")
                    self.status_label.setStyleSheet("""
                        QLabel {
                            font-size: 16px;
                            font-weight: bold;
                            color: #4CAF50;
                            padding: 5px;
                            background: rgba(76, 175, 80, 0.1);
                            border: 1px solid #4CAF50;
                            border-radius: 5px;
                        }
                    """)
            else:
                risk_level = self.system_manager.get_risk_level()
                self.status_label.setText(f"⚠️ Risk: {risk_level}")
                self.status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #FF9800;
                        padding: 5px;
                        background: rgba(255, 152, 0, 0.1);
                        border: 1px solid #FF9800;
                        border-radius: 5px;
                    }
                """)
        
        except Exception as e:
            self.logger.error(f"❌ Display update error: {e}")

def main():
    """تابع اصلی"""
    print("🧪 VIP BIG BANG - Integrated System Test")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Create test window
    window = IntegratedSystemTestUI()
    window.show()
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
