"""
VIP BIG BANG Enterprise - Advanced Logging System
Enterprise-level logging with performance monitoring and security
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
import json
import threading
from typing import Dict, Any

class ColoredFormatter(logging.Formatter):
    """Colored console formatter for better readability"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry, ensure_ascii=False)

class PerformanceFilter(logging.Filter):
    """Filter to add performance metrics to log records"""
    
    def __init__(self):
        super().__init__()
        self.start_time = datetime.now()
    
    def filter(self, record):
        # Add uptime
        uptime = (datetime.now() - self.start_time).total_seconds()
        record.uptime = f"{uptime:.2f}s"
        
        # Add memory usage (if psutil is available)
        try:
            import psutil
            process = psutil.Process()
            record.memory_mb = f"{process.memory_info().rss / 1024 / 1024:.1f}MB"
            record.cpu_percent = f"{process.cpu_percent():.1f}%"
        except ImportError:
            record.memory_mb = "N/A"
            record.cpu_percent = "N/A"
        
        return True

class SecurityFilter(logging.Filter):
    """Filter to remove sensitive information from logs"""
    
    SENSITIVE_PATTERNS = [
        'password', 'token', 'key', 'secret', 'auth', 'credential',
        'session', 'cookie', 'api_key', 'access_token'
    ]
    
    def filter(self, record):
        # Check message for sensitive data
        message = record.getMessage().lower()
        
        for pattern in self.SENSITIVE_PATTERNS:
            if pattern in message:
                # Replace sensitive data with asterisks
                record.msg = str(record.msg).replace(
                    record.args[0] if record.args else '',
                    '*' * 8
                )
                break
        
        return True

def setup_logger(name: str = "VIP_BIG_BANG", level: str = "INFO", 
                config: Dict[str, Any] = None) -> logging.Logger:
    """
    Setup enterprise-level logger with multiple handlers
    
    Args:
        name: Logger name
        level: Logging level
        config: Additional configuration
    
    Returns:
        Configured logger instance
    """
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create logs directory
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Console handler with colors
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    console_format = ColoredFormatter(
        '%(asctime)s | %(levelname)-8s | %(name)-15s | %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_format)
    
    # Add filters
    console_handler.addFilter(SecurityFilter())
    logger.addHandler(console_handler)
    
    # File handler for general logs
    file_handler = logging.handlers.RotatingFileHandler(
        logs_dir / f"{name.lower()}.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    
    file_format = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)-15s | %(module)-15s | %(funcName)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_format)
    file_handler.addFilter(SecurityFilter())
    file_handler.addFilter(PerformanceFilter())
    logger.addHandler(file_handler)
    
    # JSON handler for structured logs
    json_handler = logging.handlers.RotatingFileHandler(
        logs_dir / f"{name.lower()}_structured.json",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=3,
        encoding='utf-8'
    )
    json_handler.setLevel(logging.INFO)
    json_handler.setFormatter(JSONFormatter())
    json_handler.addFilter(SecurityFilter())
    json_handler.addFilter(PerformanceFilter())
    logger.addHandler(json_handler)
    
    # Error handler for critical issues
    error_handler = logging.handlers.RotatingFileHandler(
        logs_dir / f"{name.lower()}_errors.log",
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(file_format)
    logger.addHandler(error_handler)
    
    # Performance handler for performance metrics
    perf_handler = logging.handlers.RotatingFileHandler(
        logs_dir / f"{name.lower()}_performance.log",
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=2,
        encoding='utf-8'
    )
    perf_handler.setLevel(logging.DEBUG)
    perf_handler.addFilter(lambda record: hasattr(record, 'performance'))
    
    perf_format = logging.Formatter(
        '%(asctime)s | %(uptime)s | %(memory_mb)s | %(cpu_percent)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    perf_handler.setFormatter(perf_format)
    logger.addHandler(perf_handler)
    
    # Trade handler for trading-specific logs
    trade_handler = logging.handlers.RotatingFileHandler(
        logs_dir / f"{name.lower()}_trades.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10,  # Keep more trade history
        encoding='utf-8'
    )
    trade_handler.setLevel(logging.INFO)
    trade_handler.addFilter(lambda record: 'trade' in record.getMessage().lower())
    trade_handler.setFormatter(file_format)
    logger.addHandler(trade_handler)
    
    # Configure based on config
    if config:
        if config.get('file_logging', True) == False:
            # Remove file handlers if disabled
            logger.handlers = [h for h in logger.handlers if isinstance(h, logging.StreamHandler)]
        
        if config.get('console_logging', True) == False:
            # Remove console handler if disabled
            logger.handlers = [h for h in logger.handlers if not isinstance(h, logging.StreamHandler)]
        
        # Set custom level
        custom_level = config.get('level', level)
        logger.setLevel(getattr(logging, custom_level.upper()))
    
    # Log startup message
    logger.info(f"{name} Logger initialized - Level: {level}")
    logger.info(f"Log files location: {logs_dir.absolute()}")
    
    return logger

class LoggerManager:
    """Centralized logger management"""
    
    _loggers = {}
    _lock = threading.Lock()
    
    @classmethod
    def get_logger(cls, name: str, level: str = "INFO", config: Dict[str, Any] = None) -> logging.Logger:
        """Get or create logger instance"""
        with cls._lock:
            if name not in cls._loggers:
                cls._loggers[name] = setup_logger(name, level, config)
            return cls._loggers[name]
    
    @classmethod
    def shutdown_all(cls):
        """Shutdown all loggers"""
        with cls._lock:
            for logger in cls._loggers.values():
                for handler in logger.handlers:
                    handler.close()
                logger.handlers.clear()
            cls._loggers.clear()

def log_performance(func):
    """Decorator to log function performance"""
    def wrapper(*args, **kwargs):
        logger = logging.getLogger("Performance")
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            execution_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(
                f"Function {func.__name__} executed in {execution_time:.4f}s",
                extra={'performance': True, 'execution_time': execution_time}
            )
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(
                f"Function {func.__name__} failed after {execution_time:.4f}s: {str(e)}",
                extra={'performance': True, 'execution_time': execution_time, 'error': str(e)}
            )
            raise
    
    return wrapper

def log_trade(trade_data: Dict[str, Any]):
    """Log trade information"""
    logger = logging.getLogger("Trading")
    
    trade_info = {
        'timestamp': datetime.now().isoformat(),
        'trade_id': trade_data.get('id'),
        'direction': trade_data.get('direction'),
        'amount': trade_data.get('amount'),
        'asset': trade_data.get('asset'),
        'entry_price': trade_data.get('entry_price'),
        'result': trade_data.get('result'),
        'profit': trade_data.get('profit')
    }
    
    logger.info(f"TRADE: {json.dumps(trade_info)}")

# Example usage and testing
if __name__ == "__main__":
    # Test the logging system
    logger = setup_logger("TEST", "DEBUG")
    
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")
    
    # Test performance logging
    @log_performance
    def test_function():
        import time
        time.sleep(0.1)
        return "Test result"
    
    result = test_function()
    
    # Test trade logging
    log_trade({
        'id': 'test_trade_123',
        'direction': 'CALL',
        'amount': 10.0,
        'asset': 'EUR/USD',
        'entry_price': 1.0850,
        'result': 'WIN',
        'profit': 8.0
    })
    
    print("Logging test completed. Check the logs directory for output files.")
