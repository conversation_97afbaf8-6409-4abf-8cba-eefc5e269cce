#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Launcher
راه‌انداز VIP BIG BANG
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def print_banner():
    """نمایش بنر VIP BIG BANG"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    🎮 VIP BIG BANG 🎮                        ║
    ║                                                              ║
    ║              Ultimate Trading Bot Interface                  ║
    ║                                                              ║
    ║                     🚀 LAUNCHING... 🚀                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """بررسی وابستگی‌ها"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'PySide6',
        'sys',
        'os',
        'random',
        'math',
        'datetime'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package in ['sys', 'os', 'random', 'math', 'datetime']:
                # Built-in packages
                __import__(package)
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - Missing!")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        for package in missing_packages:
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package
                ])
                print(f"  ✅ {package} installed successfully!")
            except subprocess.CalledProcessError:
                print(f"  ❌ Failed to install {package}")
                return False
    
    return True

def check_files():
    """بررسی فایل‌های مورد نیاز"""
    print("\n📁 Checking required files...")
    
    required_files = [
        'vip_exact_ui.py',
        'vip_styles.py'
    ]
    
    current_dir = Path(__file__).parent
    
    for file_name in required_files:
        file_path = current_dir / file_name
        if file_path.exists():
            print(f"  ✅ {file_name}")
        else:
            print(f"  ❌ {file_name} - Missing!")
            return False
    
    return True

def launch_ui():
    """راه‌اندازی UI"""
    print("\n🎮 Launching VIP BIG BANG UI...")
    
    try:
        # Import and run
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        from vip_exact_ui import VIPExactUI
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont, QIcon
        
        # Create application
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("🎮 VIP BIG BANG")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("VIP Trading")
        app.setApplicationDisplayName("VIP BIG BANG Trading Bot")
        
        # Set global font
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # Create and show main window
        print("  🏗️ Creating main window...")
        window = VIPExactUI()
        
        print("  📱 Showing window...")
        window.show()
        
        # Center window on screen
        screen = app.primaryScreen().geometry()
        window.move(
            (screen.width() - window.width()) // 2,
            (screen.height() - window.height()) // 2
        )
        
        print("  ✅ VIP BIG BANG launched successfully!")
        print(f"  📏 Window size: {window.width()}x{window.height()}")
        print(f"  📍 Window position: ({window.x()}, {window.y()})")
        
        # Show success message
        print("\n" + "="*60)
        print("🎯 VIP BIG BANG is now running!")
        print("🎮 Enjoy your ultimate trading experience!")
        print("="*60)
        
        # Run application
        return app.exec()
        
    except ImportError as e:
        print(f"  ❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Launch Error: {e}")
        return False

def main():
    """تابع اصلی"""
    # Clear screen
    os.system('cls' if os.name == 'nt' else 'clear')
    
    # Show banner
    print_banner()
    
    # Add delay for effect
    time.sleep(1)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed!")
        input("Press Enter to exit...")
        return 1
    
    # Check files
    if not check_files():
        print("\n❌ File check failed!")
        input("Press Enter to exit...")
        return 1
    
    # Launch UI
    try:
        result = launch_ui()
        return result
    except KeyboardInterrupt:
        print("\n\n🛑 Launch interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
