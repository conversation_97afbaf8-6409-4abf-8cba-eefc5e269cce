"""
VIP BIG BANG Enterprise - Buyer/Seller Power Analyzer
Advanced market sentiment analysis through volume and price action
"""

import pandas as pd  # type: ignore
from typing import Dict
import logging
from datetime import datetime

class BuyerSellerPowerAnalyzer:
    """
    Enterprise-level buyer/seller power analysis
    Analyzes market sentiment through volume distribution and price action
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("BuyerSellerPowerAnalyzer")
        
        # Analysis parameters
        self.volume_period = 20
        self.price_period = 14
        self.power_smoothing = 5
        
        # Thresholds
        self.strong_power_threshold = 0.7
        self.weak_power_threshold = 0.3
        
        self.logger.debug("Buyer/Seller Power Analyzer initialized")
    
    def calculate_volume_profile(self, data: pd.DataFrame) -> Dict:
        """Calculate volume distribution analysis"""
        if 'volume' not in data.columns:
            return {
                'buying_volume': 0.5,
                'selling_volume': 0.5,
                'volume_trend': 'NEUTRAL',
                'volume_strength': 0.5
            }
        
        # Simple volume analysis without complex operations
        if len(data) < 2:
            return {
                'buying_power': 0.5,
                'selling_power': 0.5,
                'power_ratio': 1.0,
                'volume_trend': 'NEUTRAL'
            }

        # Get recent data
        recent_data = data.tail(self.volume_period) if len(data) >= self.volume_period else data

        # If buying/selling volume columns don't exist, estimate them
        if 'buying_volume' not in recent_data.columns:
            # Estimate buying/selling volume based on price movement and volume
            recent_data = recent_data.copy()
            recent_data['price_change'] = recent_data['close'].pct_change()

            # Estimate buying volume when price goes up
            recent_data['buying_volume'] = recent_data.apply(
                lambda row: row['volume'] * 0.6 if row['price_change'] > 0 else row['volume'] * 0.4, axis=1
            )

            # Estimate selling volume when price goes down
            recent_data['selling_volume'] = recent_data.apply(
                lambda row: row['volume'] * 0.6 if row['price_change'] < 0 else row['volume'] * 0.4, axis=1
            )

            # Neutral volume is the remainder
            recent_data['neutral_volume'] = recent_data['volume'] - recent_data['buying_volume'] - recent_data['selling_volume']

        total_buying = recent_data['buying_volume'].sum()
        total_selling = recent_data['selling_volume'].sum()
        total_neutral = recent_data['neutral_volume'].sum()
        total_volume = total_buying + total_selling + total_neutral
        
        if total_volume == 0:
            return {
                'buying_volume': 0.5,
                'selling_volume': 0.5,
                'volume_trend': 'NEUTRAL',
                'volume_strength': 0.0
            }
        
        buying_ratio = total_buying / total_volume
        selling_ratio = total_selling / total_volume
        
        # Determine volume trend
        volume_trend = 'NEUTRAL'
        if buying_ratio > 0.6:
            volume_trend = 'BUYING'
        elif selling_ratio > 0.6:
            volume_trend = 'SELLING'
        
        # Calculate volume strength
        volume_strength = abs(buying_ratio - selling_ratio)
        
        return {
            'buying_volume': buying_ratio,
            'selling_volume': selling_ratio,
            'volume_trend': volume_trend,
            'volume_strength': volume_strength,
            'total_volume': total_volume
        }
    
    def calculate_price_action_power(self, data: pd.DataFrame) -> Dict:
        """Analyze buyer/seller power through price action"""
        if len(data) < self.price_period:
            return {
                'buyer_power': 0.5,
                'seller_power': 0.5,
                'power_trend': 'NEUTRAL'
            }
        
        # Use OHLC data if available, otherwise use price
        high_prices = data['high'] if 'high' in data.columns else data['price']
        low_prices = data['low'] if 'low' in data.columns else data['price']
        close_prices = data['close'] if 'close' in data.columns else data['price']
        open_prices = data['open'] if 'open' in data.columns else data['price']
        
        # Calculate buying and selling pressure
        buying_pressure = []
        selling_pressure = []
        
        for i in range(len(data)):
            high_val = high_prices.iloc[i]
            low_val = low_prices.iloc[i]
            close_val = close_prices.iloc[i]
            open_val = open_prices.iloc[i]
            
            # Range calculations
            total_range = high_val - low_val if high_val != low_val else 0.0001
            
            # Buying pressure: how much of the range was covered by upward movement
            if close_val >= open_val:
                # Bullish candle
                buying_press = (close_val - low_val) / total_range
                selling_press = (high_val - close_val) / total_range
            else:
                # Bearish candle
                buying_press = (high_val - open_val) / total_range
                selling_press = (open_val - low_val) / total_range
            
            buying_pressure.append(buying_press)
            selling_pressure.append(selling_press)
        
        # Calculate recent averages
        recent_buying_data = buying_pressure[-self.price_period:]
        recent_selling_data = selling_pressure[-self.price_period:]

        recent_buying = sum(recent_buying_data) / len(recent_buying_data) if recent_buying_data else 0
        recent_selling = sum(recent_selling_data) / len(recent_selling_data) if recent_selling_data else 0
        
        # Normalize to ensure they sum to 1
        total_pressure = recent_buying + recent_selling
        if total_pressure > 0:
            buyer_power = recent_buying / total_pressure
            seller_power = recent_selling / total_pressure
        else:
            buyer_power = 0.5
            seller_power = 0.5
        
        # Determine power trend
        power_trend = 'NEUTRAL'
        if buyer_power > 0.6:
            power_trend = 'BUYERS_CONTROL'
        elif seller_power > 0.6:
            power_trend = 'SELLERS_CONTROL'
        
        return {
            'buyer_power': buyer_power,
            'seller_power': seller_power,
            'power_trend': power_trend,
            'power_difference': abs(buyer_power - seller_power)
        }
    
    def calculate_momentum_power(self, data: pd.DataFrame) -> Dict:
        """Calculate power based on price momentum"""
        close_prices = data['close'] if 'close' in data.columns else data['price']
        
        if len(close_prices) < 10:
            return {
                'momentum_power': 0.5,
                'momentum_direction': 'NEUTRAL',
                'momentum_strength': 0.0
            }
        
        # Calculate different momentum periods
        short_momentum = close_prices.pct_change(periods=3).tail(5).mean()
        medium_momentum = close_prices.pct_change(periods=7).tail(3).mean()
        long_momentum = close_prices.pct_change(periods=14).tail(1).iloc[0] if len(close_prices) >= 14 else 0
        
        # Weighted momentum score
        momentum_score = (
            short_momentum * 0.5 +
            medium_momentum * 0.3 +
            long_momentum * 0.2
        )
        
        # Normalize momentum to 0-1 scale
        momentum_power = 0.5 + (momentum_score * 10)  # Scale factor
        momentum_power = max(0, min(1, momentum_power))
        
        # Determine direction
        momentum_direction = 'NEUTRAL'
        if momentum_power > 0.6:
            momentum_direction = 'BULLISH'
        elif momentum_power < 0.4:
            momentum_direction = 'BEARISH'
        
        momentum_strength = abs(momentum_power - 0.5) * 2
        
        return {
            'momentum_power': momentum_power,
            'momentum_direction': momentum_direction,
            'momentum_strength': momentum_strength,
            'raw_momentum': momentum_score
        }
    
    def calculate_volatility_power(self, data: pd.DataFrame) -> Dict:
        """Analyze power through volatility patterns"""
        close_prices = data['close'] if 'close' in data.columns else data['price']
        
        if len(close_prices) < 10:
            return {
                'volatility_power': 0.5,
                'volatility_trend': 'NEUTRAL'
            }
        
        # Calculate rolling volatility
        returns = close_prices.pct_change().dropna()
        volatility = returns.rolling(window=10).std()
        
        # Recent volatility vs historical
        recent_vol = volatility.tail(3).mean()
        historical_vol = volatility.tail(20).mean()
        
        # Volatility power (higher volatility can indicate stronger moves)
        if historical_vol > 0:
            vol_ratio = recent_vol / historical_vol
        else:
            vol_ratio = 1.0
        
        # Normalize volatility power
        volatility_power = min(vol_ratio / 2, 1.0)  # Cap at 1.0
        
        volatility_trend = 'INCREASING' if vol_ratio > 1.2 else 'DECREASING' if vol_ratio < 0.8 else 'STABLE'
        
        return {
            'volatility_power': volatility_power,
            'volatility_trend': volatility_trend,
            'volatility_ratio': vol_ratio
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main buyer/seller power analysis function
        Returns score between 0 (seller dominance) and 1 (buyer dominance)
        """
        try:
            if len(data) < 10:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for buyer/seller power analysis'
                }
            
            # Calculate different power components
            volume_profile = self.calculate_volume_profile(data)
            price_action_power = self.calculate_price_action_power(data)
            momentum_power = self.calculate_momentum_power(data)
            volatility_power = self.calculate_volatility_power(data)
            
            # Combine power scores with weights
            power_components = [
                volume_profile['buying_volume'] * 0.3,  # Volume weight
                price_action_power['buyer_power'] * 0.4,  # Price action weight
                momentum_power['momentum_power'] * 0.2,  # Momentum weight
                volatility_power['volatility_power'] * 0.1  # Volatility weight
            ]
            
            # Calculate final power score
            final_score = sum(power_components)
            final_score = max(0, min(1, final_score))
            
            # Calculate confidence based on agreement between components
            component_values = [
                volume_profile['buying_volume'],
                price_action_power['buyer_power'],
                momentum_power['momentum_power']
            ]
            
            # Confidence is higher when components agree
            if component_values:
                mean_val = sum(component_values) / len(component_values)
                variance = sum((x - mean_val) ** 2 for x in component_values) / len(component_values)
                std_dev = variance ** 0.5
                confidence = max(0, 1 - (std_dev * 3))  # Lower std = higher confidence
            else:
                confidence = 0.5
            
            # Determine direction
            if final_score > 0.6:
                direction = 'CALL'
                market_sentiment = 'BUYERS_CONTROL'
            elif final_score < 0.4:
                direction = 'PUT'
                market_sentiment = 'SELLERS_CONTROL'
            else:
                direction = 'NEUTRAL'
                market_sentiment = 'BALANCED'
            
            # Power strength classification
            power_strength = 'WEAK'
            power_difference = abs(final_score - 0.5) * 2
            
            if power_difference > 0.8:
                power_strength = 'VERY_STRONG'
            elif power_difference > 0.6:
                power_strength = 'STRONG'
            elif power_difference > 0.4:
                power_strength = 'MODERATE'
            
            result = {
                'score': final_score,
                'direction': direction,
                'confidence': confidence,
                'market_sentiment': market_sentiment,
                'power_strength': power_strength,
                'components': {
                    'volume_profile': volume_profile,
                    'price_action_power': price_action_power,
                    'momentum_power': momentum_power,
                    'volatility_power': volatility_power
                },
                'power_balance': {
                    'buyer_dominance': final_score,
                    'seller_dominance': 1 - final_score,
                    'power_difference': power_difference
                },
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.debug(f"Buyer/Seller Power analysis: Score={final_score:.3f}, Direction={direction}, Sentiment={market_sentiment}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Buyer/Seller power analysis failed: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
