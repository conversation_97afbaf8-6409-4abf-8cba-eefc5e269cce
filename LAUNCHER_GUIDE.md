# 🚀 VIP BIG BANG - Complete Launcher Guide
## راهنمای کامل تمام launcher های VIP BIG BANG

---

## 📋 فهرست Launcher ها

### 1. 🚀 QUICK_START.bat
**بهترین گزینه برای شروع سریع**

```bash
QUICK_START.bat
```

**ویژگی‌ها:**
- ✅ راه‌اندازی فوری (کمتر از 30 ثانیه)
- ✅ بررسی خودکار dependencies
- ✅ نصب خودکار Playwright و Pandas
- ✅ سازگار با تمام نسخه‌های Windows
- ✅ مناسب برای کاربران مبتدی

**کاربرد:**
- اولین بار که VIP BIG BANG را اجرا می‌کنید
- زمانی که می‌خواهید سریع شروع کنید
- برای تست سریع سیستم

---

### 2. 🔧 VIP_ADVANCED_LAUNCHER.bat
**پیشرفته‌ترین launcher با تمام امکانات**

```bash
VIP_ADVANCED_LAUNCHER.bat
```

**ویژگی‌ها:**
- ✅ منوی تعاملی کامل
- ✅ تست کامل سیستم
- ✅ راهنمای نصب Chrome Extension
- ✅ تنظیمات پیشرفته
- ✅ عیب‌یابی و بازیابی اضطراری
- ✅ گزارش وضعیت سیستم
- ✅ پاکسازی سیستم

**منوی اصلی:**
```
[1] 🚀 Quick Launch (Recommended)
[2] 🔧 Full System Test
[3] 🌐 Chrome Extension Setup
[4] 🎭 Playwright Test
[5] 📊 Real Data Test
[6] ⚙️  Advanced Configuration
[7] 🔄 System Cleanup
[8] 📋 View System Status
[9] 🆘 Emergency Recovery
[0] ❌ Exit
```

**کاربرد:**
- زمانی که مشکل دارید
- برای تنظیمات پیشرفته
- برای عیب‌یابی سیستم
- برای کاربران حرفه‌ای

---

### 3. ⚡ VIP_ULTIMATE_LAUNCHER.ps1
**PowerShell launcher با قابلیت‌های Enterprise**

```powershell
PowerShell -ExecutionPolicy Bypass -File VIP_ULTIMATE_LAUNCHER.ps1
```

**یا با پارامتر:**
```powershell
# Quick Launch
.\VIP_ULTIMATE_LAUNCHER.ps1 -QuickLaunch

# Full Test
.\VIP_ULTIMATE_LAUNCHER.ps1 -FullTest

# Silent Mode
.\VIP_ULTIMATE_LAUNCHER.ps1 -Silent
```

**ویژگی‌ها:**
- ✅ رابط رنگی زیبا
- ✅ پشتیبانی از Command Line Arguments
- ✅ تشخیص خودکار Administrator privileges
- ✅ تست شبکه و اتصال
- ✅ گزارش‌دهی پیشرفته
- ✅ بازیابی اضطراری هوشمند

**کاربرد:**
- محیط‌های Enterprise
- کاربران PowerShell
- اتوماسیون و scripting
- مدیریت سیستم‌های چندگانه

---

### 4. 🐍 vip_smart_launcher.py
**Python launcher هوشمند**

```bash
python vip_smart_launcher.py
```

**یا با پارامتر:**
```bash
# Quick Launch
python vip_smart_launcher.py --quick

# Full Setup
python vip_smart_launcher.py --full

# Test Only
python vip_smart_launcher.py --test
```

**ویژگی‌ها:**
- ✅ Cross-platform (Windows, Linux, macOS)
- ✅ تشخیص خودکار Python version
- ✅ مدیریت Virtual Environment
- ✅ نصب هوشمند dependencies
- ✅ رابط تعاملی رنگی
- ✅ گزارش‌دهی دقیق

**کاربرد:**
- کاربران Linux/macOS
- محیط‌های توسعه
- CI/CD pipelines
- کاربران Python

---

## 🎯 انتخاب بهترین Launcher

### برای کاربران مبتدی:
```bash
QUICK_START.bat
```

### برای کاربران متوسط:
```bash
VIP_ADVANCED_LAUNCHER.bat
```

### برای کاربران حرفه‌ای:
```powershell
VIP_ULTIMATE_LAUNCHER.ps1
```

### برای توسعه‌دهندگان:
```bash
python vip_smart_launcher.py
```

---

## 🔧 عیب‌یابی

### مشکل 1: "Python not found"
**راه‌حل:**
1. Python را از https://python.org نصب کنید
2. مطمئن شوید Python در PATH است
3. Command Prompt را restart کنید

### مشکل 2: "Virtual environment failed"
**راه‌حل:**
1. از VIP_ADVANCED_LAUNCHER.bat استفاده کنید
2. گزینه "Emergency Recovery" را انتخاب کنید
3. یا دستی virtual environment بسازید:
```bash
python -m venv venv
```

### مشکل 3: "Dependencies installation failed"
**راه‌حل:**
1. اتصال اینترنت را بررسی کنید
2. از VPN استفاده کنید اگر محدودیت دارید
3. دستی dependencies را نصب کنید:
```bash
pip install playwright pandas cryptography
```

### مشکل 4: "PowerShell execution policy"
**راه‌حل:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

---

## 📊 مقایسه Launcher ها

| ویژگی | QUICK_START | ADVANCED | ULTIMATE | SMART |
|--------|-------------|----------|----------|-------|
| سرعت راه‌اندازی | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| امکانات | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| عیب‌یابی | ⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| سادگی | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| Cross-platform | ❌ | ❌ | ❌ | ✅ |

---

## 🚀 مراحل راه‌اندازی کامل

### مرحله 1: انتخاب Launcher
بر اساس سطح تجربه خود یکی از launcher ها را انتخاب کنید

### مرحله 2: اجرای Launcher
فایل انتخابی را اجرا کنید

### مرحله 3: نصب Chrome Extension
از راهنمای launcher برای نصب extension استفاده کنید

### مرحله 4: اتصال به Quotex
1. Chrome را باز کنید
2. به https://qxbroker.com/en/trade بروید
3. وارد حساب خود شوید
4. Extension را فعال کنید

### مرحله 5: تأیید عملکرد
در VIP BIG BANG بررسی کنید که اطلاعات واقعی نمایش داده می‌شود

---

## 📞 پشتیبانی

در صورت بروز مشکل:

1. **تست سیستم:**
```bash
python test_real_data_system.py
```

2. **بازیابی اضطراری:**
```bash
VIP_ADVANCED_LAUNCHER.bat
# سپس گزینه 9 (Emergency Recovery)
```

3. **بررسی لاگ‌ها:**
```
logs/ folder را بررسی کنید
```

---

**🎉 با هر کدام از launcher ها که راحت‌تر هستید، VIP BIG BANG را راه‌اندازی کنید!**
