"""
🚀 VIP BIG BANG ULTIMATE LIVE TRADING SYSTEM
🌐 LIVE QUOTEX CHART + QUANTUM ANALYSIS + CHROME EXTENSION
🔥 COMPLETE PROFESSIONAL TRADING ROBOT WITH EMBEDDED BROWSER
"""

import sys
import asyncio
import logging
from datetime import datetime
from PySide6.QtWidgets import (Q<PERSON><PERSON>lication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QLabel, QPushButton, QGroupBox,
                               QSpinBox, QTextEdit)
from PySide6.QtCore import QObject, Signal, Slot, QTimer, Qt
from PySide6.QtGui import QFont

# Import VIP BIG BANG components
from core.quantum_ultra_fast_engine import QuantumUltraFastEngine
from core.ultimate_hybrid_connector import UltimateHybridConnector
from core.auto_login_manager import AutoLoginManager, LoginDialog
from ui.live_quotex_webview import LiveQuotexWebView
from core.settings import Settings
from utils.logger import setup_logger

class VIPUltimateLiveTradingSystem(QMainWindow):
    """
    🚀 VIP BIG BANG ULTIMATE LIVE TRADING SYSTEM
    🌐 Complete trading robot with embedded live Quotex chart
    🔥 Quantum analysis + Multiple connection methods
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 VIP BIG BANG - Ultimate Live Trading System")

        # Initialize logger first
        self.logger = setup_logger("UltimateLiveSystem")

        # Initialize Ultimate System Detector
        try:
            from core.ultimate_system_detector import UltimateSystemDetector, SystemInfoWidget

            self.system_detector = UltimateSystemDetector()
            self.system_info_widget = SystemInfoWidget(self.system_detector)

            # Get optimal window configuration
            optimal_config = self.system_detector.get_optimal_window_config()

            if optimal_config:
                # Apply optimal window size and position
                self.setGeometry(
                    optimal_config["x"],
                    optimal_config["y"],
                    optimal_config["width"],
                    optimal_config["height"]
                )
                self.logger.info(f"🎯 Optimal window applied: {optimal_config['width']}x{optimal_config['height']}")
            else:
                # Fallback geometry
                self.setGeometry(100, 100, 1600, 1000)
                self.logger.warning("⚠️ Using fallback window geometry")

            # Start performance monitoring
            self.system_detector.start_performance_monitoring(5000)  # Every 5 seconds

            self.logger.info("🖥️ Ultimate System Detector initialized")

        except Exception as e:
            self.logger.error(f"❌ Ultimate System Detector error: {e}")
            self.system_detector = None
            self.system_info_widget = None
            # Set manual geometry as fallback
            self.setGeometry(100, 100, 1600, 1000)
        
        # Initialize components
        self.settings = Settings()
        self.quantum_engine = QuantumUltraFastEngine(self.settings)
        self.hybrid_connector = UltimateHybridConnector(self.settings)
        self.auto_login_manager = AutoLoginManager()
        
        # UI components
        self.live_webview = None
        self.control_panel = None
        self.analysis_panel = None
        self.stats_panel = None
        
        # System state
        self.auto_trade_enabled = False
        self.system_active = False
        self.current_timeframe = (15, 5)  # 15s analysis, 5s trades
        
        # Trading statistics
        self.trading_stats = {
            'session_start': datetime.now(),
            'total_trades': 0,
            'successful_trades': 0,
            'total_profit': 0.0,
            'win_rate': 0.0,
            'current_streak': 0
        }
        
        # Setup UI
        self.setup_ui()
        self.setup_styles()
        self.connect_signals()
        
        # Auto-start quantum system
        self.startup_timer = QTimer()
        self.startup_timer.timeout.connect(self.start_quantum_ultimate_system)
        self.startup_timer.setSingleShot(True)
        self.startup_timer.start(2000)  # Give more time for quantum initialization
        
        self.logger.info("🚀 VIP Ultimate Live Trading System initialized")
    
    def setup_ui(self):
        """🎨 Setup the main UI layout"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Left panel (Controls + Analysis)
        left_panel = self.create_left_panel()
        main_layout.addWidget(left_panel, 1)
        
        # Center panel (Live Quotex WebView)
        center_panel = self.create_center_panel()
        main_layout.addWidget(center_panel, 3)
        
        # Right panel (Statistics + Logs)
        right_panel = self.create_right_panel()
        main_layout.addWidget(right_panel, 1)
    
    def create_left_panel(self):
        """🎛️ Create left control panel"""
        panel = QWidget()
        panel.setMaximumWidth(350)
        layout = QVBoxLayout(panel)
        
        # Header
        header = QLabel("🎛️ VIP BIG BANG CONTROL")
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("font-size: 18px; font-weight: bold; color: #00FF00; padding: 10px;")
        layout.addWidget(header)
        
        # System status
        self.system_status = self.create_system_status()
        layout.addWidget(self.system_status)
        
        # Trading controls
        self.trading_controls = self.create_trading_controls()
        layout.addWidget(self.trading_controls)
        
        # Quantum analysis display
        self.analysis_display = self.create_analysis_display()
        layout.addWidget(self.analysis_display)
        
        # Timeframe controls
        self.timeframe_controls = self.create_timeframe_controls()
        layout.addWidget(self.timeframe_controls)
        
        layout.addStretch()
        
        return panel
    
    def create_center_panel(self):
        """🌐 Create center panel with live Quotex WebView"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Header
        header = QLabel("🌐 LIVE QUOTEX TRADING PLATFORM")
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("font-size: 16px; font-weight: bold; color: #00FF00; padding: 5px;")
        layout.addWidget(header)
        
        # Live WebView
        self.live_webview = LiveQuotexWebView()
        layout.addWidget(self.live_webview)
        
        return panel
    
    def create_right_panel(self):
        """📊 Create right statistics panel"""
        panel = QWidget()
        panel.setMaximumWidth(350)
        layout = QVBoxLayout(panel)
        
        # Header
        header = QLabel("📊 TRADING STATISTICS")
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("font-size: 18px; font-weight: bold; color: #00FF00; padding: 10px;")
        layout.addWidget(header)
        
        # Statistics display
        self.stats_display = self.create_stats_display()
        layout.addWidget(self.stats_display)
        
        # Trade log
        self.trade_log = self.create_trade_log()
        layout.addWidget(self.trade_log)
        
        # Connection status
        self.connection_display = self.create_connection_display()
        layout.addWidget(self.connection_display)
        
        return panel
    
    def create_system_status(self):
        """📊 Create system status widget"""
        widget = QGroupBox("🔋 System Status")
        layout = QVBoxLayout(widget)
        
        self.status_labels = {
            'quantum': QLabel("⚡ Quantum Engine: Initializing..."),
            'webview': QLabel("🌐 Live WebView: Loading..."),
            'hybrid': QLabel("🔗 Hybrid Connector: Starting..."),
            'extension': QLabel("🔌 Chrome Extension: Detecting...")
        }
        
        for label in self.status_labels.values():
            label.setStyleSheet("color: #FFAA00; padding: 5px;")
            layout.addWidget(label)
        
        return widget
    
    def create_trading_controls(self):
        """🎮 Create trading control buttons"""
        widget = QGroupBox("🎮 Trading Controls")
        layout = QVBoxLayout(widget)
        
        # Auto-trade toggle
        self.auto_trade_btn = QPushButton("🤖 Enable Auto-Trade")
        self.auto_trade_btn.clicked.connect(self.toggle_auto_trade)
        layout.addWidget(self.auto_trade_btn)
        
        # Manual trade buttons
        manual_layout = QHBoxLayout()
        
        self.call_btn = QPushButton("📈 CALL")
        self.call_btn.clicked.connect(lambda: self.manual_trade("CALL"))
        self.call_btn.setStyleSheet("background: #00AA00;")
        manual_layout.addWidget(self.call_btn)
        
        self.put_btn = QPushButton("📉 PUT")
        self.put_btn.clicked.connect(lambda: self.manual_trade("PUT"))
        self.put_btn.setStyleSheet("background: #AA0000;")
        manual_layout.addWidget(self.put_btn)
        
        layout.addLayout(manual_layout)
        
        # Trade amount
        amount_layout = QHBoxLayout()
        amount_layout.addWidget(QLabel("💰 Amount:"))
        self.amount_input = QSpinBox()
        self.amount_input.setRange(1, 1000)
        self.amount_input.setValue(10)
        self.amount_input.setSuffix(" $")
        amount_layout.addWidget(self.amount_input)
        layout.addLayout(amount_layout)
        
        return widget
    
    def create_analysis_display(self):
        """🧠 Create quantum analysis display"""
        widget = QGroupBox("🧠 Quantum Analysis")
        layout = QVBoxLayout(widget)
        
        self.analysis_labels = {
            'signal': QLabel("🎯 Signal: Analyzing..."),
            'confidence': QLabel("💪 Confidence: 0%"),
            'direction': QLabel("🧭 Direction: NEUTRAL"),
            'speed': QLabel("⚡ Speed: 0ms")
        }
        
        for label in self.analysis_labels.values():
            label.setStyleSheet("color: #00AAFF; padding: 3px;")
            layout.addWidget(label)
        
        return widget
    
    def create_timeframe_controls(self):
        """⏱️ Create timeframe control widget"""
        widget = QGroupBox("⏱️ Timeframe Settings")
        layout = QVBoxLayout(widget)
        
        # Analysis interval
        analysis_layout = QHBoxLayout()
        analysis_layout.addWidget(QLabel("📊 Analysis:"))
        self.analysis_interval = QSpinBox()
        self.analysis_interval.setRange(5, 300)
        self.analysis_interval.setValue(15)
        self.analysis_interval.setSuffix("s")
        analysis_layout.addWidget(self.analysis_interval)
        layout.addLayout(analysis_layout)
        
        # Trade duration
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("⏱️ Duration:"))
        self.trade_duration = QSpinBox()
        self.trade_duration.setRange(5, 300)
        self.trade_duration.setValue(5)
        self.trade_duration.setSuffix("s")
        duration_layout.addWidget(self.trade_duration)
        layout.addLayout(duration_layout)
        
        # Apply button
        apply_btn = QPushButton("✅ Apply Timeframe")
        apply_btn.clicked.connect(self.apply_timeframe)
        layout.addWidget(apply_btn)
        
        return widget
    
    def create_stats_display(self):
        """📈 Create statistics display"""
        widget = QGroupBox("📈 Session Statistics")
        layout = QVBoxLayout(widget)
        
        self.stats_labels = {
            'session_time': QLabel("⏰ Session: 00:00:00"),
            'total_trades': QLabel("🎯 Total Trades: 0"),
            'win_rate': QLabel("🏆 Win Rate: 0%"),
            'profit': QLabel("💰 Profit: $0.00"),
            'streak': QLabel("🔥 Streak: 0")
        }
        
        for label in self.stats_labels.values():
            label.setStyleSheet("color: #00FF88; padding: 3px;")
            layout.addWidget(label)
        
        # Update timer
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats_display)
        self.stats_timer.start(1000)
        
        return widget
    
    def create_trade_log(self):
        """📝 Create trade log widget"""
        widget = QGroupBox("📝 Trade Log")
        layout = QVBoxLayout(widget)
        
        self.trade_log_text = QTextEdit()
        self.trade_log_text.setMaximumHeight(200)
        self.trade_log_text.setStyleSheet("""
            QTextEdit {
                background: #0a0a0a;
                color: #00FF00;
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
        """)
        layout.addWidget(self.trade_log_text)
        
        return widget
    
    def create_connection_display(self):
        """🔗 Create connection status display"""
        widget = QGroupBox("🔗 Connection Status")
        layout = QVBoxLayout(widget)
        
        self.connection_labels = {
            'webview': QLabel("🌐 WebView: 🔴"),
            'extension': QLabel("🔌 Extension: 🔴"),
            'websocket': QLabel("📡 WebSocket: 🔴"),
            'dom': QLabel("🕵️‍♂️ DOM: 🔴")
        }
        
        for label in self.connection_labels.values():
            label.setStyleSheet("color: #FFFF00; padding: 3px;")
            layout.addWidget(label)
        
        return widget
    
    def setup_styles(self):
        """🎨 Setup application styles"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0a0a0a, stop:1 #1a1a2e);
                color: #00FF00;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #00FF00;
                border-radius: 10px;
                margin: 8px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a1a2e, stop:1 #16213e);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #00FF00;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #16213e, stop:1 #0f1419);
                color: #00FF00;
                border: 2px solid #00FF00;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
                margin: 4px;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00FF00, stop:1 #00AA00);
                color: #000000;
                border: 2px solid #00AA00;
            }
            QPushButton:pressed {
                background: #00FF00;
                color: #000000;
                border: 2px solid #00FF00;
            }
            QLabel {
                color: #00FF00;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
                padding: 4px;
            }
            QSpinBox {
                background: #1a1a2e;
                color: #00FF00;
                border: 2px solid #00FF00;
                padding: 8px;
                border-radius: 5px;
                font-size: 12px;
                min-height: 20px;
            }
            QSpinBox:focus {
                border: 2px solid #00AA00;
                background: #16213e;
            }
            QTextEdit {
                background: #0a0a0a;
                color: #00FF00;
                border: 2px solid #00FF00;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                padding: 5px;
            }
        """)
    
    def connect_signals(self):
        """🔗 Connect all signals"""
        if self.live_webview:
            self.live_webview.priceUpdated.connect(self.on_price_updated)
            self.live_webview.tradeExecuted.connect(self.on_trade_executed)
            self.live_webview.balanceChanged.connect(self.on_balance_changed)
            self.live_webview.connectionChanged.connect(self.on_webview_connection_changed)
    
    def start_ultimate_system(self):
        """🚀 Start the ultimate trading system"""
        try:
            # Create event loop if not exists
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Start async system
            asyncio.ensure_future(self._async_start_system())

        except Exception as e:
            print(f"❌ System startup error: {e}")

    def start_quantum_ultimate_system(self):
        """🚀 Start the quantum ultimate trading system"""
        try:
            self.logger.info("🚀 Starting Quantum Ultimate System...")

            # Update status
            self.status_labels['quantum'].setText("🚀 Quantum Engine: Initializing...")
            self.status_labels['webview'].setText("🌐 Quantum WebView: Starting...")
            self.status_labels['hybrid'].setText("🔗 Quantum Connector: Preparing...")
            self.status_labels['extension'].setText("🔌 Quantum Extension: Auto-Installing...")

            # Start quantum system
            self.start_ultimate_system()

            self.logger.info("🏆 Quantum Ultimate System startup initiated")

        except Exception as e:
            self.logger.error(f"❌ Quantum system startup error: {e}")
    
    async def _async_start_system(self):
        """🚀 Async system startup"""
        try:
            self.logger.info("🚀 Starting Ultimate Live Trading System...")
            
            # Start quantum engine
            await self.quantum_engine.set_timeframe_and_duration(15, 5)
            self.status_labels['quantum'].setText("⚡ Quantum Engine: ✅ Active")
            self.status_labels['quantum'].setStyleSheet("color: #00FF00; padding: 5px;")
            
            # Start hybrid connector
            result = await self.hybrid_connector.start_ultimate_connection()
            if result['success']:
                self.status_labels['hybrid'].setText("🔗 Hybrid Connector: ✅ Active")
                self.status_labels['hybrid'].setStyleSheet("color: #00FF00; padding: 5px;")
                
                # Update connection status
                methods = result.get('methods', {})
                self.update_connection_status(methods)
            
            # Start market analysis
            self.start_market_analysis()
            
            self.system_active = True
            self.logger.info("🏆 Ultimate Live Trading System started successfully")
            
        except Exception as e:
            self.logger.error(f"❌ System startup failed: {e}")
    
    def start_market_analysis(self):
        """📊 Start continuous market analysis"""
        self.analysis_timer = QTimer()
        self.analysis_timer.timeout.connect(self.perform_quantum_analysis)
        self.analysis_timer.start(1000)  # Analyze every second
    
    def perform_quantum_analysis(self):
        """🧠 Perform quantum analysis"""
        asyncio.create_task(self._async_quantum_analysis())
    
    async def _async_quantum_analysis(self):
        """🧠 Async quantum analysis"""
        try:
            # Get current price from WebView
            current_price = self.live_webview.get_current_price() if self.live_webview else 1.07000
            
            # Prepare market data
            market_data = {
                'price': current_price,
                'volume': 5000,
                'high': current_price + 0.0005,
                'low': current_price - 0.0005,
                'open': current_price,
                'close': current_price,
                'timestamp': datetime.now().timestamp()
            }
            
            # Quantum analysis
            import time
            start_time = time.perf_counter()
            signal = await self.quantum_engine.quantum_lightning_analysis(market_data)
            analysis_time = (time.perf_counter() - start_time) * 1000
            
            # Update analysis display
            self.analysis_labels['signal'].setText(f"🎯 Signal: {signal.direction}")
            self.analysis_labels['confidence'].setText(f"💪 Confidence: {signal.confidence:.1%}")
            self.analysis_labels['direction'].setText(f"🧭 Direction: {signal.direction}")
            self.analysis_labels['speed'].setText(f"⚡ Speed: {analysis_time:.1f}ms")
            
            # Auto-trade if enabled and signal is strong
            if (self.auto_trade_enabled and 
                signal.confidence >= 0.85 and 
                signal.direction != 'NEUTRAL'):
                
                await self.execute_auto_trade(signal)
            
        except Exception as e:
            self.logger.debug(f"Analysis error: {e}")
    
    async def execute_auto_trade(self, signal):
        """🤖 Execute automatic trade"""
        try:
            amount = self.amount_input.value()
            duration = self.trade_duration.value()
            
            # Execute through WebView
            if self.live_webview and self.live_webview.is_webview_connected():
                success = self.live_webview.execute_trade("EUR/USD", signal.direction, amount, duration)
                
                if success:
                    self.log_trade(f"🤖 AUTO: {signal.direction} ${amount} (Conf: {signal.confidence:.1%})")
            
        except Exception as e:
            self.logger.error(f"❌ Auto-trade error: {e}")
    
    def toggle_auto_trade(self):
        """🤖 Toggle auto-trading"""
        self.auto_trade_enabled = not self.auto_trade_enabled
        
        if self.auto_trade_enabled:
            self.auto_trade_btn.setText("🛑 Disable Auto-Trade")
            self.auto_trade_btn.setStyleSheet("background: #AA0000;")
        else:
            self.auto_trade_btn.setText("🤖 Enable Auto-Trade")
            self.auto_trade_btn.setStyleSheet("")
        
        self.logger.info(f"🤖 Auto-trade: {'Enabled' if self.auto_trade_enabled else 'Disabled'}")
    
    def manual_trade(self, direction):
        """📈📉 Execute manual trade"""
        try:
            amount = self.amount_input.value()
            duration = self.trade_duration.value()
            
            if self.live_webview and self.live_webview.is_webview_connected():
                success = self.live_webview.execute_trade("EUR/USD", direction, amount, duration)
                
                if success:
                    self.log_trade(f"👤 MANUAL: {direction} ${amount}")
            
        except Exception as e:
            self.logger.error(f"❌ Manual trade error: {e}")
    
    def apply_timeframe(self):
        """⏱️ Apply new timeframe settings"""
        analysis_interval = self.analysis_interval.value()
        trade_duration = self.trade_duration.value()
        
        asyncio.create_task(self.quantum_engine.set_timeframe_and_duration(analysis_interval, trade_duration))
        
        self.current_timeframe = (analysis_interval, trade_duration)
        self.logger.info(f"⏱️ Timeframe updated: {analysis_interval}s/{trade_duration}s")
    
    def on_price_updated(self, prices):
        """📈 Handle price updates from WebView"""
        # Price updates are already handled by WebView UI
        pass
    
    def on_trade_executed(self, trade_data):
        """💰 Handle trade execution results"""
        try:
            direction = trade_data.get('direction', 'N/A')
            result = trade_data.get('result', 'pending')
            profit = trade_data.get('profit', 0)
            
            # Update statistics
            self.trading_stats['total_trades'] += 1
            
            if result == 'win':
                self.trading_stats['successful_trades'] += 1
                self.trading_stats['current_streak'] += 1
            else:
                self.trading_stats['current_streak'] = 0
            
            self.trading_stats['total_profit'] += profit
            self.trading_stats['win_rate'] = (
                self.trading_stats['successful_trades'] / 
                self.trading_stats['total_trades'] * 100
            )
            
            # Log trade result
            self.log_trade(f"💰 RESULT: {direction} - {result.upper()} (${profit:+.2f})")
            
        except Exception as e:
            self.logger.error(f"❌ Trade result error: {e}")
    
    def on_balance_changed(self, balance):
        """💳 Handle balance changes"""
        # Balance updates are already handled by WebView UI
        pass
    
    def on_webview_connection_changed(self, connected):
        """🌐 Handle WebView connection changes"""
        if connected:
            self.status_labels['webview'].setText("🌐 Live WebView: ✅ Connected")
            self.status_labels['webview'].setStyleSheet("color: #00FF00; padding: 5px;")
            self.connection_labels['webview'].setText("🌐 WebView: 🟢")
        else:
            self.status_labels['webview'].setText("🌐 Live WebView: 🔴 Disconnected")
            self.status_labels['webview'].setStyleSheet("color: #FF0000; padding: 5px;")
            self.connection_labels['webview'].setText("🌐 WebView: 🔴")
    
    def update_connection_status(self, methods):
        """🔗 Update connection status display"""
        status_map = {True: "🟢", False: "🔴"}
        
        self.connection_labels['extension'].setText(f"🔌 Extension: {status_map.get(methods.get('extension', False))}")
        self.connection_labels['websocket'].setText(f"📡 WebSocket: {status_map.get(methods.get('websocket', False))}")
        self.connection_labels['dom'].setText(f"🕵️‍♂️ DOM: {status_map.get(methods.get('stealth_browser', False))}")
    
    def update_stats_display(self):
        """📊 Update statistics display"""
        try:
            # Session time
            session_duration = datetime.now() - self.trading_stats['session_start']
            hours, remainder = divmod(session_duration.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            session_time = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            
            # Update labels
            self.stats_labels['session_time'].setText(f"⏰ Session: {session_time}")
            self.stats_labels['total_trades'].setText(f"🎯 Total Trades: {self.trading_stats['total_trades']}")
            self.stats_labels['win_rate'].setText(f"🏆 Win Rate: {self.trading_stats['win_rate']:.1f}%")
            self.stats_labels['profit'].setText(f"💰 Profit: ${self.trading_stats['total_profit']:+.2f}")
            self.stats_labels['streak'].setText(f"🔥 Streak: {self.trading_stats['current_streak']}")
            
        except Exception as e:
            self.logger.debug(f"Stats update error: {e}")
    
    def log_trade(self, message):
        """📝 Log trade message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        self.trade_log_text.append(log_message)
        
        # Keep only last 100 lines
        if self.trade_log_text.document().blockCount() > 100:
            cursor = self.trade_log_text.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
    
    def closeEvent(self, event):
        """🛑 Handle application close"""
        try:
            self.logger.info("🛑 Shutting down Ultimate Live Trading System...")
            
            # Stop timers
            if hasattr(self, 'analysis_timer'):
                self.analysis_timer.stop()
            if hasattr(self, 'stats_timer'):
                self.stats_timer.stop()
            
            # Stop hybrid connector
            asyncio.create_task(self.hybrid_connector.stop_hybrid_system())
            
            event.accept()
            
        except Exception as e:
            self.logger.error(f"❌ Shutdown error: {e}")
            event.accept()

def main():
    """🚀 Main application entry point"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("VIP BIG BANG Ultimate Live Trading System")
    app.setApplicationVersion("3.0.0")
    app.setOrganizationName("VIP BIG BANG")
    
    # Create and show main window
    window = VIPUltimateLiveTradingSystem()
    window.show()
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
