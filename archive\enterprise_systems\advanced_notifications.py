"""
🔔 VIP BIG BANG - Advanced Notification System
سیستم اعلان‌های پیشرفته با انیمیشن و صدا
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtMultimedia import QSoundEffect

class NotificationWidget(QFrame):
    """ویجت اعلان با انیمیشن"""
    
    # Signals
    notification_clicked = Signal(str)
    notification_closed = Signal(str)
    
    def __init__(self, notification_id, title, message, notification_type="info", duration=5000):
        super().__init__()
        self.notification_id = notification_id
        self.title = title
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        
        self.setup_widget()
        self.setup_animations()
        
        # Auto-close timer
        if duration > 0:
            QTimer.singleShot(duration, self.close_notification)
    
    def setup_widget(self):
        """تنظیم ویجت اعلان"""
        self.setFixedSize(350, 100)
        self.setFrameStyle(QFrame.Shape.Box)
        
        # Layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(10)
        
        # Icon
        icon_label = QLabel()
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        if self.notification_type == "success":
            icon_label.setText("✅")
            color = "#7ED321"
        elif self.notification_type == "warning":
            icon_label.setText("⚠️")
            color = "#F5A623"
        elif self.notification_type == "error":
            icon_label.setText("❌")
            color = "#D0021B"
        elif self.notification_type == "signal":
            icon_label.setText("📡")
            color = "#4A90E2"
        elif self.notification_type == "trade":
            icon_label.setText("💰")
            color = "#9013FE"
        else:
            icon_label.setText("ℹ️")
            color = "#00BCD4"
        
        icon_label.setStyleSheet(f"""
            font-size: 24px;
            background: {color};
            border-radius: 20px;
            border: 2px solid white;
        """)
        layout.addWidget(icon_label)
        
        # Content
        content_layout = QVBoxLayout()
        content_layout.setSpacing(5)
        
        # Title
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 14px;
            font-weight: bold;
            color: {color};
        """)
        content_layout.addWidget(title_label)
        
        # Message
        message_label = QLabel(self.message)
        message_label.setStyleSheet("""
            font-size: 12px;
            color: white;
        """)
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)
        
        layout.addLayout(content_layout)
        
        # Close button
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(25, 25)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                border: 1px solid white;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background: rgba(255,0,0,0.8);
            }}
        """)
        close_btn.clicked.connect(self.close_notification)
        layout.addWidget(close_btn, alignment=Qt.AlignmentFlag.AlignTop)
        
        # Widget styling
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0,0,0,0.9),
                    stop:1 rgba(0,0,0,0.7));
                border: 2px solid {color};
                border-radius: 15px;
            }}
        """)
        
        # Drop shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        self.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """تنظیم انیمیشن‌ها"""
        # Slide in animation
        self.slide_animation = QPropertyAnimation(self, b"geometry")
        self.slide_animation.setDuration(500)
        self.slide_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Fade animation
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        
        # Pulse animation for important notifications
        if self.notification_type in ["signal", "trade", "error"]:
            self.pulse_animation = QPropertyAnimation(self, b"geometry")
            self.pulse_animation.setDuration(1000)
            self.pulse_animation.setLoopCount(3)
            self.pulse_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
    
    def show_notification(self, target_pos):
        """نمایش اعلان با انیمیشن"""
        # Start position (off-screen)
        start_pos = QRect(target_pos.x() + 400, target_pos.y(), self.width(), self.height())
        end_pos = QRect(target_pos.x(), target_pos.y(), self.width(), self.height())
        
        self.setGeometry(start_pos)
        self.show()
        
        # Animate slide in
        self.slide_animation.setStartValue(start_pos)
        self.slide_animation.setEndValue(end_pos)
        self.slide_animation.start()
        
        # Start pulse for important notifications
        if hasattr(self, 'pulse_animation'):
            QTimer.singleShot(600, self.start_pulse)
    
    def start_pulse(self):
        """شروع انیمیشن پالس"""
        if hasattr(self, 'pulse_animation'):
            current_geo = self.geometry()
            pulse_geo = QRect(
                current_geo.x() - 5,
                current_geo.y() - 5,
                current_geo.width() + 10,
                current_geo.height() + 10
            )
            
            self.pulse_animation.setStartValue(current_geo)
            self.pulse_animation.setEndValue(pulse_geo)
            self.pulse_animation.start()
    
    def close_notification(self):
        """بستن اعلان با انیمیشن"""
        # Fade out
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(self.on_fade_finished)
        self.fade_animation.start()
    
    def on_fade_finished(self):
        """پایان انیمیشن محو شدن"""
        self.notification_closed.emit(self.notification_id)
        self.deleteLater()
    
    def mousePressEvent(self, event):
        """کلیک روی اعلان"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.notification_clicked.emit(self.notification_id)

class VIPNotificationManager(QObject):
    """مدیر اعلان‌های VIP BIG BANG"""
    
    # Signals
    notification_shown = Signal(str, str)
    notification_clicked = Signal(str)
    
    def __init__(self, parent_widget=None):
        super().__init__()
        self.parent_widget = parent_widget
        self.notifications = {}
        self.notification_counter = 0
        self.max_notifications = 5
        
        # Sound effects
        self.setup_sounds()
        
        # System tray
        self.setup_system_tray()
    
    def setup_sounds(self):
        """تنظیم صداها"""
        self.sounds = {}
        sounds_dir = Path("sounds")
        sounds_dir.mkdir(exist_ok=True)
        
        # Create simple sound effects (you can replace with actual sound files)
        sound_files = {
            "success": "success.wav",
            "warning": "warning.wav", 
            "error": "error.wav",
            "signal": "signal.wav",
            "trade": "trade.wav",
            "info": "info.wav"
        }
        
        for sound_type, filename in sound_files.items():
            sound_path = sounds_dir / filename
            if sound_path.exists():
                sound_effect = QSoundEffect()
                sound_effect.setSource(QUrl.fromLocalFile(str(sound_path)))
                self.sounds[sound_type] = sound_effect
    
    def setup_system_tray(self):
        """تنظیم سیستم تری"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon()
            
            # Icon
            icon = QIcon()
            pixmap = QPixmap(32, 32)
            pixmap.fill(QColor("#4A90E2"))
            icon.addPixmap(pixmap)
            self.tray_icon.setIcon(icon)
            
            # Menu
            tray_menu = QMenu()
            
            show_action = QAction("نمایش VIP BIG BANG", self)
            show_action.triggered.connect(self.show_main_window)
            tray_menu.addAction(show_action)
            
            tray_menu.addSeparator()
            
            quit_action = QAction("خروج", self)
            quit_action.triggered.connect(QApplication.quit)
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
            
            # Tray messages
            self.tray_icon.messageClicked.connect(self.on_tray_message_clicked)
    
    def show_notification(self, title, message, notification_type="info", duration=5000, play_sound=True):
        """نمایش اعلان"""
        self.notification_counter += 1
        notification_id = f"notif_{self.notification_counter}"
        
        # Remove oldest notification if too many
        if len(self.notifications) >= self.max_notifications:
            oldest_id = min(self.notifications.keys())
            self.close_notification(oldest_id)
        
        # Create notification widget
        notification = NotificationWidget(
            notification_id, title, message, notification_type, duration
        )
        notification.notification_clicked.connect(self.on_notification_clicked)
        notification.notification_closed.connect(self.on_notification_closed)
        
        # Position calculation
        if self.parent_widget:
            parent_geo = self.parent_widget.geometry()
            x = parent_geo.right() - 370
            y = parent_geo.top() + 50 + (len(self.notifications) * 110)
            target_pos = QPoint(x, y)
        else:
            # Default position (top-right of screen)
            screen = QApplication.primaryScreen().geometry()
            x = screen.right() - 370
            y = 50 + (len(self.notifications) * 110)
            target_pos = QPoint(x, y)
        
        # Show notification
        notification.show_notification(target_pos)
        self.notifications[notification_id] = notification
        
        # Play sound
        if play_sound and notification_type in self.sounds:
            self.sounds[notification_type].play()
        
        # System tray notification for important types
        if notification_type in ["signal", "trade", "error"] and hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage(
                title, message, 
                QSystemTrayIcon.MessageIcon.Information, 
                duration
            )
        
        self.notification_shown.emit(notification_id, title)
        print(f"🔔 Notification: {title} - {message}")
        
        return notification_id
    
    def close_notification(self, notification_id):
        """بستن اعلان خاص"""
        if notification_id in self.notifications:
            self.notifications[notification_id].close_notification()
    
    def close_all_notifications(self):
        """بستن همه اعلان‌ها"""
        for notification_id in list(self.notifications.keys()):
            self.close_notification(notification_id)
    
    def on_notification_clicked(self, notification_id):
        """کلیک روی اعلان"""
        self.notification_clicked.emit(notification_id)
        print(f"🖱️ Notification clicked: {notification_id}")
    
    def on_notification_closed(self, notification_id):
        """بستن اعلان"""
        if notification_id in self.notifications:
            del self.notifications[notification_id]
        
        # Reposition remaining notifications
        self.reposition_notifications()
    
    def reposition_notifications(self):
        """تنظیم مجدد موقعیت اعلان‌ها"""
        for i, (notification_id, notification) in enumerate(self.notifications.items()):
            if self.parent_widget:
                parent_geo = self.parent_widget.geometry()
                x = parent_geo.right() - 370
                y = parent_geo.top() + 50 + (i * 110)
            else:
                screen = QApplication.primaryScreen().geometry()
                x = screen.right() - 370
                y = 50 + (i * 110)
            
            # Animate to new position
            current_geo = notification.geometry()
            new_geo = QRect(x, y, current_geo.width(), current_geo.height())
            
            animation = QPropertyAnimation(notification, b"geometry")
            animation.setDuration(300)
            animation.setStartValue(current_geo)
            animation.setEndValue(new_geo)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            animation.start()
    
    def show_main_window(self):
        """نمایش پنجره اصلی"""
        if self.parent_widget:
            self.parent_widget.show()
            self.parent_widget.raise_()
            self.parent_widget.activateWindow()
    
    def on_tray_message_clicked(self):
        """کلیک روی پیام سیستم تری"""
        self.show_main_window()
    
    # Predefined notification methods
    def show_signal_notification(self, signal_type, direction, confidence):
        """اعلان سیگنال تریدینگ"""
        title = f"🚨 سیگنال {signal_type}"
        message = f"جهت: {direction}\nاطمینان: {confidence}%"
        return self.show_notification(title, message, "signal", 8000)
    
    def show_trade_notification(self, direction, amount, result=None):
        """اعلان معامله"""
        if result:
            title = f"💰 معامله {'موفق' if result == 'win' else 'ناموفق'}"
            message = f"{direction} ${amount}\nنتیجه: {result}"
            notif_type = "success" if result == "win" else "error"
        else:
            title = f"📊 معامله جدید"
            message = f"{direction} ${amount}\nدر حال اجرا..."
            notif_type = "trade"
        
        return self.show_notification(title, message, notif_type, 6000)
    
    def show_connection_notification(self, status):
        """اعلان وضعیت اتصال"""
        if status == "connected":
            title = "✅ اتصال برقرار"
            message = "اتصال به Quotex موفقیت‌آمیز"
            notif_type = "success"
        else:
            title = "❌ قطع اتصال"
            message = "اتصال به Quotex قطع شد"
            notif_type = "error"
        
        return self.show_notification(title, message, notif_type, 5000)
    
    def show_balance_notification(self, old_balance, new_balance):
        """اعلان تغییر موجودی"""
        change = new_balance - old_balance
        if change > 0:
            title = "💰 افزایش موجودی"
            message = f"موجودی: ${new_balance:.2f}\nتغییر: +${change:.2f}"
            notif_type = "success"
        else:
            title = "📉 کاهش موجودی"
            message = f"موجودی: ${new_balance:.2f}\nتغییر: ${change:.2f}"
            notif_type = "warning"
        
        return self.show_notification(title, message, notif_type, 4000)

# Demo application
class NotificationDemo(QMainWindow):
    """نمایش سیستم اعلان‌ها"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔔 VIP BIG BANG - Notification Demo")
        self.setGeometry(100, 100, 800, 600)
        
        # Setup notification manager
        self.notification_manager = VIPNotificationManager(self)
        
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1A1A2E, stop:1 #0F3460);
                color: white;
            }
            QPushButton {
                background: #4A90E2;
                border: 2px solid #4A90E2;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                padding: 10px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: #357ABD;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(50, 50, 50, 50)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("🔔 سیستم اعلان‌های پیشرفته VIP BIG BANG")
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #4A90E2;
            text-align: center;
            padding: 20px;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Buttons grid
        buttons_layout = QGridLayout()
        buttons_layout.setSpacing(15)
        
        # Test buttons
        test_buttons = [
            ("🚨 سیگنال تریدینگ", lambda: self.notification_manager.show_signal_notification("MA6", "CALL", 85)),
            ("💰 معامله موفق", lambda: self.notification_manager.show_trade_notification("CALL", 25, "win")),
            ("📉 معامله ناموفق", lambda: self.notification_manager.show_trade_notification("PUT", 15, "loss")),
            ("✅ اتصال برقرار", lambda: self.notification_manager.show_connection_notification("connected")),
            ("❌ قطع اتصال", lambda: self.notification_manager.show_connection_notification("disconnected")),
            ("💎 افزایش موجودی", lambda: self.notification_manager.show_balance_notification(1000, 1150)),
            ("⚠️ هشدار ریسک", lambda: self.notification_manager.show_notification("هشدار ریسک", "ریسک بالا تشخیص داده شد", "warning")),
            ("ℹ️ اطلاعات عمومی", lambda: self.notification_manager.show_notification("اطلاعات", "سیستم به‌روزرسانی شد", "info")),
            ("🔄 بستن همه", self.notification_manager.close_all_notifications)
        ]
        
        for i, (text, callback) in enumerate(test_buttons):
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            btn.setFixedHeight(60)
            buttons_layout.addWidget(btn, i // 3, i % 3)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    demo = NotificationDemo()
    demo.show()
    
    sys.exit(app.exec())
