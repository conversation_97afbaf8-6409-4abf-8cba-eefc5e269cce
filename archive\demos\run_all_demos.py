"""
🚀 Run All ChatGPT Technique Demos
اجرای تمام نمونه‌های تکنیک‌های ChatGPT
"""

import sys
import subprocess
import os
from PySide6.QtWidgets import *
from PySide6.QtCore import Qt

class DemoLauncher(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 ChatGPT UI Techniques - Demo Launcher")
        self.setGeometry(100, 100, 800, 600)
        
        # ChatGPT-style background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:0.3 #1a1a2e, stop:0.7 #16213e, stop:1 #0f3460);
                color: white;
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(24)
        
        # Title
        title = QLabel("🎨 ChatGPT UI Techniques")
        title.setStyleSheet("""
            font-size: 36px;
            font-weight: 800;
            color: white;
            letter-spacing: 2px;
            margin-bottom: 16px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        subtitle = QLabel("Learn Professional UI Design Techniques")
        subtitle.setStyleSheet("""
            font-size: 16px;
            font-weight: 500;
            color: rgba(255,255,255,0.8);
            letter-spacing: 1px;
            margin-bottom: 32px;
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Demo buttons
        demos = [
            {
                "title": "🔤 Modern Typography",
                "description": "Font hierarchy, weights, and spacing",
                "file": "typography_demo.py",
                "color": "#4CAF50"
            },
            {
                "title": "🌈 Gradient Backgrounds", 
                "description": "Modern gradient techniques and patterns",
                "file": "gradient_demo.py",
                "color": "#2196F3"
            },
            {
                "title": "💫 Shadow Effects",
                "description": "Professional shadow and depth effects",
                "file": "shadow_demo.py",
                "color": "#9C27B0"
            },
            {
                "title": "📐 Perfect Spacing",
                "description": "8-point spacing system and layout",
                "file": "spacing_demo.py",
                "color": "#FF9800"
            },
            {
                "title": "🧩 Component Design",
                "description": "Reusable component architecture",
                "file": "component_demo.py",
                "color": "#F44336"
            }
        ]
        
        # Create demo buttons
        for demo in demos:
            demo_button = self.create_demo_button(
                demo["title"],
                demo["description"], 
                demo["file"],
                demo["color"]
            )
            layout.addWidget(demo_button)
        
        # VIP BIG BANG UIs section
        layout.addWidget(self.create_separator())
        
        vip_title = QLabel("🚀 VIP BIG BANG UIs")
        vip_title.setStyleSheet("""
            font-size: 24px;
            font-weight: 700;
            color: #4CAF50;
            margin: 16px 0;
        """)
        vip_title.setAlignment(Qt.AlignCenter)
        layout.addWidget(vip_title)
        
        # VIP UI buttons
        vip_uis = [
            {
                "title": "🎯 Ultimate UI",
                "description": "Best version with all techniques",
                "file": "vip_ui_ultimate.py",
                "color": "#FFD700"
            },
            {
                "title": "🎨 ChatGPT Style UI",
                "description": "ChatGPT-inspired design",
                "file": "vip_ui_chatgpt_style.py", 
                "color": "#4CAF50"
            },
            {
                "title": "📋 Exact Replica UI",
                "description": "Exact match to your image",
                "file": "vip_ui_final_exact.py",
                "color": "#2196F3"
            }
        ]
        
        for ui in vip_uis:
            ui_button = self.create_demo_button(
                ui["title"],
                ui["description"],
                ui["file"], 
                ui["color"]
            )
            layout.addWidget(ui_button)
        
        layout.addStretch()
    
    def create_demo_button(self, title, description, filename, color):
        """Create a demo launch button"""
        button = QPushButton()
        button.setFixedHeight(80)
        button.clicked.connect(lambda: self.launch_demo(filename))
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Icon/Color indicator
        color_indicator = QWidget()
        color_indicator.setFixedSize(8, 60)
        color_indicator.setStyleSheet(f"""
            background: {color};
            border-radius: 4px;
        """)
        button_layout.addWidget(color_indicator)
        
        # Text content
        text_layout = QVBoxLayout()
        text_layout.setSpacing(4)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
            text-align: left;
        """)
        text_layout.addWidget(title_label)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 12px;
            color: rgba(255,255,255,0.7);
            text-align: left;
        """)
        text_layout.addWidget(desc_label)
        
        button_layout.addLayout(text_layout)
        button_layout.addStretch()
        
        # Launch icon
        launch_icon = QLabel("▶")
        launch_icon.setStyleSheet(f"""
            font-size: 20px;
            color: {color};
            margin-right: 16px;
        """)
        button_layout.addWidget(launch_icon)
        
        # Set button layout
        button.setLayout(button_layout)
        
        # Button styling
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.08),
                    stop:1 rgba(255,255,255,0.04));
                border: 1px solid rgba(255,255,255,0.15);
                border-radius: 12px;
                padding: 16px;
                text-align: left;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.12),
                    stop:1 rgba(255,255,255,0.08));
                border: 1px solid {color};
            }}
            QPushButton:pressed {{
                background: rgba(255,255,255,0.05);
            }}
        """)
        
        return button
    
    def create_separator(self):
        """Create a visual separator"""
        separator = QWidget()
        separator.setFixedHeight(2)
        separator.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent, stop:0.5 rgba(255,255,255,0.2), stop:1 transparent);
            margin: 16px 0;
        """)
        return separator
    
    def launch_demo(self, filename):
        """Launch a demo file"""
        try:
            if os.path.exists(filename):
                subprocess.Popen([sys.executable, filename])
            else:
                self.show_error(f"File not found: {filename}")
        except Exception as e:
            self.show_error(f"Error launching {filename}: {str(e)}")
    
    def show_error(self, message):
        """Show error message"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("Error")
        msg_box.setText(message)
        msg_box.setStyleSheet("""
            QMessageBox {
                background: #1a1a2e;
                color: white;
            }
            QMessageBox QPushButton {
                background: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 600;
            }
        """)
        msg_box.exec()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    window = DemoLauncher()
    window.show()
    sys.exit(app.exec())
