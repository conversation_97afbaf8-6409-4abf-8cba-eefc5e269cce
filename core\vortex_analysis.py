"""
VIP BIG BANG Enterprise - Vortex Analyzer
Advanced Vortex Indicator analysis for trend detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import logging
from datetime import datetime

class VortexAnalyzer:
    """
    Enterprise-level Vortex Indicator analysis
    Identifies trend changes and momentum shifts
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("VortexAnalyzer")
        
        # Vortex parameters (Original VIP BIG BANG settings)
        self.period = 5
        self.short_period = 5
        self.long_period = 6
        
        # Signal thresholds
        self.strong_signal_threshold = 0.1
        self.crossover_confirmation = 3
        
        self.logger.debug("Vortex Analyzer initialized")
    
    def calculate_true_range(self, data: pd.DataFrame) -> pd.Series:
        """Calculate True Range for Vortex calculation"""
        high = data['high'] if 'high' in data.columns else data['price']
        low = data['low'] if 'low' in data.columns else data['price']
        close = data['close'] if 'close' in data.columns else data['price']
        
        # Previous close
        prev_close = close.shift(1)
        
        # True Range components
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        # True Range is the maximum of the three
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        return true_range
    
    def calculate_vortex_movements(self, data: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """Calculate positive and negative vortex movements"""
        high = data['high'] if 'high' in data.columns else data['price']
        low = data['low'] if 'low' in data.columns else data['price']
        
        # Previous high and low
        prev_high = high.shift(1)
        prev_low = low.shift(1)
        
        # Vortex movements
        vm_positive = abs(high - prev_low)
        vm_negative = abs(low - prev_high)
        
        return vm_positive, vm_negative
    
    def calculate_vortex_indicators(self, data: pd.DataFrame, period: int = 14) -> Tuple[pd.Series, pd.Series]:
        """Calculate VI+ and VI- indicators"""
        true_range = self.calculate_true_range(data)
        vm_positive, vm_negative = self.calculate_vortex_movements(data)
        
        # Sum over the period
        tr_sum = true_range.rolling(window=period).sum()
        vm_pos_sum = vm_positive.rolling(window=period).sum()
        vm_neg_sum = vm_negative.rolling(window=period).sum()
        
        # Vortex Indicators
        vi_positive = vm_pos_sum / tr_sum
        vi_negative = vm_neg_sum / tr_sum
        
        return vi_positive, vi_negative
    
    def detect_crossovers(self, vi_pos: pd.Series, vi_neg: pd.Series) -> Dict:
        """Detect crossover signals"""
        if len(vi_pos) < 2 or len(vi_neg) < 2:
            return {'crossover': 'NONE', 'strength': 0}
        
        # Current and previous values
        current_pos = vi_pos.iloc[-1]
        current_neg = vi_neg.iloc[-1]
        prev_pos = vi_pos.iloc[-2]
        prev_neg = vi_neg.iloc[-2]
        
        crossover = 'NONE'
        strength = 0
        
        # Bullish crossover: VI+ crosses above VI-
        if prev_pos <= prev_neg and current_pos > current_neg:
            crossover = 'BULLISH'
            strength = abs(current_pos - current_neg)
        
        # Bearish crossover: VI- crosses above VI+
        elif prev_neg <= prev_pos and current_neg > current_pos:
            crossover = 'BEARISH'
            strength = abs(current_neg - current_pos)
        
        # Check for continuation signals
        elif current_pos > current_neg:
            crossover = 'BULLISH_CONTINUATION'
            strength = (current_pos - current_neg) * 0.5
        elif current_neg > current_pos:
            crossover = 'BEARISH_CONTINUATION'
            strength = (current_neg - current_pos) * 0.5
        
        return {
            'crossover': crossover,
            'strength': strength,
            'vi_positive': current_pos,
            'vi_negative': current_neg,
            'difference': current_pos - current_neg
        }
    
    def calculate_vortex_divergence(self, data: pd.DataFrame, vi_pos: pd.Series, vi_neg: pd.Series) -> Dict:
        """Detect divergence between price and vortex indicators"""
        close_prices = data['close'] if 'close' in data.columns else data['price']
        
        if len(close_prices) < 10 or len(vi_pos) < 10:
            return {'divergence': 'NONE', 'strength': 0}
        
        # Recent price trend (last 5 periods)
        recent_prices = close_prices.tail(5)
        price_trend = 'UP' if recent_prices.iloc[-1] > recent_prices.iloc[0] else 'DOWN'
        
        # Recent vortex trend
        recent_vi_diff = (vi_pos - vi_neg).tail(5)
        vortex_trend = 'UP' if recent_vi_diff.iloc[-1] > recent_vi_diff.iloc[0] else 'DOWN'
        
        divergence = 'NONE'
        strength = 0
        
        # Bullish divergence: Price down, Vortex up
        if price_trend == 'DOWN' and vortex_trend == 'UP':
            divergence = 'BULLISH'
            strength = abs(recent_vi_diff.iloc[-1] - recent_vi_diff.iloc[0])
        
        # Bearish divergence: Price up, Vortex down
        elif price_trend == 'UP' and vortex_trend == 'DOWN':
            divergence = 'BEARISH'
            strength = abs(recent_vi_diff.iloc[-1] - recent_vi_diff.iloc[0])
        
        return {
            'divergence': divergence,
            'strength': strength,
            'price_trend': price_trend,
            'vortex_trend': vortex_trend
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main vortex analysis function
        Returns score between 0 (bearish) and 1 (bullish)
        """
        try:
            if len(data) < self.long_period:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for vortex analysis'
                }
            
            # Calculate vortex indicators for different periods
            vi_pos_short, vi_neg_short = self.calculate_vortex_indicators(data, self.short_period)
            vi_pos_medium, vi_neg_medium = self.calculate_vortex_indicators(data, self.period)
            vi_pos_long, vi_neg_long = self.calculate_vortex_indicators(data, self.long_period)
            
            # Analyze crossovers for different timeframes
            crossover_short = self.detect_crossovers(vi_pos_short, vi_neg_short)
            crossover_medium = self.detect_crossovers(vi_pos_medium, vi_neg_medium)
            crossover_long = self.detect_crossovers(vi_pos_long, vi_neg_long)
            
            # Analyze divergence
            divergence = self.calculate_vortex_divergence(data, vi_pos_medium, vi_neg_medium)
            
            # Scoring system
            score_components = []
            
            # Crossover scoring
            for crossover in [crossover_short, crossover_medium, crossover_long]:
                if crossover['crossover'] in ['BULLISH', 'BULLISH_CONTINUATION']:
                    score_components.append(0.7 + crossover['strength'] * 0.3)
                elif crossover['crossover'] in ['BEARISH', 'BEARISH_CONTINUATION']:
                    score_components.append(0.3 - crossover['strength'] * 0.3)
                else:
                    score_components.append(0.5)
            
            # Divergence scoring
            if divergence['divergence'] == 'BULLISH':
                score_components.append(0.8)
            elif divergence['divergence'] == 'BEARISH':
                score_components.append(0.2)
            else:
                score_components.append(0.5)
            
            # Current vortex position scoring
            current_diff = crossover_medium['difference']
            if current_diff > 0.05:
                score_components.append(0.8)
            elif current_diff < -0.05:
                score_components.append(0.2)
            else:
                score_components.append(0.5 + current_diff * 5)  # Normalize around 0.5
            
            # Calculate final score
            final_score = np.mean(score_components)
            final_score = max(0, min(1, final_score))  # Ensure 0-1 range
            
            # Determine direction and confidence
            if final_score > 0.65:
                direction = 'CALL'
                confidence = (final_score - 0.5) * 2
            elif final_score < 0.35:
                direction = 'PUT'
                confidence = (0.5 - final_score) * 2
            else:
                direction = 'NEUTRAL'
                confidence = 1.0 - abs(final_score - 0.5) * 2
            
            # Signal strength
            signal_strength = 'WEAK'
            if confidence > 0.8:
                signal_strength = 'VERY_STRONG'
            elif confidence > 0.6:
                signal_strength = 'STRONG'
            elif confidence > 0.4:
                signal_strength = 'MODERATE'
            
            result = {
                'score': final_score,
                'direction': direction,
                'confidence': confidence,
                'strength': signal_strength,
                'crossovers': {
                    'short': crossover_short,
                    'medium': crossover_medium,
                    'long': crossover_long
                },
                'divergence': divergence,
                'vortex_values': {
                    'vi_positive': crossover_medium['vi_positive'],
                    'vi_negative': crossover_medium['vi_negative'],
                    'difference': crossover_medium['difference']
                },
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.debug(f"Vortex analysis: Score={final_score:.3f}, Direction={direction}, Confidence={confidence:.3f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Vortex analysis failed: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
