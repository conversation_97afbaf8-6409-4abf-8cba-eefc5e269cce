#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 VIP BIG BANG Performance Test
تست عملکرد واقعی سیستم
"""

import sys
import time

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

def test_performance():
    """Test real performance"""
    print('=== Testing VIP BIG BANG Real Performance ===')

    try:
        from core.analysis_engine import AnalysisEngine
        from core.settings import Settings
        
        settings = Settings()
        engine = AnalysisEngine(settings)
        
        # Add some historical data
        for i in range(50):
            engine.update_market_data({
                'price': 1.07500 + (i * 0.0001),
                'volume': 1000 + i,
                'high': 1.07600 + (i * 0.0001),
                'low': 1.07400 + (i * 0.0001),
                'open': 1.07450 + (i * 0.0001),
                'close': 1.07500 + (i * 0.0001)
            })

        print('Market data updated with 50 points')

        # Test analysis
        start_time = time.time()
        result = engine.analyze()
        end_time = time.time()

        processing_time = end_time - start_time
        print(f'Analysis completed in {processing_time:.3f} seconds')

        if 'error' in result:
            print(f'[ERROR] Analysis failed: {result["error"]}')
            return False
        else:
            print(f'[OK] Analysis successful')
            print(f'  Direction: {result.get("direction", "N/A")}')
            print(f'  Confidence: {result.get("confidence", 0):.3f}')
            print(f'  Overall Score: {result.get("overall_score", 0):.3f}')
            print(f'  Signals: {len(result.get("signals", {}))}')

        # Performance check
        if processing_time > 2.0:
            print(f'[WARNING] Analysis too slow: {processing_time:.3f}s (target: <1s)')
        elif processing_time > 1.0:
            print(f'[CAUTION] Analysis acceptable: {processing_time:.3f}s (target: <1s)')
        else:
            print(f'[EXCELLENT] Analysis fast: {processing_time:.3f}s')
            
        return True
        
    except Exception as e:
        print(f'[ERROR] Performance test failed: {e}')
        return False

def test_missing_components():
    """Test for missing components"""
    print('\n=== Checking for Missing Components ===')
    
    missing_components = []
    
    # Check if real-time data feed is working
    try:
        from core.data_extractor import DataExtractor
        print('[OK] Data Extractor available')
    except Exception as e:
        print(f'[MISSING] Data Extractor: {e}')
        missing_components.append('Data Extractor')
    
    # Check if Chrome integration is working
    try:
        from core.chrome_devtools_connector import ChromeDevToolsConnector
        print('[OK] Chrome DevTools Connector available')
    except Exception as e:
        print(f'[MISSING] Chrome DevTools: {e}')
        missing_components.append('Chrome DevTools')
    
    # Check if Quotex login is working
    try:
        from core.quotex_login import QuotexLogin
        print('[OK] Quotex Login available')
    except Exception as e:
        print(f'[MISSING] Quotex Login: {e}')
        missing_components.append('Quotex Login')
    
    # Check if real-time connector is working
    try:
        from core.realtime_quotex_connector import RealtimeQuotexConnector
        print('[OK] Realtime Quotex Connector available')
    except Exception as e:
        print(f'[MISSING] Realtime Connector: {e}')
        missing_components.append('Realtime Connector')
    
    return missing_components

def main():
    """Main test function"""
    print('VIP BIG BANG Diagnostic Test')
    print('Checking for missing or broken components')
    print('-' * 50)
    
    # Test performance
    performance_ok = test_performance()
    
    # Test missing components
    missing = test_missing_components()
    
    print('\n=== Summary ===')
    print(f'Performance Test: {"PASS" if performance_ok else "FAIL"}')
    print(f'Missing Components: {len(missing)}')
    
    if missing:
        print('\nMissing Components:')
        for component in missing:
            print(f'  - {component}')
        
        print('\nSuggestions:')
        print('1. Check if files were accidentally deleted')
        print('2. Restore from archive/ directory')
        print('3. Use GitHub Copilot to regenerate missing components')
    else:
        print('\nAll components present!')
    
    if not performance_ok:
        print('\nPerformance Issues:')
        print('1. Check if analyzers are working correctly')
        print('2. Verify market data is being fed properly')
        print('3. Check for import errors in analyzers')

if __name__ == "__main__":
    main()
