"""
🏢 VIP BIG BANG Enterprise Complex UI
هیچ چیز ساده نباشد - Architecture پیچیده و حرفه‌ای
"""

import sys
import math
import random
import time
from datetime import datetime
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Enterprise Color Scheme
class EnterpriseColors:
    # Primary Enterprise Colors
    ENTERPRISE_BLUE = "#0066CC"
    ENTERPRISE_CYAN = "#00CCFF"
    ENTERPRISE_GREEN = "#00FF66"
    ENTERPRISE_ORANGE = "#FF9900"
    ENTERPRISE_RED = "#FF3366"
    ENTERPRISE_PURPLE = "#9966FF"
    ENTERPRISE_GOLD = "#FFD700"
    
    # Background Gradients
    DARK_PRIMARY = "#0A0A0F"
    DARK_SECONDARY = "#1A1A2E"
    DARK_TERTIARY = "#16213E"
    DARK_QUATERNARY = "#0F3460"
    
    # Status Colors
    STATUS_ONLINE = "#00FF41"
    STATUS_WARNING = "#FFD700"
    STATUS_ERROR = "#FF4444"
    STATUS_PROCESSING = "#00CCFF"

class EnterpriseMetricsWidget(QWidget):
    """Enterprise-level metrics display with real-time updates"""
    
    def __init__(self, title="", value="", unit="", trend=0, color=EnterpriseColors.ENTERPRISE_CYAN):
        super().__init__()
        self.title = title
        self.value = value
        self.unit = unit
        self.trend = trend
        self.color = color
        self.history = []
        self.setup_widget()
        
        # Real-time update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_metrics)
        self.update_timer.start(1000)  # Update every second
    
    def setup_widget(self):
        """Setup enterprise metrics widget"""
        self.setFixedSize(180, 100)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(4)
        
        # Title with enterprise styling
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            font-size: 10px;
            font-weight: 600;
            color: rgba(255,255,255,0.8);
            letter-spacing: 0.5px;
            text-transform: uppercase;
        """)
        layout.addWidget(title_label)
        
        # Value display with trend
        value_layout = QHBoxLayout()
        
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 18px;
            font-weight: 700;
            color: {self.color};
            letter-spacing: 1px;
        """)
        value_layout.addWidget(self.value_label)
        
        # Unit display
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet(f"""
                font-size: 12px;
                color: rgba(255,255,255,0.6);
                font-weight: 500;
            """)
            value_layout.addWidget(unit_label)
        
        value_layout.addStretch()
        
        # Trend indicator
        trend_icon = "↗" if self.trend > 0 else "↘" if self.trend < 0 else "→"
        trend_color = EnterpriseColors.STATUS_ONLINE if self.trend > 0 else EnterpriseColors.STATUS_ERROR if self.trend < 0 else EnterpriseColors.STATUS_WARNING
        
        self.trend_label = QLabel(trend_icon)
        self.trend_label.setStyleSheet(f"""
            font-size: 16px;
            color: {trend_color};
            font-weight: bold;
        """)
        value_layout.addWidget(self.trend_label)
        
        layout.addLayout(value_layout)
        
        # Mini chart
        self.chart_widget = QWidget()
        self.chart_widget.setFixedHeight(25)
        self.chart_widget.paintEvent = self.paint_mini_chart
        layout.addWidget(self.chart_widget)
        
        # Widget styling
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(0, 0, 0, 0.6));
                border: 1px solid {self.color};
                border-radius: 12px;
            }}
        """)
        
        # Add enterprise glow
        self.add_enterprise_glow()
    
    def add_enterprise_glow(self):
        """Add enterprise-level glow effect"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(self.color).darker(120))
        self.setGraphicsEffect(shadow)
    
    def paint_mini_chart(self, event):
        """Paint mini trend chart"""
        painter = QPainter(self.chart_widget)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        if len(self.history) > 1:
            width = self.chart_widget.width()
            height = self.chart_widget.height()
            
            # Draw trend line
            painter.setPen(QPen(QColor(self.color), 2))
            
            for i in range(len(self.history) - 1):
                x1 = (i / max(len(self.history) - 1, 1)) * width
                x2 = ((i + 1) / max(len(self.history) - 1, 1)) * width
                y1 = height - (self.history[i] / 100) * height
                y2 = height - (self.history[i + 1] / 100) * height
                painter.drawLine(int(x1), int(y1), int(x2), int(y2))
    
    def update_metrics(self):
        """Update metrics with simulated real-time data"""
        # Simulate real-time updates
        if "Balance" in self.title:
            new_value = random.uniform(1200, 1300)
            self.value_label.setText(f"${new_value:.2f}")
        elif "%" in self.value:
            new_value = random.uniform(80, 98)
            self.value_label.setText(f"{new_value:.1f}%")
            self.history.append(new_value)
        elif "ms" in self.unit:
            new_value = random.uniform(0.5, 3.0)
            self.value_label.setText(f"{new_value:.1f}")
            self.history.append(new_value * 10)
        else:
            new_value = random.randint(10, 100)
            self.value_label.setText(str(new_value))
            self.history.append(new_value)
        
        # Keep history limited
        if len(self.history) > 20:
            self.history.pop(0)
        
        # Update trend
        if len(self.history) >= 2:
            self.trend = 1 if self.history[-1] > self.history[-2] else -1 if self.history[-1] < self.history[-2] else 0
            trend_icon = "↗" if self.trend > 0 else "↘" if self.trend < 0 else "→"
            trend_color = EnterpriseColors.STATUS_ONLINE if self.trend > 0 else EnterpriseColors.STATUS_ERROR if self.trend < 0 else EnterpriseColors.STATUS_WARNING
            self.trend_label.setText(trend_icon)
            self.trend_label.setStyleSheet(f"""
                font-size: 16px;
                color: {trend_color};
                font-weight: bold;
            """)
        
        self.chart_widget.update()

class EnterpriseControlMatrix(QWidget):
    """Enterprise control matrix with complex functionality"""
    
    def __init__(self):
        super().__init__()
        self.setup_matrix()
    
    def setup_matrix(self):
        """Setup enterprise control matrix"""
        layout = QGridLayout(self)
        layout.setSpacing(12)
        
        # Enterprise control buttons with complex functionality
        controls = [
            (0, 0, "🚀", "AutoTrade\nEngine", "primary", self.activate_autotrade),
            (0, 1, "✓", "Confirm\nMode", "success", self.toggle_confirm_mode),
            (1, 0, "🎯", "Signal\nProcessor", "warning", self.open_signal_processor),
            (1, 1, "🔥", "Market\nHeatmap", "danger", self.show_heatmap),
            (2, 0, "📊", "Economic\nNews", "info", self.open_news_center),
            (2, 1, "😊", "Brothers\nCan", "special", self.activate_brothers_can),
            (3, 0, "⚙️", "System\nConfig", "neutral", self.open_system_config),
            (3, 1, "🔒", "Security\nCenter", "secure", self.open_security_center)
        ]
        
        for row, col, icon, text, btn_type, callback in controls:
            btn = self.create_enterprise_button(icon, text, btn_type, callback)
            layout.addWidget(btn, row, col)
    
    def create_enterprise_button(self, icon, text, btn_type, callback):
        """Create enterprise-level button"""
        btn = QPushButton()
        btn.setFixedSize(110, 80)
        btn.clicked.connect(callback)
        
        # Button content
        content_layout = QVBoxLayout(btn)
        content_layout.setContentsMargins(8, 8, 8, 8)
        content_layout.setSpacing(4)
        
        # Icon
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px; background: transparent;")
        content_layout.addWidget(icon_label)
        
        # Text
        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_label.setWordWrap(True)
        text_label.setStyleSheet("""
            font-size: 9px;
            font-weight: 600;
            color: white;
            background: transparent;
            letter-spacing: 0.5px;
        """)
        content_layout.addWidget(text_label)
        
        # Button styling based on type
        colors = {
            "primary": EnterpriseColors.ENTERPRISE_BLUE,
            "success": EnterpriseColors.ENTERPRISE_GREEN,
            "warning": EnterpriseColors.ENTERPRISE_ORANGE,
            "danger": EnterpriseColors.ENTERPRISE_RED,
            "info": EnterpriseColors.ENTERPRISE_CYAN,
            "special": EnterpriseColors.ENTERPRISE_PURPLE,
            "neutral": EnterpriseColors.ENTERPRISE_GOLD,
            "secure": "#FF6B6B"
        }
        
        color = colors.get(btn_type, EnterpriseColors.ENTERPRISE_CYAN)
        
        btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 0, 0, 0.8),
                    stop:0.5 {color}33,
                    stop:1 rgba(0, 0, 0, 0.8));
                border: 2px solid {color};
                border-radius: 15px;
                color: white;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}66,
                    stop:1 {color}33);
                border: 2px solid {color};
            }}
            QPushButton:pressed {{
                background: {color}99;
            }}
        """)
        
        # Add enterprise glow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(12)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(color).darker(150))
        btn.setGraphicsEffect(shadow)
        
        return btn
    
    # Enterprise callback methods
    def activate_autotrade(self):
        """Activate AutoTrade Engine"""
        print("🚀 AutoTrade Engine Activated")
    
    def toggle_confirm_mode(self):
        """Toggle Confirm Mode"""
        print("✓ Confirm Mode Toggled")
    
    def open_signal_processor(self):
        """Open Signal Processor"""
        print("🎯 Signal Processor Opened")
    
    def show_heatmap(self):
        """Show Market Heatmap"""
        print("🔥 Market Heatmap Displayed")
    
    def open_news_center(self):
        """Open Economic News Center"""
        print("📊 Economic News Center Opened")
    
    def activate_brothers_can(self):
        """Activate Brothers Can Pattern"""
        print("😊 Brothers Can Pattern Activated")
    
    def open_system_config(self):
        """Open System Configuration"""
        print("⚙️ System Configuration Opened")
    
    def open_security_center(self):
        """Open Security Center"""
        print("🔒 Security Center Opened")

class EnterpriseStatusBar(QWidget):
    """Enterprise-level status bar with real-time monitoring"""
    
    def __init__(self):
        super().__init__()
        self.setup_status_bar()
        
        # Real-time status updates
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(2000)  # Update every 2 seconds
    
    def setup_status_bar(self):
        """Setup enterprise status bar"""
        self.setFixedHeight(40)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 5, 15, 5)
        layout.setSpacing(20)
        
        # System status indicators
        status_items = [
            ("🟢", "SYSTEM ONLINE", EnterpriseColors.STATUS_ONLINE),
            ("📡", "API CONNECTED", EnterpriseColors.STATUS_ONLINE),
            ("🔒", "SECURE", EnterpriseColors.STATUS_ONLINE),
            ("⚡", "HIGH PERFORMANCE", EnterpriseColors.STATUS_WARNING),
            ("🧠", "AI LEARNING", EnterpriseColors.STATUS_PROCESSING)
        ]
        
        for icon, text, color in status_items:
            status_widget = self.create_status_indicator(icon, text, color)
            layout.addWidget(status_widget)
        
        layout.addStretch()
        
        # Real-time clock
        self.clock_label = QLabel()
        self.clock_label.setStyleSheet("""
            font-family: 'Consolas', monospace;
            font-size: 12px;
            color: white;
            font-weight: 600;
        """)
        layout.addWidget(self.clock_label)
        
        # Update clock
        self.update_clock()
        clock_timer = QTimer()
        clock_timer.timeout.connect(self.update_clock)
        clock_timer.start(1000)
        
        # Status bar styling
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {EnterpriseColors.DARK_PRIMARY},
                    stop:1 {EnterpriseColors.DARK_SECONDARY});
                border-top: 1px solid {EnterpriseColors.ENTERPRISE_CYAN};
            }}
        """)
    
    def create_status_indicator(self, icon, text, color):
        """Create status indicator"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 14px;")
        layout.addWidget(icon_label)
        
        text_label = QLabel(text)
        text_label.setStyleSheet(f"""
            font-size: 10px;
            color: {color};
            font-weight: 600;
            letter-spacing: 0.5px;
        """)
        layout.addWidget(text_label)
        
        return widget
    
    def update_status(self):
        """Update status indicators"""
        # Simulate status changes
        pass
    
    def update_clock(self):
        """Update real-time clock"""
        current_time = datetime.now().strftime("%H:%M:%S UTC")
        self.clock_label.setText(f"🕐 {current_time}")

class VIPEnterpriseComplexUI(QMainWindow):
    """VIP BIG BANG Enterprise Complex UI - هیچ چیز ساده نباشد"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏢 VIP BIG BANG Enterprise Complex")
        self.setGeometry(100, 100, 1400, 900)

        # Enterprise styling
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {EnterpriseColors.DARK_PRIMARY},
                    stop:0.2 {EnterpriseColors.DARK_SECONDARY},
                    stop:0.5 {EnterpriseColors.DARK_TERTIARY},
                    stop:0.8 {EnterpriseColors.DARK_QUATERNARY},
                    stop:1 {EnterpriseColors.DARK_PRIMARY});
                color: white;
            }}
            QLabel {{
                color: white;
                font-family: 'Segoe UI', 'Roboto', sans-serif;
            }}
        """)

        self.setup_enterprise_ui()

    def setup_enterprise_ui(self):
        """Setup enterprise-level complex UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Enterprise header
        self.create_enterprise_header(main_layout)

        # Main enterprise content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)

        self.create_enterprise_left_panel(content_layout)
        self.create_enterprise_center_panel(content_layout)
        self.create_enterprise_right_panel(content_layout)

        main_layout.addLayout(content_layout)

        # Enterprise status bar
        status_bar = EnterpriseStatusBar()
        main_layout.addWidget(status_bar)

    def create_enterprise_header(self, main_layout):
        """Create enterprise header with complex controls"""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)

        # Left - Enterprise branding
        branding_layout = QHBoxLayout()

        logo = QLabel("🏢")
        logo.setStyleSheet("font-size: 32px;")
        branding_layout.addWidget(logo)

        title_layout = QVBoxLayout()
        title_layout.setSpacing(2)

        main_title = QLabel("VIP BIG BANG")
        main_title.setStyleSheet(f"""
            font-size: 24px;
            font-weight: 800;
            color: {EnterpriseColors.ENTERPRISE_CYAN};
            letter-spacing: 2px;
        """)
        title_layout.addWidget(main_title)

        subtitle = QLabel("ENTERPRISE COMPLEX EDITION")
        subtitle.setStyleSheet(f"""
            font-size: 12px;
            color: {EnterpriseColors.ENTERPRISE_GOLD};
            font-weight: 600;
            letter-spacing: 1px;
        """)
        title_layout.addWidget(subtitle)

        branding_layout.addLayout(title_layout)
        header_layout.addLayout(branding_layout)

        header_layout.addStretch()

        # Center - Currency matrix
        currency_matrix = QHBoxLayout()
        currency_matrix.setSpacing(10)

        currencies = [
            ("✓ BUG/USD", True, EnterpriseColors.ENTERPRISE_GREEN),
            ("GBP/USD", False, EnterpriseColors.ENTERPRISE_CYAN),
            ("EUR/JPY", False, EnterpriseColors.ENTERPRISE_CYAN),
            ("LIVE", True, EnterpriseColors.ENTERPRISE_ORANGE)
        ]

        for text, active, color in currencies:
            btn = QPushButton(text)
            btn.setFixedSize(100, 35)

            if active:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background: {color}33;
                        border: 2px solid {color};
                        border-radius: 8px;
                        color: {color};
                        font-weight: 700;
                        font-size: 11px;
                    }}
                """)
            else:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background: rgba(255,255,255,0.1);
                        border: 1px solid rgba(255,255,255,0.3);
                        border-radius: 8px;
                        color: white;
                        font-size: 11px;
                    }}
                    QPushButton:hover {{
                        border: 1px solid {color};
                    }}
                """)

            currency_matrix.addWidget(btn)

        header_layout.addLayout(currency_matrix)
        header_layout.addStretch()

        # Right - Enterprise controls
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(10)

        # Mode controls
        modes = [("OTC", False), ("LIVE", True), ("DEMO", False)]
        for mode, active in modes:
            btn = QPushButton(mode)
            btn.setFixedSize(70, 35)

            if active:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background: {EnterpriseColors.ENTERPRISE_GREEN}33;
                        border: 2px solid {EnterpriseColors.ENTERPRISE_GREEN};
                        border-radius: 8px;
                        color: {EnterpriseColors.ENTERPRISE_GREEN};
                        font-weight: 700;
                        font-size: 11px;
                    }}
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        background: rgba(255,255,255,0.1);
                        border: 1px solid rgba(255,255,255,0.3);
                        border-radius: 8px;
                        color: white;
                        font-size: 11px;
                    }
                """)

            controls_layout.addWidget(btn)

        # Trading controls
        buy_btn = QPushButton("BUY")
        buy_btn.setFixedSize(80, 35)
        buy_btn.setStyleSheet(f"""
            QPushButton {{
                background: {EnterpriseColors.ENTERPRISE_GREEN};
                border: none;
                border-radius: 18px;
                color: white;
                font-weight: 700;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background: {EnterpriseColors.ENTERPRISE_GREEN}CC;
            }}
        """)
        controls_layout.addWidget(buy_btn)

        sell_btn = QPushButton("SELL")
        sell_btn.setFixedSize(80, 35)
        sell_btn.setStyleSheet(f"""
            QPushButton {{
                background: {EnterpriseColors.ENTERPRISE_RED};
                border: none;
                border-radius: 18px;
                color: white;
                font-weight: 700;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background: {EnterpriseColors.ENTERPRISE_RED}CC;
            }}
        """)
        controls_layout.addWidget(sell_btn)

        header_layout.addLayout(controls_layout)
        main_layout.addLayout(header_layout)

    def create_enterprise_left_panel(self, content_layout):
        """Create enterprise left panel"""
        left_widget = QWidget()
        left_widget.setFixedWidth(300)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)

        # Enterprise metrics
        metrics_title = QLabel("📊 ENTERPRISE METRICS")
        metrics_title.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 700;
            color: {EnterpriseColors.ENTERPRISE_CYAN};
            letter-spacing: 1px;
            margin-bottom: 10px;
        """)
        metrics_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(metrics_title)

        # Metrics grid
        metrics_grid = QGridLayout()
        metrics_grid.setSpacing(10)

        metrics = [
            (0, 0, "Account Balance", "$1,251.76", "", 1, EnterpriseColors.ENTERPRISE_GREEN),
            (0, 1, "Performance", "87.5", "%", 1, EnterpriseColors.ENTERPRISE_CYAN),
            (1, 0, "Latency", "1.2", "ms", -1, EnterpriseColors.ENTERPRISE_ORANGE),
            (1, 1, "Success Rate", "94.2", "%", 1, EnterpriseColors.ENTERPRISE_PURPLE)
        ]

        for row, col, title, value, unit, trend, color in metrics:
            metric = EnterpriseMetricsWidget(title, value, unit, trend, color)
            metrics_grid.addWidget(metric, row, col)

        left_layout.addLayout(metrics_grid)
        left_layout.addStretch()
        content_layout.addWidget(left_widget)

    def create_enterprise_center_panel(self, content_layout):
        """Create enterprise center panel"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(15)

        # Chart title
        chart_title = QLabel("📈 ADVANCED TRADING INTELLIGENCE")
        chart_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 700;
            color: {EnterpriseColors.ENTERPRISE_GOLD};
            letter-spacing: 1px;
        """)
        chart_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        center_layout.addWidget(chart_title)

        # Chart area placeholder
        chart_area = QWidget()
        chart_area.setMinimumHeight(400)
        chart_area.setStyleSheet(f"""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 0, 0, 0.9),
                stop:0.5 {EnterpriseColors.ENTERPRISE_CYAN}22,
                stop:1 rgba(0, 0, 0, 0.9));
            border: 2px solid {EnterpriseColors.ENTERPRISE_CYAN};
            border-radius: 15px;
        """)

        chart_layout = QVBoxLayout(chart_area)
        chart_placeholder = QLabel("📊 REAL-TIME HOLOGRAPHIC CHART")
        chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        chart_placeholder.setStyleSheet(f"""
            color: {EnterpriseColors.ENTERPRISE_CYAN};
            font-size: 20px;
            font-weight: 600;
            letter-spacing: 2px;
        """)
        chart_layout.addWidget(chart_placeholder)

        center_layout.addWidget(chart_area)
        content_layout.addWidget(center_widget, 2)

    def create_enterprise_right_panel(self, content_layout):
        """Create enterprise right panel"""
        right_widget = QWidget()
        right_widget.setFixedWidth(300)
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(15)

        # Control matrix title
        control_title = QLabel("🎛️ ENTERPRISE CONTROL MATRIX")
        control_title.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 700;
            color: {EnterpriseColors.ENTERPRISE_PURPLE};
            letter-spacing: 1px;
            margin-bottom: 10px;
        """)
        control_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        right_layout.addWidget(control_title)

        # Control matrix
        control_matrix = EnterpriseControlMatrix()
        right_layout.addWidget(control_matrix)

        right_layout.addStretch()
        content_layout.addWidget(right_widget)

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set enterprise application style
    app.setStyle('Fusion')

    window = VIPEnterpriseComplexUI()
    window.show()
    sys.exit(app.exec())
