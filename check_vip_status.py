#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 Check VIP BIG BANG System Status
"""

import psutil
import os
import time

def check_vip_system():
    """Check if VIP BIG BANG system is running"""
    print("🔍 Checking VIP BIG BANG System Status...")
    print("=" * 50)
    
    # Check for Python processes
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
        try:
            if proc.info['name'] == 'python.exe':
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                
                python_processes.append({
                    'pid': proc.info['pid'],
                    'cmdline': cmdline,
                    'memory_mb': memory_mb
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"🐍 Found {len(python_processes)} Python processes:")
    
    vip_found = False
    for proc in python_processes:
        print(f"  PID: {proc['pid']}")
        print(f"  Memory: {proc['memory_mb']:.1f} MB")
        print(f"  Command: {proc['cmdline'][:100]}...")
        
        if 'VIP_BIG_BANG_UNIFIED.py' in proc['cmdline']:
            print("  🎯 >>> VIP BIG BANG UNIFIED SYSTEM FOUND! <<<")
            vip_found = True
        
        print()
    
    if vip_found:
        print("✅ VIP BIG BANG System is RUNNING!")
        print("🎨 UI should be visible on your screen")
        print("💡 If you don't see it, try Alt+Tab to find the window")
    else:
        print("❌ VIP BIG BANG System not found")
        print("🔄 You may need to restart it")
    
    print("=" * 50)
    
    # Check data files
    print("📊 Data Files Status:")
    data_files = [
        "shared_quotex_data.json",
        "VIP_BIG_BANG_UNIFIED.py"
    ]
    
    for file in data_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            mtime = os.path.getmtime(file)
            age = time.time() - mtime
            print(f"✅ {file} ({size:,} bytes, {age:.1f}s old)")
        else:
            print(f"❌ {file} (missing)")
    
    return vip_found

if __name__ == "__main__":
    check_vip_system()
