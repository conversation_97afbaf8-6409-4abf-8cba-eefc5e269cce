"""
VIP BIG BANG Enterprise - Fake Breakout Analyzer
Detection of false breakouts from support/resistance levels
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import logging
from datetime import datetime

class FakeBreakoutAnalyzer:
    """
    Fake Breakout - Original VIP BIG BANG indicator
    Detects false breakouts from support/resistance levels
    Places warning alerts on chart for fake breakouts
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("FakeBreakoutAnalyzer")
        
        # Breakout detection parameters
        self.lookback_period = 20
        self.min_touches = 2  # Minimum touches to form support/resistance
        self.breakout_threshold = 0.002  # 0.2% breakout threshold
        self.fake_threshold = 0.001  # 0.1% fake breakout threshold
        
        # Time parameters
        self.breakout_confirmation_candles = 3
        self.fake_detection_candles = 5
        
        self.logger.debug("Fake Breakout Analyzer initialized")
    
    def identify_support_resistance_levels(self, data: pd.DataFrame) -> Dict:
        """Identify key support and resistance levels"""
        if len(data) < self.lookback_period:
            return {
                'support_levels': [],
                'resistance_levels': []
            }
        
        highs = data['high'] if 'high' in data.columns else data['price']
        lows = data['low'] if 'low' in data.columns else data['price']
        
        # Find local highs and lows
        resistance_levels = self.find_resistance_levels(highs)
        support_levels = self.find_support_levels(lows)
        
        return {
            'support_levels': support_levels,
            'resistance_levels': resistance_levels
        }
    
    def find_resistance_levels(self, highs: pd.Series) -> List[Dict]:
        """Find resistance levels from price highs"""
        levels = []
        
        # Find local maxima
        for i in range(2, len(highs) - 2):
            if (highs.iloc[i] > highs.iloc[i-1] and 
                highs.iloc[i] > highs.iloc[i-2] and
                highs.iloc[i] > highs.iloc[i+1] and 
                highs.iloc[i] > highs.iloc[i+2]):
                
                level_price = highs.iloc[i]
                touches = self.count_level_touches(highs, level_price, 'resistance')
                
                if touches >= self.min_touches:
                    levels.append({
                        'price': level_price,
                        'touches': touches,
                        'index': i,
                        'strength': min(touches / 5.0, 1.0)
                    })
        
        # Sort by strength and return top levels
        levels.sort(key=lambda x: x['strength'], reverse=True)
        return levels[:5]  # Top 5 resistance levels
    
    def find_support_levels(self, lows: pd.Series) -> List[Dict]:
        """Find support levels from price lows"""
        levels = []
        
        # Find local minima
        for i in range(2, len(lows) - 2):
            if (lows.iloc[i] < lows.iloc[i-1] and 
                lows.iloc[i] < lows.iloc[i-2] and
                lows.iloc[i] < lows.iloc[i+1] and 
                lows.iloc[i] < lows.iloc[i+2]):
                
                level_price = lows.iloc[i]
                touches = self.count_level_touches(lows, level_price, 'support')
                
                if touches >= self.min_touches:
                    levels.append({
                        'price': level_price,
                        'touches': touches,
                        'index': i,
                        'strength': min(touches / 5.0, 1.0)
                    })
        
        # Sort by strength and return top levels
        levels.sort(key=lambda x: x['strength'], reverse=True)
        return levels[:5]  # Top 5 support levels
    
    def count_level_touches(self, prices: pd.Series, level: float, level_type: str) -> int:
        """Count how many times price touched a level"""
        tolerance = level * 0.001  # 0.1% tolerance
        touches = 0
        
        for price in prices:
            if level_type == 'resistance':
                if abs(price - level) <= tolerance and price >= level - tolerance:
                    touches += 1
            else:  # support
                if abs(price - level) <= tolerance and price <= level + tolerance:
                    touches += 1
        
        return touches
    
    def detect_breakouts(self, data: pd.DataFrame, levels: Dict) -> Dict:
        """Detect breakouts from support/resistance levels"""
        if len(data) < 3:
            return {
                'breakout_type': 'NONE',
                'breakout_level': 0,
                'breakout_strength': 0
            }
        
        current_price = data['close'].iloc[-1] if 'close' in data.columns else data['price'].iloc[-1]
        previous_price = data['close'].iloc[-2] if 'close' in data.columns else data['price'].iloc[-2]
        
        # Check resistance breakouts
        for resistance in levels['resistance_levels']:
            level_price = resistance['price']
            
            # Breakout condition: previous below, current above
            if (previous_price <= level_price and 
                current_price > level_price * (1 + self.breakout_threshold)):
                
                breakout_strength = (current_price - level_price) / level_price
                return {
                    'breakout_type': 'RESISTANCE_BREAKOUT',
                    'breakout_level': level_price,
                    'breakout_strength': breakout_strength,
                    'level_strength': resistance['strength']
                }
        
        # Check support breakouts
        for support in levels['support_levels']:
            level_price = support['price']
            
            # Breakout condition: previous above, current below
            if (previous_price >= level_price and 
                current_price < level_price * (1 - self.breakout_threshold)):
                
                breakout_strength = (level_price - current_price) / level_price
                return {
                    'breakout_type': 'SUPPORT_BREAKOUT',
                    'breakout_level': level_price,
                    'breakout_strength': breakout_strength,
                    'level_strength': support['strength']
                }
        
        return {
            'breakout_type': 'NONE',
            'breakout_level': 0,
            'breakout_strength': 0
        }
    
    def detect_fake_breakouts(self, data: pd.DataFrame, levels: Dict) -> Dict:
        """Detect fake breakouts (failed breakouts)"""
        if len(data) < self.fake_detection_candles:
            return {
                'fake_breakout': False,
                'fake_type': 'NONE',
                'fake_strength': 0
            }
        
        # Look for recent breakouts that failed
        recent_data = data.tail(self.fake_detection_candles)
        
        for i in range(len(recent_data) - 2):
            breakout_data = recent_data.iloc[:i+3]
            breakout_info = self.detect_breakouts(breakout_data, levels)
            
            if breakout_info['breakout_type'] != 'NONE':
                # Check if breakout failed
                current_price = recent_data['close'].iloc[-1] if 'close' in recent_data.columns else recent_data['price'].iloc[-1]
                breakout_level = breakout_info['breakout_level']
                
                if breakout_info['breakout_type'] == 'RESISTANCE_BREAKOUT':
                    # Fake if price came back below resistance
                    if current_price < breakout_level * (1 - self.fake_threshold):
                        return {
                            'fake_breakout': True,
                            'fake_type': 'FAKE_RESISTANCE_BREAKOUT',
                            'fake_strength': (breakout_level - current_price) / breakout_level,
                            'original_breakout': breakout_info
                        }
                
                elif breakout_info['breakout_type'] == 'SUPPORT_BREAKOUT':
                    # Fake if price came back above support
                    if current_price > breakout_level * (1 + self.fake_threshold):
                        return {
                            'fake_breakout': True,
                            'fake_type': 'FAKE_SUPPORT_BREAKOUT',
                            'fake_strength': (current_price - breakout_level) / breakout_level,
                            'original_breakout': breakout_info
                        }
        
        return {
            'fake_breakout': False,
            'fake_type': 'NONE',
            'fake_strength': 0
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main fake breakout analysis function
        Returns comprehensive fake breakout analysis
        """
        try:
            if len(data) < self.lookback_period:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for fake breakout analysis'
                }
            
            # Identify support/resistance levels
            levels = self.identify_support_resistance_levels(data)
            
            # Detect current breakouts
            breakout_info = self.detect_breakouts(data, levels)
            
            # Detect fake breakouts
            fake_info = self.detect_fake_breakouts(data, levels)
            
            # Calculate overall score
            score = 0.5  # Neutral base
            confidence = 0.0
            direction = 'NEUTRAL'
            
            # Adjust score based on fake breakouts
            if fake_info['fake_breakout']:
                if fake_info['fake_type'] == 'FAKE_RESISTANCE_BREAKOUT':
                    # Fake resistance breakout = bearish signal
                    score -= 0.4 * fake_info['fake_strength'] * 10  # Amplify signal
                    direction = 'DOWN'
                    confidence = min(fake_info['fake_strength'] * 10, 1.0)
                elif fake_info['fake_type'] == 'FAKE_SUPPORT_BREAKOUT':
                    # Fake support breakout = bullish signal
                    score += 0.4 * fake_info['fake_strength'] * 10  # Amplify signal
                    direction = 'UP'
                    confidence = min(fake_info['fake_strength'] * 10, 1.0)
            
            # Adjust for valid breakouts (opposite signal)
            elif breakout_info['breakout_type'] != 'NONE':
                if breakout_info['breakout_type'] == 'RESISTANCE_BREAKOUT':
                    score += 0.2 * breakout_info['breakout_strength'] * 100
                    if score > 0.6:
                        direction = 'UP'
                elif breakout_info['breakout_type'] == 'SUPPORT_BREAKOUT':
                    score -= 0.2 * breakout_info['breakout_strength'] * 100
                    if score < 0.4:
                        direction = 'DOWN'
                
                confidence = min(breakout_info['breakout_strength'] * 50, 0.8)
            
            # Ensure bounds
            score = max(0, min(1, score))
            confidence = max(0, min(1, confidence))
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'levels': levels,
                'breakout_info': breakout_info,
                'fake_info': fake_info,
                'details': f'Fake: {fake_info["fake_type"]}, Breakout: {breakout_info["breakout_type"]}'
            }
            
        except Exception as e:
            self.logger.error(f"Fake breakout analysis error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'details': f'Fake breakout analysis failed: {str(e)}'
            }
