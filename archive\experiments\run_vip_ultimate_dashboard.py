#!/usr/bin/env python3
"""
🚀 VIP BIG BANG ULTIMATE DASHBOARD LAUNCHER
💎 Professional Trading System with Real Quotex Connection
🎮 Gaming-style UI with Quantum Stealth Technology
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def check_requirements():
    """🔍 Check and install requirements"""
    print("🔍 Checking requirements...")
    
    required_packages = [
        "PySide6",
        "requests", 
        "websockets",
        "selenium",
        "undetected-chromedriver",
        "pandas",
        "numpy",
        "asyncio"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
            print(f"✅ {package} - OK")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - Missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} installed successfully")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    return True

def setup_chrome_extension():
    """🔧 Setup Chrome extension"""
    print("🔧 Setting up Chrome extension...")
    
    extension_dir = Path("chrome_extension")
    if not extension_dir.exists():
        print("⚠️ Chrome extension directory not found")
        return False
    
    required_files = [
        "manifest.json",
        "background.js", 
        "content.js",
        "stealth-injector.js",
        "stealth-content.js"
    ]
    
    for file in required_files:
        if not (extension_dir / file).exists():
            print(f"❌ Missing extension file: {file}")
            return False
        else:
            print(f"✅ {file} - OK")
    
    print("✅ Chrome extension ready")
    return True

def check_core_systems():
    """🔍 Check VIP BIG BANG core systems"""
    print("🔍 Checking VIP BIG BANG core systems...")
    
    core_modules = [
        "core.realtime_quotex_connector",
        "core.stealth_quotex_connector", 
        "core.quantum_stealth_chrome_connector",
        "core.dynamic_timeframe_manager",
        "core.analysis_engine",
        "core.signal_manager",
        "trading.autotrade",
        "core.settings"
    ]
    
    available_modules = []
    
    for module in core_modules:
        try:
            __import__(module)
            available_modules.append(module)
            print(f"✅ {module} - OK")
        except ImportError as e:
            print(f"⚠️ {module} - Not available ({e})")
    
    if len(available_modules) >= len(core_modules) // 2:
        print("✅ Core systems partially available")
        return True
    else:
        print("⚠️ Core systems limited, will run in demo mode")
        return False

def launch_dashboard():
    """🚀 Launch VIP BIG BANG Ultimate Dashboard"""
    print("\n" + "="*60)
    print("🚀 LAUNCHING VIP BIG BANG ULTIMATE DASHBOARD")
    print("="*60)
    
    try:
        # Import and run the dashboard
        from vip_big_bang_ultimate_dashboard import main
        main()
        
    except ImportError as e:
        print(f"❌ Failed to import dashboard: {e}")
        print("🔧 Trying alternative launch method...")
        
        # Alternative: Run as subprocess
        try:
            subprocess.run([sys.executable, "vip_big_bang_ultimate_dashboard.py"])
        except Exception as e:
            print(f"❌ Alternative launch failed: {e}")
            return False
    
    except Exception as e:
        print(f"❌ Dashboard launch failed: {e}")
        return False
    
    return True

def show_system_info():
    """📊 Show system information"""
    print("\n" + "="*60)
    print("📊 VIP BIG BANG ULTIMATE SYSTEM INFORMATION")
    print("="*60)
    print("🚀 System: VIP BIG BANG Ultimate Trading Dashboard")
    print("💎 Version: 3.0.0")
    print("🎮 UI Framework: PySide6/Qt6")
    print("🔗 Connection: Real Quotex with Quantum Stealth")
    print("📊 Analysis: Dynamic Timeframe Management")
    print("🤖 Trading: Multi-OTC Auto-Trading")
    print("🛡️ Security: Quantum-level Anti-Detection")
    print("🖥️ Display: 4K Support with Auto-Scaling")
    print("🌐 Language: Persian/English Support")
    print("="*60)
    
    print("\n🎯 KEY FEATURES:")
    print("  ✅ Real-time Quotex connection with quantum stealth")
    print("  ✅ Live chart with 5s/1m candles and indicators")
    print("  ✅ Dynamic timeframe adjustment (15s/5s default)")
    print("  ✅ Multi-OTC analysis (5 pairs simultaneously)")
    print("  ✅ Professional gaming UI with 4K support")
    print("  ✅ Quantum-level anti-detection technology")
    print("  ✅ Automatic signal-based trading")
    print("  ✅ Advanced analysis modules (MA6, Vortex, etc.)")
    print("  ✅ Real-time performance monitoring")
    print("  ✅ Emergency stop and risk management")
    
    print("\n🔧 SYSTEM REQUIREMENTS:")
    print("  • Python 3.8+")
    print("  • PySide6/Qt6")
    print("  • Chrome Browser")
    print("  • Internet Connection")
    print("  • 4GB+ RAM")
    print("  • 1366x768+ Display")
    
    print("\n⚠️ IMPORTANT NOTES:")
    print("  • This system connects to your actual Quotex account")
    print("  • Use demo mode for testing before live trading")
    print("  • Quantum stealth technology ensures undetectable operation")
    print("  • All trading activities are logged for analysis")
    print("  • Emergency stop is available at all times")

def main():
    """🚀 Main launcher function"""
    print("🚀 VIP BIG BANG ULTIMATE DASHBOARD LAUNCHER")
    print("💎 Professional Trading System Initialization")
    print("="*60)
    
    # Show system information
    show_system_info()
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements check failed")
        input("Press Enter to exit...")
        return
    
    # Setup Chrome extension
    if not setup_chrome_extension():
        print("⚠️ Chrome extension setup incomplete, some features may be limited")
    
    # Check core systems
    core_available = check_core_systems()
    if not core_available:
        print("⚠️ Running in demo mode due to limited core systems")
    
    print("\n🚀 All checks completed, launching dashboard...")
    time.sleep(2)
    
    # Launch dashboard
    if launch_dashboard():
        print("✅ Dashboard launched successfully")
    else:
        print("❌ Dashboard launch failed")
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Launch cancelled by user")
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        input("Press Enter to exit...")
