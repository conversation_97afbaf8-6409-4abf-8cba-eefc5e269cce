"""
🔧 VIP BIG BANG CHROME PROCESS MANAGER
🚀 SMART CHROME MANAGEMENT - PREVENT MULTIPLE INSTANCES
🎯 SINGLE CHROME INSTANCE WITH REUSE CAPABILITY
"""

import os
import psutil
import subprocess
import time
import logging
from typing import Optional, List

class ChromeProcessManager:
    """
    🔧 CHROME PROCESS MANAGER
    🚀 Manages Chrome instances to prevent multiple launches
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ChromeProcessManager")
        
        # Chrome identification
        self.vip_chrome_profile = "VIPBigBang"
        self.vip_user_data_dir = os.path.join(os.path.expanduser("~"), "VIP_BIG_BANG_Chrome")
        
        # Chrome paths
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        self.logger.info("🔧 Chrome Process Manager initialized")
    
    def find_chrome_executable(self) -> Optional[str]:
        """🔍 Find Chrome executable"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                self.logger.info(f"✅ Chrome found: {path}")
                return path
        
        self.logger.error("❌ Chrome not found")
        return None
    
    def get_vip_chrome_processes(self) -> List[psutil.Process]:
        """🔍 Get existing VIP BIG BANG Chrome processes"""
        vip_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome.exe' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any(self.vip_chrome_profile in arg for arg in cmdline):
                            vip_processes.append(proc)
                            self.logger.info(f"🔍 Found VIP Chrome process: PID {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            self.logger.error(f"❌ Error scanning processes: {e}")
        
        return vip_processes
    
    def is_vip_chrome_running(self) -> bool:
        """✅ Check if VIP Chrome is already running"""
        vip_processes = self.get_vip_chrome_processes()
        
        if vip_processes:
            self.logger.info(f"✅ VIP Chrome is running ({len(vip_processes)} processes)")
            return True
        else:
            self.logger.info("❌ VIP Chrome is not running")
            return False
    
    def focus_existing_chrome(self) -> bool:
        """🎯 Focus existing Chrome window"""
        try:
            vip_processes = self.get_vip_chrome_processes()
            
            if vip_processes:
                # Try to bring Chrome to front using Windows API
                try:
                    import win32gui
                    import win32con
                    
                    def enum_windows_callback(hwnd, windows):
                        if win32gui.IsWindowVisible(hwnd):
                            window_title = win32gui.GetWindowText(hwnd)
                            if 'chrome' in window_title.lower() or 'quotex' in window_title.lower():
                                windows.append(hwnd)
                        return True
                    
                    windows = []
                    win32gui.EnumWindows(enum_windows_callback, windows)
                    
                    if windows:
                        # Focus the first Chrome window found
                        hwnd = windows[0]
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        win32gui.SetForegroundWindow(hwnd)
                        self.logger.info("🎯 Focused existing Chrome window")
                        return True
                        
                except ImportError:
                    self.logger.warning("⚠️ win32gui not available, cannot focus window")
                
                # Fallback: just indicate Chrome is running
                self.logger.info("✅ VIP Chrome is already running")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error focusing Chrome: {e}")
            return False
    
    def close_vip_chrome(self) -> bool:
        """🛑 Close VIP Chrome processes"""
        try:
            vip_processes = self.get_vip_chrome_processes()
            
            if not vip_processes:
                self.logger.info("ℹ️ No VIP Chrome processes to close")
                return True
            
            self.logger.info(f"🛑 Closing {len(vip_processes)} VIP Chrome processes...")
            
            for proc in vip_processes:
                try:
                    proc.terminate()
                    self.logger.info(f"🛑 Terminated Chrome process PID {proc.pid}")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    self.logger.warning(f"⚠️ Could not terminate process {proc.pid}: {e}")
            
            # Wait for processes to close
            time.sleep(2)
            
            # Force kill if still running
            remaining_processes = self.get_vip_chrome_processes()
            if remaining_processes:
                self.logger.warning("⚠️ Some processes still running, force killing...")
                for proc in remaining_processes:
                    try:
                        proc.kill()
                        self.logger.info(f"💀 Force killed Chrome process PID {proc.pid}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
            
            self.logger.info("✅ VIP Chrome processes closed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error closing Chrome: {e}")
            return False
    
    def launch_new_chrome(self, url: str = "https://quotex.io", bypass_flags: List[str] = None) -> bool:
        """🚀 Launch new VIP Chrome instance"""
        try:
            chrome_exe = self.find_chrome_executable()
            if not chrome_exe:
                return False
            
            # Ensure user data directory exists
            os.makedirs(self.vip_user_data_dir, exist_ok=True)
            
            # Default bypass flags if none provided (Fully Security-Safe)
            if bypass_flags is None:
                bypass_flags = [
                    # === CORE SAFE FLAGS === #
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-infobars",
                    "--disable-default-apps",

                    # === SAFE PERFORMANCE FLAGS === #
                    "--disable-features=VizDisplayCompositor,TranslateUI,BlinkGenPropertyTrees",
                    "--disable-background-timer-throttling",
                    "--disable-renderer-backgrounding",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-ipc-flooding-protection",

                    # === ULTIMATE STEALTH === #
                    "--disable-extensions-file-access-check",
                    "--disable-extensions-http-throttling",
                    "--disable-ipc-flooding-protection",
                    "--disable-hang-monitor",
                    "--disable-client-side-phishing-detection",
                    "--disable-popup-blocking",
                    "--disable-background-timer-throttling",
                    "--disable-renderer-backgrounding",
                    "--disable-backgrounding-occluded-windows",
                    "--no-default-browser-check",
                    "--no-first-run",
                    "--disable-default-apps",
                    "--disable-sync",
                    "--disable-translate",
                    "--password-store=basic",
                    "--use-mock-keychain",
                    "--disable-save-password-bubble",
                    "--disable-single-click-autofill",
                    "--enable-automation=false",
                    "--remote-debugging-port=9222",  # Enable DevTools for connection
                    "--disable-dev-tools"
                ]
            
            # Build command
            cmd = [
                chrome_exe,
                f"--user-data-dir={self.vip_user_data_dir}",
                f"--profile-directory={self.vip_chrome_profile}",
                f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ] + bypass_flags + [url]
            
            self.logger.info("🚀 Launching new VIP Chrome instance...")
            
            # Launch Chrome
            process = subprocess.Popen(cmd, shell=False)
            
            # Wait a moment for Chrome to start
            time.sleep(2)
            
            # Verify it started
            if self.is_vip_chrome_running():
                self.logger.info("✅ VIP Chrome launched successfully")
                return True
            else:
                self.logger.error("❌ VIP Chrome failed to start")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error launching Chrome: {e}")
            return False
    
    def smart_chrome_launch(self, url: str = "https://quotex.io", bypass_flags: List[str] = None, force_new: bool = False) -> bool:
        """🧠 Smart Chrome launch - reuse existing or create new"""
        try:
            self.logger.info("🧠 Smart Chrome launch initiated...")
            
            if force_new:
                self.logger.info("🔄 Force new Chrome requested")
                self.close_vip_chrome()
                return self.launch_new_chrome(url, bypass_flags)
            
            # Check if VIP Chrome is already running
            if self.is_vip_chrome_running():
                self.logger.info("♻️ VIP Chrome already running, focusing existing window...")
                
                # Try to navigate to URL in existing Chrome
                self.navigate_to_url(url)
                
                # Focus existing window
                return self.focus_existing_chrome()
            else:
                self.logger.info("🚀 No VIP Chrome running, launching new instance...")
                return self.launch_new_chrome(url, bypass_flags)
                
        except Exception as e:
            self.logger.error(f"❌ Smart Chrome launch error: {e}")
            return False
    
    def navigate_to_url(self, url: str) -> bool:
        """🌐 Navigate existing Chrome to URL"""
        try:
            # This is a simplified approach - in a real implementation,
            # you might use Chrome DevTools Protocol or other methods
            self.logger.info(f"🌐 Attempting to navigate to: {url}")
            
            # For now, we'll launch a new tab with the URL
            chrome_exe = self.find_chrome_executable()
            if chrome_exe:
                cmd = [chrome_exe, url]
                subprocess.Popen(cmd, shell=False)
                self.logger.info("✅ Navigation command sent")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Navigation error: {e}")
            return False
    
    def get_chrome_status(self) -> dict:
        """📊 Get Chrome status information"""
        vip_processes = self.get_vip_chrome_processes()
        
        return {
            'is_running': len(vip_processes) > 0,
            'process_count': len(vip_processes),
            'process_pids': [proc.pid for proc in vip_processes],
            'user_data_dir': self.vip_user_data_dir,
            'profile_name': self.vip_chrome_profile,
            'chrome_executable': self.find_chrome_executable()
        }
