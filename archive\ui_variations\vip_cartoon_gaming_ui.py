"""
🎮 VIP BIG BANG - Cartoon Gaming Ultimate UI
رابط کاربری نهایی کارتونی و گیمینگ برای ربات تریدینگ
بهترین طراحی مدرن با المان‌های بازی، کارتون و اپلیکیشن
"""

import sys
import math
import random
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Import our cartoon components
from cartoon_gaming_ui import CartoonGameButton, CartoonStatsWidget, CartoonGamePanel, CartoonChartDisplay, CartoonGameColors

class VIPCartoonGamingUI(QMainWindow):
    """رابط کاربری نهایی VIP BIG BANG با طراحی کارتونی و گیمینگ"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎮 VIP BIG BANG - Cartoon Gaming Ultimate")
        self.setGeometry(100, 100, 1600, 1000)
        
        # تنظیم پس‌زمینه کارتونی گیمینگ
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {CartoonGameColors.APP_DARK},
                    stop:0.2 {CartoonGameColors.APP_MEDIUM},
                    stop:0.5 {CartoonGameColors.APP_LIGHT},
                    stop:0.8 {CartoonGameColors.APP_MEDIUM},
                    stop:1 {CartoonGameColors.APP_DARK});
                color: white;
            }}
            QLabel {{
                color: white;
                font-family: 'Comic Sans MS', 'Arial', sans-serif;
            }}
        """)
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """تنظیم رابط کاربری کارتونی گیمینگ"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(25)
        
        # هدر کارتونی گیمینگ
        self.create_cartoon_header(main_layout)
        
        # محتوای اصلی
        content_layout = QHBoxLayout()
        content_layout.setSpacing(25)
        
        self.create_left_cartoon_panel(content_layout)
        self.create_center_cartoon_panel(content_layout)
        self.create_right_cartoon_panel(content_layout)
        
        main_layout.addLayout(content_layout)
    
    def create_cartoon_header(self, main_layout):
        """ایجاد هدر کارتونی گیمینگ"""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(25)
        
        # لوگو کارتونی
        logo_layout = QHBoxLayout()
        
        logo_icon = QLabel("🎮")
        logo_icon.setStyleSheet("font-size: 48px;")
        logo_layout.addWidget(logo_icon)
        
        logo_text = QLabel("VIP BIG BANG")
        logo_text.setStyleSheet(f"""
            font-family: 'Comic Sans MS', 'Arial', sans-serif;
            font-size: 32px;
            font-weight: 900;
            color: {CartoonGameColors.CARTOON_CYAN};
            letter-spacing: 3px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        """)
        logo_layout.addWidget(logo_text)
        
        header_layout.addLayout(logo_layout)
        header_layout.addStretch()
        
        # عنوان مرکزی
        title = QLabel("CARTOON GAMING EDITION")
        title.setStyleSheet(f"""
            font-family: 'Comic Sans MS', 'Arial', sans-serif;
            font-size: 28px;
            font-weight: 800;
            color: {CartoonGameColors.CARTOON_PINK};
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # کنترل‌های کارتونی
        controls_layout = QHBoxLayout()
        
        power_btn = CartoonGameButton("قدرت", "⚡", "success", (120, 50))
        settings_btn = CartoonGameButton("تنظیمات", "⚙️", "primary", (120, 50))
        exit_btn = CartoonGameButton("خروج", "🚪", "danger", (120, 50))
        
        controls_layout.addWidget(power_btn)
        controls_layout.addWidget(settings_btn)
        controls_layout.addWidget(exit_btn)
        
        header_layout.addLayout(controls_layout)
        main_layout.addLayout(header_layout)
    
    def create_left_cartoon_panel(self, content_layout):
        """ایجاد پنل چپ کارتونی"""
        left_widget = QWidget()
        left_widget.setFixedWidth(320)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(25)
        
        # پنل کنترل کارتونی
        control_panel = CartoonGamePanel("کنترل ربات", "primary")
        control_layout = QVBoxLayout(control_panel)
        
        control_title = QLabel("🤖 سیستم کنترل کارتونی")
        control_title.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 800;
            color: {CartoonGameColors.CARTOON_CYAN};
            letter-spacing: 2px;
            margin-bottom: 15px;
        """)
        control_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        control_layout.addWidget(control_title)
        
        # دکمه‌های کنترل کارتونی
        controls = [
            ("فعال‌سازی", "🚀", "primary"),
            ("حالت تقویت", "⚡", "success"),
            ("سپر محافظ", "🛡️", "warning"),
            ("توقف اضطراری", "🛑", "danger")
        ]
        
        for text, icon, btn_type in controls:
            btn = CartoonGameButton(text, icon, btn_type, (280, 60))
            control_layout.addWidget(btn)
        
        left_layout.addWidget(control_panel)
        
        # آمار کارتونی
        stats_panel = CartoonGamePanel("آمار عملکرد", "success")
        stats_layout = QVBoxLayout(stats_panel)
        
        stats_title = QLabel("📊 آمار کارتونی")
        stats_title.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 700;
            color: {CartoonGameColors.CARTOON_GREEN};
            letter-spacing: 1px;
            margin-bottom: 15px;
        """)
        stats_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        stats_layout.addWidget(stats_title)
        
        # آمار فردی
        balance_stats = CartoonStatsWidget("موجودی", "$2,751.89", "💰", CartoonGameColors.CARTOON_GREEN)
        power_stats = CartoonStatsWidget("قدرت", "94%", "⚡", CartoonGameColors.CARTOON_BLUE)
        level_stats = CartoonStatsWidget("سطح", "58", "🎯", CartoonGameColors.CARTOON_PURPLE)
        
        stats_layout.addWidget(balance_stats)
        stats_layout.addWidget(power_stats)
        stats_layout.addWidget(level_stats)
        
        left_layout.addWidget(stats_panel)
        left_layout.addStretch()
        content_layout.addWidget(left_widget)
    
    def create_center_cartoon_panel(self, content_layout):
        """ایجاد پنل مرکزی کارتونی"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(20)
        
        # نمایشگر چارت کارتونی
        chart_display = CartoonChartDisplay()
        center_layout.addWidget(chart_display)
        
        # نشانگرهای کارتونی گیمینگ
        indicators_layout = QHBoxLayout()
        indicators_layout.setSpacing(20)
        
        # ایجاد نشانگرهای کارتونی
        indicators = [
            ("🎯 هدف", "قفل شده", CartoonGameColors.CARTOON_GREEN),
            ("⚡ انرژی", "98%", CartoonGameColors.CARTOON_CYAN),
            ("🛡️ سپر", "فعال", CartoonGameColors.CARTOON_BLUE),
            ("🔥 تقویت", "آماده", CartoonGameColors.CARTOON_ORANGE),
            ("🌟 ویژه", "شارژ", CartoonGameColors.CARTOON_PURPLE)
        ]
        
        for title, value, color in indicators:
            indicator = self.create_cartoon_indicator(title, value, color)
            indicators_layout.addWidget(indicator)
        
        center_layout.addLayout(indicators_layout)
        content_layout.addWidget(center_widget, 2)
    
    def create_right_cartoon_panel(self, content_layout):
        """ایجاد پنل راست کارتونی"""
        right_widget = QWidget()
        right_widget.setFixedWidth(320)
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(25)
        
        # پنل اقدامات کارتونی گیمینگ
        actions_panel = CartoonGamePanel("اقدامات گیمینگ", "warning")
        actions_layout = QGridLayout(actions_panel)
        actions_layout.setSpacing(20)
        
        # دکمه‌های اقدام کارتونی
        gaming_actions = [
            (0, 0, "🚀", "پرتاب"),
            (0, 1, "🎯", "هدف‌گیری"),
            (1, 0, "⚡", "تقویت"),
            (1, 1, "🛡️", "محافظت"),
            (2, 0, "🔥", "آتش"),
            (2, 1, "💥", "انفجار"),
            (3, 0, "🌟", "ویژه"),
            (3, 1, "🏆", "پیروزی")
        ]
        
        for row, col, icon, text in gaming_actions:
            btn = CartoonGameButton(text, icon, "primary", (130, 70))
            actions_layout.addWidget(btn, row, col)
        
        right_layout.addWidget(actions_panel)
        
        # پنل تنظیمات سریع
        quick_settings_panel = CartoonGamePanel("تنظیمات سریع", "default")
        quick_layout = QVBoxLayout(quick_settings_panel)
        
        quick_title = QLabel("⚙️ تنظیمات سریع")
        quick_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 700;
            color: {CartoonGameColors.CARTOON_CYAN};
            letter-spacing: 1px;
            margin-bottom: 10px;
        """)
        quick_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        quick_layout.addWidget(quick_title)
        
        # تنظیمات سریع
        quick_buttons = [
            ("حالت خودکار", "🤖", "success"),
            ("حالت دستی", "🎮", "primary"),
            ("حالت آموزش", "📚", "warning")
        ]
        
        for text, icon, btn_type in quick_buttons:
            btn = CartoonGameButton(text, icon, btn_type, (280, 50))
            quick_layout.addWidget(btn)
        
        right_layout.addWidget(quick_settings_panel)
        right_layout.addStretch()
        content_layout.addWidget(right_widget)
    
    def create_cartoon_indicator(self, title, value, color):
        """ایجاد نشانگر کارتونی"""
        indicator = QWidget()
        indicator.setFixedSize(180, 100)
        
        layout = QVBoxLayout(indicator)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 12px;
            color: rgba(255,255,255,0.9);
            font-weight: 700;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 900;
            color: {color};
            letter-spacing: 1px;
        """)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)
        
        indicator.setStyleSheet(f"""
            QWidget {{
                background: rgba(0, 0, 0, 0.7);
                border: 2px solid {color};
                border-radius: 15px;
            }}
        """)
        
        return indicator
    
    def setup_animations(self):
        """تنظیم انیمیشن‌های رابط کاربری"""
        # انیمیشن ظاهر شدن ویندو
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(2000)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تنظیم استایل اپلیکیشن
    app.setStyle('Fusion')
    
    window = VIPCartoonGamingUI()
    window.show()
    sys.exit(app.exec())
