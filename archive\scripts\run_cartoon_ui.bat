@echo off
echo.
echo ========================================
echo 🎮 VIP BIG BANG - Cartoon Gaming UI
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check if required packages are installed
echo 🔍 Checking required packages...
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo ❌ PySide6 not found
    echo 📦 Installing PySide6...
    pip install PySide6
    if errorlevel 1 (
        echo ❌ Failed to install PySide6
        pause
        exit /b 1
    )
)

echo ✅ All packages ready
echo.

REM Show menu
:menu
echo ========================================
echo 🎮 VIP BIG BANG Advanced Gaming Options
echo ========================================
echo.
echo 1. 🧪 Test Cartoon Components
echo 2. 🎮 Ultimate Cartoon Gaming UI
echo 3. 🚀 Integrated Cartoon Trading System
echo 4. 🎯 QML Modern UI Demo
echo 5. 🌐 WebSocket Real-time Demo
echo 6. 🎯 Drag Drop Module Manager
echo 7. 🔔 Advanced Notifications Demo
echo 8. 🔌 Logic to UI Converter Demo
echo 9. 🛠️ VIP UI Designer
echo A. 🔗 VIP Connected UI (Full System)
echo B. 🚀 All Advanced Features Test
echo C. 🛠️ Install/Update Dependencies
echo 0. 🚪 Exit
echo.
set /p choice="انتخاب کنید (0-9,A,B,C): "

if "%choice%"=="1" goto test_components
if "%choice%"=="2" goto ultimate_ui
if "%choice%"=="3" goto integrated_system
if "%choice%"=="4" goto qml_demo
if "%choice%"=="5" goto websocket_demo
if "%choice%"=="6" goto modules_demo
if "%choice%"=="7" goto notifications_demo
if "%choice%"=="8" goto logic_to_ui_demo
if "%choice%"=="9" goto ui_designer
if "%choice%"=="A" goto connected_ui
if "%choice%"=="a" goto connected_ui
if "%choice%"=="B" goto advanced_features
if "%choice%"=="b" goto advanced_features
if "%choice%"=="C" goto install_deps
if "%choice%"=="c" goto install_deps
if "%choice%"=="0" goto exit
goto invalid_choice

:test_components
echo.
echo 🧪 Starting Cartoon Components Test...
echo.
python test_cartoon_ui.py
if errorlevel 1 (
    echo ❌ Test failed
    pause
)
goto menu

:ultimate_ui
echo.
echo 🎮 Starting Ultimate Cartoon Gaming UI...
echo.
python vip_cartoon_ultimate.py
if errorlevel 1 (
    echo ❌ UI failed to start
    pause
)
goto menu

:integrated_system
echo.
echo 🚀 Starting Integrated Cartoon Trading System...
echo.
python vip_cartoon_integrated.py
if errorlevel 1 (
    echo ❌ System failed to start
    pause
)
goto menu

:qml_demo
echo.
echo 🎯 Starting QML Modern UI Demo...
echo.
python qml_modern_ui.py
if errorlevel 1 (
    echo ❌ QML Demo failed to start
    pause
)
goto menu

:websocket_demo
echo.
echo 🌐 Starting WebSocket Real-time Demo...
echo.
python websocket_realtime.py
if errorlevel 1 (
    echo ❌ WebSocket Demo failed to start
    pause
)
goto menu

:modules_demo
echo.
echo 🎯 Starting Drag Drop Module Manager...
echo.
python drag_drop_modules.py
if errorlevel 1 (
    echo ❌ Module Manager failed to start
    pause
)
goto menu

:notifications_demo
echo.
echo 🔔 Starting Advanced Notifications Demo...
echo.
python advanced_notifications.py
if errorlevel 1 (
    echo ❌ Notifications Demo failed to start
    pause
)
goto menu

:logic_to_ui_demo
echo.
echo 🔌 Starting Logic to UI Converter Demo...
echo.
python ui/logic_to_ui_converter.py
if errorlevel 1 (
    echo ❌ Logic to UI Demo failed to start
    pause
)
goto menu

:ui_designer
echo.
echo 🛠️ Starting VIP UI Designer...
echo.
python ui/vip_ui_designer.py
if errorlevel 1 (
    echo ❌ UI Designer failed to start
    pause
)
goto menu

:connected_ui
echo.
echo 🔗 Starting VIP Connected UI (Full System)...
echo.
python ui/vip_connected_ui.py
if errorlevel 1 (
    echo ❌ Connected UI failed to start
    pause
)
goto menu

:advanced_features
echo.
echo 🚀 Starting All Advanced Features Test...
echo.
python test_advanced_features.py
if errorlevel 1 (
    echo ❌ Advanced Features Test failed to start
    pause
)
goto menu

:install_deps
echo.
echo 📦 Installing/Updating Dependencies...
echo.
pip install --upgrade PySide6
pip install --upgrade numpy
pip install --upgrade pandas
pip install --upgrade asyncio
echo.
echo ✅ Dependencies updated
pause
goto menu

:invalid_choice
echo.
echo ❌ Invalid choice! Please select 0-9, A, B, or C
echo.
pause
goto menu

:exit
echo.
echo 👋 Goodbye!
echo.
pause
exit /b 0
