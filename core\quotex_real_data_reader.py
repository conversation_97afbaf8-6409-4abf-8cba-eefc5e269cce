#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Quotex Real Data Reader
📊 خواندن اطلاعات واقعی از Quotex
⚡ اتصال مستقیم و خواندن زنده
💎 ساده اما پیشرفته
"""

import tkinter as tk
from tkinter import messagebox
import time
import threading
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import webbrowser

class QuotexRealDataReader:
    """
    🚀 Quotex Real Data Reader
    📊 خواندن اطلاعات واقعی
    ⚡ اتصال مستقیم
    💎 ساده اما پیشرفته
    """

    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.driver = None
        self.is_logged_in = False
        self.is_reading = False

        # Login data
        self.email = ""
        self.password = ""

        print("🚀 Quotex Real Data Reader initialized")

    def show_login_page(self):
        """🔐 صفحه لوگین"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Main container
            main_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            main_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(main_container, bg='#00C851', height=100)
            header.pack(fill=tk.X, pady=(0, 30))
            header.pack_propagate(False)

            tk.Label(header, text="📊 REAL QUOTEX DATA READER",
                    font=("Arial", 24, "bold"), fg="#FFFFFF", bg="#00C851").pack(pady=30)

            # Login form
            form_frame = tk.Frame(main_container, bg='#1A1A2E', relief=tk.RAISED, bd=3)
            form_frame.pack(padx=100, pady=30, fill=tk.BOTH, expand=True)

            # Email
            tk.Label(form_frame, text="📧 EMAIL",
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.email_entry = tk.Entry(form_frame, font=("Arial", 14), width=30,
                                      bg="#FFFFFF", fg="#000000")
            self.email_entry.pack(pady=10)

            # Password
            tk.Label(form_frame, text="🔒 PASSWORD",
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.password_entry = tk.Entry(form_frame, font=("Arial", 14), width=30,
                                         bg="#FFFFFF", fg="#000000", show="*")
            self.password_entry.pack(pady=10)

            # Method selection
            tk.Label(form_frame, text="🔧 CONNECTION METHOD",
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            method_frame = tk.Frame(form_frame, bg='#1A1A2E')
            method_frame.pack(pady=10)

            self.method_var = tk.StringVar(value="browser")

            tk.Radiobutton(method_frame, text="🌐 Open in Browser (Simple)",
                         variable=self.method_var, value="browser",
                         font=("Arial", 12), fg="#FFFFFF", bg="#1A1A2E",
                         selectcolor="#2D3748").pack(anchor=tk.W)

            tk.Radiobutton(method_frame, text="🤖 Direct Connection (Advanced)",
                         variable=self.method_var, value="selenium",
                         font=("Arial", 12), fg="#FFFFFF", bg="#1A1A2E",
                         selectcolor="#2D3748").pack(anchor=tk.W)

            # Connect button
            self.connect_btn = tk.Button(form_frame, text="📊 CONNECT & READ DATA",
                                       font=("Arial", 16, "bold"), bg="#00C851", fg="#FFFFFF",
                                       padx=50, pady=20, command=self.start_connection)
            self.connect_btn.pack(pady=30)

            # Status
            self.status_label = tk.Label(form_frame, text="🔴 Ready to connect",
                                       font=("Arial", 12), fg="#FF4444", bg="#1A1A2E")
            self.status_label.pack(pady=10)

            # Load saved credentials
            self.load_credentials()

            return True

        except Exception as e:
            print(f"❌ Login page error: {e}")
            return False

    def start_connection(self):
        """🚀 شروع اتصال"""
        try:
            self.email = self.email_entry.get().strip()
            self.password = self.password_entry.get().strip()

            if not self.email or not self.password:
                messagebox.showwarning("Warning", "Please enter email and password!")
                return

            # Save credentials
            self.save_credentials()

            method = self.method_var.get()

            if method == "browser":
                self.connect_via_browser()
            else:
                self.connect_via_selenium()

        except Exception as e:
            print(f"❌ Connection error: {e}")

    def connect_via_browser(self):
        """🌐 اتصال از طریق مرورگر"""
        try:
            self.status_label.config(text="🔄 Opening in browser...", fg="#FFD700")
            self.connect_btn.config(state=tk.DISABLED, text="🔄 OPENING...")

            # Open Quotex in browser
            webbrowser.open("https://qxbroker.com/en/sign-in")

            time.sleep(2)

            # Show instructions
            result = messagebox.askquestion("Browser Login",
                                          f"🌐 Quotex opened in your browser!\n\n"
                                          f"📧 Email: {self.email}\n"
                                          f"🔒 Password: ********\n\n"
                                          f"Please:\n"
                                          f"1. Login to your account\n"
                                          f"2. Go to trading page\n"
                                          f"3. Star your assets (⭐)\n"
                                          f"4. Come back here\n\n"
                                          f"Have you completed the login?")

            if result == 'yes':
                self.is_logged_in = True
                self.show_data_interface()
            else:
                self.status_label.config(text="🔴 Ready to connect", fg="#FF4444")
                self.connect_btn.config(state=tk.NORMAL, text="📊 CONNECT & READ DATA")

        except Exception as e:
            print(f"❌ Browser connection error: {e}")

    def connect_via_selenium(self):
        """🤖 اتصال مستقیم با Selenium"""
        try:
            self.status_label.config(text="🔄 Connecting directly...", fg="#FFD700")
            self.connect_btn.config(state=tk.DISABLED, text="🔄 CONNECTING...")

            def selenium_thread():
                try:
                    if self.setup_selenium_connection():
                        self.is_logged_in = True
                        self.show_data_interface()
                    else:
                        self.status_label.config(text="❌ Connection failed", fg="#FF4444")
                        self.connect_btn.config(state=tk.NORMAL, text="📊 CONNECT & READ DATA")

                except Exception as e:
                    print(f"❌ Selenium thread error: {e}")
                    self.status_label.config(text="❌ Connection error", fg="#FF4444")
                    self.connect_btn.config(state=tk.NORMAL, text="📊 CONNECT & READ DATA")

            thread = threading.Thread(target=selenium_thread, daemon=True)
            thread.start()

        except Exception as e:
            print(f"❌ Selenium connection error: {e}")

    def setup_selenium_connection(self):
        """🔧 راه‌اندازی Selenium"""
        try:
            print("🔧 Setting up Selenium connection...")

            # Setup Chrome options for existing browser
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument("--disable-extensions")

            # Try to connect to existing Chrome first
            try:
                chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
                self.driver = webdriver.Chrome(options=chrome_options)
                print("✅ Connected to existing Chrome")
            except:
                print("🔧 Starting new Chrome instance...")
                chrome_options = Options()
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                self.driver = webdriver.Chrome(options=chrome_options)

            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Check if already on Quotex
            current_url = self.driver.current_url
            if "qxbroker.com" in current_url or "quotex" in current_url.lower():
                print("✅ Already on Quotex page")
                if "trade" in current_url or "trading" in current_url:
                    print("✅ Already logged in and on trading page")
                    return True
                else:
                    print("🔐 Need to login")
                    return self.perform_selenium_login()
            else:
                # Navigate to Quotex
                self.driver.get("https://qxbroker.com/en/sign-in")

                # Wait for page load
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )

                time.sleep(3)

                # Perform login
                if self.perform_selenium_login():
                    return True
                else:
                    return False

        except Exception as e:
            print(f"❌ Selenium setup error: {e}")
            return False

    def perform_selenium_login(self):
        """🔐 لوگین با Selenium"""
        try:
            print("🔐 Performing Selenium login...")

            # Find email field
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[placeholder*='email' i]",
                "#email"
            ]

            email_field = None
            for selector in email_selectors:
                try:
                    email_field = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if email_field:
                email_field.clear()
                email_field.send_keys(self.email)
                time.sleep(1)
                print("✅ Email entered")
            else:
                print("❌ Email field not found")
                return False

            # Find password field
            password_selectors = [
                "input[type='password']",
                "input[name='password']",
                "#password"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_field.is_displayed():
                        break
                except:
                    continue

            if password_field:
                password_field.clear()
                password_field.send_keys(self.password)
                time.sleep(1)
                print("✅ Password entered")
            else:
                print("❌ Password field not found")
                return False

            # Find login button
            login_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button[class*='login' i]",
                ".login-btn"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if login_button.is_displayed() and login_button.is_enabled():
                        break
                except:
                    continue

            if login_button:
                login_button.click()
                print("✅ Login button clicked")
                time.sleep(8)

                # Check if login successful
                current_url = self.driver.current_url
                if ("trade" in current_url.lower() or
                    "trading" in current_url.lower() or
                    "platform" in current_url.lower()):
                    print("✅ Login successful!")
                    return True
                else:
                    print("❌ Login failed")
                    return False
            else:
                print("❌ Login button not found")
                return False

        except Exception as e:
            print(f"❌ Selenium login error: {e}")
            return False

    def show_data_interface(self):
        """📊 نمایش رابط خواندن اطلاعات"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Main container
            main_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            main_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(main_container, bg='#00C851', height=80)
            header.pack(fill=tk.X, pady=(0, 10))
            header.pack_propagate(False)

            tk.Label(header, text="📊 READING REAL QUOTEX DATA",
                    font=("Arial", 20, "bold"), fg="#FFFFFF", bg="#00C851").pack(pady=25)

            # Create 3-column layout
            content_frame = tk.Frame(main_container, bg='#0A0A0F')
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Left panel
            left_panel = tk.Frame(content_frame, bg='#1A1A2E', width=250, relief=tk.RAISED, bd=2)
            left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
            left_panel.pack_propagate(False)

            tk.Label(left_panel, text="📈 ANALYSIS",
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.analysis_text = tk.Text(left_panel, bg="#0D1117", fg="#00FFFF",
                                       font=("Consolas", 9), wrap=tk.WORD)
            self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Center panel - Real Data
            center_panel = tk.Frame(content_frame, bg='#2D3748', relief=tk.RAISED, bd=3)
            center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

            # Center header
            center_header = tk.Frame(center_panel, bg='#00C851', height=60)
            center_header.pack(fill=tk.X, pady=(0, 10))
            center_header.pack_propagate(False)

            tk.Label(center_header, text="📊 REAL QUOTEX DATA",
                    font=("Arial", 18, "bold"), fg="#FFFFFF", bg="#00C851").pack(pady=15)

            # Control buttons
            control_frame = tk.Frame(center_panel, bg='#2D3748')
            control_frame.pack(fill=tk.X, pady=(0, 10))

            self.start_btn = tk.Button(control_frame, text="📊 START READING",
                                     font=("Arial", 12, "bold"), bg="#00FF88", fg="#000000",
                                     padx=20, pady=10, command=self.start_reading_real_data)
            self.start_btn.pack(side=tk.LEFT, padx=10)

            self.stop_btn = tk.Button(control_frame, text="⏹️ STOP",
                                    font=("Arial", 12, "bold"), bg="#FF4444", fg="#FFFFFF",
                                    padx=20, pady=10, command=self.stop_reading_real_data, state=tk.DISABLED)
            self.stop_btn.pack(side=tk.LEFT, padx=10)

            # Real data display
            self.real_data_text = tk.Text(center_panel, bg="#0D1117", fg="#00FFFF",
                                        font=("Consolas", 10), wrap=tk.WORD)
            self.real_data_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Right panel
            right_panel = tk.Frame(content_frame, bg='#1A1A2E', width=250, relief=tk.RAISED, bd=2)
            right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
            right_panel.pack_propagate(False)

            tk.Label(right_panel, text="⚙️ SETTINGS",
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.settings_text = tk.Text(right_panel, bg="#0D1117", fg="#00FFFF",
                                       font=("Consolas", 9), wrap=tk.WORD)
            self.settings_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Initial messages
            self.add_real_log("📊 Real Data Reader Ready")
            self.add_real_log("✅ Connected to Quotex")
            self.add_real_log("🚀 Click 'START READING' to begin")

            return True

        except Exception as e:
            print(f"❌ Data interface error: {e}")
            return False

    def start_reading_real_data(self):
        """📊 شروع خواندن اطلاعات واقعی"""
        try:
            self.is_reading = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)

            self.add_real_log("📊 Starting real data reading...")

            def reading_thread():
                try:
                    while self.is_reading:
                        start_time = time.time()

                        # Read real data from Quotex
                        if self.driver:
                            real_data = self.read_real_quotex_data()
                        else:
                            real_data = self.read_browser_data()

                        # Calculate read time
                        read_time = time.time() - start_time

                        # Display real data
                        if real_data:
                            self.display_real_quotex_data(real_data, read_time)

                        # Wait before next read
                        time.sleep(1)

                except Exception as e:
                    self.add_real_log(f"❌ Reading error: {e}")

            thread = threading.Thread(target=reading_thread, daemon=True)
            thread.start()

        except Exception as e:
            self.add_real_log(f"❌ Start reading error: {e}")

    def read_real_quotex_data(self):
        """📈 خواندن اطلاعات واقعی از Quotex با Selenium"""
        try:
            # Execute comprehensive JavaScript to get ALL real data from Quotex
            real_data = self.driver.execute_script("""
                // Get all real Quotex data
                var data = {
                    timestamp: new Date().toLocaleTimeString(),
                    source: 'Direct Selenium Connection',

                    // Account information
                    balance: null,
                    accountType: null,

                    // Current trading
                    currentAsset: null,
                    currentPrice: null,
                    currentProfit: null,

                    // All assets
                    allAssets: [],
                    starredAssets: [],
                    otcAssets: [],

                    // Trading status
                    callEnabled: false,
                    putEnabled: false,
                    tradeAmount: null,

                    // Platform status
                    connectionStatus: 'CONNECTED',
                    chartVisible: false
                };

                // Try to get balance with multiple selectors
                var balanceSelectors = [
                    '.balance', '[class*="balance"]', '.account-balance',
                    '[data-test*="balance"]', '.wallet-balance', '.user-balance',
                    '.balance-amount', '[class*="wallet"]', '.money', '[class*="money"]'
                ];

                for (var i = 0; i < balanceSelectors.length; i++) {
                    var elements = document.querySelectorAll(balanceSelectors[i]);
                    for (var j = 0; j < elements.length; j++) {
                        var el = elements[j];
                        if (el && el.innerText && (el.innerText.includes('$') || el.innerText.includes('USD') || /\\d+\\.\\d+/.test(el.innerText))) {
                            data.balance = el.innerText.trim();
                            break;
                        }
                    }
                    if (data.balance) break;
                }

                // Try to get current asset
                var assetSelectors = [
                    '.asset-name', '[class*="asset"]', '.symbol-name', '.current-asset',
                    '[class*="symbol"]', '.instrument-name', '[class*="instrument"]',
                    '.pair-name', '[class*="pair"]', '.currency-pair'
                ];

                for (var i = 0; i < assetSelectors.length; i++) {
                    var elements = document.querySelectorAll(assetSelectors[i]);
                    for (var j = 0; j < elements.length; j++) {
                        var el = elements[j];
                        if (el && el.innerText && el.innerText.length > 2 && el.innerText.length < 20) {
                            data.currentAsset = el.innerText.trim();
                            break;
                        }
                    }
                    if (data.currentAsset) break;
                }

                // Try to get current price
                var priceSelectors = [
                    '.price', '[class*="price"]', '.current-price', '.rate', '[class*="rate"]',
                    '.quote', '[class*="quote"]', '.value', '[class*="value"]'
                ];

                for (var i = 0; i < priceSelectors.length; i++) {
                    var elements = document.querySelectorAll(priceSelectors[i]);
                    for (var j = 0; j < elements.length; j++) {
                        var el = elements[j];
                        if (el && el.innerText && /\\d+\\.\\d+/.test(el.innerText)) {
                            data.currentPrice = el.innerText.trim();
                            break;
                        }
                    }
                    if (data.currentPrice) break;
                }

                // Try to get profit percentage
                var profitSelectors = [
                    '.profit', '[class*="profit"]', '.payout', '[class*="payout"]',
                    '.percentage', '[class*="percentage"]', '.percent'
                ];

                for (var i = 0; i < profitSelectors.length; i++) {
                    var elements = document.querySelectorAll(profitSelectors[i]);
                    for (var j = 0; j < elements.length; j++) {
                        var el = elements[j];
                        if (el && el.innerText && el.innerText.includes('%')) {
                            data.currentProfit = el.innerText.trim();
                            break;
                        }
                    }
                    if (data.currentProfit) break;
                }

                // Get all asset items with multiple approaches
                var assetItemSelectors = [
                    '.asset-item', '[class*="asset"]', '.symbol-item', '.instrument-item',
                    '[class*="instrument"]', '.pair-item', '[class*="pair"]',
                    '.currency-item', '[class*="currency"]', '.trading-item'
                ];

                var foundAssets = [];

                for (var i = 0; i < assetItemSelectors.length; i++) {
                    var assetItems = document.querySelectorAll(assetItemSelectors[i]);
                    if (assetItems.length > 0) {
                        assetItems.forEach(function(item) {
                            try {
                                var nameEl = item.querySelector('.name, .symbol, [class*="name"], [class*="symbol"], .text, [class*="text"]');
                                var priceEl = item.querySelector('.price, .rate, [class*="price"], [class*="rate"], .value, [class*="value"]');
                                var profitEl = item.querySelector('.profit, .payout, [class*="profit"], [class*="payout"], .percent, [class*="percent"]');

                                var name = nameEl ? nameEl.innerText.trim() : item.innerText.split('\\n')[0] || 'Unknown';

                                if (name && name.length > 1 && name.length < 20 && !name.includes('undefined')) {
                                    var asset = {
                                        name: name,
                                        price: priceEl ? priceEl.innerText.trim() : 'N/A',
                                        profit: profitEl ? profitEl.innerText.trim() : 'N/A',
                                        isStarred: item.querySelector('.star, .favorite, [class*="star"], [class*="favorite"], .starred, [class*="starred"]') !== null,
                                        isOTC: item.querySelector('.otc, [class*="otc"]') !== null || item.innerText.toLowerCase().includes('otc')
                                    };

                                    // Avoid duplicates
                                    var isDuplicate = foundAssets.some(function(existing) {
                                        return existing.name === asset.name;
                                    });

                                    if (!isDuplicate) {
                                        foundAssets.push(asset);
                                        data.allAssets.push(asset);

                                        if (asset.isStarred) {
                                            data.starredAssets.push(asset);
                                        }

                                        if (asset.isOTC) {
                                            data.otcAssets.push(asset);
                                        }
                                    }
                                }
                            } catch (e) {
                                // Skip problematic items
                            }
                        });

                        if (foundAssets.length > 0) break; // Found assets, stop looking
                    }
                }

                // Check trading buttons
                var callButton = document.querySelector('.call-btn, [class*="call"], .higher-btn, [class*="higher"], .up-btn, [class*="up"]');
                var putButton = document.querySelector('.put-btn, [class*="put"], .lower-btn, [class*="lower"], .down-btn, [class*="down"]');

                data.callEnabled = callButton ? !callButton.disabled && callButton.style.display !== 'none' : false;
                data.putEnabled = putButton ? !putButton.disabled && putButton.style.display !== 'none' : false;

                // Check if chart is visible
                data.chartVisible = document.querySelector('.chart, canvas, [class*="chart"], [class*="trading-view"]') !== null;

                // Get trade amount
                var amountSelectors = [
                    '.amount, [class*="amount"], input[type="number"], .trade-amount, [class*="trade-amount"]'
                ];

                for (var i = 0; i < amountSelectors.length; i++) {
                    var amountEl = document.querySelector(amountSelectors[i]);
                    if (amountEl && (amountEl.value || amountEl.innerText)) {
                        data.tradeAmount = amountEl.value || amountEl.innerText;
                        break;
                    }
                }

                return data;
            """)

            return real_data

        except Exception as e:
            self.add_real_log(f"❌ Real data read error: {e}")
            return None

    def read_browser_data(self):
        """🌐 خواندن اطلاعات از مرورگر (شبیه‌سازی)"""
        try:
            # Since we can't directly read from browser without Selenium,
            # we'll provide a message to user
            current_time = datetime.now()

            data = {
                "timestamp": current_time.strftime("%H:%M:%S"),
                "source": "Browser Connection (Manual)",
                "message": "Please use Selenium connection for real data reading",
                "instruction": "Go back and select 'Direct Connection (Advanced)' option",
                "balance": "Please check your Quotex browser",
                "currentAsset": "Visible in your browser",
                "allAssets": [],
                "starredAssets": [],
                "otcAssets": []
            }

            return data

        except Exception as e:
            return None

    def display_real_quotex_data(self, data, read_time):
        """📊 نمایش اطلاعات واقعی Quotex"""
        try:
            # Clear previous data
            self.real_data_text.delete(1.0, tk.END)

            if data.get('message'):
                # Browser connection message
                display_text = f"""
{'='*60}
⏰ TIME: {data.get('timestamp', 'N/A')} | ⚡ READ: {read_time:.3f}s
🌐 SOURCE: {data.get('source', 'Browser')}

📋 MESSAGE: {data.get('message', 'N/A')}
💡 INSTRUCTION: {data.get('instruction', 'N/A')}

💳 BALANCE: {data.get('balance', 'N/A')}
📊 CURRENT ASSET: {data.get('currentAsset', 'N/A')}

🔄 For real data reading, please:
1. Go back to login page
2. Select 'Direct Connection (Advanced)'
3. This will read actual data from Quotex

🌐 Your browser connection is active
📊 You can manually check data in browser
{'='*60}
"""
            else:
                # Real data display
                display_text = f"""
{'='*60}
⏰ REAL TIME: {data.get('timestamp', 'N/A')} | ⚡ READ: {read_time:.3f}s
🌐 SOURCE: {data.get('source', 'Direct Connection')}

💳 BALANCE: {data.get('balance', 'Reading...')}
📊 CURRENT ASSET: {data.get('currentAsset', 'Reading...')}
💰 CURRENT PRICE: {data.get('currentPrice', 'Reading...')}
💎 CURRENT PROFIT: {data.get('currentProfit', 'Reading...')}

🔴 CALL: {'✅' if data.get('callEnabled') else '❌'} | 🔵 PUT: {'✅' if data.get('putEnabled') else '❌'}
💰 TRADE AMOUNT: {data.get('tradeAmount', 'Reading...')}

⭐ STARRED ASSETS ({len(data.get('starredAssets', []))}):{self.format_real_starred_assets(data.get('starredAssets', []))}

🏷️ OTC ASSETS ({len(data.get('otcAssets', []))}):{self.format_real_otc_assets(data.get('otcAssets', []))}

📈 ALL ASSETS ({len(data.get('allAssets', []))}):{self.format_real_all_assets(data.get('allAssets', []))}

📊 CHART: {'✅ VISIBLE' if data.get('chartVisible') else '❌ NOT VISIBLE'}
🌐 CONNECTION: {data.get('connectionStatus', 'CONNECTED')}

🚀 REAL DATA FROM YOUR QUOTEX ACCOUNT
⚡ READ SPEED: {read_time:.3f}s | 🎯 TARGET: 95% WIN RATE
{'='*60}
"""

            self.real_data_text.insert(tk.END, display_text)

        except Exception as e:
            self.add_real_log(f"❌ Display error: {e}")

    def format_real_starred_assets(self, assets):
        """⭐ فرمت ارزهای ستاره‌دار واقعی"""
        if not assets:
            return "\n   No starred assets found in your Quotex"

        formatted = ""
        for asset in assets:
            otc = "🏷️" if asset.get('isOTC') else "📊"
            formatted += f"\n   {otc} ⭐ {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"

        return formatted

    def format_real_otc_assets(self, assets):
        """🏷️ فرمت ارزهای OTC واقعی"""
        if not assets:
            return "\n   No OTC assets found in your Quotex"

        formatted = ""
        for asset in assets:
            star = "⭐" if asset.get('isStarred') else "☆"
            formatted += f"\n   🏷️ {star} {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"

        return formatted

    def format_real_all_assets(self, assets):
        """📈 فرمت تمام ارزهای واقعی"""
        if not assets:
            return "\n   No assets found in your Quotex"

        formatted = ""
        for asset in assets[:10]:  # Show first 10
            star = "⭐" if asset.get('isStarred') else "☆"
            otc = "🏷️" if asset.get('isOTC') else "📊"
            formatted += f"\n   {otc} {star} {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"

        if len(assets) > 10:
            formatted += f"\n   ... and {len(assets) - 10} more assets from your Quotex"

        return formatted

    def stop_reading_real_data(self):
        """⏹️ توقف خواندن اطلاعات واقعی"""
        try:
            self.is_reading = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.add_real_log("⏹️ Real data reading stopped")

        except Exception as e:
            self.add_real_log(f"❌ Stop error: {e}")

    def add_real_log(self, message):
        """📝 اضافه کردن لاگ واقعی"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.real_data_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.real_data_text.see(tk.END)
        except:
            pass

    def save_credentials(self):
        """💾 ذخیره اطلاعات"""
        try:
            credentials = {"email": self.email, "password": self.password}
            with open("quotex_real_credentials.json", "w") as f:
                json.dump(credentials, f)
        except Exception as e:
            print(f"❌ Save error: {e}")

    def load_credentials(self):
        """📂 بارگذاری اطلاعات"""
        try:
            if os.path.exists("quotex_real_credentials.json"):
                with open("quotex_real_credentials.json", "r") as f:
                    credentials = json.load(f)
                self.email_entry.insert(0, credentials.get("email", ""))
                self.password_entry.insert(0, credentials.get("password", ""))
        except Exception as e:
            print(f"❌ Load error: {e}")

# Test function
def test_real_data_reader():
    """🧪 تست خواننده اطلاعات واقعی"""
    print("🧪 Testing Real Data Reader...")

    root = tk.Tk()
    root.title("📊 Quotex Real Data Reader")
    root.geometry("1600x900")
    root.configure(bg='#0A0A0F')

    reader = QuotexRealDataReader(root)
    reader.show_login_page()

    root.mainloop()

if __name__ == "__main__":
    test_real_data_reader()