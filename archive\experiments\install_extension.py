#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 VIP BIG BANG - Chrome Extension Auto Installer
نصب خودکار اکستنشن Chrome
"""

import os
import sys
import subprocess
import shutil
import winreg
from pathlib import Path
import json

class VIPExtensionInstaller:
    """
    🔧 نصب‌کننده خودکار اکستنشن VIP BIG BANG
    """
    
    def __init__(self):
        self.extension_dir = Path("chrome_extension")
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
    def find_chrome(self):
        """پیدا کردن مسیر Chrome"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                print(f"✅ Chrome found: {path}")
                return path
        
        print("❌ Chrome not found!")
        return None
    
    def check_extension_files(self):
        """بررسی فایل‌های اکستنشن"""
        required_files = [
            "manifest.json",
            "background.js", 
            "content.js",
            "popup.html",
            "popup.js"
        ]
        
        print("🔍 Checking extension files...")
        
        if not self.extension_dir.exists():
            print("❌ Extension directory not found!")
            return False
        
        missing_files = []
        for file in required_files:
            file_path = self.extension_dir / file
            if file_path.exists():
                print(f"✅ {file}")
            else:
                print(f"❌ {file} - MISSING")
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ Missing files: {missing_files}")
            return False
        
        print("✅ All extension files found!")
        return True
    
    def create_extension_shortcut(self):
        """ایجاد میانبر برای نصب اکستنشن"""
        try:
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                return False
            
            extension_path = os.path.abspath(self.extension_dir)
            
            # Create batch file for easy installation
            batch_content = f'''@echo off
echo 🚀 Installing VIP BIG BANG Chrome Extension...
echo.
echo Extension Path: {extension_path}
echo.
echo Starting Chrome with extension...
"{chrome_exe}" --load-extension="{extension_path}" --disable-extensions-file-access-check --no-first-run --no-default-browser-check
echo.
echo ✅ Chrome started with VIP BIG BANG extension!
echo.
echo 📋 Manual Installation Steps:
echo 1. Open Chrome
echo 2. Go to chrome://extensions/
echo 3. Enable "Developer mode" (top right)
echo 4. Click "Load unpacked"
echo 5. Select folder: {extension_path}
echo.
pause
'''
            
            with open("install_vip_extension.bat", "w", encoding="utf-8") as f:
                f.write(batch_content)
            
            print("✅ Installation shortcut created: install_vip_extension.bat")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create shortcut: {e}")
            return False
    
    def launch_chrome_with_extension(self):
        """اجرای Chrome با اکستنشن"""
        try:
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                return False
            
            extension_path = os.path.abspath(self.extension_dir)
            
            # Chrome arguments for loading extension
            chrome_args = [
                chrome_exe,
                f"--load-extension={extension_path}",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling", 
                "--enable-experimental-extension-apis",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "https://qxbroker.com/en/trade"
            ]
            
            print("🚀 Launching Chrome with VIP BIG BANG extension...")
            subprocess.Popen(chrome_args, shell=False)
            
            print("✅ Chrome launched successfully!")
            print("🎯 Extension should be loaded automatically")
            return True
            
        except Exception as e:
            print(f"❌ Failed to launch Chrome: {e}")
            return False
    
    def create_manual_instructions(self):
        """ایجاد دستورالعمل نصب دستی"""
        instructions = f"""
# 🔧 VIP BIG BANG Chrome Extension - Manual Installation

## 📋 Step-by-Step Instructions:

### 1️⃣ Open Chrome Extensions Page:
- Open Google Chrome
- Type in address bar: chrome://extensions/
- Press Enter

### 2️⃣ Enable Developer Mode:
- Find "Developer mode" toggle (top right corner)
- Click to enable it

### 3️⃣ Load Extension:
- Click "Load unpacked" button
- Navigate to: {os.path.abspath(self.extension_dir)}
- Select the folder and click "Select Folder"

### 4️⃣ Verify Installation:
- Extension should appear in the list
- Make sure it's enabled (toggle switch on)
- Pin it to toolbar for easy access

### 5️⃣ Test Extension:
- Go to: https://qxbroker.com/en/trade
- Extension should activate automatically
- Look for VIP BIG BANG icon in toolbar

## 🚀 Quick Install (Automatic):
Run: install_vip_extension.bat

## ✅ Extension Features:
- Auto-detection of Quotex pages
- Real-time trading signals
- Anti-detection technology
- Secure communication with desktop app

## 🔧 Troubleshooting:
- If extension doesn't load: Check Developer mode is enabled
- If not working on Quotex: Refresh the page
- If connection fails: Restart Chrome and try again
"""
        
        with open("EXTENSION_INSTALL_GUIDE.md", "w", encoding="utf-8") as f:
            f.write(instructions)
        
        print("✅ Manual installation guide created: EXTENSION_INSTALL_GUIDE.md")
        return True
    
    def install_extension(self):
        """نصب کامل اکستنشن"""
        print("="*60)
        print("🔧 VIP BIG BANG Chrome Extension Installer")
        print("="*60)
        
        # Step 1: Check extension files
        if not self.check_extension_files():
            print("❌ Extension installation failed - missing files")
            return False
        
        # Step 2: Find Chrome
        if not self.find_chrome():
            print("❌ Extension installation failed - Chrome not found")
            return False
        
        # Step 3: Create installation shortcut
        self.create_extension_shortcut()
        
        # Step 4: Create manual instructions
        self.create_manual_instructions()
        
        # Step 5: Launch Chrome with extension
        success = self.launch_chrome_with_extension()
        
        print("\n" + "="*60)
        if success:
            print("✅ VIP BIG BANG Extension Installation Complete!")
            print("\n📋 What happened:")
            print("1. ✅ Extension files verified")
            print("2. ✅ Chrome found and launched")
            print("3. ✅ Extension loaded automatically")
            print("4. ✅ Installation shortcuts created")
            
            print("\n🎯 Next Steps:")
            print("1. Check Chrome extensions page (chrome://extensions/)")
            print("2. Verify VIP BIG BANG extension is enabled")
            print("3. Go to Quotex and test the extension")
            print("4. Use the desktop app to connect")
            
        else:
            print("⚠️ Automatic installation failed")
            print("\n📋 Manual Installation:")
            print("1. Run: install_vip_extension.bat")
            print("2. Or follow: EXTENSION_INSTALL_GUIDE.md")
        
        print("="*60)
        return success

def main():
    """اجرای نصب‌کننده"""
    installer = VIPExtensionInstaller()
    installer.install_extension()

if __name__ == "__main__":
    main()
