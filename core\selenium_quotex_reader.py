#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 VIP BIG BANG - Selenium Quotex Reader
🌐 اتصال مستقیم به کروم اصلی شما
📊 خواندن اطلاعات واقعی از DOM
⚡ بدون نیاز به Extension
"""

import time
import json
import psutil
import subprocess
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests

class SeleniumQuotexReader:
    """
    🤖 Selenium Quotex Reader
    🌐 اتصال مستقیم به کروم اصلی
    📊 خواندن اطلاعات واقعی
    """

    def __init__(self):
        self.driver = None
        self.is_connected = False
        self.chrome_debugger_port = 9222
        self.latest_data = {}
        
        print("🤖 Selenium Quotex Reader initialized")

    def connect_to_existing_chrome(self):
        """🔗 Connect to existing Chrome instance"""
        try:
            print("🔗 Connecting to existing Chrome...")
            
            # Check if Chrome is running
            chrome_running = self.is_chrome_running()
            
            if not chrome_running:
                print("🚀 Chrome not running, starting with debug port...")
                if not self.start_chrome_with_debug():
                    return False
            
            # Connect to Chrome via debugger port
            if self.connect_via_debugger():
                print("✅ Connected to existing Chrome via debugger")
                return True
            
            # Fallback: Start new Chrome instance
            print("🔄 Fallback: Starting new Chrome instance...")
            return self.start_new_chrome_instance()

        except Exception as e:
            print(f"❌ Chrome connection error: {e}")
            return False

    def is_chrome_running(self):
        """🔍 Check if Chrome is running"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if 'chrome' in proc.info['name'].lower():
                    return True
            return False
        except:
            return False

    def start_chrome_with_debug(self):
        """🚀 Start Chrome with debug port"""
        try:
            import os
            
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                rf"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
            ]
            
            chrome_exe = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_exe = path
                    break
            
            if not chrome_exe:
                print("❌ Chrome executable not found")
                return False
            
            # Chrome arguments for debugging
            cmd = [
                chrome_exe,
                f"--remote-debugging-port={self.chrome_debugger_port}",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-extensions",
                "--no-first-run",
                "--no-default-browser-check",
                "https://qxbroker.com/en/trade"
            ]
            
            print(f"🚀 Starting Chrome: {' '.join(cmd)}")
            
            # Start Chrome
            subprocess.Popen(cmd)
            
            # Wait for Chrome to start
            time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ Chrome start error: {e}")
            return False

    def connect_via_debugger(self):
        """🔗 Connect via Chrome debugger protocol"""
        try:
            print(f"🔗 Connecting via debugger port {self.chrome_debugger_port}...")
            
            # Check if debugger port is available
            try:
                response = requests.get(f"http://localhost:{self.chrome_debugger_port}/json")
                tabs = response.json()
                print(f"✅ Found {len(tabs)} Chrome tabs")
                
                # Find Quotex tab
                quotex_tab = None
                for tab in tabs:
                    if 'qxbroker.com' in tab.get('url', '') or 'quotex' in tab.get('url', '').lower():
                        quotex_tab = tab
                        break
                
                if quotex_tab:
                    print(f"✅ Found Quotex tab: {quotex_tab['title']}")
                    
                    # Connect Selenium to existing Chrome
                    chrome_options = Options()
                    chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.chrome_debugger_port}")
                    
                    self.driver = webdriver.Chrome(options=chrome_options)
                    
                    # Navigate to Quotex tab
                    self.driver.get(quotex_tab['url'])
                    
                    print("✅ Selenium connected to existing Chrome")
                    self.is_connected = True
                    return True
                else:
                    print("⚠️ Quotex tab not found, opening new tab...")
                    
                    # Connect to any tab and navigate to Quotex
                    chrome_options = Options()
                    chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.chrome_debugger_port}")
                    
                    self.driver = webdriver.Chrome(options=chrome_options)
                    self.driver.get("https://qxbroker.com/en/trade")
                    
                    print("✅ Selenium connected and navigated to Quotex")
                    self.is_connected = True
                    return True
                    
            except requests.exceptions.ConnectionError:
                print(f"❌ Chrome debugger port {self.chrome_debugger_port} not accessible")
                return False
                
        except Exception as e:
            print(f"❌ Debugger connection error: {e}")
            return False

    def start_new_chrome_instance(self):
        """🆕 Start new Chrome instance with Selenium"""
        try:
            print("🆕 Starting new Chrome instance...")
            
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-browser-side-navigation")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--no-default-browser-check")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-translate")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Hide automation indicators
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # Execute script to hide webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Navigate to Quotex
            self.driver.get("https://qxbroker.com/en/trade")
            
            print("✅ New Chrome instance started and navigated to Quotex")
            self.is_connected = True
            return True
            
        except Exception as e:
            print(f"❌ New Chrome instance error: {e}")
            return False

    def read_real_quotex_data(self):
        """📊 Read real data from Quotex DOM"""
        try:
            if not self.is_connected or not self.driver:
                print("❌ Not connected to Chrome")
                return None
            
            print("📊 Reading real data from Quotex...")
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            data = {
                "timestamp": time.strftime("%H:%M:%S"),
                "source": "🤖 SELENIUM CHROME READER",
                "method": "Direct DOM Reading",
                "url": self.driver.current_url,
                
                # Read balance
                "balance": self.read_balance(),
                
                # Read current asset
                "currentAsset": self.read_current_asset(),
                
                # Read current price
                "currentPrice": self.read_current_price(),
                
                # Read account type
                "accountType": self.read_account_type(),
                
                # Read today profit
                "todayProfit": self.read_today_profit(),
                
                # Read win rate
                "winRate": self.read_win_rate(),
                
                # Read trade buttons status
                "callEnabled": self.is_call_button_enabled(),
                "putEnabled": self.is_put_button_enabled(),
                
                # Read trade amount
                "tradeAmount": self.read_trade_amount(),
                
                # Connection status
                "connectionStatus": "✅ SELENIUM CONNECTED TO REAL CHROME",
                "seleniumConnected": True
            }
            
            self.latest_data = data
            print(f"✅ Real data read: Balance={data['balance']}, Asset={data['currentAsset']}")
            
            return data
            
        except Exception as e:
            print(f"❌ Data reading error: {e}")
            return None

    def read_balance(self):
        """💰 Read account balance"""
        try:
            selectors = [
                "[class*='balance']",
                "[class*='wallet']",
                "[class*='money']",
                "[data-testid*='balance']",
                ".balance",
                ".wallet-balance",
                ".user-balance",
                "[class*='account-balance']"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and ('$' in text or 'USD' in text or any(c.isdigit() for c in text)):
                            print(f"💰 Balance found: {text}")
                            return f"💰 {text} (REAL)"
                except:
                    continue
            
            return "💰 Balance not found"
            
        except Exception as e:
            print(f"❌ Balance reading error: {e}")
            return "💰 Error reading balance"

    def read_current_asset(self):
        """📊 Read current trading asset"""
        try:
            selectors = [
                "[class*='asset']",
                "[class*='symbol']",
                "[class*='pair']",
                "[class*='instrument']",
                ".current-asset",
                ".selected-asset"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) > 2 and len(text) < 15 and any(c.isupper() for c in text):
                            print(f"📊 Asset found: {text}")
                            return f"📊 {text} (CURRENT)"
                except:
                    continue
            
            return "📊 Asset not found"
            
        except Exception as e:
            print(f"❌ Asset reading error: {e}")
            return "📊 Error reading asset"

    def read_current_price(self):
        """💰 Read current price"""
        try:
            selectors = [
                "[class*='price']",
                "[class*='rate']",
                "[class*='quote']",
                "[class*='value']",
                ".current-price",
                ".asset-price"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and '.' in text and any(c.isdigit() for c in text) and '$' not in text:
                            print(f"💰 Price found: {text}")
                            return f"💰 {text} (LIVE)"
                except:
                    continue
            
            return "💰 Price not found"
            
        except Exception as e:
            print(f"❌ Price reading error: {e}")
            return "💰 Error reading price"

    def read_account_type(self):
        """🏦 Read account type (Demo/Real)"""
        try:
            # Look for demo/real indicators
            page_text = self.driver.page_source.lower()
            
            if 'demo' in page_text:
                return "DEMO ACCOUNT"
            elif 'real' in page_text:
                return "REAL ACCOUNT"
            else:
                return "REAL ACCOUNT"  # Default assumption
                
        except Exception as e:
            print(f"❌ Account type reading error: {e}")
            return "Unknown Account"

    def read_today_profit(self):
        """📈 Read today's profit"""
        try:
            selectors = [
                "[class*='profit']",
                "[class*='pnl']",
                "[class*='gain']",
                "[class*='loss']",
                ".today-profit",
                ".daily-profit"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and ('$' in text or '+' in text or '-' in text):
                            print(f"📈 Profit found: {text}")
                            return f"📈 {text} (TODAY)"
                except:
                    continue
            
            return "📈 Profit not found"
            
        except Exception as e:
            print(f"❌ Profit reading error: {e}")
            return "📈 Error reading profit"

    def read_win_rate(self):
        """🎯 Read win rate"""
        try:
            selectors = [
                "[class*='winrate']",
                "[class*='win-rate']",
                "[class*='success']",
                "[class*='percentage']"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and '%' in text:
                            print(f"🎯 Win rate found: {text}")
                            return f"🎯 {text} (WIN RATE)"
                except:
                    continue
            
            return "🎯 Win rate not found"
            
        except Exception as e:
            print(f"❌ Win rate reading error: {e}")
            return "🎯 Error reading win rate"

    def is_call_button_enabled(self):
        """🔴 Check if CALL button is enabled"""
        try:
            selectors = [
                "[class*='call']",
                "[class*='higher']",
                "[class*='up']",
                "[data-direction='call']"
            ]
            
            for selector in selectors:
                try:
                    button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    return button.is_enabled()
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"❌ CALL button check error: {e}")
            return False

    def is_put_button_enabled(self):
        """🔵 Check if PUT button is enabled"""
        try:
            selectors = [
                "[class*='put']",
                "[class*='lower']",
                "[class*='down']",
                "[data-direction='put']"
            ]
            
            for selector in selectors:
                try:
                    button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    return button.is_enabled()
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"❌ PUT button check error: {e}")
            return False

    def read_trade_amount(self):
        """💵 Read trade amount"""
        try:
            selectors = [
                "[class*='amount']",
                "[class*='investment']",
                "[class*='stake']",
                "input[type='number']"
            ]
            
            for selector in selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    value = element.get_attribute('value') or element.text
                    if value and any(c.isdigit() for c in value):
                        return f"${value}"
                except:
                    continue
            
            return "$10.00"  # Default
            
        except Exception as e:
            print(f"❌ Trade amount reading error: {e}")
            return "$10.00"

    def close_connection(self):
        """🔌 Close Selenium connection"""
        try:
            if self.driver:
                self.driver.quit()
                self.is_connected = False
                print("🔌 Selenium connection closed")
        except Exception as e:
            print(f"❌ Close connection error: {e}")

# Global instance
selenium_reader = None

def get_selenium_reader():
    """📊 Get global Selenium reader instance"""
    global selenium_reader
    if selenium_reader is None:
        selenium_reader = SeleniumQuotexReader()
    return selenium_reader

def connect_to_chrome():
    """🔗 Connect to Chrome"""
    reader = get_selenium_reader()
    return reader.connect_to_existing_chrome()

def read_real_data():
    """📊 Read real data from Chrome"""
    reader = get_selenium_reader()
    return reader.read_real_quotex_data()

def is_selenium_connected():
    """🔗 Check if Selenium is connected"""
    reader = get_selenium_reader()
    return reader.is_connected

# Test function
def test_selenium_reader():
    """🧪 Test Selenium reader"""
    print("🧪 Testing Selenium Quotex Reader...")
    
    reader = SeleniumQuotexReader()
    
    if reader.connect_to_existing_chrome():
        print("✅ Connected to Chrome")
        
        # Wait for user to login
        input("📋 Please login to Quotex in Chrome, then press Enter...")
        
        # Read data
        data = reader.read_real_quotex_data()
        if data:
            print("📊 Real data read successfully:")
            for key, value in data.items():
                print(f"  {key}: {value}")
        else:
            print("❌ Failed to read data")
        
        reader.close_connection()
    else:
        print("❌ Failed to connect to Chrome")

if __name__ == "__main__":
    test_selenium_reader()
