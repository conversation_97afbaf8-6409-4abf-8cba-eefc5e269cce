/**
 * VIP BIG BANG Enterprise - Popup Script
 * Controls the extension popup interface
 */

// Popup state
let updateInterval = null;
let isConnected = false;

// DOM elements
const elements = {
    desktopStatus: null,
    quotexStatus: null,
    extensionStatus: null,
    desktopValue: null,
    quotexValue: null,
    extensionValue: null,
    balanceValue: null,
    tradesValue: null,
    connectBtn: null,
    refreshBtn: null,
    openQuotexBtn: null,
    emergencyStopBtn: null,
    lastUpdate: null
};

/**
 * Initialize popup
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 VIP BIG BANG Popup initializing...');
    
    // Get DOM elements
    initializeElements();
    
    // Setup event listeners
    setupEventListeners();
    
    // Start status updates
    startStatusUpdates();
    
    // Initial status check
    updateStatus();
    
    console.log('✅ Popup initialized');
});

/**
 * Initialize DOM elements
 */
function initializeElements() {
    elements.desktopStatus = document.getElementById('desktop-status');
    elements.quotexStatus = document.getElementById('quotex-status');
    elements.extensionStatus = document.getElementById('extension-status');
    elements.desktopValue = document.getElementById('desktop-value');
    elements.quotexValue = document.getElementById('quotex-value');
    elements.extensionValue = document.getElementById('extension-value');
    elements.balanceValue = document.getElementById('balance-value');
    elements.tradesValue = document.getElementById('trades-value');
    elements.connectBtn = document.getElementById('connect-btn');
    elements.refreshBtn = document.getElementById('refresh-btn');
    elements.openQuotexBtn = document.getElementById('open-quotex-btn');
    elements.emergencyStopBtn = document.getElementById('emergency-stop-btn');
    elements.lastUpdate = document.getElementById('last-update');
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Connect button
    elements.connectBtn.addEventListener('click', handleConnect);
    
    // Refresh button
    elements.refreshBtn.addEventListener('click', handleRefresh);
    
    // Open Quotex button
    elements.openQuotexBtn.addEventListener('click', handleOpenQuotex);
    
    // Emergency stop button
    elements.emergencyStopBtn.addEventListener('click', handleEmergencyStop);
}

/**
 * Handle connect button click
 */
async function handleConnect() {
    try {
        elements.connectBtn.disabled = true;
        elements.connectBtn.innerHTML = '<span class="loading"></span> Connecting...';
        
        // Attempt to connect to desktop app
        const response = await sendMessageToBackground({
            type: 'CONNECT_DESKTOP'
        });
        
        if (response.success) {
            showNotification('Connected to desktop app', 'success');
            updateStatus();
        } else {
            showNotification('Failed to connect: ' + response.error, 'error');
        }
        
    } catch (error) {
        console.error('Connection error:', error);
        showNotification('Connection failed', 'error');
    } finally {
        elements.connectBtn.disabled = false;
        elements.connectBtn.innerHTML = '🔌 Connect to Desktop';
    }
}

/**
 * Handle refresh button click
 */
async function handleRefresh() {
    try {
        elements.refreshBtn.disabled = true;
        elements.refreshBtn.innerHTML = '<span class="loading"></span> Refreshing...';
        
        await updateStatus();
        showNotification('Status refreshed', 'success');
        
    } catch (error) {
        console.error('Refresh error:', error);
        showNotification('Refresh failed', 'error');
    } finally {
        elements.refreshBtn.disabled = false;
        elements.refreshBtn.innerHTML = '🔄 Refresh Status';
    }
}

/**
 * Handle open Quotex button click
 */
async function handleOpenQuotex() {
    try {
        // Open Quotex in new tab
        await chrome.tabs.create({
            url: 'https://quotex.io',
            active: true
        });
        
        showNotification('Quotex opened in new tab', 'success');
        
    } catch (error) {
        console.error('Open Quotex error:', error);
        showNotification('Failed to open Quotex', 'error');
    }
}

/**
 * Handle emergency stop button click
 */
async function handleEmergencyStop() {
    try {
        const confirmed = confirm('Are you sure you want to emergency stop all trading?');
        if (!confirmed) return;
        
        elements.emergencyStopBtn.disabled = true;
        elements.emergencyStopBtn.innerHTML = '<span class="loading"></span> Stopping...';
        
        // Send emergency stop command
        const response = await sendMessageToBackground({
            type: 'EMERGENCY_STOP'
        });
        
        if (response.success) {
            showNotification('Emergency stop activated', 'success');
            elements.emergencyStopBtn.classList.add('hidden');
        } else {
            showNotification('Emergency stop failed: ' + response.error, 'error');
        }
        
    } catch (error) {
        console.error('Emergency stop error:', error);
        showNotification('Emergency stop failed', 'error');
    } finally {
        elements.emergencyStopBtn.disabled = false;
        elements.emergencyStopBtn.innerHTML = '🛑 Emergency Stop';
    }
}

/**
 * Update status displays
 */
async function updateStatus() {
    try {
        // Get extension status
        const extensionStatus = await sendMessageToBackground({
            type: 'GET_STATUS'
        });
        
        if (extensionStatus.success) {
            const status = extensionStatus.data;
            
            // Update desktop connection status
            updateStatusItem(
                elements.desktopStatus,
                elements.desktopValue,
                status.desktopConnected,
                'Desktop App'
            );
            
            // Update Quotex connection status
            updateStatusItem(
                elements.quotexStatus,
                elements.quotexValue,
                status.quotexTabActive,
                'Quotex'
            );
            
            // Extension is always active if popup is open
            updateStatusItem(
                elements.extensionStatus,
                elements.extensionValue,
                true,
                'Extension'
            );
            
            // Show/hide emergency stop button
            if (status.desktopConnected && status.quotexTabActive) {
                elements.emergencyStopBtn.classList.remove('hidden');
            } else {
                elements.emergencyStopBtn.classList.add('hidden');
            }
        }
        
        // Get balance and trades info
        await updateTradingInfo();
        
        // Update last update time
        elements.lastUpdate.textContent = `Last update: ${new Date().toLocaleTimeString()}`;
        
    } catch (error) {
        console.error('Status update error:', error);
    }
}

/**
 * Update individual status item
 */
function updateStatusItem(statusElement, valueElement, isOnline, label) {
    if (isOnline) {
        statusElement.classList.remove('disconnected');
        statusElement.classList.add('connected');
        valueElement.classList.remove('offline');
        valueElement.classList.add('online');
        valueElement.textContent = 'Online';
    } else {
        statusElement.classList.remove('connected');
        statusElement.classList.add('disconnected');
        valueElement.classList.remove('online');
        valueElement.classList.add('offline');
        valueElement.textContent = 'Offline';
    }
}

/**
 * Update trading information
 */
async function updateTradingInfo() {
    try {
        // Get balance
        const balanceResponse = await sendMessageToBackground({
            type: 'GET_BALANCE'
        });
        
        if (balanceResponse.success) {
            const balance = balanceResponse.data.balance || 0;
            elements.balanceValue.textContent = `$${balance.toFixed(2)}`;
        }
        
        // Get trades count (would need to be implemented in background script)
        const tradesResponse = await sendMessageToBackground({
            type: 'GET_TRADES_COUNT'
        });
        
        if (tradesResponse.success) {
            const tradesCount = tradesResponse.data.count || 0;
            elements.tradesValue.textContent = tradesCount.toString();
        }
        
    } catch (error) {
        console.error('Trading info update error:', error);
    }
}

/**
 * Send message to background script
 */
function sendMessageToBackground(message) {
    return new Promise((resolve) => {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                resolve({ success: false, error: chrome.runtime.lastError.message });
            } else {
                resolve(response || { success: false, error: 'No response' });
            }
        });
    });
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        padding: 10px 15px;
        border-radius: 5px;
        color: white;
        font-size: 12px;
        z-index: 1000;
        max-width: 250px;
        word-wrap: break-word;
        ${type === 'success' ? 'background: #4a8a4a;' : ''}
        ${type === 'error' ? 'background: #8a4a4a;' : ''}
        ${type === 'info' ? 'background: #4a6a8a;' : ''}
    `;
    
    // Add to document
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

/**
 * Start periodic status updates
 */
function startStatusUpdates() {
    // Update every 5 seconds
    updateInterval = setInterval(updateStatus, 5000);
}

/**
 * Stop status updates
 */
function stopStatusUpdates() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}

/**
 * Cleanup when popup closes
 */
window.addEventListener('beforeunload', () => {
    stopStatusUpdates();
});
