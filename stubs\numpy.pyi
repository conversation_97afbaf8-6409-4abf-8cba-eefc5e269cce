"""
NumPy type stubs for VIP BIG BANG
"""

from typing import Any

class ndarray:
    def __init__(self, *args: Any, **kwargs: Any) -> None: ...
    def __getitem__(self, key: Any) -> Any: ...
    def __setitem__(self, key: Any, value: Any) -> None: ...
    def __len__(self) -> int: ...
    def mean(self) -> float: ...
    def std(self) -> float: ...
    def sum(self) -> float: ...

def array(object: Any, dtype: Any = None, **kwargs: Any) -> ndarray: ...
def mean(a: Any, axis: Any = None, **kwargs: Any) -> float: ...
def std(a: Any, axis: Any = None, **kwargs: Any) -> float: ...
def sum(a: Any, axis: Any = None, **kwargs: Any) -> float: ...
def abs(x: Any) -> Any: ...
def sqrt(x: Any) -> Any: ...
def log(x: Any) -> Any: ...
def exp(x: Any) -> Any: ...

class random:
    @staticmethod
    def seed(seed: int) -> None: ...
    @staticmethod
    def normal(loc: float = 0.0, scale: float = 1.0, size: Any = None) -> Any: ...
    @staticmethod
    def uniform(low: float = 0.0, high: float = 1.0, size: Any = None) -> Any: ...
    @staticmethod
    def randint(low: int, high: int, size: Any = None) -> Any: ...
