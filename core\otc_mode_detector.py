"""
VIP BIG BANG Enterprise - OTC Mode Detector
Automatic detection of OTC market and activation of specific settings
"""

import pandas as pd  # type: ignore
from typing import Dict, Optional, Any
import logging
from datetime import datetime, time

class OTCModeDetector:
    """
    OTC Mode Detector - VIP BIG BANG complementary analysis
    Automatically detects OTC market conditions
    Activates stricter filters and specific settings for OTC trading
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("OTCModeDetector")
        
        # OTC detection parameters
        self.otc_volatility_threshold = 0.02  # 2% volatility threshold
        self.otc_volume_threshold = 0.3       # 30% of normal volume
        self.otc_spread_threshold = 0.001     # 0.1% spread threshold
        
        # OTC time periods (when major markets are closed)
        self.otc_time_periods = [
            # Weekend (Friday 22:00 UTC to Sunday 22:00 UTC)
            {'start': time(22, 0), 'end': time(22, 0), 'days': [4, 5, 6]},  # Fri-Sun
            # Late night hours (22:00 to 06:00 UTC)
            {'start': time(22, 0), 'end': time(6, 0), 'days': list(range(7))}
        ]
        
        # OTC specific settings
        self.otc_settings = {
            'min_confidence': 0.90,        # Higher confidence required
            'confirmation_required': 5,    # More confirmations needed
            'signal_timeout': 60,          # Longer signal timeout
            'max_trade_amount': 5.0,       # Lower trade amounts
            'risk_per_trade': 1.0,         # Lower risk per trade
            'volatility_filter': True,     # Enable volatility filter
            'spread_filter': True,         # Enable spread filter
            'volume_filter': True          # Enable volume filter
        }
        
        self.logger.debug("OTC Mode Detector initialized")
    
    def is_otc_time_period(self, current_time: Optional[datetime] = None) -> Dict[str, Any]:
        """Check if current time falls within OTC periods"""
        if current_time is None:
            current_time = datetime.now()
        
        current_weekday = current_time.weekday()  # 0=Monday, 6=Sunday
        current_time_only = current_time.time()
        
        otc_reasons = []
        is_otc_time = False
        
        for period in self.otc_time_periods:
            start_time = period['start']
            end_time = period['end']
            valid_days = period['days']
            
            # Check if current day is in valid days
            if current_weekday in valid_days:
                # Handle overnight periods (end time < start time)
                if end_time < start_time:
                    if current_time_only >= start_time or current_time_only <= end_time:
                        is_otc_time = True
                        otc_reasons.append(f"Overnight period: {start_time} - {end_time}")
                else:
                    if start_time <= current_time_only <= end_time:
                        is_otc_time = True
                        otc_reasons.append(f"Day period: {start_time} - {end_time}")
        
        # Special weekend check
        if current_weekday in [5, 6]:  # Saturday, Sunday
            is_otc_time = True
            otc_reasons.append("Weekend period")
        
        return {
            'is_otc_time': is_otc_time,
            'reasons': otc_reasons,
            'current_weekday': current_weekday,
            'current_time': current_time_only
        }
    
    def analyze_market_characteristics(self, data: pd.DataFrame) -> Dict:
        """Analyze market characteristics to detect OTC conditions"""
        if len(data) < 20:
            return {
                'volatility_score': 0.5,
                'volume_score': 0.5,
                'spread_score': 0.5,
                'is_otc_market': False
            }
        
        # Calculate volatility
        prices = data['close'] if 'close' in data.columns else data['price']
        returns = prices.pct_change().dropna()
        volatility = returns.std()
        
        # Normalize volatility score (higher volatility = more likely OTC)
        volatility_score = min(volatility / self.otc_volatility_threshold, 1.0)
        
        # Calculate volume characteristics
        volumes = data['volume'] if 'volume' in data.columns else pd.Series([1] * len(data))
        avg_volume = volumes.mean()
        recent_volume = volumes.tail(5).mean()
        
        # Volume score (lower volume = more likely OTC)
        if avg_volume > 0:
            volume_ratio = recent_volume / avg_volume
            volume_score = max(0, 1 - volume_ratio)  # Inverted score
        else:
            volume_score = 0.5
        
        # Calculate spread characteristics (if available)
        if 'high' in data.columns and 'low' in data.columns:
            spreads = (data['high'] - data['low']) / data['close']
            avg_spread = spreads.mean()
            spread_score = min(avg_spread / self.otc_spread_threshold, 1.0)
        else:
            spread_score = 0.5
        
        # Determine if market characteristics suggest OTC
        otc_indicators = 0
        if volatility_score > 0.7:
            otc_indicators += 1
        if volume_score > 0.7:
            otc_indicators += 1
        if spread_score > 0.7:
            otc_indicators += 1
        
        is_otc_market = otc_indicators >= 2
        
        return {
            'volatility_score': volatility_score,
            'volume_score': volume_score,
            'spread_score': spread_score,
            'otc_indicators': otc_indicators,
            'is_otc_market': is_otc_market,
            'volatility': volatility,
            'volume_ratio': volume_ratio if 'volume_ratio' in locals() else 1.0,
            'avg_spread': avg_spread if 'avg_spread' in locals() else 0.0
        }
    
    def get_otc_adjusted_settings(self, base_settings: Dict) -> Dict:
        """Get adjusted settings for OTC mode"""
        adjusted_settings = dict(base_settings) if isinstance(base_settings, dict) else {}
        
        # Apply OTC specific adjustments
        for key, value in self.otc_settings.items():
            if key in adjusted_settings:
                adjusted_settings[key] = value
        
        return adjusted_settings
    
    def calculate_otc_risk_multiplier(self, otc_data: Dict) -> float:
        """Calculate risk multiplier for OTC conditions"""
        base_multiplier = 1.0
        
        # Increase risk based on OTC characteristics
        if otc_data['is_otc_market']:
            base_multiplier *= 1.5  # 50% higher risk
        
        # Additional risk from volatility
        volatility_risk = otc_data['volatility_score'] * 0.3
        
        # Additional risk from low volume
        volume_risk = otc_data['volume_score'] * 0.2
        
        # Additional risk from wide spreads
        spread_risk = otc_data['spread_score'] * 0.2
        
        total_multiplier = base_multiplier + volatility_risk + volume_risk + spread_risk
        
        return min(total_multiplier, 3.0)  # Cap at 3x risk
    
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Main OTC mode detection analysis
        Returns OTC status and adjusted settings
        """
        try:
            current_time = datetime.now()
            
            # Check time-based OTC detection
            time_check = self.is_otc_time_period(current_time)
            
            # Analyze market characteristics
            market_analysis = self.analyze_market_characteristics(data)
            
            # Determine overall OTC status
            is_otc_mode = time_check['is_otc_time'] or market_analysis['is_otc_market']
            
            # Calculate confidence in OTC detection
            confidence_factors = []
            if time_check['is_otc_time']:
                confidence_factors.append(0.8)  # High confidence from time
            if market_analysis['is_otc_market']:
                confidence_factors.append(0.7)  # Good confidence from market data
            
            confidence = max(confidence_factors) if confidence_factors else 0.0
            
            # Calculate risk multiplier
            risk_multiplier = self.calculate_otc_risk_multiplier(market_analysis)
            
            # Get adjusted settings
            base_settings = getattr(self.settings, 'signal_processing', {})
            adjusted_settings = self.get_otc_adjusted_settings(base_settings)
            
            # Calculate score (lower score for OTC = more restrictive)
            if is_otc_mode:
                score = 0.3  # Restrictive score for OTC
            else:
                score = 0.8  # Normal score for regular market
            
            return {
                'score': score,
                'direction': 'NEUTRAL',  # OTC detector doesn't provide direction
                'confidence': confidence,
                'is_otc_mode': is_otc_mode,
                'time_check': time_check,
                'market_analysis': market_analysis,
                'risk_multiplier': risk_multiplier,
                'adjusted_settings': adjusted_settings,
                'otc_reasons': time_check['reasons'] + (['Market characteristics'] if market_analysis['is_otc_market'] else []),
                'details': f"OTC Mode: {'ACTIVE' if is_otc_mode else 'INACTIVE'}, Risk: {risk_multiplier:.1f}x"
            }
            
        except Exception as e:
            self.logger.error(f"OTC mode detection error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'is_otc_mode': False,
                'details': f'OTC detection failed: {str(e)}'
            }
