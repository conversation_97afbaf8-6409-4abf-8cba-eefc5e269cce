"""
🎛️ VIP BIG BANG - Trading Systems Manager
🚀 Professional trading systems with configurable settings
💎 Multiple trading strategies with real-time configuration
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from typing import Dict, List, Callable
from datetime import datetime

class TradingSystemBox(tk.Frame):
    """Individual trading system configuration box"""
    
    def __init__(self, parent, system_name: str, system_config: Dict, on_config_change: Callable = None):
        super().__init__(parent)
        
        self.system_name = system_name
        self.system_config = system_config.copy()
        self.on_config_change = on_config_change
        self.is_enabled = system_config.get('enabled', False)
        
        # Setup UI
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup trading system box UI"""
        self.configure(bg='#2D1B69', relief=tk.RAISED, bd=2)
        
        # Header with enable/disable toggle
        header_frame = tk.Frame(self, bg='#2D1B69')
        header_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # System name
        name_label = tk.Label(
            header_frame,
            text=self.system_name,
            font=('Arial', 12, 'bold'),
            bg='#2D1B69',
            fg='#8B5CF6'
        )
        name_label.pack(side=tk.LEFT)
        
        # Enable/Disable toggle
        self.enabled_var = tk.BooleanVar(value=self.is_enabled)
        self.enabled_check = tk.Checkbutton(
            header_frame,
            text="",
            variable=self.enabled_var,
            bg='#2D1B69',
            selectcolor='#2D1B69',
            activebackground='#2D1B69',
            command=self._on_toggle_enabled
        )
        self.enabled_check.pack(side=tk.RIGHT)
        
        # Status indicator
        self.status_label = tk.Label(
            header_frame,
            text="🟢 ON" if self.is_enabled else "🔴 OFF",
            font=('Arial', 10, 'bold'),
            bg='#2D1B69',
            fg='#10B981' if self.is_enabled else '#EF4444'
        )
        self.status_label.pack(side=tk.RIGHT, padx=(0, 10))
        
        # Configuration frame
        config_frame = tk.Frame(self, bg='#2D1B69')
        config_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Configuration options
        self._create_config_options(config_frame)
        
        # Action buttons
        buttons_frame = tk.Frame(self, bg='#2D1B69')
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Settings button
        settings_btn = tk.Button(
            buttons_frame,
            text="⚙️ Settings",
            font=('Arial', 9),
            bg='#8B5CF6',
            fg='white',
            command=self._open_settings
        )
        settings_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Test button
        test_btn = tk.Button(
            buttons_frame,
            text="🧪 Test",
            font=('Arial', 9),
            bg='#60A5FA',
            fg='white',
            command=self._test_system
        )
        test_btn.pack(side=tk.LEFT, padx=5)
        
        # Reset button
        reset_btn = tk.Button(
            buttons_frame,
            text="🔄 Reset",
            font=('Arial', 9),
            bg='#F59E0B',
            fg='white',
            command=self._reset_system
        )
        reset_btn.pack(side=tk.RIGHT)
    
    def _create_config_options(self, parent):
        """Create configuration options"""
        # Risk Level
        risk_frame = tk.Frame(parent, bg='#2D1B69')
        risk_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(risk_frame, text="Risk Level:", font=('Arial', 9), bg='#2D1B69', fg='white').pack(side=tk.LEFT)
        
        self.risk_var = tk.StringVar(value=self.system_config.get('risk_level', 'Medium'))
        risk_combo = ttk.Combobox(
            risk_frame,
            textvariable=self.risk_var,
            values=["Low", "Medium", "High", "Ultra"],
            state="readonly",
            width=8
        )
        risk_combo.pack(side=tk.RIGHT)
        risk_combo.bind('<<ComboboxSelected>>', self._on_config_change_event)
        
        # Trade Amount
        amount_frame = tk.Frame(parent, bg='#2D1B69')
        amount_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(amount_frame, text="Amount:", font=('Arial', 9), bg='#2D1B69', fg='white').pack(side=tk.LEFT)
        
        self.amount_var = tk.StringVar(value=str(self.system_config.get('trade_amount', 10)))
        amount_entry = tk.Entry(
            amount_frame,
            textvariable=self.amount_var,
            width=8,
            font=('Arial', 9)
        )
        amount_entry.pack(side=tk.RIGHT)
        amount_entry.bind('<KeyRelease>', self._on_config_change_event)
        
        # Signal Threshold
        threshold_frame = tk.Frame(parent, bg='#2D1B69')
        threshold_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(threshold_frame, text="Threshold:", font=('Arial', 9), bg='#2D1B69', fg='white').pack(side=tk.LEFT)
        
        self.threshold_var = tk.StringVar(value=str(self.system_config.get('signal_threshold', 80)))
        threshold_entry = tk.Entry(
            threshold_frame,
            textvariable=self.threshold_var,
            width=8,
            font=('Arial', 9)
        )
        threshold_entry.pack(side=tk.RIGHT)
        threshold_entry.bind('<KeyRelease>', self._on_config_change_event)
        
        # Max Trades
        max_trades_frame = tk.Frame(parent, bg='#2D1B69')
        max_trades_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(max_trades_frame, text="Max Trades:", font=('Arial', 9), bg='#2D1B69', fg='white').pack(side=tk.LEFT)
        
        self.max_trades_var = tk.StringVar(value=str(self.system_config.get('max_trades', 5)))
        max_trades_entry = tk.Entry(
            max_trades_frame,
            textvariable=self.max_trades_var,
            width=8,
            font=('Arial', 9)
        )
        max_trades_entry.pack(side=tk.RIGHT)
        max_trades_entry.bind('<KeyRelease>', self._on_config_change_event)
    
    def _on_toggle_enabled(self):
        """Handle enable/disable toggle"""
        self.is_enabled = self.enabled_var.get()
        
        if self.is_enabled:
            self.status_label.config(text="🟢 ON", fg='#10B981')
        else:
            self.status_label.config(text="🔴 OFF", fg='#EF4444')
        
        self.system_config['enabled'] = self.is_enabled
        self._notify_config_change()
    
    def _on_config_change_event(self, event=None):
        """Handle configuration change"""
        try:
            self.system_config['risk_level'] = self.risk_var.get()
            self.system_config['trade_amount'] = float(self.amount_var.get())
            self.system_config['signal_threshold'] = int(self.threshold_var.get())
            self.system_config['max_trades'] = int(self.max_trades_var.get())
            
            self._notify_config_change()
        except ValueError:
            pass  # Invalid input, ignore
    
    def _notify_config_change(self):
        """Notify parent of configuration change"""
        if self.on_config_change:
            self.on_config_change(self.system_name, self.system_config)
    
    def _open_settings(self):
        """Open advanced settings dialog"""
        settings_window = tk.Toplevel(self)
        settings_window.title(f"{self.system_name} - Advanced Settings")
        settings_window.geometry("400x300")
        settings_window.configure(bg='#1F2937')
        
        # Advanced settings content
        tk.Label(
            settings_window,
            text=f"Advanced Settings - {self.system_name}",
            font=('Arial', 14, 'bold'),
            bg='#1F2937',
            fg='#8B5CF6'
        ).pack(pady=20)
        
        # Settings notebook
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # General tab
        general_frame = tk.Frame(notebook, bg='#374151')
        notebook.add(general_frame, text="General")
        
        # Strategy tab
        strategy_frame = tk.Frame(notebook, bg='#374151')
        notebook.add(strategy_frame, text="Strategy")
        
        # Risk Management tab
        risk_frame = tk.Frame(notebook, bg='#374151')
        notebook.add(risk_frame, text="Risk Management")
        
        # Add settings content here...
        tk.Label(general_frame, text="General settings coming soon...", bg='#374151', fg='white').pack(pady=50)
        tk.Label(strategy_frame, text="Strategy settings coming soon...", bg='#374151', fg='white').pack(pady=50)
        tk.Label(risk_frame, text="Risk management settings coming soon...", bg='#374151', fg='white').pack(pady=50)
    
    def _test_system(self):
        """Test trading system"""
        messagebox.showinfo(
            "System Test",
            f"Testing {self.system_name}...\n\n"
            f"Risk Level: {self.system_config['risk_level']}\n"
            f"Trade Amount: ${self.system_config['trade_amount']}\n"
            f"Signal Threshold: {self.system_config['signal_threshold']}%\n"
            f"Max Trades: {self.system_config['max_trades']}\n\n"
            f"✅ System test completed successfully!"
        )
    
    def _reset_system(self):
        """Reset system to defaults"""
        result = messagebox.askyesno(
            "Reset System",
            f"Are you sure you want to reset {self.system_name} to default settings?"
        )
        
        if result:
            # Reset to defaults
            defaults = {
                'enabled': False,
                'risk_level': 'Medium',
                'trade_amount': 10,
                'signal_threshold': 80,
                'max_trades': 5
            }
            
            self.system_config.update(defaults)
            
            # Update UI
            self.enabled_var.set(False)
            self.risk_var.set('Medium')
            self.amount_var.set('10')
            self.threshold_var.set('80')
            self.max_trades_var.set('5')
            
            self._on_toggle_enabled()
            self._notify_config_change()


class TradingSystemsManager(tk.Frame):
    """
    🎛️ Trading Systems Manager
    
    Features:
    - Multiple trading system configurations
    - Real-time settings adjustment
    - System performance monitoring
    - Import/Export configurations
    """
    
    def __init__(self, parent, on_system_change: Callable = None):
        super().__init__(parent)
        
        self.on_system_change = on_system_change
        
        # Default trading systems
        self.trading_systems = {
            "Momentum Scalper": {
                'enabled': True,
                'risk_level': 'High',
                'trade_amount': 15,
                'signal_threshold': 85,
                'max_trades': 3,
                'description': 'Fast momentum-based scalping system'
            },
            "Trend Follower": {
                'enabled': True,
                'risk_level': 'Medium',
                'trade_amount': 20,
                'signal_threshold': 75,
                'max_trades': 5,
                'description': 'Long-term trend following system'
            },
            "Reversal Hunter": {
                'enabled': False,
                'risk_level': 'Ultra',
                'trade_amount': 10,
                'signal_threshold': 90,
                'max_trades': 2,
                'description': 'Counter-trend reversal system'
            },
            "News Trader": {
                'enabled': False,
                'risk_level': 'Low',
                'trade_amount': 25,
                'signal_threshold': 70,
                'max_trades': 4,
                'description': 'Economic news-based trading'
            },
            "AI Predictor": {
                'enabled': True,
                'risk_level': 'Medium',
                'trade_amount': 12,
                'signal_threshold': 80,
                'max_trades': 6,
                'description': 'AI-powered prediction system'
            }
        }
        
        # System boxes
        self.system_boxes: Dict[str, TradingSystemBox] = {}
        
        # Setup UI
        self._setup_ui()
        
        # Load saved configurations
        self._load_configurations()
    
    def _setup_ui(self):
        """Setup trading systems manager UI"""
        self.configure(bg='#1F2937')
        
        # Title and controls
        header_frame = tk.Frame(self, bg='#1F2937')
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="🎛️ Trading Systems Manager",
            font=('Arial', 16, 'bold'),
            bg='#1F2937',
            fg='#8B5CF6'
        )
        title_label.pack(side=tk.LEFT)
        
        # Control buttons
        controls_frame = tk.Frame(header_frame, bg='#1F2937')
        controls_frame.pack(side=tk.RIGHT)
        
        # Enable All button
        enable_all_btn = tk.Button(
            controls_frame,
            text="✅ Enable All",
            font=('Arial', 10),
            bg='#10B981',
            fg='white',
            command=self._enable_all_systems
        )
        enable_all_btn.pack(side=tk.LEFT, padx=5)
        
        # Disable All button
        disable_all_btn = tk.Button(
            controls_frame,
            text="❌ Disable All",
            font=('Arial', 10),
            bg='#EF4444',
            fg='white',
            command=self._disable_all_systems
        )
        disable_all_btn.pack(side=tk.LEFT, padx=5)
        
        # Save Config button
        save_btn = tk.Button(
            controls_frame,
            text="💾 Save",
            font=('Arial', 10),
            bg='#8B5CF6',
            fg='white',
            command=self._save_configurations
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        # Load Config button
        load_btn = tk.Button(
            controls_frame,
            text="📁 Load",
            font=('Arial', 10),
            bg='#60A5FA',
            fg='white',
            command=self._load_configurations
        )
        load_btn.pack(side=tk.LEFT, padx=5)
        
        # Systems container
        systems_frame = tk.Frame(self, bg='#1F2937')
        systems_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Create system boxes
        for i, (system_name, config) in enumerate(self.trading_systems.items()):
            row = i // 2
            col = i % 2
            
            system_box = TradingSystemBox(
                systems_frame,
                system_name,
                config,
                on_config_change=self._on_system_config_change
            )
            system_box.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')
            
            self.system_boxes[system_name] = system_box
        
        # Configure grid weights
        for i in range(2):
            systems_frame.columnconfigure(i, weight=1)
        for i in range(3):
            systems_frame.rowconfigure(i, weight=1)
    
    def _on_system_config_change(self, system_name: str, config: Dict):
        """Handle system configuration change"""
        self.trading_systems[system_name] = config
        
        if self.on_system_change:
            self.on_system_change(system_name, config)
        
        print(f"🎛️ System updated: {system_name} - {config}")
    
    def _enable_all_systems(self):
        """Enable all trading systems"""
        for system_box in self.system_boxes.values():
            system_box.enabled_var.set(True)
            system_box._on_toggle_enabled()
    
    def _disable_all_systems(self):
        """Disable all trading systems"""
        for system_box in self.system_boxes.values():
            system_box.enabled_var.set(False)
            system_box._on_toggle_enabled()
    
    def _save_configurations(self):
        """Save configurations to file"""
        try:
            os.makedirs('configs', exist_ok=True)
            
            with open('configs/trading_systems.json', 'w') as f:
                json.dump(self.trading_systems, f, indent=2)
            
            messagebox.showinfo("Save Successful", "Trading system configurations saved successfully!")
            
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save configurations:\n{e}")
    
    def _load_configurations(self):
        """Load configurations from file"""
        try:
            if os.path.exists('configs/trading_systems.json'):
                with open('configs/trading_systems.json', 'r') as f:
                    loaded_systems = json.load(f)
                
                # Update systems
                self.trading_systems.update(loaded_systems)
                
                # Update UI
                for system_name, system_box in self.system_boxes.items():
                    if system_name in self.trading_systems:
                        config = self.trading_systems[system_name]
                        
                        system_box.system_config = config
                        system_box.enabled_var.set(config.get('enabled', False))
                        system_box.risk_var.set(config.get('risk_level', 'Medium'))
                        system_box.amount_var.set(str(config.get('trade_amount', 10)))
                        system_box.threshold_var.set(str(config.get('signal_threshold', 80)))
                        system_box.max_trades_var.set(str(config.get('max_trades', 5)))
                        
                        system_box._on_toggle_enabled()
                
                print("📁 Trading system configurations loaded")
                
        except Exception as e:
            print(f"Load error: {e}")
    
    def get_enabled_systems(self) -> List[str]:
        """Get list of enabled trading systems"""
        return [name for name, config in self.trading_systems.items() if config.get('enabled', False)]
    
    def get_system_config(self, system_name: str) -> Dict:
        """Get configuration for specific system"""
        return self.trading_systems.get(system_name, {})
    
    def get_all_configurations(self) -> Dict:
        """Get all system configurations"""
        return self.trading_systems.copy()
