#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 VIP BIG BANG - Extension Status Tester
تست وضعیت اکستنشن Chrome
"""

import subprocess
import time
import json
import os
from pathlib import Path

class ExtensionStatusTester:
    """
    🧪 تست‌کننده وضعیت اکستنشن
    """
    
    def __init__(self):
        self.extension_dir = Path("chrome_extension")
        self.temp_dir = Path("temp_extension")
    
    def check_chrome_running(self):
        """بررسی اجرای Chrome"""
        try:
            result = subprocess.run(["tasklist", "/fi", "imagename eq chrome.exe"], 
                                  capture_output=True, text=True)
            return "chrome.exe" in result.stdout
        except:
            return False
    
    def check_extension_files(self):
        """بررسی فایل‌های اکستنشن"""
        required_files = [
            "manifest.json",
            "background.js",
            "content.js",
            "popup.html",
            "popup.js"
        ]
        
        missing_files = []
        for file in required_files:
            if not (self.extension_dir / file).exists():
                missing_files.append(file)
        
        return len(missing_files) == 0, missing_files
    
    def check_temp_extension(self):
        """بررسی اکستنشن موقت"""
        return self.temp_dir.exists() and (self.temp_dir / "manifest.json").exists()
    
    def check_startup_script(self):
        """بررسی اسکریپت راه‌اندازی"""
        return os.path.exists("start_chrome_with_extension.bat")
    
    def get_extension_id_from_manifest(self):
        """دریافت ID اکستنشن از manifest"""
        try:
            manifest_path = self.extension_dir / "manifest.json"
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            return manifest.get("name", "VIP BIG BANG")
        except:
            return "Unknown"
    
    def run_comprehensive_test(self):
        """اجرای تست کامل"""
        print("="*60)
        print("🧪 VIP BIG BANG Extension Status Test")
        print("="*60)
        
        results = {}
        
        # Test 1: Chrome Running
        chrome_running = self.check_chrome_running()
        results["chrome_running"] = chrome_running
        print(f"Chrome Running: {'✅ YES' if chrome_running else '❌ NO'}")
        
        # Test 2: Extension Files
        files_ok, missing = self.check_extension_files()
        results["extension_files"] = files_ok
        print(f"Extension Files: {'✅ COMPLETE' if files_ok else '❌ MISSING: ' + str(missing)}")
        
        # Test 3: Temp Extension
        temp_ok = self.check_temp_extension()
        results["temp_extension"] = temp_ok
        print(f"Temp Extension: {'✅ CREATED' if temp_ok else '❌ NOT FOUND'}")
        
        # Test 4: Startup Script
        script_ok = self.check_startup_script()
        results["startup_script"] = script_ok
        print(f"Startup Script: {'✅ CREATED' if script_ok else '❌ NOT FOUND'}")
        
        # Test 5: Extension Name
        ext_name = self.get_extension_id_from_manifest()
        results["extension_name"] = ext_name
        print(f"Extension Name: {ext_name}")
        
        # Overall Status
        passed_tests = sum([chrome_running, files_ok, temp_ok, script_ok])
        total_tests = 4
        
        print("\n" + "="*60)
        print(f"📊 Test Results: {passed_tests}/{total_tests} passed")
        
        if passed_tests == total_tests:
            print("🎯 Status: ✅ EXTENSION READY")
            print("\n🚀 Next Steps:")
            print("1. Go to chrome://extensions/")
            print("2. Enable 'Developer mode'")
            print("3. Check if VIP BIG BANG is loaded")
            print("4. Test on Quotex page")
            
        elif passed_tests >= 2:
            print("⚠️ Status: 🟡 PARTIALLY READY")
            print("\n🔧 Fix Issues:")
            if not chrome_running:
                print("• Run: start_chrome_with_extension.bat")
            if not temp_ok:
                print("• Run: python auto_install_extension.py")
            
        else:
            print("❌ Status: 🔴 NOT READY")
            print("\n🔧 Required Actions:")
            print("1. Run: python auto_install_extension.py")
            print("2. Run: start_chrome_with_extension.bat")
            print("3. Manual installation if needed")
        
        print("="*60)
        
        return results
    
    def create_quick_fix_script(self):
        """ایجاد اسکریپت تعمیر سریع"""
        fix_script = '''@echo off
echo 🔧 VIP BIG BANG Extension Quick Fix
echo.

echo Step 1: Installing extension automatically...
python auto_install_extension.py

echo.
echo Step 2: Starting Chrome with extension...
start_chrome_with_extension.bat

echo.
echo Step 3: Manual verification...
echo 1. Go to chrome://extensions/
echo 2. Enable "Developer mode"
echo 3. Check VIP BIG BANG extension
echo 4. Go to https://qxbroker.com/en/trade
echo 5. Test the extension

echo.
echo ✅ Quick fix completed!
pause
'''
        
        with open("quick_fix_extension.bat", "w", encoding="utf-8") as f:
            f.write(fix_script)
        
        print("✅ Quick fix script created: quick_fix_extension.bat")

def main():
    """اجرای تست"""
    tester = ExtensionStatusTester()
    results = tester.run_comprehensive_test()
    
    # Create quick fix if needed
    passed = sum([
        results.get("chrome_running", False),
        results.get("extension_files", False), 
        results.get("temp_extension", False),
        results.get("startup_script", False)
    ])
    
    if passed < 4:
        print("\n🔧 Creating quick fix script...")
        tester.create_quick_fix_script()

if __name__ == "__main__":
    main()
