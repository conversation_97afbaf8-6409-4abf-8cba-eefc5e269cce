"""
VIP BIG BANG Enterprise - AutoTrade System
Ultra-fast automated trading with 5-second execution
"""

import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import logging
from collections import deque

class AutoTrader:
    """
    Enterprise-level automated trading system
    Executes trades based on confirmed signals with risk management
    """
    
    def __init__(self, quotex_client, signal_manager):
        self.quotex_client = quotex_client
        self.signal_manager = signal_manager
        self.logger = logging.getLogger("AutoTrader")
        
        # Trading state
        self.enabled = False
        self.running = False
        self.paused = False
        
        # Trading statistics
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'total_loss': 0.0,
            'win_rate': 0.0,
            'current_streak': 0,
            'max_winning_streak': 0,
            'max_losing_streak': 0
        }
        
        # Risk management
        self.daily_trades = 0
        self.daily_loss = 0.0
        self.consecutive_losses = 0
        self.last_trade_time = None
        
        # Trade tracking
        self.pending_trades = deque(maxlen=100)
        self.completed_trades = deque(maxlen=1000)
        
        # Threading
        self.trade_thread = None
        self.monitor_thread = None
        
        self.logger.info("AutoTrader initialized")
    
    def set_signal_manager(self, signal_manager):
        """Set signal manager reference"""
        self.signal_manager = signal_manager
    
    def start(self):
        """Start the automated trading system"""
        if self.running:
            self.logger.warning("AutoTrader already running")
            return
        
        self.logger.info("Starting AutoTrader...")
        self.running = True
        self.enabled = True
        
        # Start trading thread
        self.trade_thread = threading.Thread(
            target=self._trading_loop,
            daemon=True,
            name="AutoTradeThread"
        )
        self.trade_thread.start()
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="TradeMonitorThread"
        )
        self.monitor_thread.start()
        
        self.logger.info("AutoTrader started successfully")
    
    def stop(self):
        """Stop the automated trading system"""
        self.logger.info("Stopping AutoTrader...")
        self.running = False
        self.enabled = False
        
        # Wait for threads to finish
        if self.trade_thread and self.trade_thread.is_alive():
            self.trade_thread.join(timeout=5)
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("AutoTrader stopped")
    
    def pause(self):
        """Pause trading (can be resumed)"""
        self.paused = True
        self.logger.info("AutoTrader paused")
    
    def resume(self):
        """Resume trading"""
        self.paused = False
        self.logger.info("AutoTrader resumed")
    
    def _trading_loop(self):
        """Main trading loop"""
        self.logger.info("Trading loop started")
        
        while self.running:
            try:
                if not self.enabled or self.paused:
                    time.sleep(1)
                    continue
                
                # Check risk management constraints
                if not self._check_risk_constraints():
                    time.sleep(5)
                    continue
                
                # Get latest confirmed signal
                signal = self.signal_manager.get_latest_confirmed_signal()
                
                if signal and self._should_trade_signal(signal):
                    # Execute trade
                    asyncio.run(self._execute_trade(signal))
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Trading loop error: {e}")
                time.sleep(1)
        
        self.logger.info("Trading loop stopped")
    
    def _monitoring_loop(self):
        """Monitor active trades and update results"""
        self.logger.info("Trade monitoring loop started")
        
        while self.running:
            try:
                # Monitor pending trades
                self._monitor_pending_trades()
                
                # Update daily statistics
                self._update_daily_stats()
                
                # Sleep for 1 second
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(5)
        
        self.logger.info("Trade monitoring loop stopped")
    
    def _check_risk_constraints(self) -> bool:
        """Check if trading is allowed based on risk management rules"""
        settings = self.quotex_client.settings
        
        # Check daily trade limit
        if self.daily_trades >= settings.trading.max_daily_trades:
            self.logger.warning("Daily trade limit reached")
            return False
        
        # Check hourly trade limit
        current_hour_trades = self._count_recent_trades(3600)  # Last hour
        if current_hour_trades >= settings.trading.max_trades_per_hour:
            self.logger.warning("Hourly trade limit reached")
            return False
        
        # Check daily loss limit
        if self.daily_loss >= settings.risk_management.daily_loss_limit:
            self.logger.warning("Daily loss limit reached")
            return False
        
        # Check consecutive losses
        if self.consecutive_losses >= settings.risk_management.max_consecutive_losses:
            self.logger.warning("Max consecutive losses reached")
            return False
        
        # Check minimum time between trades (prevent overtrading)
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < 10:  # Minimum 10 seconds between trades
                return False
        
        # Check account balance
        balance = self.quotex_client.get_balance()
        min_balance = settings.risk_management.min_trade_amount * 10  # Keep 10x min trade amount
        if balance < min_balance:
            self.logger.warning("Insufficient balance for trading")
            return False
        
        return True
    
    def _should_trade_signal(self, signal: Dict) -> bool:
        """Determine if a signal should be traded"""
        if not signal or not signal.get('valid', False):
            return False
        
        # Check if signal is confirmed
        if not signal.get('confirmed', False):
            return False
        
        # Check signal strength
        settings = self.quotex_client.settings
        if signal.get('confidence', 0) < settings.trading.min_signal_strength:
            return False
        
        # Check signal direction
        if signal.get('direction') not in ['CALL', 'PUT']:
            return False
        
        # Check if we've already traded this signal
        signal_id = signal.get('id')
        if signal_id and self._has_traded_signal(signal_id):
            return False
        
        return True
    
    def _has_traded_signal(self, signal_id: str) -> bool:
        """Check if we've already traded this signal"""
        for trade in self.completed_trades:
            if trade.get('signal_id') == signal_id:
                return True
        
        for trade in self.pending_trades:
            if trade.get('signal_id') == signal_id:
                return True
        
        return False
    
    def _count_recent_trades(self, seconds: int) -> int:
        """Count trades in the last N seconds"""
        cutoff_time = datetime.now() - timedelta(seconds=seconds)
        count = 0
        
        for trade in self.completed_trades:
            if trade.get('timestamp', datetime.min) > cutoff_time:
                count += 1
        
        return count
    
    async def _execute_trade(self, signal: Dict):
        """Execute a trade based on signal"""
        try:
            settings = self.quotex_client.settings
            
            # Calculate trade amount
            trade_amount = self._calculate_trade_amount()
            
            # Prepare trade parameters
            asset = "EUR/USD"  # Default asset, could be dynamic
            direction = signal['direction']
            duration = settings.trading.trade_duration
            
            self.logger.info(f"Executing trade: {direction} {asset} ${trade_amount} for {duration}s")
            
            # Place the trade
            result = await self.quotex_client.place_trade(
                asset=asset,
                direction=direction,
                amount=trade_amount,
                duration=duration
            )
            
            if result.get('success', False):
                # Create trade record
                trade_record = {
                    'id': result['trade_id'],
                    'signal_id': signal.get('id'),
                    'asset': asset,
                    'direction': direction,
                    'amount': trade_amount,
                    'duration': duration,
                    'entry_price': self.quotex_client.get_current_price(asset),
                    'timestamp': datetime.now(),
                    'signal_data': signal,
                    'status': 'PENDING',
                    'expected_close_time': datetime.now() + timedelta(seconds=duration)
                }
                
                # Add to pending trades
                self.pending_trades.append(trade_record)
                
                # Update statistics
                self.stats['total_trades'] += 1
                self.daily_trades += 1
                self.last_trade_time = datetime.now()
                
                self.logger.info(f"Trade executed successfully: {result['trade_id']}")
                
            else:
                self.logger.error(f"Trade execution failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"Trade execution error: {e}")
    
    def _calculate_trade_amount(self) -> float:
        """Calculate optimal trade amount based on risk management"""
        settings = self.quotex_client.settings
        balance = self.quotex_client.get_balance()
        
        # Base amount calculation
        risk_percentage = settings.risk_management.risk_per_trade / 100
        calculated_amount = balance * risk_percentage
        
        # Apply constraints
        min_amount = settings.risk_management.min_trade_amount
        max_amount = settings.risk_management.max_trade_amount
        
        trade_amount = max(min_amount, min(max_amount, calculated_amount))
        
        # Adjust based on recent performance
        if self.consecutive_losses > 2:
            trade_amount *= 0.5  # Reduce amount after losses
        elif self.stats['win_rate'] > 0.8 and self.stats['total_trades'] > 10:
            trade_amount *= 1.2  # Increase amount when performing well
        
        return round(trade_amount, 2)
    
    def _monitor_pending_trades(self):
        """Monitor pending trades and update their status"""
        current_time = datetime.now()
        completed_trades = []
        
        for trade in list(self.pending_trades):
            # Check if trade should be completed
            if current_time >= trade['expected_close_time']:
                # Determine trade result
                result = self._determine_trade_result(trade)
                trade['status'] = 'COMPLETED'
                trade['result'] = result
                trade['close_time'] = current_time
                trade['close_price'] = self.quotex_client.get_current_price(trade['asset'])
                
                # Calculate profit/loss
                if result == 'WIN':
                    profit = trade['amount'] * 0.8  # 80% payout
                    trade['profit'] = profit
                    self.stats['winning_trades'] += 1
                    self.stats['total_profit'] += profit
                    self.consecutive_losses = 0
                    self.stats['current_streak'] = max(0, self.stats['current_streak']) + 1
                    self.stats['max_winning_streak'] = max(self.stats['max_winning_streak'], self.stats['current_streak'])
                else:
                    loss = trade['amount']
                    trade['profit'] = -loss
                    self.stats['losing_trades'] += 1
                    self.stats['total_loss'] += loss
                    self.daily_loss += loss
                    self.consecutive_losses += 1
                    self.stats['current_streak'] = min(0, self.stats['current_streak']) - 1
                    self.stats['max_losing_streak'] = max(self.stats['max_losing_streak'], abs(self.stats['current_streak']))
                
                # Update win rate
                total_completed = self.stats['winning_trades'] + self.stats['losing_trades']
                if total_completed > 0:
                    self.stats['win_rate'] = self.stats['winning_trades'] / total_completed
                
                # Update signal manager with result
                if self.signal_manager and trade.get('signal_id'):
                    self.signal_manager.update_signal_result(
                        trade['signal_id'], 
                        result == 'WIN'
                    )
                
                completed_trades.append(trade)
                self.logger.info(f"Trade completed: {trade['id']} - {result} - Profit: ${trade['profit']:.2f}")
        
        # Move completed trades
        for trade in completed_trades:
            self.pending_trades.remove(trade)
            self.completed_trades.append(trade)
    
    def _determine_trade_result(self, trade: Dict) -> str:
        """Determine if a trade won or lost"""
        entry_price = trade['entry_price']
        current_price = self.quotex_client.get_current_price(trade['asset'])
        direction = trade['direction']
        
        if current_price is None:
            return 'LOSS'  # Default to loss if price unavailable
        
        if direction == 'CALL':
            return 'WIN' if current_price > entry_price else 'LOSS'
        else:  # PUT
            return 'WIN' if current_price < entry_price else 'LOSS'
    
    def _update_daily_stats(self):
        """Update daily statistics"""
        # Reset daily counters at midnight
        now = datetime.now()
        if hasattr(self, '_last_reset_date'):
            if now.date() != self._last_reset_date:
                self.daily_trades = 0
                self.daily_loss = 0.0
                self._last_reset_date = now.date()
        else:
            self._last_reset_date = now.date()
    
    def process_signals(self, signals: Dict):
        """Process incoming signals (called by main loop)"""
        # This method is called by the main analysis loop
        # The actual trading decisions are made in the trading loop
        pass
    
    def get_statistics(self) -> Dict:
        """Get trading statistics"""
        stats = self.stats.copy()
        stats.update({
            'enabled': self.enabled,
            'running': self.running,
            'paused': self.paused,
            'daily_trades': self.daily_trades,
            'daily_loss': self.daily_loss,
            'consecutive_losses': self.consecutive_losses,
            'pending_trades': len(self.pending_trades),
            'balance': self.quotex_client.get_balance()
        })
        return stats
