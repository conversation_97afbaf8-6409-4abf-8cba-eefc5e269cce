"""
🖥️ ULTIMATE SYSTEM DETECTOR
🔍 COMPLETE SYSTEM DETECTION LIKE GAMES
💻 CPU, RAM, GPU, SCREEN, OS, USER - EVERYTHING
"""

import os
import sys
import platform
import psutil
import getpass
import socket
import logging
from datetime import datetime
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QObject, Signal

class UltimateSystemDetector(QObject):
    """
    🖥️ ULTIMATE SYSTEM DETECTOR
    🔍 Detects everything about the system like games do
    """
    
    # Signals
    system_detected = Signal(dict)
    performance_updated = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("UltimateSystemDetector")
        
        # System information
        self.system_info = {}
        self.performance_info = {}
        self.screen_info = {}
        self.user_info = {}
        
        # Performance monitoring timer
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self.update_performance)
        
        # Detect everything
        self.detect_complete_system()
        
        self.logger.info("🖥️ Ultimate System Detector initialized")
    
    def detect_complete_system(self):
        """🔍 Detect complete system information"""
        try:
            # Detect all components
            self.detect_hardware()
            self.detect_operating_system()
            self.detect_user_info()
            self.detect_screen_info()
            self.detect_network_info()
            self.detect_performance()
            
            # Combine all information
            complete_info = {
                "hardware": self.system_info,
                "performance": self.performance_info,
                "screen": self.screen_info,
                "user": self.user_info,
                "detection_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Emit signal
            self.system_detected.emit(complete_info)
            
            # Log summary
            self.log_system_summary()
            
        except Exception as e:
            self.logger.error(f"❌ Complete system detection error: {e}")
    
    def detect_hardware(self):
        """💻 Detect hardware information"""
        try:
            # CPU Information
            cpu_info = {
                "name": platform.processor(),
                "architecture": platform.machine(),
                "cores_physical": psutil.cpu_count(logical=False),
                "cores_logical": psutil.cpu_count(logical=True),
                "frequency_max": psutil.cpu_freq().max if psutil.cpu_freq() else "Unknown",
                "frequency_current": psutil.cpu_freq().current if psutil.cpu_freq() else "Unknown"
            }
            
            # RAM Information
            memory = psutil.virtual_memory()
            ram_info = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "percentage": memory.percent
            }
            
            # Disk Information
            disk_info = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info.append({
                        "device": partition.device,
                        "mountpoint": partition.mountpoint,
                        "filesystem": partition.fstype,
                        "total_gb": round(usage.total / (1024**3), 2),
                        "used_gb": round(usage.used / (1024**3), 2),
                        "free_gb": round(usage.free / (1024**3), 2),
                        "percentage": round((usage.used / usage.total) * 100, 1)
                    })
                except:
                    continue
            
            # GPU Information (basic)
            gpu_info = {
                "detected": False,
                "name": "Unknown",
                "memory": "Unknown"
            }
            
            # Try to detect GPU
            try:
                import subprocess
                result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # Skip header
                        if line.strip() and 'Microsoft' not in line:
                            gpu_info["detected"] = True
                            gpu_info["name"] = line.strip()
                            break
            except:
                pass
            
            self.system_info = {
                "cpu": cpu_info,
                "ram": ram_info,
                "disk": disk_info,
                "gpu": gpu_info
            }
            
        except Exception as e:
            self.logger.error(f"❌ Hardware detection error: {e}")
    
    def detect_operating_system(self):
        """🖥️ Detect operating system information"""
        try:
            os_info = {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "python_implementation": platform.python_implementation(),
                "boot_time": datetime.fromtimestamp(psutil.boot_time()).strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.system_info["os"] = os_info
            
        except Exception as e:
            self.logger.error(f"❌ OS detection error: {e}")
    
    def detect_user_info(self):
        """👤 Detect user information"""
        try:
            self.user_info = {
                "username": getpass.getuser(),
                "home_directory": os.path.expanduser("~"),
                "current_directory": os.getcwd(),
                "environment_variables": len(os.environ),
                "is_admin": self.check_admin_privileges()
            }
            
        except Exception as e:
            self.logger.error(f"❌ User info detection error: {e}")
    
    def detect_screen_info(self):
        """📺 Detect screen information"""
        try:
            app = QApplication.instance()
            if app:
                primary_screen = app.primaryScreen()
                all_screens = app.screens()
                
                # Primary screen info
                screen_geometry = primary_screen.geometry()
                available_geometry = primary_screen.availableGeometry()
                
                primary_info = {
                    "width": screen_geometry.width(),
                    "height": screen_geometry.height(),
                    "available_width": available_geometry.width(),
                    "available_height": available_geometry.height(),
                    "dpi": primary_screen.logicalDotsPerInch(),
                    "physical_dpi": primary_screen.physicalDotsPerInch(),
                    "device_pixel_ratio": primary_screen.devicePixelRatio(),
                    "refresh_rate": primary_screen.refreshRate(),
                    "name": primary_screen.name()
                }
                
                # All screens info
                all_screens_info = []
                for i, screen in enumerate(all_screens):
                    geometry = screen.geometry()
                    all_screens_info.append({
                        "index": i,
                        "name": screen.name(),
                        "width": geometry.width(),
                        "height": geometry.height(),
                        "dpi": screen.logicalDotsPerInch(),
                        "is_primary": screen == primary_screen
                    })
                
                self.screen_info = {
                    "primary": primary_info,
                    "all_screens": all_screens_info,
                    "screen_count": len(all_screens),
                    "total_width": sum(s["width"] for s in all_screens_info),
                    "total_height": max(s["height"] for s in all_screens_info)
                }
                
                # Calculate optimal window size
                self.calculate_optimal_window_size()
                
        except Exception as e:
            self.logger.error(f"❌ Screen detection error: {e}")
    
    def detect_network_info(self):
        """🌐 Detect network information"""
        try:
            network_info = {
                "hostname": socket.gethostname(),
                "ip_address": socket.gethostbyname(socket.gethostname()),
                "network_interfaces": []
            }
            
            # Network interfaces
            for interface, addresses in psutil.net_if_addrs().items():
                for addr in addresses:
                    if addr.family == socket.AF_INET:  # IPv4
                        network_info["network_interfaces"].append({
                            "interface": interface,
                            "ip": addr.address,
                            "netmask": addr.netmask
                        })
            
            self.system_info["network"] = network_info
            
        except Exception as e:
            self.logger.error(f"❌ Network detection error: {e}")
    
    def detect_performance(self):
        """⚡ Detect current performance"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_per_core = psutil.cpu_percent(interval=1, percpu=True)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk_io = psutil.disk_io_counters()
            
            # Network usage
            network_io = psutil.net_io_counters()
            
            self.performance_info = {
                "cpu_percent": cpu_percent,
                "cpu_per_core": cpu_per_core,
                "memory_percent": memory.percent,
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "disk_read_mb": round(disk_io.read_bytes / (1024**2), 2) if disk_io else 0,
                "disk_write_mb": round(disk_io.write_bytes / (1024**2), 2) if disk_io else 0,
                "network_sent_mb": round(network_io.bytes_sent / (1024**2), 2) if network_io else 0,
                "network_recv_mb": round(network_io.bytes_recv / (1024**2), 2) if network_io else 0,
                "processes_count": len(psutil.pids()),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
        except Exception as e:
            self.logger.error(f"❌ Performance detection error: {e}")
    
    def calculate_optimal_window_size(self):
        """📐 Calculate optimal window size based on screen"""
        try:
            if "primary" in self.screen_info:
                primary = self.screen_info["primary"]
                
                # Base calculation on available space
                available_width = primary["available_width"]
                available_height = primary["available_height"]
                
                # Calculate optimal size based on screen resolution
                if available_width >= 3840:  # 4K
                    scale_factor = 0.7
                elif available_width >= 2560:  # 2K
                    scale_factor = 0.75
                elif available_width >= 1920:  # Full HD
                    scale_factor = 0.8
                else:  # HD or lower
                    scale_factor = 0.85
                
                # Adjust for high DPI
                if primary["dpi"] > 120:
                    scale_factor *= 1.1
                
                optimal_width = int(available_width * scale_factor)
                optimal_height = int(available_height * scale_factor)
                
                # Ensure reasonable limits
                optimal_width = max(1000, min(optimal_width, 1800))
                optimal_height = max(700, min(optimal_height, 1200))
                
                self.screen_info["optimal"] = {
                    "width": optimal_width,
                    "height": optimal_height,
                    "scale_factor": scale_factor,
                    "x": (available_width - optimal_width) // 2,
                    "y": (available_height - optimal_height) // 2
                }
                
        except Exception as e:
            self.logger.error(f"❌ Optimal size calculation error: {e}")
    
    def check_admin_privileges(self):
        """🔐 Check if running with admin privileges"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:  # Unix/Linux
                return os.geteuid() == 0
        except:
            return False
    
    def update_performance(self):
        """🔄 Update performance information"""
        try:
            self.detect_performance()
            self.performance_updated.emit(self.performance_info)
        except Exception as e:
            self.logger.error(f"❌ Performance update error: {e}")
    
    def start_performance_monitoring(self, interval_ms=5000):
        """🔄 Start performance monitoring"""
        self.performance_timer.start(interval_ms)
        self.logger.info(f"🔄 Performance monitoring started (interval: {interval_ms}ms)")
    
    def stop_performance_monitoring(self):
        """🛑 Stop performance monitoring"""
        self.performance_timer.stop()
        self.logger.info("🛑 Performance monitoring stopped")
    
    def log_system_summary(self):
        """📊 Log system summary"""
        try:
            self.logger.info("🖥️ === COMPLETE SYSTEM DETECTION SUMMARY ===")
            
            # Hardware
            if "cpu" in self.system_info:
                cpu = self.system_info["cpu"]
                self.logger.info(f"💻 CPU: {cpu['name']} ({cpu['cores_physical']}C/{cpu['cores_logical']}T)")
            
            if "ram" in self.system_info:
                ram = self.system_info["ram"]
                self.logger.info(f"🧠 RAM: {ram['total_gb']}GB total, {ram['available_gb']}GB available")
            
            if "gpu" in self.system_info:
                gpu = self.system_info["gpu"]
                if gpu["detected"]:
                    self.logger.info(f"🎮 GPU: {gpu['name']}")
                else:
                    self.logger.info("🎮 GPU: Not detected")
            
            # OS
            if "os" in self.system_info:
                os_info = self.system_info["os"]
                self.logger.info(f"🖥️ OS: {os_info['system']} {os_info['release']}")
            
            # User
            self.logger.info(f"👤 User: {self.user_info.get('username', 'Unknown')}")
            self.logger.info(f"🔐 Admin: {'Yes' if self.user_info.get('is_admin', False) else 'No'}")
            
            # Screen
            if "primary" in self.screen_info:
                primary = self.screen_info["primary"]
                self.logger.info(f"📺 Screen: {primary['width']}x{primary['height']} @ {primary['dpi']:.0f}DPI")
                
                if "optimal" in self.screen_info:
                    optimal = self.screen_info["optimal"]
                    self.logger.info(f"🎯 Optimal Window: {optimal['width']}x{optimal['height']}")
            
            # Performance
            if self.performance_info:
                perf = self.performance_info
                self.logger.info(f"⚡ CPU: {perf.get('cpu_percent', 0):.1f}% | RAM: {perf.get('memory_percent', 0):.1f}%")
            
            self.logger.info("🏆 === SYSTEM DETECTION COMPLETE ===")
            
        except Exception as e:
            self.logger.error(f"❌ Log system summary error: {e}")
    
    def get_complete_info(self):
        """📊 Get complete system information"""
        return {
            "hardware": self.system_info,
            "performance": self.performance_info,
            "screen": self.screen_info,
            "user": self.user_info,
            "detection_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def get_optimal_window_config(self):
        """🎯 Get optimal window configuration"""
        if "optimal" in self.screen_info:
            return self.screen_info["optimal"]
        return None

class SystemInfoWidget:
    """
    📊 SYSTEM INFO WIDGET
    🖥️ Displays complete system information like games
    """

    def __init__(self, system_detector):
        self.detector = system_detector
        self.logger = logging.getLogger("SystemInfoWidget")

    def create_system_info_display(self, layout):
        """📊 Create system information display"""
        try:
            from PySide6.QtWidgets import QGroupBox, QVBoxLayout, QHBoxLayout, QLabel

            # System Info Group
            system_group = QGroupBox("🖥️ Complete System Information")
            system_group.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #4CAF50;
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    color: white;
                    background: rgba(76, 175, 80, 0.1);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
            """)
            system_layout = QVBoxLayout(system_group)

            # Get complete system info
            complete_info = self.detector.get_complete_info()

            # Hardware Info
            if "hardware" in complete_info:
                hardware = complete_info["hardware"]

                # CPU Info
                if "cpu" in hardware:
                    cpu = hardware["cpu"]
                    cpu_label = QLabel(f"💻 CPU: {cpu.get('name', 'Unknown')} ({cpu.get('cores_physical', 0)}C/{cpu.get('cores_logical', 0)}T)")
                    cpu_label.setStyleSheet("color: white; font-weight: bold; padding: 3px; font-size: 11px;")
                    system_layout.addWidget(cpu_label)

                # RAM Info
                if "ram" in hardware:
                    ram = hardware["ram"]
                    ram_label = QLabel(f"🧠 RAM: {ram.get('total_gb', 0)}GB Total | {ram.get('available_gb', 0)}GB Available | {ram.get('percentage', 0):.1f}% Used")
                    ram_label.setStyleSheet("color: #81C784; font-weight: bold; padding: 3px; font-size: 11px;")
                    system_layout.addWidget(ram_label)

                # GPU Info
                if "gpu" in hardware:
                    gpu = hardware["gpu"]
                    if gpu.get("detected", False):
                        gpu_label = QLabel(f"🎮 GPU: {gpu.get('name', 'Unknown')}")
                    else:
                        gpu_label = QLabel("🎮 GPU: Not Detected")
                    gpu_label.setStyleSheet("color: #FFB74D; font-weight: bold; padding: 3px; font-size: 11px;")
                    system_layout.addWidget(gpu_label)

            # OS Info
            if "os" in complete_info["hardware"]:
                os_info = complete_info["hardware"]["os"]
                os_label = QLabel(f"🖥️ OS: {os_info.get('system', 'Unknown')} {os_info.get('release', '')}")
                os_label.setStyleSheet("color: #64B5F6; font-weight: bold; padding: 3px; font-size: 11px;")
                system_layout.addWidget(os_label)

            # User Info
            if "user" in complete_info:
                user = complete_info["user"]
                admin_status = "Admin" if user.get("is_admin", False) else "User"
                user_label = QLabel(f"👤 User: {user.get('username', 'Unknown')} ({admin_status})")
                user_label.setStyleSheet("color: #F06292; font-weight: bold; padding: 3px; font-size: 11px;")
                system_layout.addWidget(user_label)

            # Screen Info
            if "screen" in complete_info and "primary" in complete_info["screen"]:
                screen = complete_info["screen"]["primary"]
                screen_label = QLabel(f"📺 Display: {screen.get('width', 0)}x{screen.get('height', 0)} @ {screen.get('dpi', 0):.0f}DPI | {screen.get('refresh_rate', 0):.0f}Hz")
                screen_label.setStyleSheet("color: #BA68C8; font-weight: bold; padding: 3px; font-size: 11px;")
                system_layout.addWidget(screen_label)

                # Optimal window info
                if "optimal" in complete_info["screen"]:
                    optimal = complete_info["screen"]["optimal"]
                    optimal_label = QLabel(f"🎯 Optimal Window: {optimal.get('width', 0)}x{optimal.get('height', 0)} (Scale: {optimal.get('scale_factor', 1.0):.2f})")
                    optimal_label.setStyleSheet("color: #4CAF50; font-weight: bold; padding: 3px; font-size: 11px;")
                    system_layout.addWidget(optimal_label)

            # Performance Info
            if "performance" in complete_info:
                perf = complete_info["performance"]
                perf_label = QLabel(f"⚡ Performance: CPU {perf.get('cpu_percent', 0):.1f}% | RAM {perf.get('memory_percent', 0):.1f}% | Processes: {perf.get('processes_count', 0)}")
                perf_label.setStyleSheet("color: #FFF176; font-weight: bold; padding: 3px; font-size: 11px;")
                system_layout.addWidget(perf_label)

            # Detection time
            detection_time = complete_info.get("detection_time", "Unknown")
            time_label = QLabel(f"🕐 Detected: {detection_time}")
            time_label.setStyleSheet("color: #90A4AE; font-weight: bold; padding: 3px; font-size: 10px;")
            system_layout.addWidget(time_label)

            layout.addWidget(system_group)

            self.logger.info("📊 System info display created")

        except Exception as e:
            self.logger.error(f"❌ Create system info display error: {e}")

    def update_performance_display(self, performance_info):
        """🔄 Update performance display"""
        # This would update the performance labels in real-time
        pass
