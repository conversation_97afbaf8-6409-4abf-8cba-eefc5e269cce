// VIP BIG BANG Content Script
// Runs immediately when page loads

console.log('🚀 VIP BIG BANG Extension Loading...');

// Inject the main script
const script = document.createElement('script');
script.src = chrome.runtime.getURL('injected.js');
script.onload = function() {
    this.remove();
};
(document.head || document.documentElement).appendChild(script);

// Communication with background script
let bridgePort = null;
let isConnected = false;

// Connect to background script
function connectToBridge() {
    try {
        bridgePort = chrome.runtime.connect({ name: 'quotex-bridge' });
        
        bridgePort.onMessage.addListener((message) => {
            if (message.type === 'ping') {
                bridgePort.postMessage({ type: 'pong', data: 'Extension active' });
            }
        });
        
        bridgePort.onDisconnect.addListener(() => {
            isConnected = false;
            setTimeout(connectToBridge, 1000);
        });
        
        isConnected = true;
        console.log('✅ VIP BIG BANG Bridge connected');
        
    } catch (error) {
        console.log('⚠️ Bridge connection failed, retrying...');
        setTimeout(connectToBridge, 2000);
    }
}

// Start bridge connection
connectToBridge();

// Data extraction and monitoring
let lastDataSent = 0;
let extractionInterval = null;

function startDataExtraction() {
    if (extractionInterval) return;
    
    extractionInterval = setInterval(() => {
        try {
            // Get data from injected script
            const data = window.VIP_BIG_BANG_DATA;
            
            if (data && Date.now() - lastDataSent > 500) {
                // Send to background script
                if (isConnected && bridgePort) {
                    bridgePort.postMessage({
                        type: 'quotex-data',
                        data: data
                    });
                    lastDataSent = Date.now();
                }
                
                // Send to local server (if available)
                sendToLocalServer(data);
            }
            
        } catch (error) {
            // Silent error handling
        }
    }, 1000);
}

// Send data to local server
function sendToLocalServer(data) {
    try {
        fetch('http://localhost:8888/data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        }).catch(() => {
            // Silent error - server might not be running
        });
    } catch (error) {
        // Silent error
    }
}

// Start data extraction after page load
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(startDataExtraction, 2000);
    });
} else {
    setTimeout(startDataExtraction, 2000);
}

// Listen for page navigation
let currentUrl = window.location.href;
setInterval(() => {
    if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        // Restart data extraction on navigation
        if (extractionInterval) {
            clearInterval(extractionInterval);
            extractionInterval = null;
        }
        setTimeout(startDataExtraction, 3000);
    }
}, 1000);

console.log('✅ VIP BIG BANG Content Script Active');
