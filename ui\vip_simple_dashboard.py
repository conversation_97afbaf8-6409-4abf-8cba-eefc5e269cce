"""
🎯 VIP BIG BANG - Simple Dashboard UI
Clean and professional interface matching the provided design
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Add paths
sys.path.append(str(Path(__file__).parent.parent))

# Import extension components
from ui.extension_data_manager import ExtensionDataManager
from ui.components.extension_data_widget import ExtensionDataWidget

class VIPSimpleDashboard(QMainWindow):
    """
    🎯 Simple VIP BIG BANG Dashboard
    Clean interface matching the provided design
    """
    
    def __init__(self):
        super().__init__()
        
        # Window setup
        self.setWindowTitle("🚀 VIP BIG BANG - Professional Trading Dashboard")
        self.setGeometry(100, 100, 1400, 800)
        
        # Extension data manager
        self.extension_manager = None
        
        # Setup UI
        self._setup_ui()
        self._apply_styles()
        
        # Setup extension integration
        self._setup_extension_integration()
        
    def _setup_ui(self):
        """Setup main UI layout"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Header
        header = self._create_header()
        main_layout.addWidget(header)
        
        # Main content area
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)
        
        # Left panel - Analysis boxes
        left_panel = self._create_left_panel()
        content_layout.addWidget(left_panel, 1)
        
        # Center panel - Chart
        center_panel = self._create_center_panel()
        content_layout.addWidget(center_panel, 3)
        
        # Right panel - Trading controls
        right_panel = self._create_right_panel()
        content_layout.addWidget(right_panel, 1)
        
        main_layout.addLayout(content_layout)
        
        # Bottom panel - Analysis indicators
        bottom_panel = self._create_bottom_panel()
        main_layout.addWidget(bottom_panel)
        
    def _create_header(self):
        """Create header with title and status"""
        header = QFrame()
        header.setFixedHeight(60)
        header.setObjectName("header")
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # Logo and title
        title_layout = QHBoxLayout()
        
        logo = QLabel("🚀")
        logo.setStyleSheet("font-size: 24px;")
        title_layout.addWidget(logo)
        
        title = QLabel("VIP BIG BANG - Professional Trading Dashboard")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        title_layout.addWidget(title)
        
        layout.addLayout(title_layout)
        layout.addStretch()
        
        # Status indicators
        status_layout = QHBoxLayout()
        
        # Timeframe
        timeframe_label = QLabel("TIMEFRAME")
        timeframe_label.setStyleSheet("color: #888; font-size: 12px;")
        status_layout.addWidget(timeframe_label)
        
        timeframe_value = QLabel("1m")
        timeframe_value.setStyleSheet("color: white; font-weight: bold; margin-right: 20px;")
        status_layout.addWidget(timeframe_value)
        
        # Connection status
        self.connection_status = QLabel("🔴 Disconnected")
        self.connection_status.setStyleSheet("color: #EF4444; font-weight: bold; margin-right: 20px;")
        status_layout.addWidget(self.connection_status)
        
        # Balance
        balance_label = QLabel("💰 $1000.00")
        balance_label.setStyleSheet("color: #10B981; font-weight: bold; font-size: 16px;")
        status_layout.addWidget(balance_label)
        
        layout.addLayout(status_layout)
        
        return header
        
    def _create_left_panel(self):
        """Create left panel with analysis boxes"""
        panel = QFrame()
        panel.setFixedWidth(200)
        panel.setObjectName("leftPanel")
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Analysis boxes (2x4 grid)
        grid_layout = QGridLayout()
        grid_layout.setSpacing(8)
        
        # Analysis indicators
        analyses = [
            ("⚡", "Momentum", "85%", "#8B5CF6"),
            ("🔥", "Heatmap", "Moderate", "#F59E0B"),
            ("🎯", "Button Call", "Active", "#10B981"),
            ("📊", "Live Signals", "BUY", "#10B981"),
            ("✅", "Confirm Mode", "ON", "#8B5CF6"),
            ("📰", "Economic News", "Low", "#6366F1"),
            ("🤝", "Strong Level", "1.07312", "#EF4444"),
            ("📈", "Volume Pulse", "High", "#EC4899")
        ]
        
        for i, (icon, name, value, color) in enumerate(analyses):
            box = self._create_analysis_box(icon, name, value, color)
            grid_layout.addWidget(box, i // 2, i % 2)
            
        layout.addLayout(grid_layout)
        layout.addStretch()
        
        return panel
        
    def _create_analysis_box(self, icon, name, value, color):
        """Create individual analysis box"""
        box = QFrame()
        box.setFixedSize(90, 70)
        box.setObjectName("analysisBox")
        box.setStyleSheet(f"""
            QFrame#analysisBox {{
                background: rgba(30, 30, 30, 0.8);
                border: 1px solid {color};
                border-radius: 8px;
                padding: 5px;
            }}
            QFrame#analysisBox:hover {{
                background: rgba(40, 40, 40, 0.9);
                border: 2px solid {color};
            }}
        """)
        
        layout = QVBoxLayout(box)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)
        
        # Icon
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(icon_label)
        
        # Name
        name_label = QLabel(name)
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setStyleSheet("color: white; font-size: 10px; font-weight: bold;")
        name_label.setWordWrap(True)
        layout.addWidget(name_label)
        
        # Value
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; font-size: 11px; font-weight: bold;")
        layout.addWidget(value_label)
        
        return box
        
    def _create_center_panel(self):
        """Create center panel with chart"""
        panel = QFrame()
        panel.setObjectName("centerPanel")
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Chart area
        chart_area = QFrame()
        chart_area.setMinimumHeight(400)
        chart_area.setObjectName("chartArea")
        chart_area.setStyleSheet("""
            QFrame#chartArea {
                background: #1a1a1a;
                border: 1px solid #333;
                border-radius: 8px;
            }
        """)
        
        # Chart placeholder
        chart_layout = QVBoxLayout(chart_area)
        chart_layout.setAlignment(Qt.AlignCenter)
        
        chart_label = QLabel("📈 TRADING CHART")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #666; font-size: 24px; font-weight: bold;")
        chart_layout.addWidget(chart_label)
        
        price_label = QLabel("EUR/USD: 1.07312")
        price_label.setAlignment(Qt.AlignCenter)
        price_label.setStyleSheet("color: white; font-size: 18px; margin-top: 10px;")
        chart_layout.addWidget(price_label)
        
        layout.addWidget(chart_area)
        
        return panel
        
    def _create_right_panel(self):
        """Create right panel with trading controls"""
        panel = QFrame()
        panel.setFixedWidth(200)
        panel.setObjectName("rightPanel")
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Extension Data Widget
        self.extension_widget = ExtensionDataWidget()
        layout.addWidget(self.extension_widget)
        
        # Trading controls
        controls_group = QGroupBox("Trading Controls")
        controls_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #444;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        controls_layout = QVBoxLayout(controls_group)
        controls_layout.setSpacing(10)
        
        # Volume control
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("Volume:"))
        volume_spin = QSpinBox()
        volume_spin.setRange(1, 1000)
        volume_spin.setValue(10)
        volume_spin.setSuffix(" $")
        volume_layout.addWidget(volume_spin)
        controls_layout.addLayout(volume_layout)
        
        # Trade buttons
        call_btn = QPushButton("📈 CALL")
        call_btn.setStyleSheet("""
            QPushButton {
                background: #10B981;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #059669;
            }
        """)
        controls_layout.addWidget(call_btn)
        
        put_btn = QPushButton("📉 PUT")
        put_btn.setStyleSheet("""
            QPushButton {
                background: #EF4444;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #DC2626;
            }
        """)
        controls_layout.addWidget(put_btn)
        
        layout.addWidget(controls_group)
        
        # AutoTrade Status
        autotrade_group = QGroupBox("AutoTrade Status")
        autotrade_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #444;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        
        autotrade_layout = QVBoxLayout(autotrade_group)
        
        status_label = QLabel("🔴 OFF")
        status_label.setStyleSheet("color: #EF4444; font-weight: bold; font-size: 14px;")
        autotrade_layout.addWidget(status_label)
        
        toggle_btn = QPushButton("Enable AutoTrade")
        toggle_btn.setStyleSheet("""
            QPushButton {
                background: #374151;
                color: white;
                border: 1px solid #6B7280;
                padding: 8px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background: #4B5563;
            }
        """)
        autotrade_layout.addWidget(toggle_btn)
        
        layout.addWidget(autotrade_group)
        layout.addStretch()
        
        return panel
        
    def _create_bottom_panel(self):
        """Create bottom panel with analysis indicators"""
        panel = QFrame()
        panel.setFixedHeight(120)
        panel.setObjectName("bottomPanel")
        
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # Analysis intervals
        intervals = [
            ("MA6", "1.07312", "#8B5CF6"),
            ("Vortex", "VI+ 1.02 | VI- 0.98", "#60A5FA"),
            ("Volume Pulse", "High", "#10B981"),
            ("Multi-OTC Analysis", "Active", "#F59E0B")
        ]
        
        for name, value, color in intervals:
            interval_widget = self._create_interval_widget(name, value, color)
            layout.addWidget(interval_widget)
            
        # Dynamic Timeframe Control
        timeframe_widget = self._create_timeframe_widget()
        layout.addWidget(timeframe_widget)
        
        # System Online indicator
        system_widget = self._create_system_widget()
        layout.addWidget(system_widget)
        
        return panel
        
    def _create_interval_widget(self, name, value, color):
        """Create interval analysis widget"""
        widget = QFrame()
        widget.setFixedSize(180, 80)
        widget.setStyleSheet(f"""
            QFrame {{
                background: rgba(30, 30, 30, 0.8);
                border: 1px solid {color};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        name_label = QLabel(name)
        name_label.setStyleSheet("color: white; font-weight: bold; font-size: 12px;")
        layout.addWidget(name_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 14px;")
        layout.addWidget(value_label)
        
        return widget
        
    def _create_timeframe_widget(self):
        """Create timeframe control widget"""
        widget = QFrame()
        widget.setFixedSize(150, 80)
        widget.setStyleSheet("""
            QFrame {
                background: rgba(30, 30, 30, 0.8);
                border: 1px solid #6366F1;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(5)
        
        title = QLabel("Analysis Interval")
        title.setStyleSheet("color: white; font-weight: bold; font-size: 10px;")
        layout.addWidget(title)
        
        timeframe_combo = QComboBox()
        timeframe_combo.addItems(["1m", "5m", "15m", "1h"])
        timeframe_combo.setCurrentText("1m")
        timeframe_combo.setStyleSheet("""
            QComboBox {
                background: #374151;
                color: white;
                border: 1px solid #6B7280;
                padding: 5px;
                border-radius: 4px;
            }
        """)
        layout.addWidget(timeframe_combo)
        
        return widget
        
    def _create_system_widget(self):
        """Create system status widget"""
        widget = QFrame()
        widget.setFixedSize(120, 80)
        widget.setStyleSheet("""
            QFrame {
                background: rgba(30, 30, 30, 0.8);
                border: 1px solid #10B981;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        layout.setAlignment(Qt.AlignCenter)
        
        status_label = QLabel("🟢 System Online")
        status_label.setStyleSheet("color: #10B981; font-weight: bold; font-size: 12px;")
        status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(status_label)
        
        accuracy_label = QLabel("Accuracy: 95%")
        accuracy_label.setStyleSheet("color: white; font-size: 10px;")
        accuracy_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(accuracy_label)
        
        return widget
        
    def _setup_extension_integration(self):
        """Setup extension data integration"""
        try:
            self.extension_manager = ExtensionDataManager()
            self.extension_manager.register_ui_component(self.extension_widget)
            self.extension_manager.data_received.connect(self._on_extension_data)
            self.extension_manager.connection_status_changed.connect(self._on_connection_changed)
            self.extension_manager.start_listening()
            print("✅ Extension integration setup complete")
        except Exception as e:
            print(f"⚠️ Extension integration setup failed: {e}")
            
    def _on_extension_data(self, data):
        """Handle extension data received"""
        # Update connection status
        if data.get('source') == 'REAL_CHROME_EXTENSION':
            self.connection_status.setText("🟢 Connected")
            self.connection_status.setStyleSheet("color: #10B981; font-weight: bold;")
            
    def _on_connection_changed(self, connected):
        """Handle connection status change"""
        if connected:
            self.connection_status.setText("🟢 Connected")
            self.connection_status.setStyleSheet("color: #10B981; font-weight: bold;")
        else:
            self.connection_status.setText("🔴 Disconnected")
            self.connection_status.setStyleSheet("color: #EF4444; font-weight: bold;")
            
    def _apply_styles(self):
        """Apply global styles"""
        self.setStyleSheet("""
            QMainWindow {
                background: #0f0f0f;
                color: white;
            }
            QFrame#header {
                background: rgba(20, 20, 20, 0.9);
                border: 1px solid #333;
                border-radius: 8px;
            }
            QFrame#leftPanel, QFrame#rightPanel {
                background: rgba(20, 20, 20, 0.8);
                border: 1px solid #333;
                border-radius: 8px;
            }
            QFrame#centerPanel {
                background: rgba(15, 15, 15, 0.9);
                border: 1px solid #333;
                border-radius: 8px;
            }
            QFrame#bottomPanel {
                background: rgba(20, 20, 20, 0.8);
                border: 1px solid #333;
                border-radius: 8px;
            }
            QLabel {
                color: white;
            }
            QSpinBox {
                background: #374151;
                color: white;
                border: 1px solid #6B7280;
                padding: 5px;
                border-radius: 4px;
            }
        """)


def main():
    """Main function"""
    print("🎯 VIP BIG BANG - Simple Dashboard")
    print("=" * 40)
    
    app = QApplication(sys.argv)
    
    # Create and show dashboard
    dashboard = VIPSimpleDashboard()
    dashboard.show()
    
    print("✅ Simple Dashboard launched")
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
