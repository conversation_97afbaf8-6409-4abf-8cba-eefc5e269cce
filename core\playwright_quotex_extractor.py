"""
🚀 VIP BIG BANG - Advanced Playwright Quotex Data Extractor
💎 Real-time data extraction from Quotex using browser automation
⚡ Bypasses anti-scraping protection with stealth techniques
"""

import asyncio
import json
import time
import random
from datetime import datetime
from typing import Dict, Any, Optional, Callable
import logging

try:
    from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright not installed. Run: pip install playwright")

class PlaywrightQuotexExtractor:
    """🎯 Advanced Playwright-based Quotex data extractor"""
    
    def __init__(self, callback: Optional[Callable] = None):
        self.callback = callback
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.is_running = False
        self.extraction_count = 0
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Stealth settings
        self.stealth_config = {
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'viewport': {'width': 1920, 'height': 1080},
            'locale': 'en-US',
            'timezone': 'America/New_York'
        }
        
        # Data extraction selectors
        self.selectors = {
            'balance': [
                '[data-testid="balance"]',
                '.balance',
                '[class*="balance"]',
                '[class*="Balance"]',
                '.header__balance',
                '.user-balance',
                '#balance'
            ],
            'asset': [
                '[data-testid="asset"]',
                '.asset-name',
                '[class*="asset"]',
                '[class*="Asset"]',
                '.trading-asset',
                '.current-asset',
                '.symbol'
            ],
            'price': [
                '[data-testid="price"]',
                '.current-price',
                '[class*="price"]',
                '[class*="Price"]',
                '.trading-price',
                '.quote-price',
                '.rate'
            ],
            'payout': [
                '[data-testid="payout"]',
                '.payout',
                '[class*="payout"]',
                '[class*="Payout"]',
                '.profit-rate',
                '.percentage'
            ]
        }

    async def start_extraction(self, quotex_url: str = "https://qxbroker.com/en/trade") -> bool:
        """🚀 Start Playwright extraction"""
        try:
            if not PLAYWRIGHT_AVAILABLE:
                print("❌ Playwright not available")
                return False
                
            print("🚀 Starting Playwright Quotex Extractor...")
            
            # Launch browser with stealth settings
            playwright = await async_playwright().start()
            
            self.browser = await playwright.chromium.launch(
                headless=False,  # Visible browser for debugging
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-dev-shm-usage',
                    '--no-first-run',
                    '--disable-extensions-except=',
                    '--disable-plugins-discovery',
                    '--disable-default-apps'
                ]
            )
            
            # Create stealth context
            self.context = await self.browser.new_context(
                user_agent=self.stealth_config['user_agent'],
                viewport=self.stealth_config['viewport'],
                locale=self.stealth_config['locale'],
                timezone_id=self.stealth_config['timezone'],
                permissions=['geolocation'],
                extra_http_headers={
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
            )
            
            # Create page
            self.page = await self.context.new_page()
            
            # Add stealth scripts
            await self._add_stealth_scripts()
            
            # Navigate to Quotex
            print(f"🌐 Navigating to: {quotex_url}")
            await self.page.goto(quotex_url, wait_until='networkidle', timeout=30000)
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            print("✅ Playwright browser launched successfully")
            print("💡 Please login to your Quotex account manually")
            print("🎯 Data extraction will start automatically...")
            
            # Start extraction loop
            self.is_running = True
            await self._extraction_loop()
            
            return True
            
        except Exception as e:
            print(f"❌ Playwright start error: {e}")
            await self.stop_extraction()
            return False

    async def _add_stealth_scripts(self):
        """🥷 Add stealth scripts to bypass detection"""
        try:
            # Remove webdriver property
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # Override plugins
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
            
            # Override languages
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            # Override permissions
            await self.page.add_init_script("""
                const originalQuery = window.navigator.permissions.query;
                return window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)
            
            print("🥷 Stealth scripts added successfully")
            
        except Exception as e:
            print(f"⚠️ Stealth script error: {e}")

    async def _extraction_loop(self):
        """🔄 Main extraction loop"""
        try:
            while self.is_running:
                # Extract data
                data = await self._extract_quotex_data()
                
                if data and self.callback:
                    # Send data to callback
                    self.callback(data)
                
                # Wait before next extraction
                await asyncio.sleep(2)  # Extract every 2 seconds
                
        except Exception as e:
            print(f"❌ Extraction loop error: {e}")

    async def _extract_quotex_data(self) -> Optional[Dict[str, Any]]:
        """📊 Extract real Quotex data"""
        try:
            self.extraction_count += 1
            
            # Wait for page to be ready
            await self.page.wait_for_load_state('networkidle', timeout=5000)
            
            # Extract data using multiple methods
            data = {
                'timestamp': datetime.now().isoformat(),
                'extraction_method': 'playwright_advanced',
                'extraction_count': self.extraction_count,
                'source': 'PLAYWRIGHT_EXTRACTOR',
                'url': self.page.url
            }
            
            # Method 1: Try CSS selectors
            balance = await self._extract_by_selectors('balance')
            asset = await self._extract_by_selectors('asset')
            price = await self._extract_by_selectors('price')
            payout = await self._extract_by_selectors('payout')
            
            # Method 2: Try JavaScript evaluation
            if not balance or not asset or not price:
                js_data = await self._extract_by_javascript()
                if js_data:
                    balance = balance or js_data.get('balance')
                    asset = asset or js_data.get('asset')
                    price = price or js_data.get('price')
                    payout = payout or js_data.get('payout')
            
            # Method 3: Try network monitoring
            if not balance or not asset or not price:
                network_data = await self._extract_from_network()
                if network_data:
                    balance = balance or network_data.get('balance')
                    asset = asset or network_data.get('asset')
                    price = price or network_data.get('price')
                    payout = payout or network_data.get('payout')
            
            # Update data
            data.update({
                'balance': balance,
                'currentAsset': asset,
                'currentPrice': price,
                'payout': payout,
                'accountType': 'REAL ACCOUNT',
                'timeframe': '15s'
            })
            
            # Validate data quality
            confidence = self._calculate_confidence(data)
            data['confidence'] = confidence
            
            print(f"📊 Extraction #{self.extraction_count}:")
            print(f"  💰 Balance: {balance}")
            print(f"  📈 Asset: {asset}")
            print(f"  💵 Price: {price}")
            print(f"  🎯 Confidence: {confidence}%")
            
            return data
            
        except Exception as e:
            print(f"❌ Data extraction error: {e}")
            return None

    async def _extract_by_selectors(self, data_type: str) -> Optional[str]:
        """🎯 Extract data using CSS selectors"""
        try:
            selectors = self.selectors.get(data_type, [])
            
            for selector in selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        text = await element.text_content()
                        if text and text.strip():
                            return text.strip()
                except:
                    continue
            
            return None
            
        except Exception as e:
            return None

    async def _extract_by_javascript(self) -> Optional[Dict[str, str]]:
        """🔧 Extract data using JavaScript evaluation"""
        try:
            # JavaScript code to extract data
            js_code = """
            () => {
                const data = {};
                
                // Try to find balance
                const balanceElements = document.querySelectorAll('*');
                for (let el of balanceElements) {
                    const text = el.textContent || '';
                    if (text.match(/\\$[0-9,]+\\.?[0-9]*/)) {
                        data.balance = text.match(/\\$[0-9,]+\\.?[0-9]*/)[0];
                        break;
                    }
                }
                
                // Try to find asset
                const assetElements = document.querySelectorAll('*');
                for (let el of assetElements) {
                    const text = el.textContent || '';
                    if (text.match(/[A-Z]{3}\\/[A-Z]{3}/)) {
                        data.asset = text.match(/[A-Z]{3}\\/[A-Z]{3}/)[0];
                        break;
                    }
                }
                
                // Try to find price
                const priceElements = document.querySelectorAll('*');
                for (let el of priceElements) {
                    const text = el.textContent || '';
                    if (text.match(/[0-9]+\\.[0-9]{4,5}/)) {
                        data.price = text.match(/[0-9]+\\.[0-9]{4,5}/)[0];
                        break;
                    }
                }
                
                return data;
            }
            """
            
            result = await self.page.evaluate(js_code)
            return result if result else None
            
        except Exception as e:
            return None

    async def _extract_from_network(self) -> Optional[Dict[str, str]]:
        """🌐 Extract data from network requests"""
        try:
            # This would require setting up request/response monitoring
            # For now, return None - can be implemented later
            return None
            
        except Exception as e:
            return None

    def _calculate_confidence(self, data: Dict[str, Any]) -> float:
        """📊 Calculate data confidence score"""
        try:
            score = 0
            total = 0
            
            # Check balance
            if data.get('balance'):
                if '$' in str(data['balance']) and data['balance'] != '$0.85':
                    score += 25
                total += 25
            
            # Check asset
            if data.get('currentAsset'):
                if '/' in str(data['currentAsset']) and data['currentAsset'] != 'Market':
                    score += 25
                total += 25
            
            # Check price
            if data.get('currentPrice'):
                if str(data['currentPrice']) != '$0.85' and str(data['currentPrice']) != '0.85':
                    score += 25
                total += 25
            
            # Check URL
            if 'qxbroker.com' in str(data.get('url', '')):
                score += 25
                total += 25
            
            return (score / total * 100) if total > 0 else 0
            
        except:
            return 0

    async def stop_extraction(self):
        """⏹️ Stop extraction and cleanup"""
        try:
            print("⏹️ Stopping Playwright extraction...")
            self.is_running = False
            
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
                
            print("✅ Playwright extraction stopped")
            
        except Exception as e:
            print(f"❌ Stop extraction error: {e}")

    async def wait_for_login(self, timeout: int = 300) -> bool:
        """⏳ Wait for user to login"""
        try:
            print("⏳ Waiting for login...")
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                # Check if logged in by looking for trading interface
                try:
                    # Look for trading elements
                    trading_elements = await self.page.query_selector_all('[class*="trading"], [class*="chart"], [class*="asset"]')
                    if len(trading_elements) > 3:
                        print("✅ Login detected!")
                        return True
                except:
                    pass
                
                await asyncio.sleep(2)
            
            print("⏰ Login timeout")
            return False
            
        except Exception as e:
            print(f"❌ Wait for login error: {e}")
            return False
