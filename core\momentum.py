"""
VIP BIG BANG Enterprise - Momentum Analyzer
Advanced momentum analysis for ultra-fast trading decisions
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import logging
from datetime import datetime

class MomentumAnalyzer:
    """
    Enterprise-level momentum analysis
    Combines multiple momentum indicators for high accuracy
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("MomentumAnalyzer")
        
        # Momentum parameters
        self.short_period = 5
        self.medium_period = 14
        self.long_period = 21
        self.roc_period = 10
        
        # Thresholds for scoring
        self.strong_momentum_threshold = 0.75
        self.weak_momentum_threshold = 0.25
        
        self.logger.debug("Momentum Analyzer initialized")
    
    def calculate_rate_of_change(self, prices: pd.Series, period: int = 10) -> pd.Series:
        """Calculate Rate of Change (ROC)"""
        return ((prices - prices.shift(period)) / prices.shift(period)) * 100
    
    def calculate_momentum_oscillator(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Momentum Oscillator"""
        return prices / prices.shift(period) * 100
    
    def calculate_price_momentum(self, data: pd.DataFrame) -> Dict:
        """Calculate various price momentum indicators"""
        close_prices = data['close'] if 'close' in data.columns else data['price']
        
        # Rate of Change for different periods
        roc_short = self.calculate_rate_of_change(close_prices, self.short_period)
        roc_medium = self.calculate_rate_of_change(close_prices, self.medium_period)
        roc_long = self.calculate_rate_of_change(close_prices, self.long_period)
        
        # Momentum Oscillator
        momentum_osc = self.calculate_momentum_oscillator(close_prices, self.medium_period)
        
        # Price velocity (rate of price change acceleration)
        price_velocity = close_prices.diff().rolling(window=5).mean()
        price_acceleration = price_velocity.diff()
        
        return {
            'roc_short': roc_short.iloc[-1] if not roc_short.empty else 0,
            'roc_medium': roc_medium.iloc[-1] if not roc_medium.empty else 0,
            'roc_long': roc_long.iloc[-1] if not roc_long.empty else 0,
            'momentum_osc': momentum_osc.iloc[-1] if not momentum_osc.empty else 100,
            'price_velocity': price_velocity.iloc[-1] if not price_velocity.empty else 0,
            'price_acceleration': price_acceleration.iloc[-1] if not price_acceleration.empty else 0
        }
    
    def calculate_volume_momentum(self, data: pd.DataFrame) -> Dict:
        """Calculate volume-based momentum indicators"""
        if 'volume' not in data.columns:
            return {'volume_momentum': 0, 'volume_acceleration': 0}
        
        volume = data['volume']
        
        # Volume momentum
        volume_momentum = volume.rolling(window=5).mean() / volume.rolling(window=20).mean()
        
        # Volume acceleration
        volume_velocity = volume.diff().rolling(window=3).mean()
        volume_acceleration = volume_velocity.diff()
        
        return {
            'volume_momentum': volume_momentum.iloc[-1] if not volume_momentum.empty else 1,
            'volume_acceleration': volume_acceleration.iloc[-1] if not volume_acceleration.empty else 0
        }
    
    def calculate_trend_momentum(self, data: pd.DataFrame) -> Dict:
        """Calculate trend-based momentum"""
        close_prices = data['close'] if 'close' in data.columns else data['price']
        
        # Moving averages for trend detection
        ma_fast = close_prices.rolling(window=5).mean()
        ma_slow = close_prices.rolling(window=20).mean()
        
        # Trend momentum
        trend_momentum = (ma_fast.iloc[-1] - ma_slow.iloc[-1]) / ma_slow.iloc[-1] * 100
        
        # Trend acceleration
        ma_fast_prev = ma_fast.iloc[-2] if len(ma_fast) > 1 else ma_fast.iloc[-1]
        ma_slow_prev = ma_slow.iloc[-2] if len(ma_slow) > 1 else ma_slow.iloc[-1]
        
        trend_acceleration = trend_momentum - ((ma_fast_prev - ma_slow_prev) / ma_slow_prev * 100)
        
        return {
            'trend_momentum': trend_momentum if not np.isnan(trend_momentum) else 0,
            'trend_acceleration': trend_acceleration if not np.isnan(trend_acceleration) else 0
        }
    
    def normalize_score(self, value: float, min_val: float, max_val: float) -> float:
        """Normalize value to 0-1 range"""
        if max_val == min_val:
            return 0.5
        normalized = (value - min_val) / (max_val - min_val)
        return max(0, min(1, normalized))
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main momentum analysis function
        Returns score between 0 (strong bearish) and 1 (strong bullish)
        """
        try:
            if len(data) < self.long_period:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for momentum analysis'
                }
            
            # Calculate different momentum components
            price_momentum = self.calculate_price_momentum(data)
            volume_momentum = self.calculate_volume_momentum(data)
            trend_momentum = self.calculate_trend_momentum(data)
            
            # Scoring components
            scores = []
            
            # Price momentum scoring
            roc_score = self.normalize_score(price_momentum['roc_medium'], -5, 5)
            momentum_osc_score = self.normalize_score(price_momentum['momentum_osc'], 95, 105)
            velocity_score = self.normalize_score(price_momentum['price_velocity'], -0.01, 0.01)
            
            scores.extend([roc_score, momentum_osc_score, velocity_score])
            
            # Volume momentum scoring (if available)
            if volume_momentum['volume_momentum'] != 0:
                volume_score = self.normalize_score(volume_momentum['volume_momentum'], 0.5, 2.0)
                scores.append(volume_score)
            
            # Trend momentum scoring
            trend_score = self.normalize_score(trend_momentum['trend_momentum'], -2, 2)
            scores.append(trend_score)
            
            # Calculate final score
            final_score = np.mean(scores)
            
            # Determine direction and confidence
            if final_score > 0.65:
                direction = 'CALL'
                confidence = min((final_score - 0.5) * 2, 1.0)
            elif final_score < 0.35:
                direction = 'PUT'
                confidence = min((0.5 - final_score) * 2, 1.0)
            else:
                direction = 'NEUTRAL'
                confidence = 1.0 - abs(final_score - 0.5) * 2
            
            # Additional momentum strength indicators
            momentum_strength = 'WEAK'
            if confidence > 0.8:
                momentum_strength = 'VERY_STRONG'
            elif confidence > 0.6:
                momentum_strength = 'STRONG'
            elif confidence > 0.4:
                momentum_strength = 'MODERATE'
            
            result = {
                'score': final_score,
                'direction': direction,
                'confidence': confidence,
                'strength': momentum_strength,
                'components': {
                    'price_momentum': price_momentum,
                    'volume_momentum': volume_momentum,
                    'trend_momentum': trend_momentum
                },
                'raw_scores': {
                    'roc_score': roc_score,
                    'momentum_osc_score': momentum_osc_score,
                    'velocity_score': velocity_score,
                    'trend_score': trend_score
                },
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.debug(f"Momentum analysis: Score={final_score:.3f}, Direction={direction}, Confidence={confidence:.3f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Momentum analysis failed: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
