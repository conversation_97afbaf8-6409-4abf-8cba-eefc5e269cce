"""
VIP BIG BANG Professional UI
Enterprise-grade component-based architecture
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
import logging

# Import professional components
from ui.styles.theme import VIPTheme
from ui.components.header import VIPHeader
from ui.components.left_panel import VIPLeftPanel
from ui.components.center_panel import VIPCenterPanel
from ui.components.right_panel import VIPRightPanel

class VIPBigBangProfessionalUI(QMainWindow):
    """
    Professional VIP BIG BANG Main Window
    Component-based architecture with proper separation of concerns
    """
    
    # Main UI signals
    currency_changed = Signal(str)
    mode_changed = Signal(str)
    manual_trading_toggled = Signal(bool)
    buy_clicked = Signal()
    sell_clicked = Signal()
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("VIPBigBangProfessionalUI")
        
        # Initialize components
        self.header = None
        self.left_panel = None
        self.center_panel = None
        self.right_panel = None
        
        # Setup window
        self._setup_window()
        
        # Apply professional theme
        self._apply_theme()
        
        # Setup UI components
        self._setup_ui()
        
        # Connect signals
        self._connect_signals()
        
        self.logger.info("VIP BIG BANG Professional UI initialized")
    
    def _setup_window(self):
        """Setup main window properties"""
        self.setWindowTitle("VIP BIG BANG - Professional Trading Platform")
        self.setGeometry(100, 100, 1024, 600)
        self.setFixedSize(1024, 600)
        
        # Set window icon if available
        try:
            self.setWindowIcon(QIcon("assets/vip_icon.png"))
        except:
            pass  # Icon file not found, continue without it
    
    def _apply_theme(self):
        """Apply the professional VIP theme"""
        stylesheet = VIPTheme.get_main_stylesheet()
        self.setStyleSheet(stylesheet)
    
    def _setup_ui(self):
        """Setup the main UI layout with professional components"""
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main vertical layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Header component
        self.header = VIPHeader()
        main_layout.addWidget(self.header)
        
        # Main content area
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)
        
        # Left panel component
        self.left_panel = VIPLeftPanel()
        content_layout.addWidget(self.left_panel)
        
        # Center panel component
        self.center_panel = VIPCenterPanel()
        content_layout.addWidget(self.center_panel)
        
        # Right panel component
        self.right_panel = VIPRightPanel()
        content_layout.addWidget(self.right_panel)
        
        main_layout.addLayout(content_layout)
    
    def _connect_signals(self):
        """Connect component signals to main UI signals"""
        # Header signals
        self.header.currency_changed.connect(self.currency_changed.emit)
        self.header.mode_changed.connect(self.mode_changed.emit)
        self.header.buy_clicked.connect(self.buy_clicked.emit)
        self.header.sell_clicked.connect(self.sell_clicked.emit)
        self.header.menu_clicked.connect(self._on_menu_clicked)
        
        # Left panel signals
        self.left_panel.manual_trading_toggled.connect(self.manual_trading_toggled.emit)
        
        # Right panel signals
        self.right_panel.autotrade_clicked.connect(self._on_autotrade_clicked)
        self.right_panel.confirm_mode_clicked.connect(self._on_confirm_mode_clicked)
        self.right_panel.heatmap_clicked.connect(self._on_heatmap_clicked)
        self.right_panel.economic_news_clicked.connect(self._on_economic_news_clicked)
        self.right_panel.can_clicked.connect(self._on_can_clicked)
        self.right_panel.settings_clicked.connect(self._on_settings_clicked)
        self.right_panel.secures_clicked.connect(self._on_secures_clicked)
    
    # === PUBLIC API METHODS ===
    
    def update_balance(self, balance: float):
        """Update account balance display"""
        if self.left_panel:
            self.left_panel.update_balance(balance)
    
    def update_price(self, price: float):
        """Update current price display"""
        if self.center_panel:
            self.center_panel.update_price(price)
    
    def update_vortex(self, value: float):
        """Update vortex indicator"""
        if self.center_panel:
            self.center_panel.update_vortex(value)
    
    def update_signals(self, buy_pct: int, sell_pct: int):
        """Update live signals"""
        if self.center_panel:
            self.center_panel.update_signals(buy_pct, sell_pct)
    
    def update_power(self, buyer_power: int):
        """Update buyer/seller power"""
        if self.center_panel:
            self.center_panel.update_power(buyer_power)
    
    def set_autotrade_status(self, enabled: bool):
        """Set autotrade status"""
        if self.left_panel:
            self.left_panel.update_autotrade_status(enabled)
        if self.right_panel:
            self.right_panel.set_autotrade_active(enabled)
    
    def set_currency(self, currency: str):
        """Set current currency pair"""
        if self.header:
            self.header.set_currency(currency)
    
    def set_mode(self, mode: str):
        """Set current trading mode"""
        if self.header:
            self.header.set_mode(mode)
    
    def update_trade_stats(self, trade_profit: int, profit_loss: int):
        """Update trade statistics"""
        if self.left_panel:
            self.left_panel.update_trade_stats(trade_profit, profit_loss)
    
    def update_pulse_colors(self, colors: list):
        """Update pulse bar colors"""
        if self.left_panel:
            self.left_panel.update_pulse_colors(colors)
    
    # === EVENT HANDLERS ===
    
    def _on_menu_clicked(self):
        """Handle menu button click"""
        self.logger.info("Menu clicked")
        # Implement menu functionality
    
    def _on_autotrade_clicked(self):
        """Handle autotrade button click"""
        self.logger.info("AutoTrade clicked")
        # Toggle autotrade state
        current_state = self.left_panel.get_autotrade_status()
        self.set_autotrade_status(not current_state)
    
    def _on_confirm_mode_clicked(self):
        """Handle confirm mode button click"""
        self.logger.info("Confirm Mode clicked")
        # Implement confirm mode functionality
    
    def _on_heatmap_clicked(self):
        """Handle heatmap button click"""
        self.logger.info("Heatmap clicked")
        # Implement heatmap functionality
    
    def _on_economic_news_clicked(self):
        """Handle economic news button click"""
        self.logger.info("Economic News clicked")
        # Implement economic news functionality
    
    def _on_can_clicked(self):
        """Handle can button click"""
        self.logger.info("Can clicked")
        # Implement can functionality
    
    def _on_settings_clicked(self):
        """Handle settings button click"""
        self.logger.info("Settings clicked")
        # Implement settings functionality
    
    def _on_secures_clicked(self):
        """Handle secures button click"""
        self.logger.info("Secures clicked")
        # Implement secures functionality
    
    # === GETTERS ===
    
    def get_current_currency(self) -> str:
        """Get currently selected currency"""
        return self.header.get_current_currency() if self.header else ""
    
    def get_current_mode(self) -> str:
        """Get currently selected mode"""
        return self.header.get_current_mode() if self.header else ""
    
    def get_balance(self) -> float:
        """Get current balance"""
        return self.left_panel.get_balance() if self.left_panel else 0.0
    
    def get_manual_trading_state(self) -> bool:
        """Get manual trading state"""
        return self.left_panel.get_manual_trading_state() if self.left_panel else False


# Test application
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create and show window
    window = VIPBigBangProfessionalUI()
    window.show()
    
    # Test data updates
    QTimer.singleShot(1000, lambda: window.update_price(1.07350))
    QTimer.singleShot(2000, lambda: window.update_signals(75, 25))
    QTimer.singleShot(3000, lambda: window.update_power(40))
    
    sys.exit(app.exec())
