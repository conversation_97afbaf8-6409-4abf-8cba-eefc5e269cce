#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎨 VIP BIG BANG - CSS Error Fixer
برطرف کردن ارورهای CSS در UI
"""

import re
import os
from pathlib import Path

class CSSErrorFixer:
    """
    🎨 برطرف‌کننده ارورهای CSS
    """
    
    def __init__(self):
        self.ui_file = "vip_complete_professional_ui.py"
        self.fixes_applied = 0
    
    def fix_transform_property(self, css_text):
        """برطرف کردن خاصیت transform"""
        # Remove transform properties that cause issues
        problematic_transforms = [
            r'transform:\s*[^;]+;',
            r'-webkit-transform:\s*[^;]+;',
            r'-moz-transform:\s*[^;]+;',
            r'-ms-transform:\s*[^;]+;'
        ]
        
        for pattern in problematic_transforms:
            if re.search(pattern, css_text):
                css_text = re.sub(pattern, '', css_text)
                self.fixes_applied += 1
        
        return css_text
    
    def fix_box_shadow_property(self, css_text):
        """برطرف کردن خاصیت box-shadow"""
        # Simplify box-shadow properties
        box_shadow_patterns = [
            r'box-shadow:\s*[^;]+;',
            r'-webkit-box-shadow:\s*[^;]+;',
            r'-moz-box-shadow:\s*[^;]+;'
        ]
        
        for pattern in box_shadow_patterns:
            if re.search(pattern, css_text):
                # Replace with simple shadow
                css_text = re.sub(pattern, 'box-shadow: 0 2px 4px rgba(0,0,0,0.1);', css_text)
                self.fixes_applied += 1
        
        return css_text
    
    def fix_css_in_file(self):
        """برطرف کردن CSS در فایل UI"""
        if not os.path.exists(self.ui_file):
            print(f"❌ File not found: {self.ui_file}")
            return False
        
        try:
            # Read file
            with open(self.ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Find CSS strings and fix them
            css_pattern = r'setStyleSheet\s*\(\s*["\']([^"\']+)["\']'
            
            def fix_css_match(match):
                css_content = match.group(1)
                fixed_css = self.fix_transform_property(css_content)
                fixed_css = self.fix_box_shadow_property(fixed_css)
                return f'setStyleSheet("{fixed_css}"'
            
            content = re.sub(css_pattern, fix_css_match, content)
            
            # Write back if changes were made
            if content != original_content:
                with open(self.ui_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ CSS fixes applied: {self.fixes_applied}")
                return True
            else:
                print("ℹ️ No CSS issues found")
                return True
                
        except Exception as e:
            print(f"❌ Error fixing CSS: {e}")
            return False
    
    def create_clean_css_styles(self):
        """ایجاد استایل‌های CSS تمیز"""
        clean_styles = {
            "modern_button": """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4F46E5, stop:1 #3730A3);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5B52F0, stop:1 #4338CA);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #3730A3, stop:1 #312E81);
                }
            """,
            
            "modern_frame": """
                QFrame {
                    background: white;
                    border: 1px solid #E5E7EB;
                    border-radius: 12px;
                    padding: 16px;
                }
            """,
            
            "modern_label": """
                QLabel {
                    color: #374151;
                    font-size: 14px;
                    font-weight: 500;
                }
            """,
            
            "gaming_style": """
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1F2937, stop:1 #111827);
                    color: #F9FAFB;
                    font-family: 'Segoe UI', Arial, sans-serif;
                }
            """
        }
        
        # Save clean styles to file
        with open("clean_css_styles.py", "w", encoding="utf-8") as f:
            f.write("# Clean CSS Styles for VIP BIG BANG\n\n")
            f.write("CLEAN_STYLES = {\n")
            for name, style in clean_styles.items():
                f.write(f'    "{name}": """{style.strip()}""",\n\n')
            f.write("}\n")
        
        print("✅ Clean CSS styles created: clean_css_styles.py")
    
    def run_css_fix(self):
        """اجرای برطرف‌کننده CSS"""
        print("="*50)
        print("🎨 VIP BIG BANG CSS Error Fixer")
        print("="*50)
        
        print("1️⃣ Fixing CSS in UI file...")
        success = self.fix_css_in_file()
        
        print("2️⃣ Creating clean CSS styles...")
        self.create_clean_css_styles()
        
        if success:
            print("\n✅ CSS fixes completed successfully!")
            print(f"📊 Total fixes applied: {self.fixes_applied}")
            print("\n🎯 Benefits:")
            print("• Removed problematic transform properties")
            print("• Simplified box-shadow properties")
            print("• Created clean CSS alternatives")
            print("• Reduced console warnings")
        else:
            print("\n❌ CSS fixes failed!")
        
        print("="*50)
        return success

def main():
    """اجرای برطرف‌کننده"""
    fixer = CSSErrorFixer()
    fixer.run_css_fix()

if __name__ == "__main__":
    main()
