/**
 * VIP BIG BANG Enterprise - Popup Script
 * Controls the extension popup interface
 */

// Popup state
let updateInterval = null;
let isConnected = false;

// DOM elements
const elements = {
    desktopValue: null,
    quotexValue: null,
    websocketValue: null,
    extensionValue: null,
    balanceValue: null,
    assetValue: null,
    priceValue: null,
    extractionsValue: null,
    startBtn: null,
    stopBtn: null,
    refreshBtn: null,
    openQuotexBtn: null,
    lastUpdate: null
};

/**
 * Initialize popup
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 VIP BIG BANG Popup initializing...');
    
    // Get DOM elements
    initializeElements();
    
    // Setup event listeners
    setupEventListeners();
    
    // Start status updates
    startStatusUpdates();
    
    // Initial status check
    updateStatus();
    
    console.log('✅ Popup initialized');
});

/**
 * Initialize DOM elements
 */
function initializeElements() {
    elements.desktopValue = document.getElementById('desktop-value');
    elements.quotexValue = document.getElementById('quotex-value');
    elements.websocketValue = document.getElementById('websocket-value');
    elements.extensionValue = document.getElementById('extension-value');
    elements.balanceValue = document.getElementById('balance-value');
    elements.assetValue = document.getElementById('asset-value');
    elements.priceValue = document.getElementById('price-value');
    elements.extractionsValue = document.getElementById('extractions-value');
    elements.startBtn = document.getElementById('start-btn');
    elements.stopBtn = document.getElementById('stop-btn');
    elements.refreshBtn = document.getElementById('refresh-btn');
    elements.openQuotexBtn = document.getElementById('open-quotex-btn');
    elements.lastUpdate = document.getElementById('last-update');
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Start button
    elements.startBtn.addEventListener('click', handleStart);

    // Stop button
    elements.stopBtn.addEventListener('click', handleStop);

    // Refresh button
    elements.refreshBtn.addEventListener('click', handleRefresh);

    // Open Quotex button
    elements.openQuotexBtn.addEventListener('click', handleOpenQuotex);
}

/**
 * Handle start button click
 */
async function handleStart() {
    try {
        elements.startBtn.disabled = true;
        elements.startBtn.innerHTML = '<span class="loading"></span> Starting...';

        // Get active tab
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        // Check if on Quotex page
        if (!tab.url || (!tab.url.includes('qxbroker.com') && !tab.url.includes('quotex'))) {
            showNotification('Please navigate to Quotex page first', 'error');
            return;
        }

        // Send message to content script
        try {
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'startExtraction'
            });

            if (response && response.status === 'started') {
                showNotification('✅ Extraction started successfully', 'success');
                elements.startBtn.classList.add('hidden');
                elements.stopBtn.classList.remove('hidden');

                // Update status immediately
                setTimeout(() => updateStatus(), 1000);
            } else {
                showNotification('❌ Failed to start extraction', 'error');
            }

        } catch (error) {
            console.error('Content script error:', error);

            // Try to inject content script if not loaded
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content.js']
                });

                // Wait a moment then try again
                setTimeout(async () => {
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {
                            action: 'startExtraction'
                        });

                        if (response && response.status === 'started') {
                            showNotification('✅ Extraction started successfully', 'success');
                            elements.startBtn.classList.add('hidden');
                            elements.stopBtn.classList.remove('hidden');
                            updateStatus();
                        }
                    } catch (e) {
                        showNotification('❌ Content script injection failed', 'error');
                    }
                }, 1000);

            } catch (injectionError) {
                showNotification('❌ Failed to inject content script', 'error');
            }
        }

    } catch (error) {
        console.error('Start error:', error);
        showNotification('❌ Start failed - Please refresh Quotex page', 'error');
    } finally {
        elements.startBtn.disabled = false;
        elements.startBtn.innerHTML = '🚀 Start Extraction';
    }
}

/**
 * Handle stop button click
 */
async function handleStop() {
    try {
        elements.stopBtn.disabled = true;
        elements.stopBtn.innerHTML = '<span class="loading"></span> Stopping...';

        // Send message to content script to stop extraction
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        const response = await chrome.tabs.sendMessage(tab.id, {
            action: 'stopExtraction'
        });

        if (response && response.status === 'stopped') {
            showNotification('Extraction stopped', 'success');
            elements.stopBtn.classList.add('hidden');
            elements.startBtn.classList.remove('hidden');
            updateStatus();
        } else {
            showNotification('Failed to stop extraction', 'error');
        }

    } catch (error) {
        console.error('Stop error:', error);
        showNotification('Stop failed', 'error');
    } finally {
        elements.stopBtn.disabled = false;
        elements.stopBtn.innerHTML = '⏹️ Stop Extraction';
    }
}

/**
 * Handle refresh button click
 */
async function handleRefresh() {
    try {
        elements.refreshBtn.disabled = true;
        elements.refreshBtn.innerHTML = '<span class="loading"></span> Refreshing...';
        
        await updateStatus();
        showNotification('Status refreshed', 'success');
        
    } catch (error) {
        console.error('Refresh error:', error);
        showNotification('Refresh failed', 'error');
    } finally {
        elements.refreshBtn.disabled = false;
        elements.refreshBtn.innerHTML = '🔄 Refresh Status';
    }
}

/**
 * Handle open Quotex button click
 */
async function handleOpenQuotex() {
    try {
        // Open Quotex in new tab
        await chrome.tabs.create({
            url: 'https://qxbroker.com/en/trade',
            active: true
        });

        showNotification('Quotex opened in new tab', 'success');

    } catch (error) {
        console.error('Open Quotex error:', error);
        showNotification('Failed to open Quotex', 'error');
    }
}

/**
 * Handle emergency stop button click
 */
async function handleEmergencyStop() {
    try {
        const confirmed = confirm('Are you sure you want to emergency stop all trading?');
        if (!confirmed) return;
        
        elements.emergencyStopBtn.disabled = true;
        elements.emergencyStopBtn.innerHTML = '<span class="loading"></span> Stopping...';
        
        // Send emergency stop command
        const response = await sendMessageToBackground({
            type: 'EMERGENCY_STOP'
        });
        
        if (response.success) {
            showNotification('Emergency stop activated', 'success');
            elements.emergencyStopBtn.classList.add('hidden');
        } else {
            showNotification('Emergency stop failed: ' + response.error, 'error');
        }
        
    } catch (error) {
        console.error('Emergency stop error:', error);
        showNotification('Emergency stop failed', 'error');
    } finally {
        elements.emergencyStopBtn.disabled = false;
        elements.emergencyStopBtn.innerHTML = '🛑 Emergency Stop';
    }
}

/**
 * Update status displays
 */
async function updateStatus() {
    try {
        // Check if we're on Quotex page
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const isQuotexPage = tab.url && (tab.url.includes('qxbroker.com') || tab.url.includes('quotex'));

        // Update Quotex status
        updateStatusValue(elements.quotexValue, isQuotexPage);

        // Extension is always online if popup is open
        updateStatusValue(elements.extensionValue, true);

        // Check WebSocket server connection
        try {
            const response = await fetch('http://localhost:8765', {
                method: 'GET',
                mode: 'no-cors'
            });
            updateStatusValue(elements.websocketValue, true);
            updateStatusValue(elements.desktopValue, true);
        } catch (error) {
            updateStatusValue(elements.websocketValue, false);
            updateStatusValue(elements.desktopValue, false);
        }

        if (isQuotexPage) {
            try {
                // Get status from content script
                const response = await chrome.tabs.sendMessage(tab.id, {
                    action: 'getStatus'
                });

                if (response) {
                    // Update WebSocket status
                    updateStatusValue(elements.websocketValue, response.activeWebSockets > 0);

                    // Update extraction data
                    if (response.lastData) {
                        const data = response.lastData;
                        elements.balanceValue.textContent = data.balance || '$0.00';
                        elements.assetValue.textContent = data.currentAsset || 'None';
                        elements.priceValue.textContent = data.currentPrice || '-';
                    }

                    elements.extractionsValue.textContent = response.extractionCount || '0';

                    // Update button states
                    if (response.isActive) {
                        elements.startBtn.classList.add('hidden');
                        elements.stopBtn.classList.remove('hidden');
                    } else {
                        elements.stopBtn.classList.add('hidden');
                        elements.startBtn.classList.remove('hidden');
                    }
                } else {
                    updateStatusValue(elements.websocketValue, false);
                }

            } catch (error) {
                console.log('Content script not ready yet');
                updateStatusValue(elements.websocketValue, false);
            }
        } else {
            updateStatusValue(elements.websocketValue, false);
            elements.balanceValue.textContent = '$0.00';
            elements.assetValue.textContent = 'Not on Quotex';
            elements.priceValue.textContent = '-';
            elements.extractionsValue.textContent = '0';
        }

        // Try to check desktop connection (placeholder)
        updateStatusValue(elements.desktopValue, false); // Will be updated when desktop connection is implemented

        // Update last update time
        elements.lastUpdate.textContent = `Last update: ${new Date().toLocaleTimeString()}`;

    } catch (error) {
        console.error('Status update error:', error);
    }
}

/**
 * Update status value element
 */
function updateStatusValue(element, isOnline) {
    if (isOnline) {
        element.classList.remove('offline', 'warning');
        element.classList.add('online');
        element.textContent = 'Online';
    } else {
        element.classList.remove('online', 'warning');
        element.classList.add('offline');
        element.textContent = 'Offline';
    }
}

/**
 * Update trading information
 */
async function updateTradingInfo() {
    try {
        // Get balance
        const balanceResponse = await sendMessageToBackground({
            type: 'GET_BALANCE'
        });
        
        if (balanceResponse.success) {
            const balance = balanceResponse.data.balance || 0;
            elements.balanceValue.textContent = `$${balance.toFixed(2)}`;
        }
        
        // Get trades count (would need to be implemented in background script)
        const tradesResponse = await sendMessageToBackground({
            type: 'GET_TRADES_COUNT'
        });
        
        if (tradesResponse.success) {
            const tradesCount = tradesResponse.data.count || 0;
            elements.tradesValue.textContent = tradesCount.toString();
        }
        
    } catch (error) {
        console.error('Trading info update error:', error);
    }
}

/**
 * Send message to background script
 */
function sendMessageToBackground(message) {
    return new Promise((resolve) => {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                resolve({ success: false, error: chrome.runtime.lastError.message });
            } else {
                resolve(response || { success: false, error: 'No response' });
            }
        });
    });
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        padding: 10px 15px;
        border-radius: 5px;
        color: white;
        font-size: 12px;
        z-index: 1000;
        max-width: 250px;
        word-wrap: break-word;
        ${type === 'success' ? 'background: #4a8a4a;' : ''}
        ${type === 'error' ? 'background: #8a4a4a;' : ''}
        ${type === 'info' ? 'background: #4a6a8a;' : ''}
    `;
    
    // Add to document
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

/**
 * Start periodic status updates
 */
function startStatusUpdates() {
    // Update every 5 seconds
    updateInterval = setInterval(updateStatus, 5000);
}

/**
 * Stop status updates
 */
function stopStatusUpdates() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}

/**
 * Cleanup when popup closes
 */
window.addEventListener('beforeunload', () => {
    stopStatusUpdates();
});
