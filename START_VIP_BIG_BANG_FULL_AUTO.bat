@echo off
title VIP BIG BANG - Single Chrome Window
color 0A

echo.
echo ===============================================
echo 🚀 VIP BIG BANG ULTIMATE TRADING ROBOT
echo 🌐 SINGLE Chrome Window (No Multiple Windows)
echo 📊 Real Data Reading System
echo ⚡ Quantum Speed Analysis Engine
echo ===============================================
echo.

echo 🔧 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)
echo ✅ Python found

echo.
echo 🔧 Installing required packages...
pip install selenium psutil requests websockets >nul 2>&1
echo ✅ Packages installed

echo.
echo 🚀 Starting VIP BIG BANG Single Chrome System...
echo.
echo 📋 This will:
echo   ✅ Close existing Chrome windows
echo   ✅ Open ONLY ONE Chrome window
echo   ✅ Load extension automatically
echo   ✅ Open Quotex in the same window
echo   ✅ Read real data from single window
echo.
echo ⚠️ IMPORTANT: Only ONE Chrome window will open!
echo.

echo 🎯 Starting in 3 seconds...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 LAUNCHING VIP BIG BANG...
echo.

python vip_real_quotex_main.py

echo.
echo 🔌 VIP BIG BANG stopped.
pause
