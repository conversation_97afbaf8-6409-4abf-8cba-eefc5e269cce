"""
🤖 VIP BIG BANG AUTO TRADE ENGINE
Advanced Automatic Trading System with Real Data Integration
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Callable
import threading

class AutoTradeEngine:
    """🤖 Advanced Automatic Trading Engine"""
    
    def __init__(self):
        self.is_running = False
        self.is_auto_trading = False
        self.trade_callbacks = []
        self.status_callbacks = []
        
        # Trading settings
        self.min_confidence = 85.0  # Minimum confidence for auto trade
        self.min_confirmations = 8  # Minimum confirmations (user preference)
        self.trade_amount = 10.0    # Default trade amount
        self.max_trades_per_hour = 20
        self.max_daily_loss = 100.0
        
        # Trading state
        self.trades_executed = 0
        self.trades_won = 0
        self.trades_lost = 0
        self.daily_profit = 0.0
        self.last_trade_time = 0
        self.trade_cooldown = 30  # 30 seconds between trades
        
        # Real data integration
        self.latest_data = {}
        self.latest_analysis = {}
        self.latest_signals = {}
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        print("🤖 Auto Trade Engine initialized")
    
    def add_trade_callback(self, callback: Callable):
        """📡 Add callback for trade execution"""
        self.trade_callbacks.append(callback)
        self.logger.info("📡 Trade callback added")
    
    def add_status_callback(self, callback: Callable):
        """📡 Add callback for status updates"""
        self.status_callbacks.append(callback)
        self.logger.info("📡 Status callback added")
    
    def update_real_data(self, data: Dict):
        """📊 Update with real Quotex data"""
        try:
            self.latest_data = data
            
            # Extract key information
            asset = data.get('currentAsset', 'Unknown')
            price = data.get('currentPrice', 'Unknown')
            balance = data.get('balance', 'Unknown')
            account_type = data.get('accountType', 'Unknown')
            
            self.logger.info(f"📊 Real data updated: {asset} @ {price}")
            
            # Check if we should execute auto trade
            if self.is_auto_trading:
                self.check_auto_trade_conditions()
                
        except Exception as e:
            self.logger.error(f"❌ Real data update error: {e}")
    
    def update_analysis(self, analysis: Dict):
        """📈 Update with analysis results"""
        try:
            self.latest_analysis = analysis
            
            # Extract analysis information
            ma6_signal = analysis.get('ma6_signal', 'UNKNOWN')
            momentum = analysis.get('momentum', 'UNKNOWN')
            power = analysis.get('power', 'UNKNOWN')
            
            self.logger.info(f"📈 Analysis updated: MA6={ma6_signal}, Momentum={momentum}")
            
            # Check if we should execute auto trade
            if self.is_auto_trading:
                self.check_auto_trade_conditions()
                
        except Exception as e:
            self.logger.error(f"❌ Analysis update error: {e}")
    
    def update_signals(self, signals: Dict):
        """🎯 Update with trading signals"""
        try:
            self.latest_signals = signals
            
            # Extract signal information
            signal = signals.get('signal', 'WAIT')
            confidence = signals.get('confidence', 0.0)
            confirmations = signals.get('confirmations', {})
            buy_count = confirmations.get('BUY', 0)
            sell_count = confirmations.get('SELL', 0)
            
            self.logger.info(f"🎯 Signals updated: {signal} ({confidence}%) - BUY:{buy_count}, SELL:{sell_count}")
            
            # Check if we should execute auto trade
            if self.is_auto_trading:
                self.check_auto_trade_conditions()
                
        except Exception as e:
            self.logger.error(f"❌ Signals update error: {e}")
    
    def check_auto_trade_conditions(self):
        """🔍 Check if conditions are met for automatic trading"""
        try:
            if not self.is_auto_trading:
                return False
            
            # Check cooldown
            current_time = time.time()
            if current_time - self.last_trade_time < self.trade_cooldown:
                return False
            
            # Check if we have all required data
            if not self.latest_data or not self.latest_signals:
                self.logger.warning("⚠️ Missing data for auto trade")
                return False
            
            # Extract signal data
            signal = self.latest_signals.get('signal', 'WAIT')
            confidence = self.latest_signals.get('confidence', 0.0)
            confirmations = self.latest_signals.get('confirmations', {})
            buy_count = confirmations.get('BUY', 0)
            sell_count = confirmations.get('SELL', 0)
            
            # Check minimum confidence
            if confidence < self.min_confidence:
                self.logger.info(f"⚠️ Confidence too low: {confidence}% < {self.min_confidence}%")
                return False
            
            # Check minimum confirmations
            total_confirmations = buy_count + sell_count
            if total_confirmations < self.min_confirmations:
                self.logger.info(f"⚠️ Not enough confirmations: {total_confirmations} < {self.min_confirmations}")
                return False
            
            # Check signal direction
            if signal == 'WAIT':
                self.logger.info("⚠️ Signal is WAIT - no trade")
                return False
            
            # Check account type (allow both DEMO and REAL)
            account_type = self.latest_data.get('accountType', 'UNKNOWN')
            if 'DEMO' in account_type.upper():
                self.logger.info("🎮 Demo account detected - auto trade ENABLED for testing")
                # Use smaller amounts for demo
                self.trade_amount = min(self.trade_amount, 5.0)
            elif 'REAL' in account_type.upper():
                self.logger.info("💰 Real account detected - auto trade ENABLED for live trading")
                # Use configured amounts for real
            else:
                self.logger.warning("⚠️ Unknown account type - proceeding with caution")
                # Use very small amounts for unknown accounts
                self.trade_amount = min(self.trade_amount, 1.0)

            # Continue with auto trading regardless of account type
            
            # Check daily limits
            if self.trades_executed >= self.max_trades_per_hour:
                self.logger.warning("⚠️ Max trades per hour reached")
                return False
            
            if self.daily_profit <= -self.max_daily_loss:
                self.logger.warning("⚠️ Max daily loss reached")
                return False
            
            # All conditions met - execute trade
            self.execute_auto_trade(signal, confidence)
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Auto trade condition check error: {e}")
            return False
    
    def execute_auto_trade(self, signal: str, confidence: float):
        """💰 Execute automatic trade"""
        try:
            self.logger.info(f"🚀 EXECUTING AUTO TRADE: {signal} with {confidence}% confidence")
            
            # Prepare trade data
            trade_data = {
                'signal': signal,
                'confidence': confidence,
                'amount': self.trade_amount,
                'asset': self.latest_data.get('currentAsset', 'Unknown'),
                'price': self.latest_data.get('currentPrice', 'Unknown'),
                'timestamp': datetime.now().isoformat(),
                'trade_id': f"AUTO_{int(time.time())}",
                'execution_method': 'AUTOMATIC'
            }
            
            # Update trade statistics
            self.trades_executed += 1
            self.last_trade_time = time.time()
            
            # Notify callbacks
            for callback in self.trade_callbacks:
                try:
                    callback(trade_data)
                except Exception as e:
                    self.logger.error(f"❌ Trade callback error: {e}")
            
            # Send trade to Chrome Extension
            self.send_trade_to_extension(trade_data)
            
            # Update status
            self.update_status(f"AUTO TRADE EXECUTED: {signal}")
            
            self.logger.info(f"✅ Auto trade executed successfully: {trade_data['trade_id']}")
            
        except Exception as e:
            self.logger.error(f"❌ Auto trade execution error: {e}")
    
    def send_trade_to_extension(self, trade_data: Dict):
        """📤 Send trade command to Chrome Extension"""
        try:
            # This would send the trade command to the Chrome Extension
            # The extension would then execute the trade on Quotex
            
            command = {
                'type': 'execute_trade',
                'data': trade_data,
                'timestamp': datetime.now().isoformat()
            }
            
            # In a real implementation, this would send via WebSocket
            self.logger.info(f"📤 Trade command sent to extension: {trade_data['signal']}")
            
            # Simulate trade execution result (in real implementation, this comes from extension)
            self.simulate_trade_result(trade_data)
            
        except Exception as e:
            self.logger.error(f"❌ Send trade to extension error: {e}")
    
    def simulate_trade_result(self, trade_data: Dict):
        """🎲 Simulate trade result (for testing)"""
        try:
            # Simulate win/loss based on confidence
            confidence = trade_data.get('confidence', 50.0)
            win_probability = min(confidence / 100.0, 0.95)  # Max 95% win rate
            
            import random
            is_win = random.random() < win_probability
            
            if is_win:
                self.trades_won += 1
                profit = trade_data['amount'] * 0.94  # 94% payout
                self.daily_profit += profit
                result = 'WIN'
                self.logger.info(f"✅ TRADE WON: +${profit:.2f}")
            else:
                self.trades_lost += 1
                loss = trade_data['amount']
                self.daily_profit -= loss
                result = 'LOSS'
                self.logger.info(f"❌ TRADE LOST: -${loss:.2f}")
            
            # Update trade result
            trade_result = {
                **trade_data,
                'result': result,
                'profit': profit if is_win else -trade_data['amount'],
                'completed_at': datetime.now().isoformat()
            }
            
            # Calculate win rate
            win_rate = (self.trades_won / self.trades_executed * 100) if self.trades_executed > 0 else 0
            
            # Update status
            status = f"Trades: {self.trades_executed} | Win Rate: {win_rate:.1f}% | Profit: ${self.daily_profit:.2f}"
            self.update_status(status)
            
        except Exception as e:
            self.logger.error(f"❌ Trade result simulation error: {e}")
    
    def update_status(self, status: str):
        """📊 Update trading status"""
        try:
            status_data = {
                'status': status,
                'timestamp': datetime.now().isoformat(),
                'trades_executed': self.trades_executed,
                'trades_won': self.trades_won,
                'trades_lost': self.trades_lost,
                'win_rate': (self.trades_won / self.trades_executed * 100) if self.trades_executed > 0 else 0,
                'daily_profit': self.daily_profit,
                'is_auto_trading': self.is_auto_trading
            }
            
            # Notify status callbacks
            for callback in self.status_callbacks:
                try:
                    callback(status_data)
                except Exception as e:
                    self.logger.error(f"❌ Status callback error: {e}")
                    
        except Exception as e:
            self.logger.error(f"❌ Status update error: {e}")
    
    def start_auto_trading(self):
        """🚀 Start automatic trading"""
        try:
            self.is_auto_trading = True
            self.logger.info("🚀 AUTO TRADING STARTED")
            self.update_status("AUTO TRADING ACTIVE")
            return True
        except Exception as e:
            self.logger.error(f"❌ Start auto trading error: {e}")
            return False
    
    def stop_auto_trading(self):
        """⏹️ Stop automatic trading"""
        try:
            self.is_auto_trading = False
            self.logger.info("⏹️ AUTO TRADING STOPPED")
            self.update_status("AUTO TRADING STOPPED")
            return True
        except Exception as e:
            self.logger.error(f"❌ Stop auto trading error: {e}")
            return False
    
    def get_trading_stats(self) -> Dict:
        """📊 Get trading statistics"""
        try:
            win_rate = (self.trades_won / self.trades_executed * 100) if self.trades_executed > 0 else 0
            
            return {
                'trades_executed': self.trades_executed,
                'trades_won': self.trades_won,
                'trades_lost': self.trades_lost,
                'win_rate': win_rate,
                'daily_profit': self.daily_profit,
                'is_auto_trading': self.is_auto_trading,
                'min_confidence': self.min_confidence,
                'min_confirmations': self.min_confirmations,
                'trade_amount': self.trade_amount
            }
        except Exception as e:
            self.logger.error(f"❌ Get trading stats error: {e}")
            return {}
    
    def update_settings(self, settings: Dict):
        """⚙️ Update trading settings"""
        try:
            if 'min_confidence' in settings:
                self.min_confidence = float(settings['min_confidence'])
            
            if 'min_confirmations' in settings:
                self.min_confirmations = int(settings['min_confirmations'])
            
            if 'trade_amount' in settings:
                self.trade_amount = float(settings['trade_amount'])
            
            if 'max_trades_per_hour' in settings:
                self.max_trades_per_hour = int(settings['max_trades_per_hour'])
            
            if 'max_daily_loss' in settings:
                self.max_daily_loss = float(settings['max_daily_loss'])
            
            self.logger.info(f"⚙️ Trading settings updated: {settings}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Update settings error: {e}")
            return False

# Global auto trade engine instance
auto_trade_engine = None

def get_auto_trade_engine():
    """🤖 Get global auto trade engine instance"""
    global auto_trade_engine
    if auto_trade_engine is None:
        auto_trade_engine = AutoTradeEngine()
    return auto_trade_engine

def start_auto_trading():
    """🚀 Start automatic trading"""
    engine = get_auto_trade_engine()
    return engine.start_auto_trading()

def stop_auto_trading():
    """⏹️ Stop automatic trading"""
    engine = get_auto_trade_engine()
    return engine.stop_auto_trading()

def get_trading_stats():
    """📊 Get trading statistics"""
    engine = get_auto_trade_engine()
    return engine.get_trading_stats()

# Test function
def test_auto_trade_engine():
    """🧪 Test auto trade engine"""
    print("🧪 Testing Auto Trade Engine...")
    
    engine = AutoTradeEngine()
    
    def trade_callback(trade_data):
        print(f"💰 Trade executed: {trade_data}")
    
    def status_callback(status_data):
        print(f"📊 Status update: {status_data}")
    
    engine.add_trade_callback(trade_callback)
    engine.add_status_callback(status_callback)
    
    # Test with sample data
    sample_data = {
        'currentAsset': 'EUR/USD',
        'currentPrice': '1.08456',
        'balance': '$1000.00',
        'accountType': 'REAL ACCOUNT'
    }
    
    sample_signals = {
        'signal': 'BUY',
        'confidence': 87.5,
        'confirmations': {'BUY': 8, 'SELL': 2}
    }
    
    engine.update_real_data(sample_data)
    engine.update_signals(sample_signals)
    
    # Start auto trading
    engine.start_auto_trading()
    
    print("✅ Auto Trade Engine test completed")

if __name__ == "__main__":
    test_auto_trade_engine()
