"""
VIP BIG BANG Enterprise - Brothers Can Pattern
Analysis of two consecutive confirming candles pattern
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import logging
from datetime import datetime

class BrothersCanPattern:
    """
    Brothers Can Pattern - VIP BIG BANG complementary analysis
    Analyzes pattern of two consecutive candles that confirm each other
    Examples: Two bullish candles with high volume, Two bearish candles with momentum
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("BrothersCanPattern")
        
        # Pattern parameters
        self.min_body_size = 0.3          # Minimum body size ratio
        self.volume_confirmation = 1.2    # Volume should be 20% higher than average
        self.price_movement_threshold = 0.002  # 0.2% minimum price movement
        
        # Pattern strength thresholds
        self.strong_pattern_threshold = 0.8
        self.medium_pattern_threshold = 0.6
        self.weak_pattern_threshold = 0.4
        
        self.logger.debug("Brothers Can Pattern analyzer initialized")
    
    def calculate_candle_properties(self, candle_data: pd.Series) -> Dict:
        """Calculate properties of a single candle"""
        open_price = candle_data.get('open', candle_data.get('price', 0))
        high_price = candle_data.get('high', candle_data.get('price', 0))
        low_price = candle_data.get('low', candle_data.get('price', 0))
        close_price = candle_data.get('close', candle_data.get('price', 0))
        volume = candle_data.get('volume', 1)
        
        # Calculate candle metrics
        total_range = high_price - low_price
        body_size = abs(close_price - open_price)
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price
        
        # Calculate ratios
        body_ratio = body_size / total_range if total_range > 0 else 0
        upper_shadow_ratio = upper_shadow / total_range if total_range > 0 else 0
        lower_shadow_ratio = lower_shadow / total_range if total_range > 0 else 0
        
        # Determine candle direction
        if close_price > open_price:
            direction = 'BULLISH'
        elif close_price < open_price:
            direction = 'BEARISH'
        else:
            direction = 'DOJI'
        
        # Calculate price movement percentage
        price_movement = (close_price - open_price) / open_price if open_price > 0 else 0
        
        return {
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume,
            'total_range': total_range,
            'body_size': body_size,
            'body_ratio': body_ratio,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'upper_shadow_ratio': upper_shadow_ratio,
            'lower_shadow_ratio': lower_shadow_ratio,
            'direction': direction,
            'price_movement': price_movement,
            'price_movement_percent': price_movement * 100
        }
    
    def detect_brothers_pattern(self, data: pd.DataFrame) -> Dict:
        """Detect Brothers Can pattern in the last two candles"""
        if len(data) < 2:
            return {
                'pattern_detected': False,
                'pattern_type': 'NONE',
                'pattern_strength': 0.0,
                'reason': 'Insufficient data'
            }
        
        # Get last two candles
        candle1 = self.calculate_candle_properties(data.iloc[-2])
        candle2 = self.calculate_candle_properties(data.iloc[-1])
        
        # Check if candles are in same direction
        if candle1['direction'] != candle2['direction'] or candle1['direction'] == 'DOJI':
            return {
                'pattern_detected': False,
                'pattern_type': 'NONE',
                'pattern_strength': 0.0,
                'reason': 'Candles not in same direction or contain doji'
            }
        
        # Check minimum body size requirement
        if candle1['body_ratio'] < self.min_body_size or candle2['body_ratio'] < self.min_body_size:
            return {
                'pattern_detected': False,
                'pattern_type': 'NONE',
                'pattern_strength': 0.0,
                'reason': 'Body size too small'
            }
        
        # Check minimum price movement
        if (abs(candle1['price_movement']) < self.price_movement_threshold or 
            abs(candle2['price_movement']) < self.price_movement_threshold):
            return {
                'pattern_detected': False,
                'pattern_type': 'NONE',
                'pattern_strength': 0.0,
                'reason': 'Price movement too small'
            }
        
        # Pattern detected - determine type
        pattern_type = f"BROTHERS_{candle1['direction']}"
        
        # Calculate pattern strength
        strength_factors = []
        
        # Body size factor (larger bodies = stronger pattern)
        avg_body_ratio = (candle1['body_ratio'] + candle2['body_ratio']) / 2
        body_strength = min(avg_body_ratio / 0.7, 1.0)  # Normalize to 0.7 max
        strength_factors.append(body_strength)
        
        # Price movement factor
        avg_movement = (abs(candle1['price_movement']) + abs(candle2['price_movement'])) / 2
        movement_strength = min(avg_movement / 0.01, 1.0)  # Normalize to 1% max
        strength_factors.append(movement_strength)
        
        # Volume confirmation factor
        if len(data) >= 10:
            avg_volume = data['volume'].tail(10).mean() if 'volume' in data.columns else 1
            volume1_ratio = candle1['volume'] / avg_volume if avg_volume > 0 else 1
            volume2_ratio = candle2['volume'] / avg_volume if avg_volume > 0 else 1
            
            volume_strength = min((volume1_ratio + volume2_ratio) / (2 * self.volume_confirmation), 1.0)
            strength_factors.append(volume_strength)
        
        # Shadow factor (smaller shadows = stronger pattern)
        avg_upper_shadow = (candle1['upper_shadow_ratio'] + candle2['upper_shadow_ratio']) / 2
        avg_lower_shadow = (candle1['lower_shadow_ratio'] + candle2['lower_shadow_ratio']) / 2
        
        if candle1['direction'] == 'BULLISH':
            # For bullish pattern, small upper shadows are better
            shadow_strength = 1.0 - avg_upper_shadow
        else:
            # For bearish pattern, small lower shadows are better
            shadow_strength = 1.0 - avg_lower_shadow
        
        strength_factors.append(max(shadow_strength, 0.3))  # Minimum 0.3
        
        # Calculate overall pattern strength
        pattern_strength = np.mean(strength_factors)
        
        return {
            'pattern_detected': True,
            'pattern_type': pattern_type,
            'pattern_strength': pattern_strength,
            'candle1_properties': candle1,
            'candle2_properties': candle2,
            'strength_factors': {
                'body_strength': body_strength,
                'movement_strength': movement_strength,
                'volume_strength': strength_factors[2] if len(strength_factors) > 2 else 0.5,
                'shadow_strength': shadow_strength
            },
            'reason': f'Brothers {candle1["direction"].lower()} pattern detected'
        }
    
    def analyze_pattern_context(self, data: pd.DataFrame, pattern_data: Dict) -> Dict:
        """Analyze pattern in market context"""
        if not pattern_data['pattern_detected'] or len(data) < 5:
            return {
                'context_strength': 0.0,
                'trend_alignment': False,
                'context_description': 'No context analysis available'
            }
        
        # Analyze recent trend
        prices = data['close'] if 'close' in data.columns else data['price']
        recent_prices = prices.tail(5)
        
        # Calculate trend
        x = np.arange(len(recent_prices))
        y = recent_prices.values
        slope = np.polyfit(x, y, 1)[0]
        avg_price = recent_prices.mean()
        normalized_slope = slope / avg_price if avg_price > 0 else 0
        
        # Determine trend direction
        if normalized_slope > 0.002:
            trend_direction = 'UP'
        elif normalized_slope < -0.002:
            trend_direction = 'DOWN'
        else:
            trend_direction = 'SIDEWAYS'
        
        # Check trend alignment
        pattern_direction = pattern_data['pattern_type'].split('_')[1]  # BULLISH or BEARISH
        
        if pattern_direction == 'BULLISH' and trend_direction == 'UP':
            trend_alignment = True
            context_strength = 0.8
            context_description = 'Bullish pattern aligns with uptrend'
        elif pattern_direction == 'BEARISH' and trend_direction == 'DOWN':
            trend_alignment = True
            context_strength = 0.8
            context_description = 'Bearish pattern aligns with downtrend'
        elif trend_direction == 'SIDEWAYS':
            trend_alignment = False
            context_strength = 0.5
            context_description = 'Pattern in sideways market'
        else:
            trend_alignment = False
            context_strength = 0.3
            context_description = 'Pattern against trend - potential reversal'
        
        return {
            'context_strength': context_strength,
            'trend_alignment': trend_alignment,
            'trend_direction': trend_direction,
            'context_description': context_description,
            'normalized_slope': normalized_slope
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main Brothers Can pattern analysis
        Returns pattern detection and strength analysis
        """
        try:
            if len(data) < 2:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for Brothers Can pattern analysis'
                }
            
            # Detect pattern
            pattern_data = self.detect_brothers_pattern(data)
            
            # Analyze context
            context_data = self.analyze_pattern_context(data, pattern_data)
            
            # Calculate overall score
            if pattern_data['pattern_detected']:
                base_score = pattern_data['pattern_strength']
                context_adjustment = context_data['context_strength'] * 0.3
                
                if pattern_data['pattern_type'] == 'BROTHERS_BULLISH':
                    score = 0.5 + (base_score * 0.4) + context_adjustment
                    direction = 'UP'
                else:  # BROTHERS_BEARISH
                    score = 0.5 - (base_score * 0.4) - context_adjustment
                    direction = 'DOWN'
                
                confidence = pattern_data['pattern_strength'] * context_data['context_strength']
            else:
                score = 0.5
                direction = 'NEUTRAL'
                confidence = 0.0
            
            # Ensure bounds
            score = max(0, min(1, score))
            confidence = max(0, min(1, confidence))
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'pattern_data': pattern_data,
                'context_data': context_data,
                'pattern_detected': pattern_data['pattern_detected'],
                'pattern_type': pattern_data['pattern_type'],
                'pattern_strength': pattern_data['pattern_strength'],
                'details': f"Brothers: {pattern_data['pattern_type']} ({pattern_data['pattern_strength']:.2f})"
            }
            
        except Exception as e:
            self.logger.error(f"Brothers Can pattern analysis error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'details': f'Brothers Can analysis failed: {str(e)}'
            }
