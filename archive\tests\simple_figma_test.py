"""
🎨 Simple Figma Integration Test
تست ساده ادغام Figma
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class SimpleFigmaTest(QMainWindow):
    """تست ساده Figma"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎨 Simple Figma Test")
        self.setGeometry(100, 100, 800, 600)
        
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1A1A2E, stop:1 #0F3460);
                color: white;
            }
            QPushButton {
                background: #4A90E2;
                border: 2px solid #4A90E2;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                padding: 15px;
                font-size: 14px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: #357ABD;
            }
            QLabel {
                color: white;
                font-size: 16px;
                padding: 10px;
            }
            QTextEdit {
                background: rgba(0,0,0,0.7);
                border: 2px solid #4A90E2;
                border-radius: 8px;
                color: white;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # Header
        header = QLabel("🎨 Figma Integration Test")
        header.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #00BCD4;
            text-align: center;
            padding: 20px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            border: 2px solid #00BCD4;
        """)
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        test_colors_btn = QPushButton("🌈 Test Colors")
        test_colors_btn.clicked.connect(self.test_colors)
        buttons_layout.addWidget(test_colors_btn)
        
        test_components_btn = QPushButton("🧩 Test Components")
        test_components_btn.clicked.connect(self.test_components)
        buttons_layout.addWidget(test_components_btn)
        
        test_tokens_btn = QPushButton("🎨 Test Tokens")
        test_tokens_btn.clicked.connect(self.test_tokens)
        buttons_layout.addWidget(test_tokens_btn)
        
        layout.addLayout(buttons_layout)
        
        # Output area
        self.output_area = QTextEdit()
        self.output_area.setPlaceholderText("Test results will appear here...")
        layout.addWidget(self.output_area)
        
        # Status
        self.status_label = QLabel("✅ Ready for testing")
        self.status_label.setStyleSheet("color: #7ED321; font-weight: bold;")
        layout.addWidget(self.status_label)
    
    def test_colors(self):
        """تست پالت رنگ‌ها"""
        self.status_label.setText("🌈 Testing color palette...")
        
        colors = {
            "Primary Blue": "#4A90E2",
            "Primary Green": "#7ED321", 
            "Primary Orange": "#F5A623",
            "Primary Red": "#D0021B",
            "Primary Purple": "#9013FE",
            "Neon Cyan": "#00FFFF",
            "Neon Magenta": "#FF00FF",
            "Neon Green": "#00FF41",
            "Background Dark 1": "#1A1A2E",
            "Background Dark 2": "#16213E"
        }
        
        output = "🌈 VIP BIG BANG Color Palette:\n\n"
        for name, hex_color in colors.items():
            output += f"{name}: {hex_color}\n"
        
        output += "\n✅ Color palette test completed!"
        
        self.output_area.setPlainText(output)
        self.status_label.setText("✅ Color test completed")
    
    def test_components(self):
        """تست کامپوننت‌ها"""
        self.status_label.setText("🧩 Testing components...")
        
        components = {
            "Trading Button": {
                "width": 150,
                "height": 60,
                "border_radius": 15,
                "variants": ["primary", "success", "warning", "danger"]
            },
            "Stats Card": {
                "width": 200,
                "height": 120,
                "border_radius": 15,
                "variants": ["balance", "winrate", "trades", "profit"]
            },
            "Chart Container": {
                "min_width": 400,
                "min_height": 300,
                "border_radius": 15,
                "features": ["grid", "real-time", "animations"]
            }
        }
        
        output = "🧩 VIP BIG BANG Components:\n\n"
        for comp_name, comp_data in components.items():
            output += f"📦 {comp_name}:\n"
            for key, value in comp_data.items():
                output += f"  • {key}: {value}\n"
            output += "\n"
        
        output += "✅ Components test completed!"
        
        self.output_area.setPlainText(output)
        self.status_label.setText("✅ Components test completed")
    
    def test_tokens(self):
        """تست Design Tokens"""
        self.status_label.setText("🎨 Testing design tokens...")
        
        tokens = {
            "colors": {
                "primary": "#4A90E2",
                "success": "#7ED321",
                "warning": "#F5A623",
                "danger": "#D0021B"
            },
            "typography": {
                "heading_1": {"size": "32px", "weight": 900},
                "heading_2": {"size": "24px", "weight": 800},
                "body": {"size": "14px", "weight": 400}
            },
            "spacing": {
                "xs": "4px",
                "sm": "8px", 
                "md": "16px",
                "lg": "24px",
                "xl": "32px"
            },
            "border_radius": {
                "small": "8px",
                "medium": "15px",
                "large": "25px"
            }
        }
        
        output = "🎨 Design Tokens:\n\n"
        for category, items in tokens.items():
            output += f"📋 {category.upper()}:\n"
            for key, value in items.items():
                output += f"  • {key}: {value}\n"
            output += "\n"
        
        output += "✅ Design tokens test completed!"
        
        self.output_area.setPlainText(output)
        self.status_label.setText("✅ Design tokens test completed")

class FigmaStyleButton(QPushButton):
    """دکمه با استایل Figma"""
    
    def __init__(self, text="", button_type="primary"):
        super().__init__(text)
        self.button_type = button_type
        self.setup_figma_style()
    
    def setup_figma_style(self):
        """اعمال استایل Figma"""
        colors = {
            "primary": "#4A90E2",
            "success": "#7ED321",
            "warning": "#F5A623", 
            "danger": "#D0021B"
        }
        
        color = colors.get(self.button_type, "#4A90E2")
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 rgba(0,0,0,0.3));
                border: 3px solid {color};
                border-radius: 15px;
                color: white;
                font-family: 'Arial', sans-serif;
                font-weight: bold;
                font-size: 14px;
                padding: 15px 25px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: {color};
                border: 4px solid white;
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: rgba(0,0,0,0.5);
                border: 2px solid {color};
            }}
        """)

def test_figma_buttons():
    """تست دکمه‌های Figma"""
    app = QApplication(sys.argv)
    
    window = QWidget()
    window.setWindowTitle("🎨 Figma Buttons Test")
    window.setGeometry(200, 200, 600, 200)
    window.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1A1A2E, stop:1 #0F3460);
        }
    """)
    
    layout = QHBoxLayout(window)
    layout.setSpacing(20)
    layout.setContentsMargins(30, 30, 30, 30)
    
    # Test buttons
    primary_btn = FigmaStyleButton("PRIMARY", "primary")
    success_btn = FigmaStyleButton("SUCCESS", "success")
    warning_btn = FigmaStyleButton("WARNING", "warning")
    danger_btn = FigmaStyleButton("DANGER", "danger")
    
    layout.addWidget(primary_btn)
    layout.addWidget(success_btn)
    layout.addWidget(warning_btn)
    layout.addWidget(danger_btn)
    
    window.show()
    return app.exec()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Choose test mode
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--buttons":
        sys.exit(test_figma_buttons())
    else:
        window = SimpleFigmaTest()
        window.show()
        sys.exit(app.exec())
