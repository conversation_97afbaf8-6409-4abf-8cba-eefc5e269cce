"""
🎯 VIP BIG BANG DYNAMIC TIMEFRAME TEST
⚡ TEST AUTOMATIC ADJUSTMENT OF INDICATORS BASED ON TIMEFRAME
🔧 VERIFY ALL PARAMETERS CHANGE CORRECTLY
"""

import asyncio
import time
import random
from datetime import datetime
from core.quantum_ultra_fast_engine import QuantumUltraFastEngine
from core.settings import Settings

class DynamicTimeframeTest:
    """🎯 Test dynamic timeframe functionality"""
    
    def __init__(self):
        self.settings = Settings()
        self.quantum_engine = QuantumUltraFastEngine(self.settings)
        
    async def run_comprehensive_test(self):
        """🔥 Run comprehensive dynamic timeframe test"""
        print("🎯 VIP BIG BANG DYNAMIC TIMEFRAME TEST")
        print("=" * 60)
        print("🔧 Testing automatic adjustment of all indicators")
        print()
        
        # Test different timeframe combinations
        test_scenarios = [
            (5, 5, "🚀 Ultra Fast"),
            (15, 5, "⚡ VIP BIG BANG Default"),
            (60, 5, "🎯 Standard Fast"),
            (60, 60, "📊 Medium"),
            (300, 60, "🕐 Long Analysis"),
            (300, 300, "📈 Extended"),
            (30, 10, "🔧 Custom 1"),
            (120, 30, "🔧 Custom 2")
        ]
        
        for analysis_interval, trade_duration, name in test_scenarios:
            await self._test_timeframe_scenario(analysis_interval, trade_duration, name)
            print()
        
        # Test real-time switching
        await self._test_realtime_switching()
        
        print("✅ DYNAMIC TIMEFRAME TEST COMPLETED!")
    
    async def _test_timeframe_scenario(self, analysis_interval: int, trade_duration: int, name: str):
        """🔧 Test specific timeframe scenario"""
        print(f"🔧 Testing: {name}")
        print(f"   ⏱️ Analysis: {analysis_interval}s | Trade: {trade_duration}s")
        
        # Set new timeframe
        success = await self.quantum_engine.set_timeframe_and_duration(analysis_interval, trade_duration)
        
        if not success:
            print(f"   ❌ Failed to set timeframe")
            return
        
        # Get configuration details
        config_info = self.quantum_engine.get_current_timeframe_info()
        
        print(f"   📊 Configuration Applied:")
        print(f"      🔢 Data Points Needed: {config_info.get('data_points_needed', 'N/A')}")
        print(f"      📈 MA Periods: {config_info.get('ma_periods', 'N/A')}")
        print(f"      🚀 RSI Period: {config_info.get('rsi_period', 'N/A')}")
        print(f"      🌪️ Vortex Period: {config_info.get('vortex_period', 'N/A')}")
        print(f"      🎯 Confidence Threshold: {config_info.get('confidence_threshold', 'N/A')}")
        print(f"      💪 Signal Multiplier: {config_info.get('signal_strength_multiplier', 'N/A')}")
        print(f"      💾 Buffer Size: {config_info.get('buffer_size', 'N/A')}")
        
        # Test analysis with this timeframe
        await self._test_analysis_with_timeframe(analysis_interval, trade_duration)
    
    async def _test_analysis_with_timeframe(self, analysis_interval: int, trade_duration: int):
        """📊 Test analysis with specific timeframe"""
        # Generate test data
        market_data = self._generate_test_data()
        
        # Run quantum analysis
        start_time = time.perf_counter()
        signal = await self.quantum_engine.quantum_lightning_analysis(market_data)
        analysis_time = (time.perf_counter() - start_time) * 1000
        
        print(f"   ⚡ Analysis Results:")
        print(f"      🎯 Direction: {signal.direction}")
        print(f"      💪 Confidence: {signal.confidence:.3f}")
        print(f"      ⏱️ Execution Time: {analysis_time:.2f}ms")
        print(f"      🧠 Neural Prediction: {signal.neural_prediction:.3f}")
        print(f"      🎮 GPU Accelerated: {signal.gpu_accelerated}")
        
        # Verify speed target based on timeframe
        if analysis_interval <= 15:
            target_time = 300  # Ultra fast target
        elif analysis_interval <= 60:
            target_time = 500  # Fast target
        else:
            target_time = 1000  # Standard target
        
        if analysis_time < target_time:
            print(f"      ✅ Speed Target Met: < {target_time}ms")
        else:
            print(f"      ⚠️ Speed Target Missed: {analysis_time:.2f}ms > {target_time}ms")
    
    async def _test_realtime_switching(self):
        """🔄 Test real-time timeframe switching"""
        print("🔄 REAL-TIME TIMEFRAME SWITCHING TEST")
        print("-" * 40)
        
        # Quick succession of timeframe changes
        timeframes = [
            (5, 5),
            (60, 60),
            (15, 5),
            (300, 60)
        ]
        
        for i, (analysis_interval, trade_duration) in enumerate(timeframes, 1):
            print(f"🔧 Switch {i}: {analysis_interval}s/{trade_duration}s")
            
            start_time = time.perf_counter()
            success = await self.quantum_engine.set_timeframe_and_duration(analysis_interval, trade_duration)
            switch_time = (time.perf_counter() - start_time) * 1000
            
            if success:
                print(f"   ✅ Switched in {switch_time:.2f}ms")
                
                # Quick analysis test
                market_data = self._generate_test_data()
                signal = await self.quantum_engine.quantum_lightning_analysis(market_data)
                print(f"   📊 Quick test: {signal.direction} ({signal.confidence:.2f})")
            else:
                print(f"   ❌ Switch failed")
            
            # Small delay between switches
            await asyncio.sleep(0.1)
    
    def _generate_test_data(self) -> dict:
        """📊 Generate realistic test market data"""
        base_price = 1.07000
        
        return {
            'price': base_price + random.uniform(-0.002, 0.002),
            'volume': random.uniform(1000, 10000),
            'high': base_price + random.uniform(0.0005, 0.002),
            'low': base_price + random.uniform(-0.002, -0.0005),
            'open': base_price + random.uniform(-0.001, 0.001),
            'close': base_price + random.uniform(-0.001, 0.001),
            'timestamp': time.time()
        }
    
    async def test_available_timeframes(self):
        """📋 Test available timeframes listing"""
        print("📋 AVAILABLE TIMEFRAMES:")
        print("-" * 30)
        
        available = self.quantum_engine.get_available_timeframes()
        
        for timeframe in available:
            print(f"🎯 {timeframe['name']}")
            print(f"   📊 {timeframe['description']}")
            print(f"   🎯 Confidence: {timeframe['confidence_threshold']}")
            print(f"   💡 Best for: {timeframe['recommended_for']}")
            print()
    
    async def test_parameter_scaling(self):
        """🔧 Test parameter scaling for custom timeframes"""
        print("🔧 PARAMETER SCALING TEST")
        print("-" * 30)
        
        # Test custom timeframes that require scaling
        custom_timeframes = [
            (7, 3, "Custom Ultra Fast"),
            (45, 15, "Custom Medium"),
            (180, 90, "Custom Slow"),
            (600, 120, "Custom Very Slow")
        ]
        
        for analysis_interval, trade_duration, name in custom_timeframes:
            print(f"🔧 Testing: {name} ({analysis_interval}s/{trade_duration}s)")
            
            success = await self.quantum_engine.set_timeframe_and_duration(analysis_interval, trade_duration)
            
            if success:
                config = self.quantum_engine.get_current_timeframe_info()
                print(f"   ✅ Scaled Parameters:")
                print(f"      📈 MA Periods: {config.get('ma_periods', {})}")
                print(f"      🚀 RSI Period: {config.get('rsi_period', 'N/A')}")
                print(f"      🌪️ Vortex Period: {config.get('vortex_period', 'N/A')}")
                print(f"      🎯 Confidence: {config.get('confidence_threshold', 'N/A')}")
            else:
                print(f"   ❌ Failed to set custom timeframe")
            
            print()

async def main():
    """🚀 Main test function"""
    tester = DynamicTimeframeTest()
    
    print("🎯 VIP BIG BANG DYNAMIC TIMEFRAME SYSTEM")
    print("⚡ AUTOMATIC INDICATOR ADJUSTMENT TEST")
    print()
    
    # Test menu
    tests = [
        ("1", "Comprehensive Timeframe Test", tester.run_comprehensive_test),
        ("2", "Available Timeframes", tester.test_available_timeframes),
        ("3", "Parameter Scaling Test", tester.test_parameter_scaling),
        ("4", "All Tests", None)
    ]
    
    print("📋 Available tests:")
    for key, name, _ in tests:
        print(f"   {key}. {name}")
    
    print()
    choice = input("🎯 Select test (1-4): ").strip()
    
    if choice == "1":
        await tester.run_comprehensive_test()
    elif choice == "2":
        await tester.test_available_timeframes()
    elif choice == "3":
        await tester.test_parameter_scaling()
    elif choice == "4":
        print("🔥 Running all tests...")
        await tester.test_available_timeframes()
        print()
        await tester.test_parameter_scaling()
        print()
        await tester.run_comprehensive_test()
    else:
        print("❌ Invalid choice, running comprehensive test...")
        await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
