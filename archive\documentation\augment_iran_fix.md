# 🔧 راه‌حل مشکل Augment در ایران

## 🚨 مشکل:
برنامه Augment در VS Code وقتی فیلترشکن وصل نیست کار نمی‌کند.

## ✅ راه‌حل‌های کامل:

### 1️⃣ **تنظیمات Proxy در VS Code:**

#### روش اول - تنظیمات VS Code:
1. در VS Code بروید به: `File > Preferences > Settings`
2. جستجو کنید: `proxy`
3. تنظیمات زیر را اضافه کنید:

```json
{
    "http.proxy": "http://127.0.0.1:10809",
    "http.proxyStrictSSL": false,
    "http.proxyAuthorization": null,
    "http.proxySupport": "on"
}
```

#### روش دوم - فایل settings.json:
1. `Ctrl+Shift+P` بزنید
2. تایپ کنید: `Preferences: Open Settings (JSON)`
3. این کدها را اضافه کنید:

```json
{
    "http.proxy": "socks5://127.0.0.1:10808",
    "http.proxyStrictSSL": false,
    "http.proxySupport": "on",
    "https.proxy": "socks5://127.0.0.1:10808"
}
```

### 2️⃣ **تنظیمات Environment Variables:**

#### در Windows:
```cmd
set HTTP_PROXY=http://127.0.0.1:10809
set HTTPS_PROXY=http://127.0.0.1:10809
set NO_PROXY=localhost,127.0.0.1
```

#### در PowerShell:
```powershell
$env:HTTP_PROXY="http://127.0.0.1:10809"
$env:HTTPS_PROXY="http://127.0.0.1:10809"
$env:NO_PROXY="localhost,127.0.0.1"
```

### 3️⃣ **تنظیمات Git (اگر از Git استفاده می‌کنید):**

```bash
git config --global http.proxy http://127.0.0.1:10809
git config --global https.proxy http://127.0.0.1:10809
```

### 4️⃣ **تنظیمات Node.js/npm:**

```bash
npm config set proxy http://127.0.0.1:10809
npm config set https-proxy http://127.0.0.1:10809
npm config set registry https://registry.npmjs.org/
```

### 5️⃣ **تنظیمات Python/pip:**

فایل `pip.conf` یا `pip.ini` ایجاد کنید:

```ini
[global]
proxy = http://127.0.0.1:10809
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
```

### 6️⃣ **راه‌حل‌های خاص Augment:**

#### الف) تنظیمات Augment Extension:
1. در VS Code بروید به Extensions
2. Augment را پیدا کنید
3. روی تنظیمات کلیک کنید
4. این موارد را اضافه کنید:

```json
{
    "augment.proxy.enabled": true,
    "augment.proxy.host": "127.0.0.1",
    "augment.proxy.port": 10809,
    "augment.network.timeout": 30000,
    "augment.network.retries": 3
}
```

#### ب) فایل .augmentrc:
در root پروژه فایل `.augmentrc` ایجاد کنید:

```json
{
    "proxy": {
        "http": "http://127.0.0.1:10809",
        "https": "http://127.0.0.1:10809"
    },
    "network": {
        "timeout": 30000,
        "retries": 3
    }
}
```

### 7️⃣ **تنظیمات فیلترشکن:**

#### برای V2Ray:
- پورت HTTP: `10809`
- پورت SOCKS5: `10808`
- Local Address: `127.0.0.1`

#### برای Clash:
- Mixed Port: `7890`
- HTTP Port: `7890`
- SOCKS5 Port: `7891`

### 8️⃣ **اسکریپت خودکار:**

فایل `setup_augment_proxy.bat` ایجاد کنید:

```batch
@echo off
echo Setting up Augment proxy for Iran...

REM Set environment variables
set HTTP_PROXY=http://127.0.0.1:10809
set HTTPS_PROXY=http://127.0.0.1:10809
set NO_PROXY=localhost,127.0.0.1

REM Configure Git
git config --global http.proxy http://127.0.0.1:10809
git config --global https.proxy http://127.0.0.1:10809

REM Configure npm
npm config set proxy http://127.0.0.1:10809
npm config set https-proxy http://127.0.0.1:10809

echo Proxy setup completed!
echo Please restart VS Code and Augment.
pause
```

### 9️⃣ **تست اتصال:**

```python
# test_augment_connection.py
import requests
import json

def test_connection():
    proxies = {
        'http': 'http://127.0.0.1:10809',
        'https': 'http://127.0.0.1:10809'
    }
    
    try:
        response = requests.get('https://api.github.com', 
                              proxies=proxies, 
                              timeout=10)
        print("✅ Connection successful!")
        print(f"Status: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    test_connection()
```

### 🔟 **راه‌حل نهایی - Offline Mode:**

اگر هیچ‌کدام کار نکرد، Augment را در حالت آفلاین استفاده کنید:

```json
{
    "augment.mode": "offline",
    "augment.cache.enabled": true,
    "augment.network.disabled": true
}
```

## 🎯 **مراحل عملی:**

1. **فیلترشکن را روشن کنید**
2. **پورت‌های فیلترشکن را چک کنید** (معمولاً 10809 یا 7890)
3. **تنظیمات VS Code را اعمال کنید**
4. **VS Code را restart کنید**
5. **Augment Extension را reload کنید**
6. **تست کنید**

## 📞 **در صورت مشکل:**

اگر باز هم کار نکرد:
1. فیلترشکن را عوض کنید
2. پورت‌های مختلف امتحان کنید
3. DNS را تغییر دهید (*******, *******)
4. Windows Firewall را چک کنید

## ✅ **تأیید عملکرد:**

وقتی همه چیز درست شد، باید:
- Augment بدون خطا کار کند
- اتصال به سرورهای Augment برقرار شود
- Code completion و suggestions کار کند
- ربات VIP BIG BANG شما هم کاملاً کار کند

---

**نکته مهم:** این تنظیمات فقط برای Augment است و ربوت VIP BIG BANG شما کاملاً مستقل کار می‌کند! 🚀
