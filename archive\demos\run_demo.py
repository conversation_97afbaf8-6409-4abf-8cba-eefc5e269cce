#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import platform
import json
import hashlib
from datetime import datetime

def main():
    print("🚀 VIP BIG BANG - Fingerprinting Demo")
    print("=" * 50)
    
    # System Info
    print("\n🖥️ System Information:")
    print(f"OS: {platform.system()}")
    print(f"Release: {platform.release()}")
    print(f"Platform: {platform.platform()}")
    print(f"Architecture: {platform.architecture()[0]}")
    print(f"Machine: {platform.machine()}")
    print(f"Processor: {platform.processor()}")
    print(f"Python: {platform.python_version()}")
    
    # VM Detection
    print("\n🔍 VM Detection:")
    processor = platform.processor().lower()
    platform_info = platform.platform().lower()
    
    vm_signs = ['virtual', 'vmware', 'virtualbox', 'qemu']
    vm_detected = any(sign in processor or sign in platform_info for sign in vm_signs)
    
    if vm_detected:
        print("🚨 Virtual Machine DETECTED")
    else:
        print("✅ Physical Machine CONFIRMED")
    
    # Hardware Fingerprint
    print("\n🔐 Hardware Fingerprint:")
    data = {
        'os': platform.system(),
        'processor': platform.processor(),
        'vm': vm_detected
    }
    
    fingerprint = hashlib.sha256(str(data).encode()).hexdigest()[:16]
    print(f"Fingerprint: {fingerprint}")
    
    # Browser Script
    print("\n🌐 Generating Browser Script...")
    script = '''
// VIP BIG BANG Browser Fingerprinting
const fingerprint = {
    deviceMemory: navigator.deviceMemory || 'unknown',
    hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
    screenWidth: screen.width,
    screenHeight: screen.height,
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    webdriver: navigator.webdriver
};
console.log('VIP Fingerprint:', fingerprint);
'''
    
    with open("vip_demo_script.js", "w") as f:
        f.write(script)
    
    print("✅ Script saved to: vip_demo_script.js")
    
    # Report
    report = {
        "timestamp": datetime.now().isoformat(),
        "system": platform.system(),
        "vm_detected": vm_detected,
        "fingerprint": fingerprint
    }
    
    with open("vip_demo_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("✅ Report saved to: vip_demo_report.json")
    
    print("\n🎉 Demo Completed Successfully!")
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Error: {e}")
