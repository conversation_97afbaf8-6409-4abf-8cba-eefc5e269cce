"""
🎯 VIP BIG BANG REAL DATA GENERATOR
Advanced System for Generating Realistic Trading Data for Testing
"""

import random
import time
import json
from datetime import datetime
from typing import Dict, List, Optional
import threading

class RealDataGenerator:
    """🎯 Advanced Real Data Generator for Testing"""
    
    def __init__(self):
        self.is_running = False
        self.generation_thread = None
        self.data_callbacks = []
        
        # Real trading pairs
        self.real_assets = [
            'EUR/USD', 'GBP/USD', 'USD/JPY', 'AUD/USD', 'USD/CAD',
            'USD/CHF', 'NZD/USD', 'EUR/GBP', 'EUR/JPY', 'GBP/JPY',
            'OTC EUR/USD', 'OTC GBP/USD', 'OTC USD/JPY', 'OTC AUD/USD',
            'BTC/USD', 'ETH/USD', 'LTC/USD', 'XRP/USD',
            '#AAPL', '#GOOGL', '#MSFT', '#AMZN', '#TSLA'
        ]
        
        # Current state
        self.current_asset = random.choice(self.real_assets)
        self.current_price = self.get_realistic_price(self.current_asset)
        self.current_balance = random.uniform(100.0, 50000.0)
        self.account_type = random.choice(['DEMO ACCOUNT', 'REAL ACCOUNT'])
        
        # Price movement parameters
        self.price_volatility = 0.0001
        self.trend_direction = random.choice([-1, 0, 1])
        self.trend_strength = random.uniform(0.1, 0.5)
        
        print("🎯 Real Data Generator initialized")
    
    def get_realistic_price(self, asset: str) -> float:
        """💰 Get realistic price for asset"""
        if 'EUR/USD' in asset:
            return random.uniform(1.05000, 1.12000)
        elif 'GBP/USD' in asset:
            return random.uniform(1.20000, 1.35000)
        elif 'USD/JPY' in asset:
            return random.uniform(140.000, 155.000)
        elif 'AUD/USD' in asset:
            return random.uniform(0.65000, 0.75000)
        elif 'USD/CAD' in asset:
            return random.uniform(1.25000, 1.40000)
        elif 'BTC/USD' in asset:
            return random.uniform(40000.0, 70000.0)
        elif 'ETH/USD' in asset:
            return random.uniform(2000.0, 4000.0)
        elif '#' in asset:  # Stocks
            return random.uniform(100.0, 500.0)
        else:
            return random.uniform(0.50000, 2.00000)
    
    def add_data_callback(self, callback):
        """📡 Add data callback"""
        self.data_callbacks.append(callback)
        print("📡 Real data callback added")
    
    def start_generation(self):
        """🚀 Start generating real data"""
        if self.is_running:
            print("⚠️ Generator already running")
            return
        
        self.is_running = True
        self.generation_thread = threading.Thread(target=self._generation_loop, daemon=True)
        self.generation_thread.start()
        print("🚀 Real Data Generator started")
    
    def stop_generation(self):
        """⏹️ Stop generating data"""
        self.is_running = False
        if self.generation_thread:
            self.generation_thread.join(timeout=1.0)
        print("⏹️ Real Data Generator stopped")
    
    def _generation_loop(self):
        """🔄 Main generation loop"""
        while self.is_running:
            try:
                # Generate new data
                data = self.generate_realistic_data()
                
                # Send to callbacks
                for callback in self.data_callbacks:
                    try:
                        callback(data)
                    except Exception as e:
                        print(f"❌ Callback error: {e}")
                
                # Wait before next generation
                time.sleep(random.uniform(2.0, 8.0))  # Random intervals
                
            except Exception as e:
                print(f"❌ Generation loop error: {e}")
                time.sleep(1.0)
    
    def generate_realistic_data(self) -> Dict:
        """🎯 Generate realistic trading data"""
        try:
            # Update price with realistic movement
            self.update_price()
            
            # Update balance with realistic changes
            self.update_balance()
            
            # Occasionally change asset
            if random.random() < 0.1:  # 10% chance
                self.current_asset = random.choice(self.real_assets)
                self.current_price = self.get_realistic_price(self.current_asset)
            
            # Occasionally change account type
            if random.random() < 0.05:  # 5% chance
                self.account_type = random.choice(['DEMO ACCOUNT', 'REAL ACCOUNT'])
            
            # Generate realistic data
            data = {
                'timestamp': datetime.now().isoformat(),
                'url': 'https://qxbroker.com/en/trade',
                'title': 'Live trading | Quotex',
                'scanNumber': random.randint(1, 1000),
                'extraction_method': 'real_data_generator_v1',
                'currentAsset': self.current_asset,
                'currentPrice': f"{self.current_price:.5f}",
                'balance': f"${self.current_balance:,.2f}",
                'accountType': self.account_type,
                'payout': f"{self.current_asset} {random.randint(75, 95)}%",
                'timeframe': random.choice(['5s', '10s', '15s', '30s', '1m']),
                'urlData': {
                    'url': 'https://qxbroker.com/en/trade',
                    'asset': self.current_asset.lower().replace('/', '').replace(' ', ''),
                    'symbol': None,
                    'pair': self.current_asset,
                    'timeframe': None,
                    'mode': 'live'
                },
                'titleData': {
                    'title': 'Live trading | Quotex',
                    'asset': self.current_asset,
                    'mode': 'Live'
                },
                'metaData': {
                    'viewport': 'width=device-width, initial-scale=1.0',
                    'description': 'Trade together with Quotex and earn extra profit on financial markets'
                },
                'scriptData': [],
                'source': 'REAL_DATA_GENERATOR',
                'server_received': time.time(),
                'todayProfit': f"${random.uniform(-100.0, 500.0):+.2f}",
                'winRate': f"{random.uniform(60.0, 95.0):.1f}%",
                'tradesCount': random.randint(5, 50),
                'spread': f"{random.uniform(0.1, 2.0):.1f}",
                'volume': random.randint(1000, 100000)
            }
            
            print(f"🎯 Generated realistic data: {self.current_asset} @ {self.current_price:.5f}")
            return data
            
        except Exception as e:
            print(f"❌ Data generation error: {e}")
            return {}
    
    def update_price(self):
        """💰 Update price with realistic movement"""
        try:
            # Base random movement
            base_change = random.uniform(-self.price_volatility, self.price_volatility)
            
            # Add trend component
            trend_change = self.trend_direction * self.trend_strength * self.price_volatility
            
            # Combine movements
            total_change = base_change + trend_change
            
            # Apply change
            self.current_price += total_change
            
            # Ensure price stays within realistic bounds
            min_price, max_price = self.get_price_bounds(self.current_asset)
            self.current_price = max(min_price, min(max_price, self.current_price))
            
            # Occasionally change trend
            if random.random() < 0.05:  # 5% chance
                self.trend_direction = random.choice([-1, 0, 1])
                self.trend_strength = random.uniform(0.1, 0.5)
            
        except Exception as e:
            print(f"❌ Price update error: {e}")
    
    def get_price_bounds(self, asset: str) -> tuple:
        """📊 Get realistic price bounds for asset"""
        if 'EUR/USD' in asset:
            return (1.00000, 1.20000)
        elif 'GBP/USD' in asset:
            return (1.15000, 1.40000)
        elif 'USD/JPY' in asset:
            return (130.000, 160.000)
        elif 'AUD/USD' in asset:
            return (0.60000, 0.80000)
        elif 'USD/CAD' in asset:
            return (1.20000, 1.45000)
        elif 'BTC/USD' in asset:
            return (30000.0, 80000.0)
        elif 'ETH/USD' in asset:
            return (1500.0, 5000.0)
        elif '#' in asset:  # Stocks
            return (50.0, 1000.0)
        else:
            return (0.10000, 3.00000)
    
    def update_balance(self):
        """💵 Update balance with realistic changes"""
        try:
            # Small random changes to balance
            if random.random() < 0.3:  # 30% chance of balance change
                change_percent = random.uniform(-0.05, 0.15)  # -5% to +15%
                change_amount = self.current_balance * change_percent
                self.current_balance += change_amount
                
                # Keep balance within reasonable bounds
                self.current_balance = max(0.01, min(1000000.0, self.current_balance))
                
        except Exception as e:
            print(f"❌ Balance update error: {e}")
    
    def generate_manual_data(self, asset: str = None, price: float = None, balance: float = None) -> Dict:
        """🎯 Generate specific data manually"""
        try:
            if asset:
                self.current_asset = asset
                if price is None:
                    self.current_price = self.get_realistic_price(asset)
            
            if price is not None:
                self.current_price = price
            
            if balance is not None:
                self.current_balance = balance
            
            return self.generate_realistic_data()
            
        except Exception as e:
            print(f"❌ Manual data generation error: {e}")
            return {}

# Global generator instance
real_data_generator = None

def get_real_data_generator():
    """🎯 Get global real data generator"""
    global real_data_generator
    if real_data_generator is None:
        real_data_generator = RealDataGenerator()
    return real_data_generator

def start_real_data_generation():
    """🚀 Start real data generation"""
    generator = get_real_data_generator()
    generator.start_generation()

def stop_real_data_generation():
    """⏹️ Stop real data generation"""
    generator = get_real_data_generator()
    generator.stop_generation()

def generate_sample_real_data():
    """🎯 Generate sample real data"""
    generator = get_real_data_generator()
    return generator.generate_realistic_data()

# Test function
def test_real_data_generator():
    """🧪 Test real data generator"""
    print("🧪 Testing Real Data Generator...")
    
    generator = RealDataGenerator()
    
    def data_callback(data):
        print(f"📊 Generated data: {data.get('currentAsset')} @ {data.get('currentPrice')}")
    
    generator.add_data_callback(data_callback)
    
    # Generate some test data
    for i in range(5):
        data = generator.generate_realistic_data()
        print(f"Test {i+1}: {data.get('currentAsset')} - {data.get('currentPrice')} - {data.get('balance')}")
        time.sleep(1)
    
    print("✅ Real Data Generator test completed")

if __name__ == "__main__":
    test_real_data_generator()
