console.log('🚀 VIP BIG BANG ADVANCED QUOTEX SCANNER - Version 5.0');

// Advanced Quotex Scanner - COMPLETE DOM SCANNING
class AdvancedQuotexScanner {
    constructor() {
        this.isRunning = false;
        this.scanInterval = null;
        this.websocket = null;
        this.lastData = {};
        this.scanCount = 0;
        this.realDataFound = false;
        
        console.log('🔍 Advanced Quotex Scanner initialized - COMPLETE SCANNING');
    }
    
    // Check if we're on Quotex
    isQuotexPage() {
        const url = window.location.href.toLowerCase();
        const title = document.title.toLowerCase();
        
        return url.includes('quotex.io') || 
               url.includes('quotex.com') ||
               url.includes('qxbroker.com') ||
               title.includes('quotex');
    }
    
    // COMPLETE DOM SCAN - Every element, every text
    performCompleteScan() {
        try {
            if (!this.isQuotexPage()) {
                console.log('⚠️ Not on Quotex page');
                return null;
            }
            
            this.scanCount++;
            console.log(`🔍 COMPLETE SCAN #${this.scanCount} - Scanning EVERY element...`);
            
            const scanResults = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                title: document.title,
                scanNumber: this.scanCount,
                extraction_method: 'advanced_complete_scanner_v5'
            };
            
            // SCAN 1: Complete element scan
            const allElements = document.querySelectorAll('*');
            console.log(`📊 Scanning ${allElements.length} elements...`);
            
            const foundData = {
                assets: [],
                prices: [],
                balances: [],
                accounts: [],
                payouts: [],
                timeframes: []
            };
            
            // Scan every single element
            allElements.forEach((element, index) => {
                try {
                    const text = element.textContent?.trim();
                    const innerHTML = element.innerHTML?.trim();
                    const className = element.className;
                    const id = element.id;
                    
                    if (!text || text.length > 100) return; // Skip empty or very long text
                    
                    // Asset detection patterns
                    if (this.isAssetPattern(text)) {
                        foundData.assets.push({
                            text: text,
                            element: element.tagName,
                            class: className,
                            id: id,
                            index: index
                        });
                    }
                    
                    // Price detection patterns
                    if (this.isPricePattern(text)) {
                        foundData.prices.push({
                            text: text,
                            element: element.tagName,
                            class: className,
                            id: id,
                            index: index
                        });
                    }
                    
                    // Balance detection patterns
                    if (this.isBalancePattern(text)) {
                        foundData.balances.push({
                            text: text,
                            element: element.tagName,
                            class: className,
                            id: id,
                            index: index
                        });
                    }
                    
                    // Account type detection
                    if (this.isAccountPattern(text)) {
                        foundData.accounts.push({
                            text: text,
                            element: element.tagName,
                            class: className,
                            id: id,
                            index: index
                        });
                    }
                    
                    // Payout detection
                    if (this.isPayoutPattern(text)) {
                        foundData.payouts.push({
                            text: text,
                            element: element.tagName,
                            class: className,
                            id: id,
                            index: index
                        });
                    }
                    
                    // Timeframe detection
                    if (this.isTimeframePattern(text)) {
                        foundData.timeframes.push({
                            text: text,
                            element: element.tagName,
                            class: className,
                            id: id,
                            index: index
                        });
                    }
                    
                } catch (e) {
                    // Continue scanning
                }
            });
            
            // Log all found data
            console.log('🔍 SCAN RESULTS:');
            console.log(`📊 Assets found: ${foundData.assets.length}`, foundData.assets);
            console.log(`💰 Prices found: ${foundData.prices.length}`, foundData.prices);
            console.log(`💵 Balances found: ${foundData.balances.length}`, foundData.balances);
            console.log(`🏦 Accounts found: ${foundData.accounts.length}`, foundData.accounts);
            console.log(`📈 Payouts found: ${foundData.payouts.length}`, foundData.payouts);
            console.log(`⏰ Timeframes found: ${foundData.timeframes.length}`, foundData.timeframes);
            
            // Select best data from findings
            scanResults.currentAsset = this.selectBestAsset(foundData.assets);
            scanResults.currentPrice = this.selectBestPrice(foundData.prices);
            scanResults.balance = this.selectBestBalance(foundData.balances);
            scanResults.accountType = this.selectBestAccount(foundData.accounts);
            scanResults.payout = this.selectBestPayout(foundData.payouts);
            scanResults.timeframe = this.selectBestTimeframe(foundData.timeframes);
            
            // Additional scan methods
            scanResults.urlData = this.scanURL();
            scanResults.titleData = this.scanTitle();
            scanResults.metaData = this.scanMetaTags();
            scanResults.scriptData = this.scanScripts();
            
            console.log('✅ FINAL SCAN RESULTS:', scanResults);
            
            // Send to robot
            this.sendToRobot(scanResults);
            
            this.lastData = scanResults;
            return scanResults;
            
        } catch (error) {
            console.error('❌ Complete scan error:', error);
            return null;
        }
    }
    
    // Pattern detection methods
    isAssetPattern(text) {
        if (!text || text.length < 3 || text.length > 20) return false;
        
        const patterns = [
            /^[A-Z]{3}\/[A-Z]{3}$/,           // EUR/USD
            /^OTC\s+[A-Z]{3}\/[A-Z]{3}$/,     // OTC EUR/USD
            /^[A-Z]{3}[A-Z]{3}$/,             // EURUSD
            /^[A-Z]{2,4}\/[A-Z]{2,4}$/,       // BTC/USD
            /^#[A-Z]+$/,                      // #AAPL
            /^[A-Z]+\.[A-Z]+$/                // STOCK.US
        ];
        
        return patterns.some(pattern => pattern.test(text.toUpperCase()));
    }
    
    isPricePattern(text) {
        if (!text) return false;
        
        const patterns = [
            /^\d+\.\d{2,5}$/,                 // 1.23456
            /^\d{1,3}(,\d{3})*\.\d{2,5}$/,    // 1,234.56789
            /^\d+,\d{2,5}$/,                  // European format
            /^\$?\d+\.\d{2,5}$/               // $1.23456
        ];
        
        return patterns.some(pattern => pattern.test(text.replace(/\s/g, '')));
    }
    
    isBalancePattern(text) {
        if (!text) return false;
        
        const patterns = [
            /\$\d{1,3}(,\d{3})*\.\d{2}/,      // $1,234.56
            /\d{1,3}(,\d{3})*\.\d{2}\s*\$/,   // 1,234.56 $
            /€\d{1,3}(,\d{3})*\.\d{2}/,       // €1,234.56
            /£\d{1,3}(,\d{3})*\.\d{2}/,       // £1,234.56
            /Balance.*\$?\d+\.\d{2}/i,        // Balance: $123.45
            /\$\d+\.\d{2}/                    // $123.45
        ];
        
        return patterns.some(pattern => pattern.test(text));
    }
    
    isAccountPattern(text) {
        if (!text) return false;
        
        const lowerText = text.toLowerCase();
        return lowerText.includes('demo') || 
               lowerText.includes('real') || 
               lowerText.includes('live') || 
               lowerText.includes('practice') ||
               lowerText.includes('account');
    }
    
    isPayoutPattern(text) {
        if (!text) return false;
        
        return /\d{1,3}%/.test(text) && 
               parseInt(text.match(/\d+/)?.[0] || 0) >= 70 && 
               parseInt(text.match(/\d+/)?.[0] || 0) <= 100;
    }
    
    isTimeframePattern(text) {
        if (!text) return false;
        
        return /\d+[smh]/.test(text) || 
               text.includes('second') || 
               text.includes('minute') || 
               text.includes('hour');
    }
    
    // Selection methods - choose best from multiple findings
    selectBestAsset(assets) {
        if (!assets || assets.length === 0) return null;
        
        // Prefer assets with specific classes or IDs
        const prioritized = assets.find(a => 
            a.class?.includes('asset') || 
            a.class?.includes('symbol') || 
            a.id?.includes('asset') ||
            a.id?.includes('symbol')
        );
        
        return prioritized ? prioritized.text : assets[0].text;
    }
    
    selectBestPrice(prices) {
        if (!prices || prices.length === 0) return null;
        
        // Prefer prices with specific classes or IDs
        const prioritized = prices.find(p => 
            p.class?.includes('price') || 
            p.class?.includes('quote') || 
            p.id?.includes('price') ||
            p.id?.includes('quote')
        );
        
        return prioritized ? prioritized.text : prices[0].text;
    }
    
    selectBestBalance(balances) {
        if (!balances || balances.length === 0) return null;
        
        // Prefer balances with specific classes or IDs
        const prioritized = balances.find(b => 
            b.class?.includes('balance') || 
            b.class?.includes('money') || 
            b.id?.includes('balance') ||
            b.id?.includes('money')
        );
        
        return prioritized ? prioritized.text : balances[0].text;
    }
    
    selectBestAccount(accounts) {
        if (!accounts || accounts.length === 0) return null;
        
        const account = accounts[0].text.toLowerCase();
        if (account.includes('demo') || account.includes('practice')) {
            return 'DEMO ACCOUNT';
        } else if (account.includes('real') || account.includes('live')) {
            return 'REAL ACCOUNT';
        }
        
        return accounts[0].text;
    }
    
    selectBestPayout(payouts) {
        if (!payouts || payouts.length === 0) return null;
        return payouts[0].text;
    }
    
    selectBestTimeframe(timeframes) {
        if (!timeframes || timeframes.length === 0) return null;
        return timeframes[0].text;
    }
    
    // Additional scanning methods
    scanURL() {
        const url = window.location.href;
        const params = new URLSearchParams(window.location.search);
        
        return {
            url: url,
            asset: params.get('asset'),
            symbol: params.get('symbol'),
            pair: params.get('pair'),
            timeframe: params.get('timeframe'),
            mode: params.get('mode')
        };
    }
    
    scanTitle() {
        const title = document.title;
        
        // Extract data from title
        const assetMatch = title.match(/([A-Z]{3}\/[A-Z]{3})/);
        const modeMatch = title.match(/(demo|real|live)/i);
        
        return {
            title: title,
            asset: assetMatch ? assetMatch[1] : null,
            mode: modeMatch ? modeMatch[1] : null
        };
    }
    
    scanMetaTags() {
        const metaTags = document.querySelectorAll('meta');
        const metaData = {};
        
        metaTags.forEach(meta => {
            const name = meta.getAttribute('name') || meta.getAttribute('property');
            const content = meta.getAttribute('content');
            
            if (name && content) {
                metaData[name] = content;
            }
        });
        
        return metaData;
    }
    
    scanScripts() {
        const scripts = document.querySelectorAll('script');
        const scriptData = [];
        
        scripts.forEach(script => {
            const src = script.src;
            const content = script.textContent;
            
            if (src && (src.includes('quotex') || src.includes('trading'))) {
                scriptData.push({ type: 'external', src: src });
            }
            
            if (content && content.includes('price') || content.includes('balance')) {
                // Look for data in script content
                const priceMatch = content.match(/price["\']?\s*:\s*["\']?(\d+\.\d+)/i);
                const balanceMatch = content.match(/balance["\']?\s*:\s*["\']?(\d+\.\d+)/i);
                
                if (priceMatch || balanceMatch) {
                    scriptData.push({
                        type: 'inline',
                        price: priceMatch ? priceMatch[1] : null,
                        balance: balanceMatch ? balanceMatch[1] : null
                    });
                }
            }
        });
        
        return scriptData;
    }
    
    // Send data to VIP BIG BANG robot
    sendToRobot(data) {
        try {
            console.log('📡 Sending COMPLETE SCAN data to VIP BIG BANG robot...');
            
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'complete_scan_data',
                    data: data
                }));
                console.log('✅ Complete scan data sent via existing WebSocket');
                return;
            }
            
            // Create new WebSocket connection
            this.websocket = new WebSocket('ws://localhost:8765');
            
            this.websocket.onopen = () => {
                console.log('✅ WebSocket connected to VIP BIG BANG robot');
                this.websocket.send(JSON.stringify({
                    type: 'complete_scan_data',
                    data: data
                }));
                console.log('✅ COMPLETE SCAN data sent to robot');
            };
            
            this.websocket.onmessage = (event) => {
                console.log('📨 Response from robot:', event.data);

                try {
                    const message = JSON.parse(event.data);
                    if (message.type === 'execute_trade') {
                        console.log('🤖 Trade command received:', message.data);
                        this.executeTradeOnQuotex(message.data);
                    }
                } catch (e) {
                    console.log('📨 Non-JSON response:', event.data);
                }
            };
            
            this.websocket.onclose = () => {
                console.log('🔌 WebSocket connection closed');
                this.websocket = null;
            };
            
            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
                this.websocket = null;
            };
            
        } catch (error) {
            console.error('❌ Send to robot error:', error);
        }
    }
    
    // Start complete scanning
    start() {
        if (this.isRunning) {
            console.log('⚠️ Scanner already running');
            return;
        }
        
        console.log('🚀 Starting Advanced Quotex Scanner - COMPLETE SCANNING...');
        this.isRunning = true;
        
        // Scan immediately
        this.performCompleteScan();
        
        // Set up periodic scanning
        this.scanInterval = setInterval(() => {
            this.performCompleteScan();
        }, 5000); // Every 5 seconds
        
        // Set up DOM monitoring for real-time changes
        this.startDOMMonitoring();
        
        console.log('✅ Advanced Quotex Scanner started with complete scanning');
    }
    
    // Monitor DOM changes
    startDOMMonitoring() {
        console.log('🔍 Starting DOM monitoring for real-time changes...');
        
        const observer = new MutationObserver((mutations) => {
            let hasRelevantChange = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const target = mutation.target;
                    const text = target.textContent || '';
                    
                    // Check for trading-related changes
                    if (text.match(/\d+\.\d{2,5}/) || 
                        text.includes('$') || 
                        text.includes('%') ||
                        text.match(/[A-Z]{3}\/[A-Z]{3}/)) {
                        hasRelevantChange = true;
                        console.log('🔄 Trading data change detected:', text.substring(0, 50));
                    }
                }
            });
            
            if (hasRelevantChange) {
                console.log('🔄 Relevant change detected, performing scan...');
                setTimeout(() => {
                    this.performCompleteScan();
                }, 2000); // Wait 2 seconds for changes to settle
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        console.log('✅ DOM monitoring started');
    }
    
    // Execute trade on Quotex
    executeTradeOnQuotex(tradeData) {
        try {
            console.log('🚀 EXECUTING TRADE ON QUOTEX:', tradeData);

            const signal = tradeData.signal; // 'BUY' or 'SELL'
            const amount = tradeData.amount || 10;
            const asset = tradeData.asset || 'EUR/USD';

            // Find trade buttons
            const callButtons = document.querySelectorAll('button, div, span').filter(el => {
                const text = el.textContent?.toLowerCase() || '';
                return text.includes('call') || text.includes('up') || text.includes('higher') || text.includes('↑');
            });

            const putButtons = document.querySelectorAll('button, div, span').filter(el => {
                const text = el.textContent?.toLowerCase() || '';
                return text.includes('put') || text.includes('down') || text.includes('lower') || text.includes('↓');
            });

            // Set amount if possible
            this.setTradeAmount(amount);

            // Execute trade based on signal
            if (signal === 'BUY' || signal === 'CALL') {
                console.log('📈 Executing CALL trade...');
                this.clickTradeButton(callButtons, 'CALL');
            } else if (signal === 'SELL' || signal === 'PUT') {
                console.log('📉 Executing PUT trade...');
                this.clickTradeButton(putButtons, 'PUT');
            }

            // Send confirmation back to robot
            this.sendTradeConfirmation(tradeData, 'EXECUTED');

        } catch (error) {
            console.error('❌ Trade execution error:', error);
            this.sendTradeConfirmation(tradeData, 'FAILED', error.message);
        }
    }

    // Set trade amount
    setTradeAmount(amount) {
        try {
            // Look for amount input fields
            const amountInputs = document.querySelectorAll('input[type="number"], input[type="text"]');

            for (const input of amountInputs) {
                const placeholder = input.placeholder?.toLowerCase() || '';
                const label = input.getAttribute('aria-label')?.toLowerCase() || '';
                const parentText = input.parentElement?.textContent?.toLowerCase() || '';

                if (placeholder.includes('amount') ||
                    label.includes('amount') ||
                    parentText.includes('amount') ||
                    parentText.includes('$')) {

                    console.log(`💰 Setting amount to ${amount}`);
                    input.value = amount;
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));
                    break;
                }
            }
        } catch (error) {
            console.error('❌ Amount setting error:', error);
        }
    }

    // Click trade button
    clickTradeButton(buttons, type) {
        try {
            if (buttons.length > 0) {
                const button = buttons[0];
                console.log(`🎯 Clicking ${type} button:`, button);

                // Simulate human-like click
                button.scrollIntoView({ behavior: 'smooth', block: 'center' });

                setTimeout(() => {
                    button.click();
                    console.log(`✅ ${type} button clicked`);
                }, 500);

                return true;
            } else {
                console.log(`⚠️ No ${type} buttons found`);
                return false;
            }
        } catch (error) {
            console.error(`❌ ${type} button click error:`, error);
            return false;
        }
    }

    // Send trade confirmation
    sendTradeConfirmation(tradeData, status, error = null) {
        try {
            const confirmation = {
                type: 'trade_confirmation',
                data: {
                    ...tradeData,
                    status: status,
                    executed_at: new Date().toISOString(),
                    error: error
                }
            };

            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify(confirmation));
                console.log(`✅ Trade confirmation sent: ${status}`);
            }
        } catch (error) {
            console.error('❌ Trade confirmation error:', error);
        }
    }

    // Stop scanning
    stop() {
        console.log('⏹️ Stopping Advanced Quotex Scanner...');
        this.isRunning = false;

        if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }

        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }

        console.log('✅ Advanced Quotex Scanner stopped');
    }
}

// Initialize and start the advanced scanner
const advancedScanner = new AdvancedQuotexScanner();

// Wait for page to load
function waitForPageLoad() {
    if (document.readyState === 'complete') {
        console.log('✅ Page loaded, starting Advanced Quotex Scanner...');
        setTimeout(() => {
            advancedScanner.start();
        }, 3000); // Wait 3 seconds after page load
    } else {
        console.log('⏳ Waiting for page to load...');
        window.addEventListener('load', () => {
            console.log('✅ Page loaded, starting Advanced Quotex Scanner...');
            setTimeout(() => {
                advancedScanner.start();
            }, 3000);
        });
    }
}

// Start when script loads
console.log('🚀 VIP BIG BANG Advanced Quotex Scanner: Initializing...');
waitForPageLoad();
