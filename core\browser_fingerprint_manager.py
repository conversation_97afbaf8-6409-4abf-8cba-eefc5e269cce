"""
🌐 BROWSER FINGERPRINT MANAGER
🔍 مدیریت fingerprinting مرورگر و ادغام با Chrome Extension
🛡️ شامل anti-detection و stealth technology
"""

import json
import asyncio
import websockets
import logging
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebEngineCore import QWebEngineScript

class BrowserFingerprintManager(QObject):
    """
    🌐 مدیر fingerprinting مرورگر
    ادغام با Chrome Extension و تشخیص پیشرفته
    """
    
    # Signals
    fingerprint_received = Signal(dict)
    chrome_extension_connected = Signal(bool)
    suspicious_activity_detected = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("BrowserFingerprintManager")
        
        # Browser fingerprint data
        self.browser_fingerprint = {}
        self.chrome_extension_active = False
        self.websocket_server = None
        self.websocket_port = 8765
        
        # Suspicious activity monitoring
        self.suspicious_indicators = []
        self.vm_detection_results = {}
        
        # WebSocket server for communication
        self.start_websocket_server()
        
        self.logger.info("🌐 Browser Fingerprint Manager initialized")
    
    def start_websocket_server(self):
        """🔌 راه‌اندازی WebSocket server برای ارتباط با مرورگر"""
        try:
            def run_server():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    async def handle_client(websocket, path):
                        try:
                            async for message in websocket:
                                data = json.loads(message)
                                await self.handle_browser_message(data)
                        except Exception as e:
                            self.logger.error(f"❌ WebSocket client error: {e}")

                    start_server = websockets.serve(handle_client, "localhost", self.websocket_port)
                    loop.run_until_complete(start_server)
                    loop.run_forever()
                except Exception as e:
                    self.logger.warning(f"⚠️ WebSocket server failed to start: {e}")

            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            self.logger.info(f"🔌 WebSocket server starting on port {self.websocket_port}")

        except Exception as e:
            self.logger.warning(f"⚠️ WebSocket server startup failed: {e}")
    
    async def handle_browser_message(self, data):
        """📨 پردازش پیام‌های دریافتی از مرورگر"""
        try:
            message_type = data.get("type", "")
            
            if message_type == "browser_fingerprint":
                await self.process_browser_fingerprint(data.get("data", {}))
            elif message_type == "chrome_extension_status":
                self.chrome_extension_active = data.get("active", False)
                self.chrome_extension_connected.emit(self.chrome_extension_active)
            elif message_type == "suspicious_activity":
                await self.handle_suspicious_activity(data.get("data", {}))
            
        except Exception as e:
            self.logger.error(f"❌ Browser message handling error: {e}")
    
    async def process_browser_fingerprint(self, fingerprint_data):
        """🔍 پردازش fingerprint دریافتی از مرورگر"""
        try:
            self.browser_fingerprint = fingerprint_data
            
            # تحلیل fingerprint
            analysis_results = self.analyze_browser_fingerprint(fingerprint_data)
            
            # ترکیب با نتایج تحلیل
            complete_fingerprint = {
                "raw_data": fingerprint_data,
                "analysis": analysis_results,
                "timestamp": datetime.now().isoformat(),
                "source": "browser_javascript"
            }
            
            # ارسال سیگنال
            self.fingerprint_received.emit(complete_fingerprint)
            
            self.logger.info("🔍 Browser fingerprint processed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Browser fingerprint processing error: {e}")
    
    def analyze_browser_fingerprint(self, fingerprint_data):
        """🔍 تحلیل fingerprint مرورگر"""
        try:
            analysis = {
                "vm_indicators": [],
                "bot_indicators": [],
                "risk_score": 0,
                "device_type": "unknown",
                "browser_type": "unknown",
                "os_type": "unknown"
            }
            
            # تحلیل GPU برای تشخیص VM
            gpu_vendor = fingerprint_data.get("gpuVendor", "").lower()
            gpu_renderer = fingerprint_data.get("gpuRenderer", "").lower()
            
            vm_gpu_indicators = [
                "microsoft basic", "vmware", "virtualbox", "parallels", 
                "qemu", "swiftshader", "llvmpipe", "software"
            ]
            
            for indicator in vm_gpu_indicators:
                if indicator in gpu_vendor or indicator in gpu_renderer:
                    analysis["vm_indicators"].append(f"GPU: {indicator}")
                    analysis["risk_score"] += 25
            
            # تحلیل RAM
            device_memory = fingerprint_data.get("deviceMemory", 0)
            if isinstance(device_memory, (int, float)) and device_memory <= 4:
                analysis["vm_indicators"].append("Low RAM (≤4GB)")
                analysis["risk_score"] += 15
            
            # تحلیل CPU
            hardware_concurrency = fingerprint_data.get("hardwareConcurrency", 0)
            if isinstance(hardware_concurrency, int) and hardware_concurrency <= 2:
                analysis["vm_indicators"].append("Low CPU cores (≤2)")
                analysis["risk_score"] += 10
            
            # تشخیص WebDriver
            if fingerprint_data.get("webdriver"):
                analysis["bot_indicators"].append("WebDriver detected")
                analysis["risk_score"] += 50
            
            # تشخیص Phantom/Selenium
            if fingerprint_data.get("phantom") or fingerprint_data.get("selenium"):
                analysis["bot_indicators"].append("Automation tool detected")
                analysis["risk_score"] += 50
            
            # تحلیل User Agent
            user_agent = fingerprint_data.get("userAgent", "").lower()
            if "headless" in user_agent or "phantom" in user_agent:
                analysis["bot_indicators"].append("Headless browser detected")
                analysis["risk_score"] += 40
            
            # تشخیص نوع دستگاه
            if fingerprint_data.get("maxTouchPoints", 0) > 0:
                analysis["device_type"] = "mobile/tablet"
            else:
                analysis["device_type"] = "desktop"
            
            # تشخیص مرورگر
            if "chrome" in user_agent:
                analysis["browser_type"] = "chrome"
            elif "firefox" in user_agent:
                analysis["browser_type"] = "firefox"
            elif "safari" in user_agent:
                analysis["browser_type"] = "safari"
            elif "edge" in user_agent:
                analysis["browser_type"] = "edge"
            
            # تشخیص سیستم‌عامل
            platform = fingerprint_data.get("platform", "").lower()
            if "win" in platform:
                analysis["os_type"] = "windows"
            elif "mac" in platform:
                analysis["os_type"] = "macos"
            elif "linux" in platform:
                analysis["os_type"] = "linux"
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ Fingerprint analysis error: {e}")
            return {"error": str(e)}
    
    async def handle_suspicious_activity(self, activity_data):
        """🚨 پردازش فعالیت مشکوک"""
        try:
            self.suspicious_indicators.append({
                "activity": activity_data,
                "timestamp": datetime.now().isoformat()
            })
            
            # ارسال سیگنال
            self.suspicious_activity_detected.emit(activity_data)
            
            self.logger.warning(f"🚨 Suspicious activity detected: {activity_data}")
            
        except Exception as e:
            self.logger.error(f"❌ Suspicious activity handling error: {e}")
    
    def inject_fingerprint_script(self, web_view: QWebEngineView):
        """💉 تزریق اسکریپت fingerprinting به WebView"""
        try:
            from core.advanced_hardware_detector import AdvancedHardwareDetector
            
            # دریافت اسکریپت fingerprinting
            detector = AdvancedHardwareDetector()
            script_content = detector.get_browser_fingerprint_script()
            
            # ایجاد QWebEngineScript
            script = QWebEngineScript()
            script.setSourceCode(script_content)
            script.setName("VIP_BIG_BANG_Fingerprinting")
            script.setWorldId(QWebEngineScript.ScriptWorldId.MainWorld)
            script.setInjectionPoint(QWebEngineScript.InjectionPoint.DocumentReady)
            script.setRunsOnSubFrames(True)
            
            # تزریق اسکریپت
            web_view.page().scripts().insert(script)
            
            self.logger.info("💉 Fingerprinting script injected successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Script injection error: {e}")
    
    def get_chrome_extension_script(self):
        """🔧 تولید اسکریپت برای Chrome Extension"""
        return """
        // 🚀 VIP BIG BANG Chrome Extension Enhanced Fingerprinting
        
        // Enhanced hardware detection
        function getEnhancedHardwareInfo() {
            const info = {
                // Basic info
                deviceMemory: navigator.deviceMemory,
                hardwareConcurrency: navigator.hardwareConcurrency,
                
                // Screen info
                screen: {
                    width: screen.width,
                    height: screen.height,
                    colorDepth: screen.colorDepth,
                    pixelDepth: screen.pixelDepth,
                    availWidth: screen.availWidth,
                    availHeight: screen.availHeight
                },
                
                // Browser info
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                languages: navigator.languages,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack,
                maxTouchPoints: navigator.maxTouchPoints,
                
                // Timezone
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                timezoneOffset: new Date().getTimezoneOffset(),
                
                // WebDriver detection
                webdriver: navigator.webdriver,
                
                // Automation detection
                phantom: window.callPhantom || window._phantom,
                selenium: window.document.$cdc_asdjflasutopfhvcZLmcfl_,
                
                // Chrome specific
                chrome: !!window.chrome,
                chromeRuntime: !!(window.chrome && window.chrome.runtime),
                
                // Performance
                connection: navigator.connection ? {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt
                } : null
            };
            
            return info;
        }
        
        // Enhanced GPU detection
        function getEnhancedGPUInfo() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (!gl) return { error: 'WebGL not supported' };
                
                const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                const gpuInfo = {
                    vendor: 'unknown',
                    renderer: 'unknown',
                    version: gl.getParameter(gl.VERSION),
                    shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                    extensions: gl.getSupportedExtensions()
                };
                
                if (debugInfo) {
                    gpuInfo.vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                    gpuInfo.renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                }
                
                // Additional GPU parameters
                gpuInfo.maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
                gpuInfo.maxViewportDims = gl.getParameter(gl.MAX_VIEWPORT_DIMS);
                gpuInfo.maxVertexAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
                
                return gpuInfo;
            } catch (e) {
                return { error: e.message };
            }
        }
        
        // VM Detection
        function detectVirtualMachine() {
            const indicators = [];
            let confidence = 0;
            
            // GPU-based detection
            const gpuInfo = getEnhancedGPUInfo();
            const gpuRenderer = (gpuInfo.renderer || '').toLowerCase();
            const gpuVendor = (gpuInfo.vendor || '').toLowerCase();
            
            const vmGpuIndicators = [
                'microsoft basic', 'vmware', 'virtualbox', 'parallels',
                'qemu', 'swiftshader', 'llvmpipe', 'software'
            ];
            
            vmGpuIndicators.forEach(indicator => {
                if (gpuRenderer.includes(indicator) || gpuVendor.includes(indicator)) {
                    indicators.push(`GPU: ${indicator}`);
                    confidence += 30;
                }
            });
            
            // Memory-based detection
            if (navigator.deviceMemory && navigator.deviceMemory <= 4) {
                indicators.push('Low memory (≤4GB)');
                confidence += 20;
            }
            
            // CPU-based detection
            if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
                indicators.push('Low CPU cores (≤2)');
                confidence += 15;
            }
            
            // Screen resolution detection
            if (screen.width === 1024 && screen.height === 768) {
                indicators.push('Common VM resolution (1024x768)');
                confidence += 10;
            }
            
            return {
                isVM: confidence >= 50,
                confidence: confidence,
                indicators: indicators
            };
        }
        
        // Collect all fingerprint data
        function collectFingerprint() {
            return {
                hardware: getEnhancedHardwareInfo(),
                gpu: getEnhancedGPUInfo(),
                vmDetection: detectVirtualMachine(),
                timestamp: new Date().toISOString(),
                url: window.location.href
            };
        }
        
        // Send fingerprint to Python application
        function sendFingerprintToPython() {
            const fingerprint = collectFingerprint();
            
            // Try WebSocket first
            try {
                const ws = new WebSocket('ws://localhost:8765');
                ws.onopen = function() {
                    ws.send(JSON.stringify({
                        type: 'browser_fingerprint',
                        data: fingerprint
                    }));
                    ws.close();
                };
            } catch (e) {
                console.log('WebSocket failed:', e);
            }
            
            // Store in window for extension access
            window.VIP_BIG_BANG_FINGERPRINT = fingerprint;
            
            return fingerprint;
        }
        
        // Auto-execute
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', sendFingerprintToPython);
        } else {
            sendFingerprintToPython();
        }
        """
    
    def get_complete_fingerprint(self):
        """📊 دریافت fingerprint کامل"""
        return {
            "browser_fingerprint": self.browser_fingerprint,
            "chrome_extension_active": self.chrome_extension_active,
            "suspicious_indicators": self.suspicious_indicators,
            "last_update": datetime.now().isoformat()
        }
    
    def is_suspicious_environment(self):
        """🚨 بررسی محیط مشکوک"""
        if not self.browser_fingerprint:
            return False
        
        analysis = self.browser_fingerprint.get("analysis", {})
        risk_score = analysis.get("risk_score", 0)
        
        return risk_score >= 50
    
    def get_risk_score(self):
        """📊 دریافت امتیاز ریسک"""
        if not self.browser_fingerprint:
            return 0
        
        analysis = self.browser_fingerprint.get("analysis", {})
        return analysis.get("risk_score", 0)
    
    def log_fingerprint_summary(self):
        """📊 نمایش خلاصه fingerprint"""
        try:
            if not self.browser_fingerprint:
                self.logger.warning("⚠️ No browser fingerprint available")
                return
            
            self.logger.info("🌐 === BROWSER FINGERPRINT SUMMARY ===")
            
            raw_data = self.browser_fingerprint.get("raw_data", {})
            analysis = self.browser_fingerprint.get("analysis", {})
            
            # Hardware info
            self.logger.info(f"💻 RAM: {raw_data.get('deviceMemory', 'Unknown')}GB")
            self.logger.info(f"🧠 CPU Cores: {raw_data.get('hardwareConcurrency', 'Unknown')}")
            self.logger.info(f"🎮 GPU: {raw_data.get('gpuRenderer', 'Unknown')}")
            
            # Screen info
            self.logger.info(f"📺 Screen: {raw_data.get('screenWidth', 0)}x{raw_data.get('screenHeight', 0)}")
            
            # Browser info
            self.logger.info(f"🌐 Browser: {analysis.get('browser_type', 'Unknown')}")
            self.logger.info(f"🖥️ OS: {analysis.get('os_type', 'Unknown')}")
            self.logger.info(f"📱 Device: {analysis.get('device_type', 'Unknown')}")
            
            # Risk assessment
            risk_score = analysis.get("risk_score", 0)
            if risk_score >= 50:
                self.logger.warning(f"🚨 HIGH RISK: Score {risk_score}/100")
                for indicator in analysis.get("vm_indicators", []):
                    self.logger.warning(f"   VM: {indicator}")
                for indicator in analysis.get("bot_indicators", []):
                    self.logger.warning(f"   BOT: {indicator}")
            else:
                self.logger.info(f"✅ LOW RISK: Score {risk_score}/100")
            
            self.logger.info("🏆 === FINGERPRINT ANALYSIS COMPLETE ===")
            
        except Exception as e:
            self.logger.error(f"❌ Fingerprint summary error: {e}")
