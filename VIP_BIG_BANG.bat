@echo off
title VIP BIG BANG - Ultimate Trading Bot
color 0D

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                    🎮 VIP BIG BANG 🎮                        ║
echo ║                                                              ║
echo ║              Ultimate Trading Bot Interface                  ║
echo ║                                                              ║
echo ║                     🚀 STARTING... 🚀                       ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo 📦 Please install Python from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check if PySide6 is installed
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing PySide6...
    python -m pip install PySide6
    if errorlevel 1 (
        echo ❌ Failed to install PySide6
        pause
        exit /b 1
    )
    echo ✅ PySide6 installed successfully
)

echo ✅ PySide6 ready
echo.

REM Launch VIP BIG BANG Professional Launcher
echo 🚀 Launching VIP BIG BANG Professional Launcher...
python vip_big_bang_launcher.py

REM If launcher fails, try main.py
if errorlevel 1 (
    echo.
    echo 🔄 Launcher failed, trying main.py...
    python main.py
)

REM If main.py fails, try direct quotex system
if errorlevel 1 (
    echo.
    echo 🔄 Main failed, trying direct system...
    python vip_real_quotex_main.py
)

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo ❌ All launch methods failed
    echo 📋 Please check:
    echo    1. Python installation
    echo    2. Dependencies: pip install -r requirements.txt
    echo    3. Core modules are present
    pause
)
