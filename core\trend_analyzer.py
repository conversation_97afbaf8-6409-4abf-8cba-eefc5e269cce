"""
VIP BIG BANG Enterprise - Trend Analyzer
Comprehensive trend analysis combining multiple indicators
"""

import numpy as np
import pandas as pd
from typing import Dict
import logging
from datetime import datetime

class TrendAnalyzer:
    """
    Trend Analyzer - Original VIP BIG BANG indicator
    Analyzes overall market trend (bullish, bearish, or sideways)
    Combines multiple indicators for comprehensive trend detection
    Displays current market status in real-time
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("TrendAnalyzer")
        
        # Trend analysis parameters
        self.short_period = 5
        self.medium_period = 14
        self.long_period = 21
        self.trend_strength_threshold = 0.002  # 0.2%
        
        # Moving average periods for trend confirmation
        self.ma_fast = 8
        self.ma_slow = 21
        
        self.logger.debug("Trend Analyzer initialized")
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main trend analysis function
        Returns comprehensive trend analysis
        """
        try:
            if len(data) < self.long_period:
                return {
                    "score": 0.5,
                    "direction": "NEUTRAL",
                    "confidence": 0.0,
                    "details": "Insufficient data for trend analysis"
                }
            
            prices = data["close"] if "close" in data.columns else data["price"]
            
            # Simple trend calculation using slope
            recent_prices = prices.tail(self.medium_period)
            x = np.arange(len(recent_prices))
            y = recent_prices.values
            
            slope = np.polyfit(x, y, 1)[0]
            avg_price = recent_prices.mean()
            normalized_slope = slope / avg_price if avg_price != 0 else 0
            
            # Calculate score
            score = 0.5
            if normalized_slope > self.trend_strength_threshold:
                score += min(abs(normalized_slope) * 250, 0.4)
                direction = "UP"
            elif normalized_slope < -self.trend_strength_threshold:
                score -= min(abs(normalized_slope) * 250, 0.4)
                direction = "DOWN"
            else:
                direction = "NEUTRAL"
            
            score = max(0, min(1, score))
            confidence = min(abs(normalized_slope) * 500, 1.0)
            
            return {
                "score": score,
                "direction": direction,
                "confidence": confidence,
                "details": f"Trend: {direction}, Slope: {normalized_slope:.6f}"
            }
            
        except Exception as e:
            self.logger.error(f"Trend analysis error: {e}")
            return {
                "score": 0.5,
                "direction": "NEUTRAL",
                "confidence": 0.0,
                "details": f"Trend analysis failed: {str(e)}"
            }
