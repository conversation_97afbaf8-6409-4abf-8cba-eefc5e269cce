"""
VIP BIG BANG Final Professional UI
Single file implementation with proper styling
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
import logging

class VIPBigBangFinalUI(QMainWindow):
    """Final professional VIP BIG BANG UI exactly matching the design"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("VIPBigBangFinalUI")
        
        # Window setup
        self.setWindowTitle("VIP BIG BANG")
        self.setGeometry(100, 100, 1024, 600)
        self.setFixedSize(1024, 600)
        
        # Apply professional styles
        self._apply_styles()
        
        # Setup UI
        self._setup_ui()
        
        self.logger.info("VIP BIG BANG Final UI initialized")
    
    def _apply_styles(self):
        """Apply professional VIP BIG BANG styles"""
        style = """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #2D1B69, stop:1 #1A0F3D);
        }
        
        QWidget {
            background: transparent;
            color: white;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 12px;
        }
        
        .vip-panel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(75, 50, 150, 0.8), stop:1 rgba(45, 27, 105, 0.9));
            border: 2px solid rgba(120, 80, 200, 0.5);
            border-radius: 15px;
            padding: 10px;
        }
        
        .vip-btn {
            background: rgba(75, 50, 150, 0.6);
            border: 1px solid rgba(120, 80, 200, 0.8);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .vip-btn:hover {
            background: rgba(120, 80, 200, 0.8);
        }
        
        .vip-btn-active {
            background: #32C832;
            border-color: #228B22;
        }
        
        .vip-btn-buy {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #32C832, stop:1 #228B22);
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-size: 14px;
        }
        
        .vip-btn-sell {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #FF4444, stop:1 #CC2222);
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-size: 14px;
        }
        
        .vip-btn-control {
            background: rgba(75, 50, 150, 0.8);
            border: 2px solid rgba(120, 80, 200, 0.6);
            border-radius: 15px;
            color: white;
            font-weight: bold;
        }
        
        .vip-btn-control:hover {
            background: rgba(120, 80, 200, 0.9);
        }
        
        QProgressBar {
            background: rgba(50, 50, 50, 0.8);
            border: 1px solid rgba(120, 80, 200, 0.5);
            border-radius: 10px;
            text-align: center;
            color: white;
            font-weight: bold;
            height: 20px;
        }
        
        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #32C832, stop:1 #228B22);
            border-radius: 8px;
        }
        """
        self.setStyleSheet(style)
    
    def _setup_ui(self):
        """Setup the main UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Header
        header = self._create_header()
        main_layout.addWidget(header)
        
        # Content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)
        
        # Left panel
        left_panel = self._create_left_panel()
        content_layout.addWidget(left_panel)
        
        # Center panel
        center_panel = self._create_center_panel()
        content_layout.addWidget(center_panel)
        
        # Right panel
        right_panel = self._create_right_panel()
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
    
    def _create_header(self):
        """Create header"""
        header = QFrame()
        header.setProperty("class", "vip-panel")
        header.setFixedHeight(60)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Left - Currency pairs
        pairs_layout = QHBoxLayout()
        
        bug_usd = QPushButton("✓ BUG/USD")
        bug_usd.setProperty("class", "vip-btn-active")
        pairs_layout.addWidget(bug_usd)
        
        gbp_usd = QPushButton("GBP/USD")
        gbp_usd.setProperty("class", "vip-btn")
        pairs_layout.addWidget(gbp_usd)
        
        eur_jpy = QPushButton("EUR/JPY")
        eur_jpy.setProperty("class", "vip-btn")
        pairs_layout.addWidget(eur_jpy)
        
        live_btn = QPushButton("LIVE")
        live_btn.setProperty("class", "vip-btn")
        pairs_layout.addWidget(live_btn)
        
        layout.addLayout(pairs_layout)
        layout.addStretch()
        
        # Center - Title
        title = QLabel("VIP BIG BANG")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")
        layout.addWidget(title)
        layout.addStretch()
        
        # Right - Trading buttons
        right_layout = QHBoxLayout()
        
        buy_label = QLabel("BUY")
        buy_label.setStyleSheet("color: white; font-weight: bold;")
        right_layout.addWidget(buy_label)
        
        buy_btn = QPushButton("BUY")
        buy_btn.setProperty("class", "vip-btn-buy")
        right_layout.addWidget(buy_btn)
        
        sell_btn = QPushButton("SELL")
        sell_btn.setProperty("class", "vip-btn-sell")
        right_layout.addWidget(sell_btn)
        
        otc_btn = QPushButton("OTC")
        otc_btn.setProperty("class", "vip-btn")
        right_layout.addWidget(otc_btn)
        
        live_btn2 = QPushButton("LIVE")
        live_btn2.setProperty("class", "vip-btn")
        right_layout.addWidget(live_btn2)
        
        demo_btn = QPushButton("DEMO")
        demo_btn.setProperty("class", "vip-btn")
        right_layout.addWidget(demo_btn)
        
        menu_btn = QPushButton("≡")
        menu_btn.setProperty("class", "vip-btn")
        menu_btn.setFixedSize(40, 30)
        right_layout.addWidget(menu_btn)
        
        layout.addLayout(right_layout)
        return header
    
    def _create_left_panel(self):
        """Create left panel"""
        panel = QFrame()
        panel.setProperty("class", "vip-panel")
        panel.setFixedWidth(180)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Manual Trading
        manual_frame = QFrame()
        manual_frame.setProperty("class", "vip-panel")
        manual_layout = QVBoxLayout(manual_frame)
        
        cursor_icon = QLabel("🖱️")
        cursor_icon.setAlignment(Qt.AlignCenter)
        cursor_icon.setStyleSheet("font-size: 24px; margin-bottom: 5px;")
        manual_layout.addWidget(cursor_icon)
        
        manual_text = QLabel("Manual Trading")
        manual_text.setAlignment(Qt.AlignCenter)
        manual_text.setStyleSheet("color: white; font-weight: bold; font-size: 12px;")
        manual_layout.addWidget(manual_text)
        
        toggle_frame = QFrame()
        toggle_frame.setFixedHeight(30)
        toggle_frame.setStyleSheet("background: #32C832; border-radius: 15px; margin: 5px;")
        manual_layout.addWidget(toggle_frame)
        
        layout.addWidget(manual_frame)
        
        # Account Summary
        account_frame = QFrame()
        account_frame.setProperty("class", "vip-panel")
        account_layout = QVBoxLayout(account_frame)
        
        account_title = QLabel("Account Summary")
        account_title.setAlignment(Qt.AlignCenter)
        account_title.setStyleSheet("color: white; font-weight: bold; font-size: 12px; margin-bottom: 10px;")
        account_layout.addWidget(account_title)
        
        balance_label = QLabel("$1251,76")
        balance_label.setAlignment(Qt.AlignCenter)
        balance_label.setStyleSheet("font-size: 24px; color: #32C832; font-weight: bold;")
        account_layout.addWidget(balance_label)
        
        layout.addWidget(account_frame)
        
        # AutoTrade
        autotrade_frame = QFrame()
        autotrade_frame.setProperty("class", "vip-panel")
        autotrade_layout = QVBoxLayout(autotrade_frame)
        
        autotrade_label = QLabel("AutoTrade ON")
        autotrade_label.setStyleSheet("color: #32C832; font-weight: bold; font-size: 12px;")
        autotrade_layout.addWidget(autotrade_label)
        
        trade_stats = QLabel("Trade $ +5\\nProfit / Loss +10")
        trade_stats.setStyleSheet("color: white; font-size: 10px; margin-top: 5px;")
        autotrade_layout.addWidget(trade_stats)
        
        layout.addWidget(autotrade_frame)
        
        # PulseBar
        pulsebar_frame = QFrame()
        pulsebar_frame.setProperty("class", "vip-panel")
        pulsebar_layout = QVBoxLayout(pulsebar_frame)
        
        pulsebar_title = QLabel("PulseBar")
        pulsebar_title.setAlignment(Qt.AlignCenter)
        pulsebar_title.setStyleSheet("color: white; font-weight: bold; font-size: 12px; margin-bottom: 10px;")
        pulsebar_layout.addWidget(pulsebar_title)
        
        colors = ["#FF4444", "#FF8844", "#FFFF44", "#88FF44", "#44FF44"]
        for color in colors:
            bar = QFrame()
            bar.setFixedHeight(8)
            bar.setStyleSheet(f"background: {color}; border-radius: 4px; margin: 1px;")
            pulsebar_layout.addWidget(bar)
        
        layout.addWidget(pulsebar_frame)
        layout.addStretch()
        return panel

    def _create_center_panel(self):
        """Create center panel"""
        panel = QFrame()
        panel.setProperty("class", "vip-panel")

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Chart section
        chart_frame = QFrame()
        chart_frame.setProperty("class", "vip-panel")
        chart_frame.setFixedHeight(250)
        chart_layout = QVBoxLayout(chart_frame)

        # Price header
        price_layout = QHBoxLayout()

        alert_icon = QLabel("🔔")
        alert_icon.setStyleSheet("font-size: 24px; color: #FFD700;")
        price_layout.addWidget(alert_icon)

        price_layout.addStretch()

        price_label = QLabel("1.07329")
        price_label.setStyleSheet("font-size: 18px; color: white; background: rgba(50, 200, 50, 0.8); padding: 5px 10px; border-radius: 5px;")
        price_layout.addWidget(price_label)

        price_values = QLabel("1.07325\\n1.07320\\n1.07320\\n1.07330")
        price_values.setStyleSheet("font-size: 10px; color: #888888;")
        price_layout.addWidget(price_values)

        chart_layout.addLayout(price_layout)

        # Chart area
        chart_area = QLabel("📈 CANDLESTICK CHART")
        chart_area.setAlignment(Qt.AlignCenter)
        chart_area.setStyleSheet("background: rgba(0, 0, 0, 0.3); border: 1px solid #444; border-radius: 5px; color: #888; font-size: 14px; min-height: 180px;")
        chart_layout.addWidget(chart_area)

        layout.addWidget(chart_frame)

        # Vortex section
        vortex_frame = QFrame()
        vortex_frame.setProperty("class", "vip-panel")
        vortex_layout = QVBoxLayout(vortex_frame)

        vortex_title = QLabel("VORTEX")
        vortex_title.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        vortex_layout.addWidget(vortex_title)

        vortex_indicator = QLabel("〰️〰️〰️〰️〰️〰️〰️〰️")
        vortex_indicator.setStyleSheet("color: #4488FF; font-size: 16px;")
        vortex_indicator.setAlignment(Qt.AlignCenter)
        vortex_layout.addWidget(vortex_indicator)

        vortex_value = QLabel("0.0436")
        vortex_value.setStyleSheet("color: white; font-size: 12px;")
        vortex_value.setAlignment(Qt.AlignCenter)
        vortex_layout.addWidget(vortex_value)

        layout.addWidget(vortex_frame)

        # Bottom section
        bottom_layout = QHBoxLayout()

        # Economic News
        news_frame = QFrame()
        news_frame.setProperty("class", "vip-panel")
        news_frame.setFixedSize(80, 80)
        news_layout = QVBoxLayout(news_frame)

        news_icon = QLabel("📊")
        news_icon.setAlignment(Qt.AlignCenter)
        news_icon.setStyleSheet("font-size: 24px;")
        news_layout.addWidget(news_icon)

        news_label = QLabel("Economic\\nNews")
        news_label.setAlignment(Qt.AlignCenter)
        news_label.setStyleSheet("font-size: 10px; color: white;")
        news_layout.addWidget(news_label)

        bottom_layout.addWidget(news_frame)

        # Live Signals
        signals_frame = QFrame()
        signals_frame.setProperty("class", "vip-panel")
        signals_layout = QVBoxLayout(signals_frame)

        signals_title = QLabel("LIVE SIGNALS")
        signals_title.setAlignment(Qt.AlignCenter)
        signals_title.setStyleSheet("color: white; font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        signals_layout.addWidget(signals_title)

        # BUY signal
        buy_layout = QHBoxLayout()
        buy_label = QLabel("BUY")
        buy_label.setStyleSheet("color: #32C832; font-weight: bold; font-size: 16px;")
        buy_layout.addWidget(buy_label)

        buy_71 = QLabel("71%")
        buy_71.setStyleSheet("color: #32C832; font-weight: bold; font-size: 16px;")
        buy_layout.addWidget(buy_71)

        buy_29 = QLabel("29%")
        buy_29.setStyleSheet("color: #FF8844; font-weight: bold; font-size: 16px;")
        buy_layout.addWidget(buy_29)

        signals_layout.addLayout(buy_layout)

        # Large percentages
        large_layout = QHBoxLayout()
        large_71 = QLabel("71%")
        large_71.setStyleSheet("color: #32C832; font-weight: bold; font-size: 24px;")
        large_layout.addWidget(large_71)

        large_29 = QLabel("29%")
        large_29.setStyleSheet("color: #FF8844; font-weight: bold; font-size: 24px;")
        large_layout.addWidget(large_29)

        signals_layout.addLayout(large_layout)
        bottom_layout.addWidget(signals_frame)

        # Buyer/Seller Power
        power_frame = QFrame()
        power_frame.setProperty("class", "vip-panel")
        power_layout = QVBoxLayout(power_frame)

        power_title = QLabel("Buyer/Seller Power")
        power_title.setAlignment(Qt.AlignCenter)
        power_title.setStyleSheet("color: white; font-weight: bold; font-size: 12px; margin-bottom: 10px;")
        power_layout.addWidget(power_title)

        power_bar = QProgressBar()
        power_bar.setRange(0, 100)
        power_bar.setValue(34)
        power_layout.addWidget(power_bar)

        power_percentages = QLabel("34%        66%")
        power_percentages.setAlignment(Qt.AlignCenter)
        power_percentages.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        power_layout.addWidget(power_percentages)

        bottom_layout.addWidget(power_frame)
        layout.addLayout(bottom_layout)
        return panel

    def _create_right_panel(self):
        """Create right panel"""
        panel = QFrame()
        panel.setProperty("class", "vip-panel")
        panel.setFixedWidth(200)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # Grid of control buttons
        grid_layout = QGridLayout()
        grid_layout.setSpacing(10)

        # Create control buttons
        buttons = [
            (0, 0, "🚀", "AutoTrade"),
            (0, 1, "✓", "Confirm Mode"),
            (1, 0, "🚀", "Confirm Mode"),
            (1, 1, "🔥", "Heatmap"),
            (2, 0, "📊", "Economic News"),
            (2, 1, "😊", "Can"),
            (3, 0, "⚙️", "Settings"),
            (3, 1, "🔒", "Secures"),
        ]

        for row, col, icon, text in buttons:
            button = QPushButton()
            button.setProperty("class", "vip-btn-control")
            button.setFixedSize(80, 80)

            btn_layout = QVBoxLayout(button)
            btn_layout.setContentsMargins(5, 5, 5, 5)

            icon_label = QLabel(icon)
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("font-size: 24px; color: white; background: transparent;")
            btn_layout.addWidget(icon_label)

            text_label = QLabel(text)
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setStyleSheet("font-size: 10px; color: white; font-weight: bold; background: transparent;")
            text_label.setWordWrap(True)
            btn_layout.addWidget(text_label)

            grid_layout.addWidget(button, row, col)

        layout.addLayout(grid_layout)
        layout.addStretch()
        return panel


if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Setup logging
    logging.basicConfig(level=logging.INFO)

    window = VIPBigBangFinalUI()
    window.show()

    sys.exit(app.exec())
