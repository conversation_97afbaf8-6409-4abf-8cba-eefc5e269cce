# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs/
*.log

# Cache
.cache/
.pytest_cache/

# OS
.DS_Store
Thumbs.db

# Temporary files
temp/
tmp/
*.tmp

# Config files with sensitive data
*.key
hardware.id
license.key

# Large files
*.dll
*.exe
*.pyd
