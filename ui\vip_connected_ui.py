"""
🔌 VIP BIG BANG - Connected UI
اتصال کامل منطق VIP BIG BANG به رابط کاربری
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Add paths
sys.path.append(str(Path(__file__).parent.parent))

# Import VIP BIG BANG core
try:
    from core.signal_manager import SignalManager
    from core.analysis_engine import AnalysisEngine
    from core.complementary_engine import ComplementaryEngine
    CORE_AVAILABLE = True
except ImportError:
    CORE_AVAILABLE = False
    print("⚠️ VIP BIG BANG core not available - using demo mode")

# Import UI components
from ui.logic_to_ui_converter import VIPSignalWidget, VIPMetricWidget, UILogicConnector

class VIPConnectedUI(QMainWindow):
    """رابط کاربری متصل به منطق VIP BIG BANG"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔌 VIP BIG BANG - Connected UI")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize VIP BIG BANG components
        self.init_vip_components()
        
        # UI Components
        self.signal_widgets = {}
        self.metric_widgets = {}
        self.ui_connector = None
        
        self.setup_ui()
        self.connect_vip_logic()
        self.start_updates()
    
    def init_vip_components(self):
        """راه‌اندازی اجزای VIP BIG BANG"""
        if CORE_AVAILABLE:
            try:
                self.signal_manager = SignalManager()
                self.analysis_engine = AnalysisEngine()
                self.complementary_engine = ComplementaryEngine()
                self.vip_mode = "LIVE"
                print("✅ VIP BIG BANG core initialized")
            except Exception as e:
                print(f"❌ Error initializing VIP core: {e}")
                self.vip_mode = "DEMO"
        else:
            self.vip_mode = "DEMO"
            print("🎮 Running in DEMO mode")
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # VIP BIG BANG Gaming Theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1A1A2E,
                    stop:0.3 #16213E,
                    stop:0.7 #0F3460,
                    stop:1 #1A1A2E);
                color: white;
            }
            QLabel {
                color: white;
                font-family: 'Arial', sans-serif;
            }
            QFrame {
                border-radius: 10px;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Header
        self.create_header(layout)
        
        # Main Content
        main_layout = QHBoxLayout()
        
        # Left Panel - Original Analyses
        self.create_original_analyses_panel(main_layout)
        
        # Center Panel - Chart & Status
        self.create_center_panel(main_layout)
        
        # Right Panel - Complementary Analyses
        self.create_complementary_panel(main_layout)
        
        layout.addLayout(main_layout)
        
        # Footer
        self.create_footer(layout)
    
    def create_header(self, layout):
        """ایجاد هدر"""
        header = QFrame()
        header.setFixedHeight(80)
        header.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.3);
                border: 2px solid #00BCD4;
                border-radius: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Logo & Title
        logo_layout = QHBoxLayout()
        
        logo = QLabel("🎮")
        logo.setStyleSheet("font-size: 36px;")
        logo_layout.addWidget(logo)
        
        title = QLabel("VIP BIG BANG")
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #00BCD4;
        """)
        logo_layout.addWidget(title)
        
        subtitle = QLabel("Connected UI")
        subtitle.setStyleSheet("""
            font-size: 16px;
            color: #FF6B9D;
            margin-left: 10px;
        """)
        logo_layout.addWidget(subtitle)
        
        header_layout.addLayout(logo_layout)
        header_layout.addStretch()
        
        # Status
        self.status_label = QLabel(f"🔌 Mode: {self.vip_mode}")
        self.status_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #7ED321;
            background: rgba(0,0,0,0.5);
            padding: 8px 15px;
            border-radius: 8px;
        """)
        header_layout.addWidget(self.status_label)
        
        layout.addWidget(header)
    
    def create_original_analyses_panel(self, layout):
        """پنل تحلیل‌های اصلی"""
        panel = QFrame()
        panel.setFixedWidth(350)
        panel.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.2);
                border: 2px solid #4A90E2;
                border-radius: 15px;
            }
        """)
        
        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(15, 15, 15, 15)
        panel_layout.setSpacing(10)
        
        # Header
        header = QLabel("📊 تحلیل‌های اصلی VIP BIG BANG")
        header.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #4A90E2;
            text-align: center;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
        """)
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        panel_layout.addWidget(header)
        
        # Original 10 Analyses
        original_analyses = [
            ("MA6", "📈", "Moving Average 6"),
            ("Vortex", "🌪️", "Vortex Indicator"),
            ("Volume", "📊", "Volume Analysis"),
            ("Trap Candle", "🪤", "Trap Detection"),
            ("Shadow Candle", "👻", "Shadow Analysis"),
            ("Strong Level", "💪", "Support/Resistance"),
            ("Fake Breakout", "🎭", "False Breakout"),
            ("Momentum", "🚀", "Momentum Analysis"),
            ("Trend Analyzer", "📊", "Trend Detection"),
            ("Buyer/Seller Power", "⚖️", "Market Power")
        ]
        
        # Scroll area for analyses
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { background: transparent; border: none; }")
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(8)
        
        for name, icon, description in original_analyses:
            signal_widget = VIPSignalWidget(f"{icon} {name}")
            self.signal_widgets[name] = signal_widget
            scroll_layout.addWidget(signal_widget)
        
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        panel_layout.addWidget(scroll_area)
        
        layout.addWidget(panel)
    
    def create_center_panel(self, layout):
        """پنل مرکزی"""
        panel = QWidget()
        panel_layout = QVBoxLayout(panel)
        panel_layout.setSpacing(15)
        
        # Chart Container
        chart_container = QFrame()
        chart_container.setMinimumHeight(400)
        chart_container.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.8);
                border: 3px solid #00BCD4;
                border-radius: 15px;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_container)
        chart_layout.setContentsMargins(20, 20, 20, 20)
        
        # Chart Header
        chart_header = QLabel("📈 EUR/USD Live Chart")
        chart_header.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #00BCD4;
            text-align: center;
        """)
        chart_header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        chart_layout.addWidget(chart_header)
        
        # Chart Area (placeholder)
        self.chart_area = QLabel("Chart will be displayed here")
        self.chart_area.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.chart_area.setStyleSheet("""
            background: rgba(0,0,0,0.5);
            border: 1px dashed #666;
            border-radius: 10px;
            color: #666;
            font-size: 16px;
        """)
        chart_layout.addWidget(self.chart_area)
        
        panel_layout.addWidget(chart_container)
        
        # Metrics Row
        metrics_layout = QHBoxLayout()
        
        # Key Metrics
        metrics = [
            ("Balance", "💰", "#7ED321"),
            ("Win Rate", "🏆", "#4A90E2"),
            ("Trades", "📊", "#9013FE"),
            ("Profit", "💎", "#F5A623")
        ]
        
        for name, icon, color in metrics:
            metric_widget = VIPMetricWidget(name, icon)
            self.metric_widgets[name] = metric_widget
            metrics_layout.addWidget(metric_widget)
        
        panel_layout.addLayout(metrics_layout)
        
        layout.addWidget(panel, 2)
    
    def create_complementary_panel(self, layout):
        """پنل تحلیل‌های تکمیلی"""
        panel = QFrame()
        panel.setFixedWidth(350)
        panel.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.2);
                border: 2px solid #7ED321;
                border-radius: 15px;
            }
        """)
        
        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(15, 15, 15, 15)
        panel_layout.setSpacing(10)
        
        # Header
        header = QLabel("🔍 تحلیل‌های تکمیلی")
        header.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #7ED321;
            text-align: center;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
        """)
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        panel_layout.addWidget(header)
        
        # Complementary 10 Analyses
        complementary_analyses = [
            ("Heatmap", "🔥", "Market Heatmap"),
            ("PulseBar", "📊", "Volume Pulse"),
            ("Economic News", "📰", "News Filter"),
            ("OTC Mode", "🕐", "OTC Detection"),
            ("Live Scanner", "🔍", "Signal Scanner"),
            ("Confirm Mode", "✅", "Confirmation"),
            ("Brothers Can", "👥", "Pattern Detection"),
            ("Active Panel", "⚡", "Active Analysis"),
            ("AutoTrade Check", "🤖", "Trade Validation"),
            ("Account Safety", "🛡️", "Risk Management")
        ]
        
        # Scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { background: transparent; border: none; }")
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(8)
        
        for name, icon, description in complementary_analyses:
            signal_widget = VIPSignalWidget(f"{icon} {name}")
            self.signal_widgets[f"comp_{name}"] = signal_widget
            scroll_layout.addWidget(signal_widget)
        
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        panel_layout.addWidget(scroll_area)
        
        layout.addWidget(panel)
    
    def create_footer(self, layout):
        """ایجاد فوتر"""
        footer = QFrame()
        footer.setFixedHeight(50)
        footer.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.3);
                border: 1px solid #666;
                border-radius: 10px;
            }
        """)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        
        # Connection Status
        self.connection_status = QLabel("🔌 Connected to VIP BIG BANG Core")
        self.connection_status.setStyleSheet("color: #7ED321; font-weight: bold;")
        footer_layout.addWidget(self.connection_status)
        
        footer_layout.addStretch()
        
        # Update Info
        self.update_info = QLabel("🔄 Last Update: --")
        self.update_info.setStyleSheet("color: #666; font-size: 12px;")
        footer_layout.addWidget(self.update_info)
        
        layout.addWidget(footer)
    
    def connect_vip_logic(self):
        """اتصال منطق VIP BIG BANG به UI"""
        self.ui_connector = UILogicConnector(self)
        
        if self.vip_mode == "LIVE" and CORE_AVAILABLE:
            # Connect real VIP BIG BANG functions
            try:
                # Original analyses
                self.ui_connector.connect_logic_to_widget(
                    self.get_ma6_signal, 
                    self.signal_widgets["MA6"]
                )
                self.ui_connector.connect_logic_to_widget(
                    self.get_vortex_signal,
                    self.signal_widgets["Vortex"]
                )
                # Add more connections...
                
                print("✅ VIP BIG BANG logic connected to UI")
                
            except Exception as e:
                print(f"❌ Error connecting VIP logic: {e}")
                self.connect_demo_logic()
        else:
            self.connect_demo_logic()
    
    def connect_demo_logic(self):
        """اتصال منطق نمایشی"""
        # Demo functions
        self.ui_connector.connect_logic_to_widget(
            self.demo_ma6_signal,
            self.signal_widgets["MA6"]
        )
        self.ui_connector.connect_logic_to_widget(
            self.demo_vortex_signal,
            self.signal_widgets["Vortex"]
        )
        self.ui_connector.connect_logic_to_widget(
            self.demo_volume_signal,
            self.signal_widgets["Volume"]
        )
        
        # Metrics
        self.ui_connector.connect_logic_to_widget(
            self.demo_balance,
            self.metric_widgets["Balance"],
            "update_value"
        )
        self.ui_connector.connect_logic_to_widget(
            self.demo_winrate,
            self.metric_widgets["Win Rate"],
            "update_value"
        )
        
        print("🎮 Demo logic connected to UI")
    
    # VIP BIG BANG Logic Functions
    def get_ma6_signal(self):
        """دریافت سیگنال MA6"""
        if hasattr(self, 'analysis_engine'):
            return self.analysis_engine.get_ma6_analysis()
        return self.demo_ma6_signal()
    
    def get_vortex_signal(self):
        """دریافت سیگنال Vortex"""
        if hasattr(self, 'analysis_engine'):
            return self.analysis_engine.get_vortex_analysis()
        return self.demo_vortex_signal()
    
    # Demo Functions
    def demo_ma6_signal(self):
        import random
        return {
            "direction": random.choice(["CALL", "PUT", "NONE"]),
            "confidence": random.randint(70, 95),
            "strength": random.uniform(0.6, 1.0)
        }
    
    def demo_vortex_signal(self):
        import random
        return {
            "direction": random.choice(["CALL", "PUT", "NONE"]),
            "confidence": random.randint(65, 90)
        }
    
    def demo_volume_signal(self):
        import random
        return {
            "direction": random.choice(["HIGH", "LOW", "NORMAL"]),
            "confidence": random.randint(60, 85)
        }
    
    def demo_balance(self):
        import random
        return 3250.89 + random.uniform(-50, 100)
    
    def demo_winrate(self):
        import random
        return f"{94.5 + random.uniform(-2, 2):.1f}%"
    
    def start_updates(self):
        """شروع به‌روزرسانی‌ها"""
        if self.ui_connector:
            self.ui_connector.start_updates(2000)  # Every 2 seconds
            
            # Update status
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_status)
            self.update_timer.start(1000)
    
    def update_status(self):
        """به‌روزرسانی وضعیت"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.update_info.setText(f"🔄 Last Update: {current_time}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    window = VIPConnectedUI()
    window.show()
    
    sys.exit(app.exec())
