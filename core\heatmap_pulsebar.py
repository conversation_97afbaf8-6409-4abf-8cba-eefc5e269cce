"""
VIP BIG BANG Enterprise - Heatmap & PulseBar
Visual representation of buying/selling power and volume in candles
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import logging
from datetime import datetime

class HeatmapPulseBarAnalyzer:
    """
    Heatmap & PulseBar - VIP BIG BANG complementary analysis
    Visual display of buying/selling power and volume strength
    Combines volume and power in a visual box representation
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("HeatmapPulseBarAnalyzer")
        
        # Heatmap parameters
        self.heatmap_periods = 20
        self.intensity_threshold = 0.7
        self.color_sensitivity = 0.8
        
        # PulseBar parameters
        self.pulse_decay_rate = 0.95
        self.pulse_boost_multiplier = 1.5
        self.volume_spike_threshold = 2.0
        
        self.logger.debug("Heatmap & PulseBar Analyzer initialized")
    
    def calculate_buying_selling_intensity(self, data: pd.DataFrame) -> Dict:
        """Calculate buying and selling intensity for heatmap"""
        if len(data) < 2:
            return {
                'buying_intensity': 0.5,
                'selling_intensity': 0.5,
                'net_intensity': 0.0
            }
        
        # Price and volume data
        prices = data['close'] if 'close' in data.columns else data['price']
        volumes = data['volume'] if 'volume' in data.columns else pd.Series([1] * len(data))
        
        # Calculate price changes
        price_changes = prices.pct_change().fillna(0)
        
        # Separate buying and selling volumes
        buying_volume = np.where(price_changes > 0, volumes, 0)
        selling_volume = np.where(price_changes < 0, volumes, 0)
        neutral_volume = np.where(price_changes == 0, volumes, 0)
        
        # Calculate intensities
        total_volume = volumes.sum()
        if total_volume > 0:
            buying_intensity = buying_volume.sum() / total_volume
            selling_intensity = selling_volume.sum() / total_volume
        else:
            buying_intensity = 0.5
            selling_intensity = 0.5
        
        # Net intensity (positive = buying dominance, negative = selling dominance)
        net_intensity = buying_intensity - selling_intensity
        
        return {
            'buying_intensity': buying_intensity,
            'selling_intensity': selling_intensity,
            'net_intensity': net_intensity,
            'total_volume': total_volume
        }
    
    def generate_heatmap_colors(self, intensity_data: Dict) -> Dict:
        """Generate color codes for heatmap visualization"""
        net_intensity = intensity_data['net_intensity']
        buying_intensity = intensity_data['buying_intensity']
        selling_intensity = intensity_data['selling_intensity']
        
        # Determine primary color based on net intensity
        if net_intensity > 0.1:  # Strong buying
            primary_color = 'GREEN'
            intensity_level = min(net_intensity * 2, 1.0)
        elif net_intensity < -0.1:  # Strong selling
            primary_color = 'RED'
            intensity_level = min(abs(net_intensity) * 2, 1.0)
        else:  # Neutral
            primary_color = 'YELLOW'
            intensity_level = 0.5
        
        # Calculate color intensity (0-100)
        color_intensity = int(intensity_level * 100)
        
        # Generate RGB values
        if primary_color == 'GREEN':
            rgb = {
                'r': max(0, 100 - color_intensity),
                'g': min(255, 100 + color_intensity),
                'b': max(0, 100 - color_intensity)
            }
        elif primary_color == 'RED':
            rgb = {
                'r': min(255, 100 + color_intensity),
                'g': max(0, 100 - color_intensity),
                'b': max(0, 100 - color_intensity)
            }
        else:  # YELLOW
            rgb = {
                'r': min(255, 150 + color_intensity // 2),
                'g': min(255, 150 + color_intensity // 2),
                'b': max(0, 100 - color_intensity)
            }
        
        return {
            'primary_color': primary_color,
            'intensity_level': intensity_level,
            'color_intensity': color_intensity,
            'rgb': rgb,
            'hex_color': f"#{rgb['r']:02x}{rgb['g']:02x}{rgb['b']:02x}"
        }
    
    def calculate_pulsebar_data(self, data: pd.DataFrame) -> Dict:
        """Calculate PulseBar visualization data"""
        if len(data) < 1:
            return {
                'pulse_intensity': 0.0,
                'pulse_color': 'GRAY',
                'pulse_size': 0,
                'volume_level': 'NORMAL'
            }
        
        current_candle = data.iloc[-1]
        
        # Volume analysis
        volumes = data['volume'] if 'volume' in data.columns else pd.Series([1] * len(data))
        current_volume = volumes.iloc[-1] if not volumes.empty else 1
        avg_volume = volumes.tail(self.heatmap_periods).mean() if len(volumes) >= self.heatmap_periods else current_volume
        
        # Volume ratio
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Price movement
        if len(data) >= 2:
            price_change = (current_candle.get('close', current_candle.get('price', 0)) - 
                          data.iloc[-2].get('close', data.iloc[-2].get('price', 0)))
        else:
            price_change = 0
        
        # Calculate pulse intensity
        base_intensity = min(volume_ratio / 2.0, 1.0)
        
        # Boost for volume spikes
        if volume_ratio >= self.volume_spike_threshold:
            pulse_boost = 0.3
            volume_level = 'SPIKE'
        elif volume_ratio >= 1.5:
            pulse_boost = 0.2
            volume_level = 'HIGH'
        elif volume_ratio <= 0.5:
            pulse_boost = -0.2
            volume_level = 'LOW'
        else:
            pulse_boost = 0.0
            volume_level = 'NORMAL'
        
        pulse_intensity = min(max(base_intensity + pulse_boost, 0.0), 1.0)
        
        # Determine pulse color
        if price_change > 0:
            pulse_color = 'GREEN'
        elif price_change < 0:
            pulse_color = 'RED'
        else:
            pulse_color = 'YELLOW'
        
        # Calculate pulse size (0-100)
        pulse_size = int(pulse_intensity * 100)
        
        return {
            'pulse_intensity': pulse_intensity,
            'pulse_color': pulse_color,
            'pulse_size': pulse_size,
            'volume_level': volume_level,
            'volume_ratio': volume_ratio,
            'price_change': price_change
        }
    
    def generate_combined_visual_box(self, heatmap_data: Dict, pulsebar_data: Dict) -> Dict:
        """Generate combined visual box data for UI"""
        
        # Combine intensities
        combined_intensity = (heatmap_data['intensity_level'] + pulsebar_data['pulse_intensity']) / 2
        
        # Determine dominant signal
        if heatmap_data['primary_color'] == pulsebar_data['pulse_color']:
            # Signals agree - boost confidence
            visual_confidence = min(combined_intensity * 1.2, 1.0)
            signal_agreement = True
        else:
            # Signals disagree - reduce confidence
            visual_confidence = combined_intensity * 0.8
            signal_agreement = False
        
        # Generate visual box properties
        box_size = int(visual_confidence * 100)
        box_opacity = min(visual_confidence + 0.3, 1.0)
        
        # Animation properties
        if pulsebar_data['volume_level'] == 'SPIKE':
            animation_speed = 'FAST'
            animation_type = 'PULSE'
        elif pulsebar_data['volume_level'] == 'HIGH':
            animation_speed = 'MEDIUM'
            animation_type = 'GLOW'
        else:
            animation_speed = 'SLOW'
            animation_type = 'FADE'
        
        return {
            'combined_intensity': combined_intensity,
            'visual_confidence': visual_confidence,
            'signal_agreement': signal_agreement,
            'box_size': box_size,
            'box_opacity': box_opacity,
            'primary_color': heatmap_data['primary_color'],
            'secondary_color': pulsebar_data['pulse_color'],
            'animation_speed': animation_speed,
            'animation_type': animation_type,
            'display_text': f"{heatmap_data['primary_color']} {int(visual_confidence*100)}%"
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main heatmap & pulsebar analysis function
        Returns comprehensive visual analysis data
        """
        try:
            if len(data) < 1:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for heatmap analysis'
                }
            
            # Calculate buying/selling intensity
            intensity_data = self.calculate_buying_selling_intensity(data)
            
            # Generate heatmap colors
            heatmap_data = self.generate_heatmap_colors(intensity_data)
            
            # Calculate pulsebar data
            pulsebar_data = self.calculate_pulsebar_data(data)
            
            # Generate combined visual box
            visual_box = self.generate_combined_visual_box(heatmap_data, pulsebar_data)
            
            # Calculate overall score
            score = 0.5 + intensity_data['net_intensity'] * 0.4
            score = max(0, min(1, score))
            
            # Determine direction
            if intensity_data['net_intensity'] > 0.1:
                direction = 'UP'
            elif intensity_data['net_intensity'] < -0.1:
                direction = 'DOWN'
            else:
                direction = 'NEUTRAL'
            
            # Calculate confidence
            confidence = visual_box['visual_confidence']
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'intensity_data': intensity_data,
                'heatmap_data': heatmap_data,
                'pulsebar_data': pulsebar_data,
                'visual_box': visual_box,
                'details': f'Visual: {visual_box["primary_color"]} {int(confidence*100)}%, Volume: {pulsebar_data["volume_level"]}'
            }
            
        except Exception as e:
            self.logger.error(f"Heatmap & PulseBar analysis error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'details': f'Heatmap analysis failed: {str(e)}'
            }
