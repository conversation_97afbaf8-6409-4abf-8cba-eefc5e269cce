#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Live Monitor UI
Real-time monitoring of Quotex data extraction
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
import json
import asyncio
import websockets
from datetime import datetime

class VIPBigBangLiveMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title('🚀 VIP BIG BANG - Live Monitor')
        self.root.geometry('800x600')
        self.root.configure(bg='#0a0a0a')
        
        # Data storage
        self.last_data = {}
        self.connection_count = 0
        self.extraction_count = 0
        
        # Create UI
        self.create_ui()
        
        # Start WebSocket monitoring
        self.start_monitoring()
        
    def create_ui(self):
        # Main title
        title_frame = tk.Frame(self.root, bg='#0a0a0a')
        title_frame.pack(fill='x', pady=10)
        
        title = tk.Label(title_frame, text='🚀 VIP BIG BANG LIVE MONITOR', 
                        font=('Arial', 20, 'bold'), fg='#00d4ff', bg='#0a0a0a')
        title.pack()
        
        subtitle = tk.Label(title_frame, text='Real-time Quotex Data Extraction Monitor', 
                           font=('Arial', 12), fg='#888888', bg='#0a0a0a')
        subtitle.pack()
        
        # Status panel
        status_frame = tk.LabelFrame(self.root, text='📡 System Status', 
                                    font=('Arial', 12, 'bold'), fg='#00d4ff', bg='#1a1a1a')
        status_frame.pack(fill='x', padx=20, pady=10)
        
        # Server status
        server_frame = tk.Frame(status_frame, bg='#1a1a1a')
        server_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(server_frame, text='🖥️ Server:', font=('Arial', 11, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        self.server_status = tk.Label(server_frame, text='🟢 ONLINE', 
                                     font=('Arial', 11), fg='#4ade80', bg='#1a1a1a')
        self.server_status.pack(side='left', padx=(10, 0))
        
        # Extension status
        ext_frame = tk.Frame(status_frame, bg='#1a1a1a')
        ext_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(ext_frame, text='🔌 Extension:', font=('Arial', 11, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        self.extension_status = tk.Label(ext_frame, text='🟡 WAITING', 
                                        font=('Arial', 11), fg='#f59e0b', bg='#1a1a1a')
        self.extension_status.pack(side='left', padx=(10, 0))
        
        # Connection count
        conn_frame = tk.Frame(status_frame, bg='#1a1a1a')
        conn_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(conn_frame, text='📊 Connections:', font=('Arial', 11, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        self.connection_label = tk.Label(conn_frame, text='0', 
                                        font=('Arial', 11), fg='#00d4ff', bg='#1a1a1a')
        self.connection_label.pack(side='left', padx=(10, 0))
        
        # Data panel
        data_frame = tk.LabelFrame(self.root, text='💰 Live Trading Data', 
                                  font=('Arial', 12, 'bold'), fg='#00d4ff', bg='#1a1a1a')
        data_frame.pack(fill='x', padx=20, pady=10)
        
        # Balance
        balance_frame = tk.Frame(data_frame, bg='#1a1a1a')
        balance_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(balance_frame, text='💵 Balance:', font=('Arial', 11, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        self.balance_label = tk.Label(balance_frame, text='$0.00', 
                                     font=('Arial', 11), fg='#4ade80', bg='#1a1a1a')
        self.balance_label.pack(side='left', padx=(10, 0))
        
        # Asset
        asset_frame = tk.Frame(data_frame, bg='#1a1a1a')
        asset_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(asset_frame, text='📈 Asset:', font=('Arial', 11, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        self.asset_label = tk.Label(asset_frame, text='None', 
                                   font=('Arial', 11), fg='#00d4ff', bg='#1a1a1a')
        self.asset_label.pack(side='left', padx=(10, 0))
        
        # Price
        price_frame = tk.Frame(data_frame, bg='#1a1a1a')
        price_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(price_frame, text='💲 Price:', font=('Arial', 11, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        self.price_label = tk.Label(price_frame, text='-', 
                                   font=('Arial', 11), fg='#f59e0b', bg='#1a1a1a')
        self.price_label.pack(side='left', padx=(10, 0))
        
        # Log panel
        log_frame = tk.LabelFrame(self.root, text='📋 Live Activity Log', 
                                 font=('Arial', 12, 'bold'), fg='#00d4ff', bg='#1a1a1a')
        log_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, 
                                                 bg='#0a0a0a', fg='#ffffff', 
                                                 font=('Consolas', 10),
                                                 insertbackground='white')
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Add initial messages
        self.add_log('🚀 VIP BIG BANG Live Monitor Started')
        self.add_log('📡 WebSocket Server: ws://localhost:8765')
        self.add_log('⏳ Waiting for Extension connection...')
        self.add_log('')
        
    def add_log(self, message):
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f'[{timestamp}] {message}\n'
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Color coding
        if '✅' in message or 'Connected' in message:
            self.log_text.tag_add('success', f'end-{len(log_entry)}c', 'end-1c')
            self.log_text.tag_config('success', foreground='#4ade80')
        elif '❌' in message or 'Error' in message:
            self.log_text.tag_add('error', f'end-{len(log_entry)}c', 'end-1c')
            self.log_text.tag_config('error', foreground='#ef4444')
        elif '📊' in message or 'Data' in message:
            self.log_text.tag_add('data', f'end-{len(log_entry)}c', 'end-1c')
            self.log_text.tag_config('data', foreground='#00d4ff')
    
    def update_status(self, status_type, value):
        if status_type == 'extension':
            self.extension_status.config(text=value)
            if 'CONNECTED' in value:
                self.extension_status.config(fg='#4ade80')
            elif 'DISCONNECTED' in value:
                self.extension_status.config(fg='#ef4444')
            else:
                self.extension_status.config(fg='#f59e0b')
        elif status_type == 'connections':
            self.connection_label.config(text=str(value))
    
    def update_data(self, data):
        if 'balance' in data and data['balance']:
            self.balance_label.config(text=data['balance'])
        if 'currentAsset' in data and data['currentAsset']:
            self.asset_label.config(text=data['currentAsset'])
        if 'currentPrice' in data and data['currentPrice']:
            self.price_label.config(text=data['currentPrice'])
    
    def start_monitoring(self):
        def monitor():
            try:
                import websockets
                import asyncio
                
                async def handle_client(websocket, path):
                    self.connection_count += 1
                    self.root.after(0, self.update_status, 'connections', self.connection_count)
                    self.root.after(0, self.add_log, f'✅ Client connected from {websocket.remote_address[0]}')
                    
                    try:
                        async for message in websocket:
                            try:
                                data = json.loads(message)
                                
                                if data.get('type') == 'quotex_data':
                                    quotex_data = data.get('data', {})
                                    self.root.after(0, self.update_data, quotex_data)
                                    self.root.after(0, self.update_status, 'extension', '🟢 CONNECTED')
                                    self.root.after(0, self.add_log, f'📊 Quotex Data: Balance={quotex_data.get("balance", "N/A")}, Asset={quotex_data.get("currentAsset", "N/A")}')
                                    
                                elif data.get('type') == 'websocket_data':
                                    self.root.after(0, self.add_log, '🔍 WebSocket Data Intercepted')
                                    
                                else:
                                    self.root.after(0, self.add_log, f'📨 Message: {data.get("type", "unknown")}')
                                    
                            except json.JSONDecodeError:
                                self.root.after(0, self.add_log, f'📨 Raw Message: {message[:100]}...')
                                
                    except websockets.exceptions.ConnectionClosed:
                        pass
                    finally:
                        self.connection_count -= 1
                        self.root.after(0, self.update_status, 'connections', self.connection_count)
                        self.root.after(0, self.add_log, '🔌 Client disconnected')
                        if self.connection_count == 0:
                            self.root.after(0, self.update_status, 'extension', '🟡 WAITING')
                
                async def start_server():
                    self.root.after(0, self.add_log, '🚀 Starting WebSocket monitor server...')
                    server = await websockets.serve(handle_client, 'localhost', 8766)
                    self.root.after(0, self.add_log, '✅ Monitor server started on ws://localhost:8766')
                    await server.wait_closed()
                
                asyncio.run(start_server())
                
            except Exception as e:
                self.root.after(0, self.add_log, f'❌ Monitor Error: {e}')
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
    
    def run(self):
        self.root.mainloop()

if __name__ == '__main__':
    print('🚀 Starting VIP BIG BANG Live Monitor...')
    monitor = VIPBigBangLiveMonitor()
    monitor.run()
