@echo off
chcp 65001 >nul
title 🚀 VIP BIG BANG - Quick Start

REM ===================================================================
REM 🚀 VIP BIG BANG - Quick Start Launcher
REM راه‌اندازی سریع و آسان VIP BIG BANG
REM ===================================================================

color 0B
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 VIP BIG BANG - QUICK START 🚀                       ██
echo ██                                                            ██
echo ██    ⚡ One-Click Launch ⚡                                  ██
echo ██    🎯 Real Data Trading 🎯                                ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🚀 Starting VIP BIG BANG...
echo ═══════════════════════════════════════

REM Quick system check
echo 🔍 Quick system check...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python not found! Please install Python first.
    echo 💡 Download from: https://python.org
    pause
    exit /b 1
)

echo ✅ Python found

REM Activate virtual environment if exists
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
    echo ✅ Virtual environment activated
) else (
    echo ⚠️  No virtual environment found, using system Python
)

REM Quick dependency check
echo 📦 Checking key dependencies...
pip show playwright >nul 2>&1
if %errorLevel% neq 0 (
    echo 🔧 Installing Playwright...
    pip install playwright
    python -m playwright install chromium
)

pip show pandas >nul 2>&1
if %errorLevel% neq 0 (
    echo 🔧 Installing Pandas...
    pip install pandas
)

echo ✅ Dependencies ready

REM Launch VIP BIG BANG
echo.
echo 🚀 Launching VIP BIG BANG...
echo ⚡ Starting Quantum Trading System...
echo.

REM Try main launcher first
python main.py
if %errorLevel% neq 0 (
    echo.
    echo ⚠️  Main launcher failed, trying alternative...
    python vip_real_quotex_main.py
    if %errorLevel% neq 0 (
        echo.
        echo ❌ All launch methods failed
        echo 🔧 Please run VIP_ADVANCED_LAUNCHER.bat for detailed diagnostics
        pause
        exit /b 1
    )
)

echo.
echo ✅ VIP BIG BANG launched successfully!
echo 🎉 Happy trading!
pause
