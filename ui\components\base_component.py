"""
Base Component Class for VIP BIG BANG UI
Professional component architecture
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from typing import Optional, Dict, Any

class BaseComponent(QWidget):
    """Base class for all VIP BIG BANG UI components"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._setup_component()
    
    def _setup_component(self):
        """Setup the component - override in subclasses"""
        pass
    
    def apply_style(self, style_class: str):
        """Apply a CSS class to this component"""
        self.setProperty("class", style_class)
        self.style().unpolish(self)
        self.style().polish(self)
    
    def set_fixed_size_square(self, size: int):
        """Set component to a fixed square size"""
        self.setFixedSize(size, size)
    
    def set_minimum_size_square(self, size: int):
        """Set component to a minimum square size"""
        self.setMinimumSize(size, size)


class VIPPanel(BaseComponent):
    """Professional panel component with VIP styling"""
    
    def __init__(self, parent: Optional[QWidget] = None, title: str = ""):
        self.title = title
        super().__init__(parent)
    
    def _setup_component(self):
        """Setup the panel"""
        self.apply_style("vip-panel")
        
        # Main layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(12, 12, 12, 12)
        self.layout.setSpacing(8)
        
        # Title if provided
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.apply_style("vip-title")
            self.title_label.setAlignment(Qt.AlignCenter)
            self.layout.addWidget(self.title_label)
    
    def add_widget(self, widget: QWidget):
        """Add a widget to the panel"""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout: QLayout):
        """Add a layout to the panel"""
        self.layout.addLayout(layout)
    
    def add_stretch(self):
        """Add stretch to the panel"""
        self.layout.addStretch()


class VIPButton(QPushButton):
    """Professional button component with VIP styling"""
    
    def __init__(self, text: str = "", icon: str = "", button_type: str = "default", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.button_type = button_type
        self.icon_text = icon
        self._setup_button(text)
    
    def _setup_button(self, text: str):
        """Setup the button"""
        # Apply appropriate style based on type
        style_map = {
            "default": "vip-btn",
            "buy": "vip-btn-buy", 
            "sell": "vip-btn-sell",
            "control": "vip-btn-control",
            "active": "vip-btn-active"
        }
        
        self.setProperty("class", style_map.get(self.button_type, "vip-btn"))
        
        # Setup layout for icon + text
        if self.icon_text:
            layout = QVBoxLayout(self)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(2)
            
            # Icon
            icon_label = QLabel(self.icon_text)
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("font-size: 24px; color: white; background: transparent;")
            layout.addWidget(icon_label)
            
            # Text
            if text:
                text_label = QLabel(text)
                text_label.setAlignment(Qt.AlignCenter)
                text_label.setStyleSheet("font-size: 10px; color: white; font-weight: bold; background: transparent;")
                text_label.setWordWrap(True)
                layout.addWidget(text_label)
        else:
            self.setText(text)


class VIPLabel(QLabel):
    """Professional label component with VIP styling"""
    
    def __init__(self, text: str = "", label_type: str = "default", parent: Optional[QWidget] = None):
        super().__init__(text, parent)
        self.label_type = label_type
        self._setup_label()
    
    def _setup_label(self):
        """Setup the label"""
        style_map = {
            "default": "color: white;",
            "title": "vip-title",
            "subtitle": "vip-subtitle", 
            "value": "vip-value",
            "value-large": "vip-value-large",
            "percentage": "vip-percentage"
        }
        
        if self.label_type in style_map:
            if self.label_type == "default":
                self.setStyleSheet(style_map[self.label_type])
            else:
                self.setProperty("class", style_map[self.label_type])


class VIPProgressBar(QProgressBar):
    """Professional progress bar component with VIP styling"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self._setup_progress_bar()
    
    def _setup_progress_bar(self):
        """Setup the progress bar"""
        self.setRange(0, 100)
        self.setValue(0)
        # Styling is handled by the main stylesheet


class VIPToggleSwitch(BaseComponent):
    """Professional toggle switch component"""
    
    toggled = Signal(bool)
    
    def __init__(self, initial_state: bool = False, parent: Optional[QWidget] = None):
        self.state = initial_state
        super().__init__(parent)
    
    def _setup_component(self):
        """Setup the toggle switch"""
        self.setFixedHeight(30)
        self.setMinimumWidth(60)
        self._update_style()
        
        # Make it clickable
        self.mousePressEvent = self._on_click
    
    def _update_style(self):
        """Update the visual style based on state"""
        if self.state:
            self.apply_style("vip-toggle-on")
        else:
            self.apply_style("vip-toggle-off")
    
    def _on_click(self, event):
        """Handle click event"""
        self.toggle()
    
    def toggle(self):
        """Toggle the switch state"""
        self.state = not self.state
        self._update_style()
        self.toggled.emit(self.state)
    
    def set_state(self, state: bool):
        """Set the switch state"""
        if self.state != state:
            self.state = state
            self._update_style()
            self.toggled.emit(self.state)


class VIPPulseBar(BaseComponent):
    """Professional pulse bar component"""
    
    def __init__(self, colors: list = None, parent: Optional[QWidget] = None):
        self.colors = colors or ["#FF4444", "#FF8844", "#FFFF44", "#88FF44", "#44FF44"]
        super().__init__(parent)
    
    def _setup_component(self):
        """Setup the pulse bar"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(1)
        
        # Create color bars
        for color in self.colors:
            bar = QFrame()
            bar.setFixedHeight(8)
            bar.setStyleSheet(f"background: {color}; border-radius: 4px;")
            bar.setProperty("class", "vip-pulse-bar")
            layout.addWidget(bar)
    
    def update_colors(self, colors: list):
        """Update the pulse bar colors"""
        self.colors = colors
        # Clear existing bars
        for i in reversed(range(self.layout().count())):
            self.layout().itemAt(i).widget().setParent(None)
        
        # Recreate bars
        for color in self.colors:
            bar = QFrame()
            bar.setFixedHeight(8)
            bar.setStyleSheet(f"background: {color}; border-radius: 4px;")
            bar.setProperty("class", "vip-pulse-bar")
            self.layout().addWidget(bar)
