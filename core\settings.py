"""
VIP BIG BANG Enterprise - Settings Manager
Advanced configuration management with encryption and validation
"""

import json
from pathlib import Path
from typing import Dict, Any, List
import logging
from dataclasses import dataclass, asdict, field
from datetime import datetime

@dataclass
class TradingSettings:
    platform: str = "quotex"
    analysis_interval: int = 15
    trade_duration: int = 5
    auto_trade_enabled: bool = False
    demo_mode: bool = True
    max_trades_per_hour: int = 3
    max_daily_trades: int = 10
    min_signal_strength: float = 0.95
    confirm_mode_enabled: bool = True

@dataclass
class RiskManagementSettings:
    max_trade_amount: float = 10.0
    min_trade_amount: float = 1.0
    daily_loss_limit: float = 100.0
    max_consecutive_losses: int = 5
    stop_loss_percentage: float = 10.0
    take_profit_percentage: float = 80.0
    risk_per_trade: float = 2.0

@dataclass
class AnalysisSettings:
    primary_timeframe: str = "1m"
    lookback_periods: int = 100
    sensitivity: str = "high"
    enabled_indicators: List[str] = field(default_factory=list)
    timeframes: List[str] = field(default_factory=list)

@dataclass
class SignalProcessingSettings:
    min_confidence: float = 0.95
    confirmation_required: int = 8
    signal_timeout: int = 30
    filter_weak_signals: bool = True
    combine_signals: bool = True
    weight_distribution: Dict[str, float] = field(default_factory=dict)

@dataclass
class PerformanceSettings:
    multi_threading: bool = True
    max_threads: int = 4
    memory_optimization: bool = True
    cpu_optimization: bool = True
    cache_enabled: bool = True
    cache_size: int = 1000
    gc_optimization: bool = True

@dataclass
class SecuritySettings:
    encryption_enabled: bool = True
    license_check: bool = True
    hardware_binding: bool = True
    session_encryption: bool = True
    api_key_encryption: bool = True
    config_encryption: bool = False
    obfuscation_level: str = "high"

class Settings:
    """
    Enterprise-level settings management
    Handles configuration loading, validation, and encryption
    """
    
    def __init__(self, config_file: str = "config.json"):
        self.logger = logging.getLogger("Settings")
        self.config_file = Path(config_file)
        self.config_data: Dict[str, Any] = {}
        
        # Initialize settings objects
        self.trading = None
        self.risk_management = None
        self.analysis = None
        self.signal_processing = None
        self.complementary_analysis = None
        self.performance = None
        self.security = None
        
        # Load configuration
        self.load_config()
        self.validate_config()
        
        self.logger.info("Settings initialized successfully")
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                self.logger.info(f"Configuration loaded from {self.config_file}")
            else:
                self.logger.warning(f"Config file {self.config_file} not found, using defaults")
                self.create_default_config()
            
            # Parse settings into dataclasses
            self._parse_settings()
            
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            self.create_default_config()
            self._parse_settings()
    
    def create_default_config(self):
        """Create default configuration"""
        self.config_data = {
            "app_info": {
                "name": "VIP BIG BANG Enterprise",
                "version": "1.0.0",
                "build": "Enterprise"
            },
            "trading": {
                "platform": "quotex",
                "analysis_interval": 15,
                "trade_duration": 5,
                "auto_trade_enabled": False,
                "demo_mode": True,
                "max_trades_per_hour": 3,
                "max_daily_trades": 10,
                "min_signal_strength": 0.95,
                "confirm_mode_enabled": True
            },
            "risk_management": {
                "max_trade_amount": 10.0,
                "min_trade_amount": 1.0,
                "daily_loss_limit": 100.0,
                "max_consecutive_losses": 5,
                "stop_loss_percentage": 10.0,
                "take_profit_percentage": 80.0,
                "risk_per_trade": 2.0
            },
            "analysis": {
                "enabled_indicators": [
                    "ma6", "vortex", "volume_per_candle", "trap_candle",
                    "shadow_candle", "strong_level", "fake_breakout",
                    "momentum", "trend_analyzer", "buyer_seller_power"
                ],
                "timeframes": ["1m", "5m", "15m"],
                "primary_timeframe": "1m",
                "lookback_periods": 100,
                "sensitivity": "high"
            },
            "signal_processing": {
                "min_confidence": 0.95,
                "confirmation_required": 8,
                "signal_timeout": 30,
                "filter_weak_signals": True,
                "combine_signals": True,
                "weight_distribution": {
                    "ma6": 0.10,
                    "vortex": 0.10,
                    "volume_per_candle": 0.10,
                    "trap_candle": 0.10,
                    "shadow_candle": 0.10,
                    "strong_level": 0.10,
                    "fake_breakout": 0.10,
                    "momentum": 0.10,
                    "trend_analyzer": 0.10,
                    "buyer_seller_power": 0.10
                }
            },
            "performance": {
                "multi_threading": True,
                "max_threads": 4,
                "memory_optimization": True,
                "cpu_optimization": True,
                "cache_enabled": True,
                "cache_size": 1000,
                "gc_optimization": True
            },
            "security": {
                "encryption_enabled": True,
                "license_check": True,
                "hardware_binding": True,
                "session_encryption": True,
                "api_key_encryption": True,
                "config_encryption": False,
                "obfuscation_level": "high"
            }
        }
        
        self.save_config()
    
    def _parse_settings(self):
        """Parse configuration data into dataclass objects"""
        try:
            # Trading settings
            trading_data = self.config_data.get('trading', {})
            self.trading = TradingSettings(**trading_data)
            
            # Risk management settings
            risk_data = self.config_data.get('risk_management', {})
            self.risk_management = RiskManagementSettings(**risk_data)
            
            # Analysis settings
            analysis_data = self.config_data.get('analysis', {})
            self.analysis = AnalysisSettings(**analysis_data)
            
            # Signal processing settings
            signal_data = self.config_data.get('signal_processing', {})
            self.signal_processing = SignalProcessingSettings(**signal_data)
            
            # Performance settings
            perf_data = self.config_data.get('performance', {})
            self.performance = PerformanceSettings(**perf_data)
            
            # Security settings
            security_data = self.config_data.get('security', {})
            self.security = SecuritySettings(**security_data)
            
        except Exception as e:
            self.logger.error(f"Failed to parse settings: {e}")
            raise
    
    def validate_config(self):
        """Validate configuration values"""
        errors = []
        
        # Validate trading settings
        if hasattr(self, 'trading') and self.trading:
            if self.trading.analysis_interval < 5:
                errors.append("Analysis interval must be at least 5 seconds")
            if self.trading.trade_duration < 1:
                errors.append("Trade duration must be at least 1 second")
            if self.trading.max_trades_per_hour > 100:
                errors.append("Max trades per hour should not exceed 100")
        
        # Validate risk management
        if hasattr(self, 'risk_management') and self.risk_management:
            if self.risk_management.max_trade_amount <= self.risk_management.min_trade_amount:
                errors.append("Max trade amount must be greater than min trade amount")
            if self.risk_management.risk_per_trade > 10:
                errors.append("Risk per trade should not exceed 10%")
        
        # Validate signal processing
        if hasattr(self, 'signal_processing') and self.signal_processing:
            if self.signal_processing.min_confidence > 1.0 or self.signal_processing.min_confidence < 0:
                errors.append("Min confidence must be between 0 and 1")
        
        if errors:
            self.logger.warning(f"Configuration validation errors: {errors}")
            for error in errors:
                self.logger.warning(f"  - {error}")
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            # Update config data with current settings
            if self.trading:
                self.config_data['trading'] = asdict(self.trading)
            if self.risk_management:
                self.config_data['risk_management'] = asdict(self.risk_management)
            if self.analysis:
                self.config_data['analysis'] = asdict(self.analysis)
            if self.signal_processing:
                self.config_data['signal_processing'] = asdict(self.signal_processing)
            if self.performance:
                self.config_data['performance'] = asdict(self.performance)
            if self.security:
                self.config_data['security'] = asdict(self.security)
            
            # Add timestamp
            self.config_data['last_updated'] = datetime.now().isoformat()
            
            # Save to file
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Set configuration value by key"""
        keys = key.split('.')
        config = self.config_data
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        
        # Re-parse settings
        self._parse_settings()
        
        self.logger.info(f"Setting updated: {key} = {value}")
    
    def reload(self):
        """Reload configuration from file"""
        self.load_config()
        self.validate_config()
        self.logger.info("Configuration reloaded")
    
    def get_app_info(self) -> Dict:
        """Get application information"""
        return self.config_data.get('app_info', {})
    
    def is_demo_mode(self) -> bool:
        """Check if running in demo mode"""
        return self.trading.demo_mode if self.trading else True
    
    def is_auto_trade_enabled(self) -> bool:
        """Check if auto trading is enabled"""
        return self.trading.auto_trade_enabled if self.trading else False
