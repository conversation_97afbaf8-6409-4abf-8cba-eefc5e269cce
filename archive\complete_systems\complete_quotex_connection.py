#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 COMPLETE QUOTEX CONNECTION SYSTEM
💎 نسخه کامل اتصال به Quotex با تمام قابلیت‌ها
⚡ شامل تمام روش‌های کاری که قبلاً پیاده‌سازی شده
"""

import sys
import os
import asyncio
import time
import subprocess
import logging
import requests
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import all working systems
from utils.logger import setup_logger
from trading.quotex_client import QuotexClient
from core.stealth_quotex_connector import StealthQuotexConnector
from core.quantum_stealth_chrome_connector import QuantumStealthChromeConnector
from core.auto_login_manager import AutoLoginManager
from core.settings import Settings

class CompleteQuotexConnection(QMainWindow):
    """🚀 Complete Quotex Connection System"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger("CompleteQuotexConnection")
        
        # Initialize settings
        self.settings = Settings()
        
        # Initialize all connection systems
        self.quotex_client = QuotexClient(self.settings)
        self.stealth_connector = StealthQuotexConnector(self.settings)
        self.quantum_connector = QuantumStealthChromeConnector()
        self.auto_login_manager = AutoLoginManager()
        
        # Connection state
        self.connected = False
        self.connection_method = None
        self.chrome_process = None
        
        # Chrome paths
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        # Setup UI
        self.setup_ui()
        self.setup_styles()
        
        self.logger.info("🚀 Complete Quotex Connection System initialized")
    
    def setup_ui(self):
        """🎨 Setup complete UI"""
        self.setWindowTitle("🚀 VIP BIG BANG - Complete Quotex Connection")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header
        header = self.create_header()
        main_layout.addWidget(header)
        
        # Content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # Left panel - Connection methods
        left_panel = self.create_connection_panel()
        content_layout.addWidget(left_panel)
        
        # Right panel - Status and logs
        right_panel = self.create_status_panel()
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
        
        # Status bar
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("🚀 Ready to connect to Quotex")
    
    def create_header(self):
        """🎨 Create header"""
        header = QFrame()
        header.setProperty("class", "header-panel")
        header.setFixedHeight(100)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(30, 20, 30, 20)
        
        # Title
        title = QLabel("🚀 VIP BIG BANG")
        title.setProperty("class", "main-title")
        layout.addWidget(title)
        
        subtitle = QLabel("Complete Quotex Connection System")
        subtitle.setProperty("class", "subtitle")
        layout.addWidget(subtitle)
        
        layout.addStretch()
        
        # Connection status
        self.connection_status = QLabel("⭕ DISCONNECTED")
        self.connection_status.setProperty("class", "status-label")
        layout.addWidget(self.connection_status)
        
        return header
    
    def create_connection_panel(self):
        """🔗 Create connection methods panel"""
        panel = QFrame()
        panel.setProperty("class", "connection-panel")
        panel.setFixedWidth(400)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Connection Methods
        methods_group = QGroupBox("🚀 Connection Methods")
        methods_layout = QVBoxLayout(methods_group)
        
        # Method 1: Persistent Chrome
        self.persistent_btn = QPushButton("🔥 Persistent Chrome (BEST)")
        self.persistent_btn.setProperty("class", "method-btn-best")
        self.persistent_btn.clicked.connect(self.connect_persistent_chrome)
        methods_layout.addWidget(self.persistent_btn)
        
        # Method 2: Stealth Connection
        self.stealth_btn = QPushButton("🕵️ Stealth Connection")
        self.stealth_btn.setProperty("class", "method-btn")
        self.stealth_btn.clicked.connect(self.connect_stealth)
        methods_layout.addWidget(self.stealth_btn)
        
        # Method 3: Quantum Connection
        self.quantum_btn = QPushButton("⚡ Quantum Connection")
        self.quantum_btn.setProperty("class", "method-btn")
        self.quantum_btn.clicked.connect(self.connect_quantum)
        methods_layout.addWidget(self.quantum_btn)
        
        # Method 4: Direct Chrome
        self.direct_btn = QPushButton("🚀 Direct Chrome Launch")
        self.direct_btn.setProperty("class", "method-btn")
        self.direct_btn.clicked.connect(self.connect_direct_chrome)
        methods_layout.addWidget(self.direct_btn)
        
        # Method 5: DevTools Connection
        self.devtools_btn = QPushButton("🔗 DevTools Connection")
        self.devtools_btn.setProperty("class", "method-btn")
        self.devtools_btn.clicked.connect(self.connect_devtools)
        methods_layout.addWidget(self.devtools_btn)
        
        layout.addWidget(methods_group)
        
        # Quick Actions
        actions_group = QGroupBox("⚡ Quick Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        self.test_connection_btn = QPushButton("🧪 Test Connection")
        self.test_connection_btn.clicked.connect(self.test_connection)
        actions_layout.addWidget(self.test_connection_btn)
        
        self.open_quotex_btn = QPushButton("🌐 Open Quotex Tab")
        self.open_quotex_btn.clicked.connect(self.open_quotex_tab)
        actions_layout.addWidget(self.open_quotex_btn)
        
        self.disconnect_btn = QPushButton("🛑 Disconnect")
        self.disconnect_btn.clicked.connect(self.disconnect_all)
        self.disconnect_btn.setEnabled(False)
        actions_layout.addWidget(self.disconnect_btn)
        
        layout.addWidget(actions_group)
        
        layout.addStretch()
        
        return panel
    
    def create_status_panel(self):
        """📊 Create status and logs panel"""
        panel = QFrame()
        panel.setProperty("class", "status-panel")
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Connection Info
        info_group = QGroupBox("📊 Connection Information")
        info_layout = QVBoxLayout(info_group)
        
        self.method_label = QLabel("Method: None")
        info_layout.addWidget(self.method_label)
        
        self.chrome_status_label = QLabel("Chrome: Not Running")
        info_layout.addWidget(self.chrome_status_label)
        
        self.quotex_status_label = QLabel("Quotex: Not Connected")
        info_layout.addWidget(self.quotex_status_label)
        
        layout.addWidget(info_group)
        
        # Live Logs
        logs_group = QGroupBox("📝 Live Connection Logs")
        logs_layout = QVBoxLayout(logs_group)
        
        self.logs_display = QTextEdit()
        self.logs_display.setProperty("class", "logs-display")
        self.logs_display.setFixedHeight(400)
        self.logs_display.setPlainText("🚀 Ready to connect to Quotex...\n")
        logs_layout.addWidget(self.logs_display)
        
        layout.addWidget(logs_group)
        
        return panel
    
    def setup_styles(self):
        """🎨 Setup VIP styles"""
        style = """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1a1a2e, stop:1 #16213e);
            color: white;
        }
        
        .header-panel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #2D1B69, stop:1 #1A0F3D);
            border: 2px solid #4B0082;
            border-radius: 15px;
        }
        
        .main-title {
            font-size: 32px;
            font-weight: bold;
            color: #FFD700;
        }
        
        .subtitle {
            font-size: 18px;
            color: #FFFFFF;
            margin-left: 20px;
        }
        
        .status-label {
            font-size: 20px;
            font-weight: bold;
            color: #FF4444;
        }
        
        .connection-panel, .status-panel {
            background: rgba(75, 50, 150, 0.3);
            border: 2px solid rgba(120, 80, 200, 0.5);
            border-radius: 15px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #4B0082;
            border-radius: 10px;
            margin-top: 10px;
            padding-top: 10px;
            color: #FFD700;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        .method-btn-best {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #FFD700, stop:1 #FFA500);
            color: black;
            font-weight: bold;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            border: none;
        }
        
        .method-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #32CD32, stop:1 #228B22);
            color: white;
            font-weight: bold;
            padding: 12px;
            border-radius: 10px;
            font-size: 14px;
            border: none;
        }
        
        QPushButton {
            background: rgba(75, 50, 150, 0.8);
            color: white;
            font-weight: bold;
            padding: 10px;
            border-radius: 8px;
            border: 2px solid #4B0082;
        }
        
        QPushButton:hover {
            background: rgba(120, 80, 200, 0.9);
        }
        
        .logs-display {
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #4B0082;
            border-radius: 10px;
            color: #00FF00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        """
        
        self.setStyleSheet(style)
    
    def log_message(self, message: str):
        """📝 Add message to logs"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs_display.append(log_entry)
        self.logger.info(message)
    
    def find_chrome(self):
        """🔍 Find Chrome executable"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                return path
        return None

    def connect_persistent_chrome(self):
        """🔥 Connect using Persistent Chrome (BEST METHOD)"""
        try:
            self.log_message("🔥 Starting Persistent Chrome connection...")
            self.status_bar.showMessage("🔥 Launching Persistent Chrome...")

            # Run persistent Chrome manager
            result = subprocess.run([
                sys.executable, "persistent_chrome_manager.py"
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                self.connected = True
                self.connection_method = "Persistent Chrome"
                self.connection_status.setText("🟢 CONNECTED")
                self.connection_status.setStyleSheet("color: #32CD32;")
                self.method_label.setText("Method: Persistent Chrome")
                self.chrome_status_label.setText("Chrome: Running with Profile")
                self.disconnect_btn.setEnabled(True)

                self.log_message("✅ Persistent Chrome connection successful!")
                self.status_bar.showMessage("✅ Connected via Persistent Chrome")

                # Try to open Quotex tab
                QTimer.singleShot(2000, self.open_quotex_tab)

            else:
                self.log_message(f"❌ Persistent Chrome failed: {result.stderr}")
                self.status_bar.showMessage("❌ Persistent Chrome failed")

        except Exception as e:
            self.log_message(f"❌ Persistent Chrome error: {e}")
            self.status_bar.showMessage(f"❌ Error: {e}")

    async def connect_stealth(self):
        """🕵️ Connect using Stealth method"""
        try:
            self.log_message("🕵️ Starting Stealth connection...")
            self.status_bar.showMessage("🕵️ Initializing stealth browser...")

            # Initialize stealth browser
            if await self.stealth_connector.initialize_stealth_browser():
                self.log_message("✅ Stealth browser initialized")

                # Connect to Quotex
                success = await self.stealth_connector.connect_to_quotex()

                if success:
                    self.connected = True
                    self.connection_method = "Stealth"
                    self.connection_status.setText("🟢 STEALTH CONNECTED")
                    self.connection_status.setStyleSheet("color: #32CD32;")
                    self.method_label.setText("Method: Stealth Connection")
                    self.chrome_status_label.setText("Chrome: Stealth Mode Active")
                    self.quotex_status_label.setText("Quotex: Connected (Stealth)")
                    self.disconnect_btn.setEnabled(True)

                    self.log_message("✅ Stealth connection to Quotex successful!")
                    self.status_bar.showMessage("✅ Stealth connection established")
                else:
                    self.log_message("❌ Stealth connection to Quotex failed")
                    self.status_bar.showMessage("❌ Stealth connection failed")
            else:
                self.log_message("❌ Failed to initialize stealth browser")
                self.status_bar.showMessage("❌ Stealth browser initialization failed")

        except Exception as e:
            self.log_message(f"❌ Stealth connection error: {e}")
            self.status_bar.showMessage(f"❌ Stealth error: {e}")

    def connect_quantum(self):
        """⚡ Connect using Quantum method"""
        try:
            self.log_message("⚡ Starting Quantum connection...")
            self.status_bar.showMessage("⚡ Initializing quantum stealth...")

            # Start quantum connection
            success = self.quantum_connector.start_connection()

            if success:
                self.connected = True
                self.connection_method = "Quantum"
                self.connection_status.setText("🟢 QUANTUM CONNECTED")
                self.connection_status.setStyleSheet("color: #FFD700;")
                self.method_label.setText("Method: Quantum Stealth")
                self.chrome_status_label.setText("Chrome: Quantum Mode Active")
                self.quotex_status_label.setText("Quotex: Quantum Connected")
                self.disconnect_btn.setEnabled(True)

                self.log_message("✅ Quantum connection established!")
                self.status_bar.showMessage("✅ Quantum connection active")
            else:
                self.log_message("❌ Quantum connection failed")
                self.status_bar.showMessage("❌ Quantum connection failed")

        except Exception as e:
            self.log_message(f"❌ Quantum connection error: {e}")
            self.status_bar.showMessage(f"❌ Quantum error: {e}")

    def connect_direct_chrome(self):
        """🚀 Connect using Direct Chrome launch"""
        try:
            self.log_message("🚀 Starting Direct Chrome launch...")
            self.status_bar.showMessage("🚀 Launching Chrome directly...")

            chrome_exe = self.find_chrome()
            if not chrome_exe:
                self.log_message("❌ Chrome not found")
                self.status_bar.showMessage("❌ Chrome not found")
                return

            # Chrome flags for Quotex
            chrome_flags = [
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-popup-blocking",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--remote-debugging-port=9222",
                "--user-data-dir=" + os.path.join(os.path.expanduser("~"), "VIP_BIG_BANG_Chrome", "Direct"),
                "--window-size=1366,768",
                "--start-maximized"
            ]

            # Build command
            cmd = [chrome_exe] + chrome_flags + ["https://quotex.io/en/sign-in"]

            # Launch Chrome
            self.chrome_process = subprocess.Popen(cmd, shell=False)
            time.sleep(3)

            self.connected = True
            self.connection_method = "Direct Chrome"
            self.connection_status.setText("🟢 CHROME LAUNCHED")
            self.connection_status.setStyleSheet("color: #32CD32;")
            self.method_label.setText("Method: Direct Chrome")
            self.chrome_status_label.setText("Chrome: Direct Launch Active")
            self.quotex_status_label.setText("Quotex: Loading...")
            self.disconnect_btn.setEnabled(True)

            self.log_message("✅ Chrome launched directly to Quotex!")
            self.status_bar.showMessage("✅ Chrome launched to Quotex")

        except Exception as e:
            self.log_message(f"❌ Direct Chrome error: {e}")
            self.status_bar.showMessage(f"❌ Direct Chrome error: {e}")

    def connect_devtools(self):
        """🔗 Connect using DevTools"""
        try:
            self.log_message("🔗 Connecting via DevTools...")
            self.status_bar.showMessage("🔗 Checking Chrome DevTools...")

            # Check if Chrome DevTools is available
            response = requests.get("http://localhost:9222/json", timeout=5)

            if response.status_code == 200:
                tabs = response.json()
                self.log_message(f"✅ Chrome DevTools active - {len(tabs)} tabs")

                # Check for existing Quotex tab
                quotex_tab = None
                for tab in tabs:
                    if 'quotex' in tab.get('url', '').lower():
                        quotex_tab = tab
                        break

                if quotex_tab:
                    # Activate existing tab
                    tab_id = quotex_tab['id']
                    activate_url = f"http://localhost:9222/json/activate/{tab_id}"
                    requests.get(activate_url, timeout=2)

                    self.log_message("✅ Activated existing Quotex tab")
                else:
                    # Create new Quotex tab
                    new_tab_url = "http://localhost:9222/json/new?https://quotex.io/en/sign-in"
                    response = requests.get(new_tab_url, timeout=5)

                    if response.status_code == 200:
                        self.log_message("✅ Created new Quotex tab")
                    else:
                        self.log_message("❌ Failed to create Quotex tab")
                        return

                self.connected = True
                self.connection_method = "DevTools"
                self.connection_status.setText("🟢 DEVTOOLS CONNECTED")
                self.connection_status.setStyleSheet("color: #32CD32;")
                self.method_label.setText("Method: DevTools Connection")
                self.chrome_status_label.setText("Chrome: DevTools Active")
                self.quotex_status_label.setText("Quotex: Tab Active")
                self.disconnect_btn.setEnabled(True)

                self.status_bar.showMessage("✅ DevTools connection successful")

            else:
                self.log_message("❌ Chrome DevTools not available")
                self.status_bar.showMessage("❌ Chrome DevTools not available")

        except Exception as e:
            self.log_message(f"❌ DevTools connection error: {e}")
            self.status_bar.showMessage(f"❌ DevTools error: {e}")

    def test_connection(self):
        """🧪 Test current connection"""
        try:
            self.log_message("🧪 Testing connection...")

            # Test Chrome DevTools
            try:
                response = requests.get("http://localhost:9222/json", timeout=3)
                if response.status_code == 200:
                    tabs = response.json()
                    self.log_message(f"✅ Chrome DevTools: {len(tabs)} tabs active")

                    # Check for Quotex tabs
                    quotex_tabs = [tab for tab in tabs if 'quotex' in tab.get('url', '').lower()]
                    if quotex_tabs:
                        self.log_message(f"🎯 Found {len(quotex_tabs)} Quotex tabs")
                        for tab in quotex_tabs:
                            self.log_message(f"   📱 {tab.get('title', 'Unknown')}")
                    else:
                        self.log_message("⚠️ No Quotex tabs found")
                else:
                    self.log_message("❌ Chrome DevTools not responding")
            except:
                self.log_message("❌ Chrome DevTools not available")

            # Test Quotex access
            try:
                response = requests.get("https://quotex.io", timeout=5)
                if response.status_code == 200:
                    self.log_message("✅ Quotex.io is accessible")
                else:
                    self.log_message(f"⚠️ Quotex.io status: {response.status_code}")
            except:
                self.log_message("❌ Quotex.io not accessible (may need VPN)")

            self.status_bar.showMessage("🧪 Connection test completed")

        except Exception as e:
            self.log_message(f"❌ Test error: {e}")

    def open_quotex_tab(self):
        """🌐 Open new Quotex tab"""
        try:
            self.log_message("🌐 Opening Quotex tab...")

            # Try DevTools method first
            try:
                new_tab_url = "http://localhost:9222/json/new?https://quotex.io/en/sign-in"
                response = requests.get(new_tab_url, timeout=5)

                if response.status_code == 200:
                    self.log_message("✅ Quotex tab opened via DevTools")
                    self.quotex_status_label.setText("Quotex: Tab Opened")
                    self.status_bar.showMessage("✅ Quotex tab opened")
                    return
            except:
                pass

            # Fallback to direct Chrome launch
            chrome_exe = self.find_chrome()
            if chrome_exe:
                cmd = [chrome_exe, "https://quotex.io/en/sign-in"]
                subprocess.Popen(cmd, shell=False)
                self.log_message("✅ Quotex opened in new Chrome window")
                self.status_bar.showMessage("✅ Quotex opened in Chrome")
            else:
                self.log_message("❌ Chrome not found")
                self.status_bar.showMessage("❌ Chrome not found")

        except Exception as e:
            self.log_message(f"❌ Open Quotex error: {e}")

    def disconnect_all(self):
        """🛑 Disconnect all connections"""
        try:
            self.log_message("🛑 Disconnecting all connections...")

            # Reset connection state
            self.connected = False
            self.connection_method = None
            self.connection_status.setText("⭕ DISCONNECTED")
            self.connection_status.setStyleSheet("color: #FF4444;")
            self.method_label.setText("Method: None")
            self.chrome_status_label.setText("Chrome: Not Running")
            self.quotex_status_label.setText("Quotex: Not Connected")
            self.disconnect_btn.setEnabled(False)

            # Close Chrome process if exists
            if self.chrome_process:
                try:
                    self.chrome_process.terminate()
                    self.chrome_process = None
                except:
                    pass

            self.log_message("✅ All connections disconnected")
            self.status_bar.showMessage("🛑 Disconnected")

        except Exception as e:
            self.log_message(f"❌ Disconnect error: {e}")

def main():
    """🚀 Main function"""
    print("🚀 VIP BIG BANG - Complete Quotex Connection")
    print("💎 All Connection Methods Available")
    print("⚡ Persistent + Stealth + Quantum + Direct + DevTools")
    print("-" * 60)

    app = QApplication(sys.argv)

    # Create and show main window
    window = CompleteQuotexConnection()
    window.show()

    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
