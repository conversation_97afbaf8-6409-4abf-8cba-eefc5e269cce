@echo off
echo.
echo ========================================
echo   VIP BIG BANG Enterprise Installer
echo   Ultra-Fast Trading Robot
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo [INFO] Python detected
python --version

:: Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Python version: %PYTHON_VERSION%

:: Create virtual environment
echo.
echo [INFO] Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo [ERROR] Failed to create virtual environment
    pause
    exit /b 1
)

:: Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

:: Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip

:: Install requirements
echo.
echo [INFO] Installing dependencies...
echo This may take a few minutes...
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

:: Create necessary directories
echo.
echo [INFO] Creating directories...
if not exist "logs" mkdir logs
if not exist "data" mkdir data

:: Copy default config if not exists
if not exist "config.json" (
    echo [INFO] Creating default configuration...
    copy config.json config_backup.json >nul 2>&1
)

:: Install Chrome extension (optional)
echo.
echo [INFO] Chrome Extension Setup
echo To install the Chrome extension:
echo 1. Open Chrome and go to chrome://extensions/
echo 2. Enable "Developer mode"
echo 3. Click "Load unpacked"
echo 4. Select the "chrome_extension" folder
echo.

:: Create desktop shortcut
echo [INFO] Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\Desktop
set SHORTCUT=%DESKTOP%\VIP BIG BANG Enterprise.lnk
set TARGET=%CD%\run.bat

:: Create run.bat file
echo @echo off > run.bat
echo cd /d "%CD%" >> run.bat
echo call venv\Scripts\activate.bat >> run.bat
echo python main.py >> run.bat
echo pause >> run.bat

:: Create shortcut using PowerShell
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%TARGET%'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.IconLocation = '%CD%\icon.ico'; $Shortcut.Description = 'VIP BIG BANG Enterprise Trading Robot'; $Shortcut.Save()"

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo [SUCCESS] VIP BIG BANG Enterprise has been installed successfully!
echo.
echo Quick Start:
echo 1. Double-click "VIP BIG BANG Enterprise" on your desktop
echo 2. Or run: run.bat
echo 3. Or manually: python main.py
echo.
echo Important Notes:
echo - First run will be in DEMO mode for safety
echo - Configure settings in config.json
echo - Install Chrome extension for backup trading
echo - Check logs/ folder for troubleshooting
echo.
echo Documentation: README.md
echo Support: Check the docs/ folder
echo.
echo Happy Trading! 🚀
echo.
pause
