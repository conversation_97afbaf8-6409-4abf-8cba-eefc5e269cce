<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .content {
            padding: 20px;
        }
        
        .status-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-dot.active {
            background: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }
        
        .status-dot.inactive {
            background: #f44336;
        }
        
        .data-display {
            background: rgba(0,0,0,0.2);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .button {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            margin: 5px 0;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }
        
        .button.primary {
            background: #4CAF50;
            border-color: #4CAF50;
        }
        
        .button.primary:hover {
            background: #45a049;
        }
        
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(0,0,0,0.2);
            padding: 10px;
            border-radius: 5px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }
        
        .footer {
            text-align: center;
            padding: 15px;
            font-size: 11px;
            opacity: 0.7;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 VIP BIG BANG</h1>
        <div class="subtitle">Professional Trading Assistant</div>
    </div>
    
    <div class="content">
        <div class="status-card">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Checking status...</span>
            </div>
            
            <div class="data-display" id="dataDisplay">
                No data available
            </div>
        </div>
        
        <button class="button primary" id="activateBtn">🔄 Refresh Status</button>
        <button class="button" id="openQuotexBtn">🌐 Open Quotex</button>
        <button class="button" id="settingsBtn">⚙️ Settings</button>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="priceValue">--</div>
                <div class="stat-label">Current Price</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="balanceValue">--</div>
                <div class="stat-label">Balance</div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        VIP BIG BANG v1.0.0 - Professional Edition
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
