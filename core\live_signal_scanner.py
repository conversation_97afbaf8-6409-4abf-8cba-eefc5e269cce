"""
VIP BIG BANG Enterprise - Live Signal Scanner
Real-time scanning for opportunities where all analyses align
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import logging
from datetime import datetime

class LiveSignalScanner:
    """
    Live Signal Scanner - VIP BIG BANG complementary analysis
    Scans market in real-time for opportunities where all analyses align
    Displays signal opportunities in signal box format
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("LiveSignalScanner")
        
        # Scanner parameters
        self.min_alignment_percentage = 0.7  # 70% of analyses must agree
        self.strong_alignment_percentage = 0.8  # 80% for strong signals
        self.perfect_alignment_percentage = 0.9  # 90% for perfect signals
        
        # Signal strength thresholds
        self.weak_signal_threshold = 0.6
        self.medium_signal_threshold = 0.75
        self.strong_signal_threshold = 0.85
        
        # Signal box display settings
        self.max_signals_display = 5
        self.signal_timeout_minutes = 10
        
        self.logger.debug("Live Signal Scanner initialized")
    
    def scan_analysis_alignment(self, analysis_results: Dict) -> Dict:
        """Scan all analysis results for alignment"""
        if not analysis_results:
            return {
                'total_analyses': 0,
                'bullish_count': 0,
                'bearish_count': 0,
                'neutral_count': 0,
                'alignment_percentage': 0.0,
                'dominant_direction': 'NEUTRAL'
            }
        
        # Count directional signals
        bullish_count = 0
        bearish_count = 0
        neutral_count = 0
        total_analyses = 0
        
        # Analyze each result
        for analysis_name, result in analysis_results.items():
            if isinstance(result, dict) and 'direction' in result:
                total_analyses += 1
                direction = result['direction']
                
                if direction == 'UP':
                    bullish_count += 1
                elif direction == 'DOWN':
                    bearish_count += 1
                else:
                    neutral_count += 1
        
        # Calculate alignment
        if total_analyses > 0:
            bullish_percentage = bullish_count / total_analyses
            bearish_percentage = bearish_count / total_analyses
            neutral_percentage = neutral_count / total_analyses
            
            # Determine dominant direction and alignment
            if bullish_percentage >= bearish_percentage and bullish_percentage >= neutral_percentage:
                dominant_direction = 'UP'
                alignment_percentage = bullish_percentage
            elif bearish_percentage >= neutral_percentage:
                dominant_direction = 'DOWN'
                alignment_percentage = bearish_percentage
            else:
                dominant_direction = 'NEUTRAL'
                alignment_percentage = neutral_percentage
        else:
            dominant_direction = 'NEUTRAL'
            alignment_percentage = 0.0
        
        return {
            'total_analyses': total_analyses,
            'bullish_count': bullish_count,
            'bearish_count': bearish_count,
            'neutral_count': neutral_count,
            'bullish_percentage': bullish_percentage if total_analyses > 0 else 0,
            'bearish_percentage': bearish_percentage if total_analyses > 0 else 0,
            'neutral_percentage': neutral_percentage if total_analyses > 0 else 0,
            'alignment_percentage': alignment_percentage,
            'dominant_direction': dominant_direction
        }
    
    def calculate_signal_strength(self, alignment_data: Dict, analysis_results: Dict) -> Dict:
        """Calculate overall signal strength based on alignment and confidence"""
        alignment_percentage = alignment_data['alignment_percentage']
        
        # Calculate average confidence of aligned signals
        aligned_confidences = []
        dominant_direction = alignment_data['dominant_direction']
        
        for analysis_name, result in analysis_results.items():
            if isinstance(result, dict) and 'direction' in result and 'confidence' in result:
                if result['direction'] == dominant_direction:
                    aligned_confidences.append(result['confidence'])
        
        avg_confidence = np.mean(aligned_confidences) if aligned_confidences else 0.0
        
        # Calculate combined signal strength
        signal_strength = (alignment_percentage * 0.6) + (avg_confidence * 0.4)
        
        # Classify signal strength
        if signal_strength >= self.strong_signal_threshold:
            strength_level = 'STRONG'
            strength_color = 'GREEN' if dominant_direction == 'UP' else 'RED'
        elif signal_strength >= self.medium_signal_threshold:
            strength_level = 'MEDIUM'
            strength_color = 'YELLOW'
        elif signal_strength >= self.weak_signal_threshold:
            strength_level = 'WEAK'
            strength_color = 'ORANGE'
        else:
            strength_level = 'VERY_WEAK'
            strength_color = 'GRAY'
        
        return {
            'signal_strength': signal_strength,
            'strength_level': strength_level,
            'strength_color': strength_color,
            'avg_confidence': avg_confidence,
            'aligned_analyses_count': len(aligned_confidences)
        }
    
    def generate_signal_box_data(self, alignment_data: Dict, strength_data: Dict, analysis_results: Dict) -> Dict:
        """Generate signal box display data"""
        
        # Signal box properties
        signal_box = {
            'direction': alignment_data['dominant_direction'],
            'strength': strength_data['strength_level'],
            'color': strength_data['strength_color'],
            'percentage': int(alignment_data['alignment_percentage'] * 100),
            'confidence': int(strength_data['avg_confidence'] * 100),
            'analyses_count': alignment_data['total_analyses'],
            'aligned_count': strength_data['aligned_analyses_count'],
            'timestamp': datetime.now(),
            'display_text': f"{alignment_data['dominant_direction']} {int(alignment_data['alignment_percentage'] * 100)}%"
        }
        
        # Add detailed breakdown
        signal_box['breakdown'] = {
            'bullish': f"{alignment_data['bullish_count']}/{alignment_data['total_analyses']}",
            'bearish': f"{alignment_data['bearish_count']}/{alignment_data['total_analyses']}",
            'neutral': f"{alignment_data['neutral_count']}/{alignment_data['total_analyses']}"
        }
        
        # Add analysis details
        signal_box['analysis_details'] = []
        for analysis_name, result in analysis_results.items():
            if isinstance(result, dict) and 'direction' in result:
                signal_box['analysis_details'].append({
                    'name': analysis_name,
                    'direction': result['direction'],
                    'confidence': result.get('confidence', 0.0),
                    'score': result.get('score', 0.5)
                })
        
        return signal_box
    
    def check_signal_quality(self, signal_box: Dict) -> Dict:
        """Check signal quality and trading recommendation"""
        alignment_percentage = signal_box['percentage'] / 100
        confidence = signal_box['confidence'] / 100
        strength_level = signal_box['strength']
        
        # Quality assessment
        quality_score = (alignment_percentage * 0.6) + (confidence * 0.4)
        
        if quality_score >= 0.85 and strength_level == 'STRONG':
            quality_level = 'EXCELLENT'
            trading_recommendation = 'STRONG_BUY' if signal_box['direction'] == 'UP' else 'STRONG_SELL'
            risk_level = 'LOW'
        elif quality_score >= 0.75 and strength_level in ['STRONG', 'MEDIUM']:
            quality_level = 'GOOD'
            trading_recommendation = 'BUY' if signal_box['direction'] == 'UP' else 'SELL'
            risk_level = 'MEDIUM'
        elif quality_score >= 0.65:
            quality_level = 'FAIR'
            trading_recommendation = 'WEAK_BUY' if signal_box['direction'] == 'UP' else 'WEAK_SELL'
            risk_level = 'HIGH'
        else:
            quality_level = 'POOR'
            trading_recommendation = 'NO_TRADE'
            risk_level = 'VERY_HIGH'
        
        return {
            'quality_score': quality_score,
            'quality_level': quality_level,
            'trading_recommendation': trading_recommendation,
            'risk_level': risk_level,
            'should_trade': quality_level in ['EXCELLENT', 'GOOD']
        }
    
    def analyze(self, analysis_results: Dict) -> Dict:
        """
        Main live signal scanner analysis
        Returns signal scanning results and recommendations
        """
        try:
            # Scan for alignment
            alignment_data = self.scan_analysis_alignment(analysis_results)
            
            # Calculate signal strength
            strength_data = self.calculate_signal_strength(alignment_data, analysis_results)
            
            # Generate signal box data
            signal_box = self.generate_signal_box_data(alignment_data, strength_data, analysis_results)
            
            # Check signal quality
            quality_check = self.check_signal_quality(signal_box)
            
            # Calculate overall score
            score = strength_data['signal_strength']
            
            # Determine direction
            direction = alignment_data['dominant_direction']
            
            # Calculate confidence
            confidence = strength_data['avg_confidence']
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'alignment_data': alignment_data,
                'strength_data': strength_data,
                'signal_box': signal_box,
                'quality_check': quality_check,
                'trading_allowed': quality_check['should_trade'],
                'details': f"Scanner: {direction} {int(alignment_data['alignment_percentage']*100)}% ({strength_data['strength_level']})"
            }
            
        except Exception as e:
            self.logger.error(f"Live signal scanner error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'trading_allowed': False,
                'details': f'Signal scanner failed: {str(e)}'
            }
