"""
VIP BIG BANG Enterprise - Security Manager
Enterprise-level security with encryption, licensing, and protection
"""

import hashlib
import hmac
import base64
import os
import platform
import uuid
import json
from pathlib import Path
from typing import Dict, Optional, Tuple
import logging
from datetime import datetime, timedelta
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import psutil

class SecurityManager:
    """
    Enterprise-level security management
    Handles licensing, encryption, and hardware binding
    """
    
    def __init__(self):
        self.logger = logging.getLogger("SecurityManager")
        
        # Security configuration
        self.license_file = Path("license.key")
        self.hardware_file = Path("hardware.id")
        self.session_file = Path("session.enc")
        
        # Encryption keys
        self.master_key = None
        self.session_key = None
        
        # Hardware fingerprint
        self.hardware_id = None
        
        # License information
        self.license_info = None
        self.license_valid = False
        
        # Initialize security components
        self._initialize_security()
        
        self.logger.info("Security Manager initialized")
    
    def _initialize_security(self):
        """Initialize security components"""
        try:
            # Generate or load hardware ID
            self.hardware_id = self._get_hardware_id()
            
            # Initialize encryption
            self._initialize_encryption()
            
            # Load license information
            self._load_license()
            
        except Exception as e:
            self.logger.error(f"Security initialization failed: {e}")
    
    def _get_hardware_id(self) -> str:
        """Generate unique hardware fingerprint"""
        try:
            # Check if hardware ID already exists
            if self.hardware_file.exists():
                with open(self.hardware_file, 'r') as f:
                    stored_id = f.read().strip()
                    if self._validate_hardware_id(stored_id):
                        return stored_id
            
            # Generate new hardware ID
            hardware_info = []
            
            # CPU information
            try:
                import cpuinfo
                cpu_info = cpuinfo.get_cpu_info()
                hardware_info.append(cpu_info.get('brand_raw', ''))
            except:
                hardware_info.append(platform.processor())
            
            # System information
            hardware_info.extend([
                platform.system(),
                platform.machine(),
                str(psutil.virtual_memory().total),
                platform.node()
            ])
            
            # MAC address
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                hardware_info.append(mac)
            except:
                hardware_info.append(str(uuid.getnode()))
            
            # Create hash
            hardware_string = '|'.join(hardware_info)
            hardware_hash = hashlib.sha256(hardware_string.encode()).hexdigest()
            
            # Save hardware ID
            with open(self.hardware_file, 'w') as f:
                f.write(hardware_hash)
            
            self.logger.info("Hardware ID generated and saved")
            return hardware_hash
            
        except Exception as e:
            self.logger.error(f"Failed to generate hardware ID: {e}")
            return "default_hardware_id"
    
    def _validate_hardware_id(self, hardware_id: str) -> bool:
        """Validate hardware ID format"""
        return len(hardware_id) == 64 and all(c in '0123456789abcdef' for c in hardware_id)
    
    def _initialize_encryption(self):
        """Initialize encryption keys"""
        try:
            # Generate master key from hardware ID
            password = self.hardware_id.encode()
            salt = b'vip_big_bang_enterprise_salt'
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            self.master_key = Fernet(key)
            
            # Generate session key
            self.session_key = Fernet.generate_key()
            
            self.logger.debug("Encryption keys initialized")
            
        except Exception as e:
            self.logger.error(f"Encryption initialization failed: {e}")
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            if self.master_key:
                encrypted = self.master_key.encrypt(data.encode())
                return base64.urlsafe_b64encode(encrypted).decode()
            return data
        except Exception as e:
            self.logger.error(f"Encryption failed: {e}")
            return data
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            if self.master_key:
                decoded = base64.urlsafe_b64decode(encrypted_data.encode())
                decrypted = self.master_key.decrypt(decoded)
                return decrypted.decode()
            return encrypted_data
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            return encrypted_data
    
    def _load_license(self):
        """Load and validate license"""
        try:
            if not self.license_file.exists():
                self.logger.warning("License file not found")
                self._create_trial_license()
                return
            
            with open(self.license_file, 'r') as f:
                license_data = f.read()
            
            # Decrypt license data
            try:
                decrypted_license = self.decrypt_data(license_data)
                self.license_info = json.loads(decrypted_license)
            except:
                # Try loading as plain text (for development)
                self.license_info = json.loads(license_data)
            
            # Validate license
            self.license_valid = self._validate_license()
            
            if self.license_valid:
                self.logger.info("License validated successfully")
            else:
                self.logger.warning("License validation failed")
            
        except Exception as e:
            self.logger.error(f"License loading failed: {e}")
            self._create_trial_license()
    
    def _create_trial_license(self):
        """Create a trial license"""
        trial_license = {
            "type": "trial",
            "hardware_id": self.hardware_id,
            "issued_date": datetime.now().isoformat(),
            "expiry_date": (datetime.now() + timedelta(days=7)).isoformat(),
            "features": {
                "auto_trade": True,
                "advanced_analysis": True,
                "multi_threading": True,
                "max_trades_per_day": 50
            },
            "version": "1.0.0"
        }
        
        try:
            license_json = json.dumps(trial_license, indent=2)
            encrypted_license = self.encrypt_data(license_json)
            
            with open(self.license_file, 'w') as f:
                f.write(encrypted_license)
            
            self.license_info = trial_license
            self.license_valid = True
            
            self.logger.info("Trial license created (7 days)")
            
        except Exception as e:
            self.logger.error(f"Failed to create trial license: {e}")
    
    def _validate_license(self) -> bool:
        """Validate license information"""
        if not self.license_info:
            return False
        
        try:
            # Check hardware binding
            if self.license_info.get('hardware_id') != self.hardware_id:
                self.logger.error("License hardware binding mismatch")
                return False
            
            # Check expiry date
            expiry_str = self.license_info.get('expiry_date')
            if expiry_str:
                expiry_date = datetime.fromisoformat(expiry_str)
                if datetime.now() > expiry_date:
                    self.logger.error("License has expired")
                    return False
            
            # Check required fields
            required_fields = ['type', 'issued_date', 'features']
            for field in required_fields:
                if field not in self.license_info:
                    self.logger.error(f"License missing required field: {field}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"License validation error: {e}")
            return False
    
    def verify_license(self) -> bool:
        """Public method to verify license"""
        return self.license_valid
    
    def get_license_info(self) -> Dict:
        """Get license information"""
        if self.license_info:
            # Return safe copy without sensitive data
            safe_info = {
                'type': self.license_info.get('type', 'unknown'),
                'expiry_date': self.license_info.get('expiry_date'),
                'features': self.license_info.get('features', {}),
                'valid': self.license_valid
            }
            
            # Calculate days remaining
            if safe_info['expiry_date']:
                try:
                    expiry = datetime.fromisoformat(safe_info['expiry_date'])
                    days_remaining = (expiry - datetime.now()).days
                    safe_info['days_remaining'] = max(0, days_remaining)
                except:
                    safe_info['days_remaining'] = 0
            
            return safe_info
        
        return {'type': 'none', 'valid': False}
    
    def check_feature_access(self, feature: str) -> bool:
        """Check if a feature is available in current license"""
        if not self.license_valid or not self.license_info:
            return False
        
        features = self.license_info.get('features', {})
        return features.get(feature, False)
    
    def create_session_token(self, user_data: Dict) -> str:
        """Create encrypted session token"""
        try:
            session_data = {
                'user_data': user_data,
                'created_at': datetime.now().isoformat(),
                'hardware_id': self.hardware_id,
                'expires_at': (datetime.now() + timedelta(hours=24)).isoformat()
            }
            
            session_json = json.dumps(session_data)
            
            if self.session_key:
                fernet = Fernet(self.session_key)
                encrypted_session = fernet.encrypt(session_json.encode())
                return base64.urlsafe_b64encode(encrypted_session).decode()
            
            return base64.urlsafe_b64encode(session_json.encode()).decode()
            
        except Exception as e:
            self.logger.error(f"Session token creation failed: {e}")
            return ""
    
    def validate_session_token(self, token: str) -> Optional[Dict]:
        """Validate and decode session token"""
        try:
            decoded_token = base64.urlsafe_b64decode(token.encode())
            
            if self.session_key:
                fernet = Fernet(self.session_key)
                decrypted_data = fernet.decrypt(decoded_token)
                session_data = json.loads(decrypted_data.decode())
            else:
                session_data = json.loads(decoded_token.decode())
            
            # Check expiry
            expires_at = datetime.fromisoformat(session_data['expires_at'])
            if datetime.now() > expires_at:
                return None
            
            # Check hardware binding
            if session_data.get('hardware_id') != self.hardware_id:
                return None
            
            return session_data
            
        except Exception as e:
            self.logger.error(f"Session token validation failed: {e}")
            return None
    
    def get_security_status(self) -> Dict:
        """Get overall security status"""
        return {
            'license_valid': self.license_valid,
            'hardware_bound': bool(self.hardware_id),
            'encryption_enabled': bool(self.master_key),
            'session_security': bool(self.session_key),
            'license_info': self.get_license_info()
        }
