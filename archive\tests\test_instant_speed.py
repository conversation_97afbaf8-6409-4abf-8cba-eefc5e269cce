"""
Test script for Instant Analysis Engine
Verify sub-50ms performance for 5-second trades
"""

import time
import random
from core.instant_engine import UltraFastTradingSystem

def test_instant_performance():
    """Test the instant analysis performance"""
    print("🚀 VIP BIG BANG - INSTANT MODE TEST")
    print("=" * 50)
    print("🎯 Target: < 50ms total processing")
    print("⚡ Testing for 5-second trades")
    print("-" * 50)
    
    # Initialize ultra-fast system
    system = UltraFastTradingSystem()
    
    # Wait for background calculator to start
    time.sleep(0.5)
    
    # Feed some initial data
    print("📊 Feeding initial market data...")
    for i in range(20):
        price = 1.07000 + random.uniform(-0.001, 0.001)
        volume = random.uniform(0.8, 1.2)
        system.process_market_data(price, volume)
        time.sleep(0.01)
    
    print("✅ Initial data loaded")
    print("\n🔥 SPEED TEST - 100 iterations:")
    print("-" * 30)
    
    times = []
    
    for i in range(100):
        # Add new market data
        price = 1.07000 + random.uniform(-0.002, 0.002)
        volume = random.uniform(0.5, 1.5)
        system.process_market_data(price, volume)
        
        # Measure decision time
        start_time = time.time()
        decision = system.get_trading_decision()
        end_time = time.time()
        
        processing_time = (end_time - start_time) * 1000  # Convert to ms
        times.append(processing_time)
        
        if i % 20 == 0:
            print(f"Iteration {i+1:3d}: {processing_time:6.2f}ms - {decision['trade_decision']['signal']}")
    
    # Calculate statistics
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    under_50ms = sum(1 for t in times if t < 50)
    under_30ms = sum(1 for t in times if t < 30)
    under_10ms = sum(1 for t in times if t < 10)
    
    print("\n📊 PERFORMANCE RESULTS:")
    print("=" * 40)
    print(f"Average Time:     {avg_time:6.2f}ms")
    print(f"Minimum Time:     {min_time:6.2f}ms")
    print(f"Maximum Time:     {max_time:6.2f}ms")
    print(f"Under 50ms:       {under_50ms:3d}/100 ({under_50ms}%)")
    print(f"Under 30ms:       {under_30ms:3d}/100 ({under_30ms}%)")
    print(f"Under 10ms:       {under_10ms:3d}/100 ({under_10ms}%)")
    
    # Performance evaluation
    print("\n🎯 EVALUATION:")
    print("-" * 20)
    
    if avg_time < 10:
        print("🏆 EXCELLENT! Ultra-fast performance")
        grade = "A+"
    elif avg_time < 30:
        print("✅ GREAT! Very fast performance")
        grade = "A"
    elif avg_time < 50:
        print("👍 GOOD! Target achieved")
        grade = "B+"
    else:
        print("⚠️  NEEDS IMPROVEMENT")
        grade = "C"
    
    print(f"Performance Grade: {grade}")
    
    # Test for 5-second trades
    print(f"\n⏱️  FOR 5-SECOND TRADES:")
    print(f"Analysis Time:    {avg_time:6.2f}ms ({avg_time/50:.1f}% of trade)")
    print(f"Remaining Time:   {5000-avg_time:6.2f}ms for execution & monitoring")
    
    if avg_time < 50:
        print("✅ PERFECT for 5-second trades!")
    elif avg_time < 100:
        print("👍 ACCEPTABLE for 5-second trades")
    else:
        print("❌ TOO SLOW for 5-second trades")
    
    return avg_time < 50

def test_lightning_execution():
    """Test lightning-fast execution"""
    print("\n⚡ LIGHTNING EXECUTION TEST:")
    print("-" * 30)
    
    system = UltraFastTradingSystem()
    
    # Test execution speed
    execution_times = []
    
    for i in range(50):
        start_time = time.time()
        trade = system.trade_executor.execute_lightning_trade('BUY', 1.0)
        end_time = time.time()
        
        exec_time = (end_time - start_time) * 1000
        execution_times.append(exec_time)
    
    avg_exec_time = sum(execution_times) / len(execution_times)
    min_exec_time = min(execution_times)
    max_exec_time = max(execution_times)
    
    print(f"Average Execution: {avg_exec_time:6.2f}ms")
    print(f"Minimum Execution: {min_exec_time:6.2f}ms")
    print(f"Maximum Execution: {max_exec_time:6.2f}ms")
    
    if avg_exec_time < 20:
        print("🚀 LIGHTNING FAST execution!")
        return True
    else:
        print("⚠️  Execution needs optimization")
        return False

def main():
    """Main test function"""
    print("🎮 VIP BIG BANG - INSTANT PERFORMANCE TEST")
    print("🎯 Optimized for 5-second binary options")
    print("=" * 60)
    
    # Test analysis speed
    analysis_ok = test_instant_performance()
    
    # Test execution speed
    execution_ok = test_lightning_execution()
    
    print("\n🏁 FINAL RESULTS:")
    print("=" * 30)
    print(f"Analysis Speed:   {'✅ PASS' if analysis_ok else '❌ FAIL'}")
    print(f"Execution Speed:  {'✅ PASS' if execution_ok else '❌ FAIL'}")
    
    if analysis_ok and execution_ok:
        print("\n🏆 SYSTEM READY FOR 5-SECOND TRADES!")
        print("⚡ Lightning-fast performance achieved!")
    else:
        print("\n⚠️  SYSTEM NEEDS OPTIMIZATION")
        print("🔧 Further tuning required")

if __name__ == "__main__":
    main()
