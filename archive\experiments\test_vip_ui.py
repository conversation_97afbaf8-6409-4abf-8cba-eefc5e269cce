#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 VIP BIG BANG - UI Test
تست UI VIP BIG BANG
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from vip_exact_ui import VIPExactUI
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont
    
    def test_ui():
        """تست UI"""
        print("🧪 Starting VIP BIG BANG UI Test...")
        
        # Create application
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("VIP BIG BANG Test")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("VIP Trading")
        
        # Apply global font
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # Create main window
        print("🎮 Creating VIP UI...")
        window = VIPExactUI()
        
        # Show window
        print("📱 Showing window...")
        window.show()
        
        # Center window on screen
        screen = app.primaryScreen().geometry()
        window.move(
            (screen.width() - window.width()) // 2,
            (screen.height() - window.height()) // 2
        )
        
        print("✅ VIP BIG BANG UI Test launched successfully!")
        print("🎯 Window size:", window.size())
        print("📍 Window position:", window.pos())
        
        # Run application
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(test_ui())
        
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("📦 Installing required packages...")
    
    import subprocess
    
    # Install PySide6 if not available
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6"])
        print("✅ PySide6 installed successfully!")
        
        # Try again
        from vip_exact_ui import VIPExactUI
        from PySide6.QtWidgets import QApplication
        
        test_ui()
        
    except Exception as install_error:
        print(f"❌ Installation failed: {install_error}")
        print("🔧 Please install PySide6 manually:")
        print("   pip install PySide6")
        
except Exception as e:
    print(f"❌ Error: {e}")
    print("🔧 Please check your Python environment and dependencies.")
