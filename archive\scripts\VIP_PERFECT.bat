@echo off
title VIP BIG BANG - Perfect UI
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                   🎯 VIP BIG BANG PERFECT 🎯                ║
echo ║                                                              ║
echo ║              Ultimate Exact UI Recreation                   ║
echo ║                                                              ║
echo ║                    ✨ PERFECTION MODE ✨                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Checking system requirements...

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    echo 📦 Please install Python from https://python.org
    pause
    exit /b 1
)
echo ✅ Python ready

REM Check PySide6
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing PySide6...
    python -m pip install PySide6
    if errorlevel 1 (
        echo ❌ Failed to install PySide6
        pause
        exit /b 1
    )
)
echo ✅ PySide6 ready

REM Check Perfect UI file
if not exist "vip_perfect_ui.py" (
    echo ❌ Perfect UI file not found!
    pause
    exit /b 1
)
echo ✅ Perfect UI file ready

echo.
echo 🎯 Launching VIP BIG BANG Perfect UI...
echo ✨ Get ready for perfection!
echo.

python vip_perfect_ui.py

if errorlevel 1 (
    echo.
    echo ❌ Error occurred
    pause
)

echo.
echo 👋 Thank you for using VIP BIG BANG Perfect!
pause
