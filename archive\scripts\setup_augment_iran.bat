@echo off
chcp 65001 >nul
echo.
echo 🔧 تنظیم Augment برای ایران
echo ================================
echo.

echo 📡 تنظیم متغیرهای محیطی...
set HTTP_PROXY=http://127.0.0.1:10809
set HTTPS_PROXY=http://127.0.0.1:10809
set NO_PROXY=localhost,127.0.0.1

echo ✅ متغیرهای محیطی تنظیم شد

echo.
echo 🔧 تنظیم Git...
git config --global http.proxy http://127.0.0.1:10809
git config --global https.proxy http://127.0.0.1:10809
git config --global http.sslVerify false
echo ✅ Git تنظیم شد

echo.
echo 📦 تنظیم npm...
npm config set proxy http://127.0.0.1:10809
npm config set https-proxy http://127.0.0.1:10809
npm config set strict-ssl false
echo ✅ npm تنظیم شد

echo.
echo 🐍 تنظیم pip...
if not exist "%APPDATA%\pip" mkdir "%APPDATA%\pip"
echo [global] > "%APPDATA%\pip\pip.ini"
echo proxy = http://127.0.0.1:10809 >> "%APPDATA%\pip\pip.ini"
echo trusted-host = pypi.org >> "%APPDATA%\pip\pip.ini"
echo                pypi.python.org >> "%APPDATA%\pip\pip.ini"
echo                files.pythonhosted.org >> "%APPDATA%\pip\pip.ini"
echo ✅ pip تنظیم شد

echo.
echo 🎯 ایجاد فایل تنظیمات VS Code...
if not exist "%APPDATA%\Code\User" mkdir "%APPDATA%\Code\User"
echo { > "%APPDATA%\Code\User\settings.json.backup"
echo     "http.proxy": "http://127.0.0.1:10809", >> "%APPDATA%\Code\User\settings.json.backup"
echo     "http.proxyStrictSSL": false, >> "%APPDATA%\Code\User\settings.json.backup"
echo     "http.proxySupport": "on", >> "%APPDATA%\Code\User\settings.json.backup"
echo     "https.proxy": "http://127.0.0.1:10809" >> "%APPDATA%\Code\User\settings.json.backup"
echo } >> "%APPDATA%\Code\User\settings.json.backup"
echo ✅ فایل تنظیمات VS Code ایجاد شد

echo.
echo 🔧 ایجاد فایل .augmentrc...
echo { > ".augmentrc"
echo     "proxy": { >> ".augmentrc"
echo         "http": "http://127.0.0.1:10809", >> ".augmentrc"
echo         "https": "http://127.0.0.1:10809" >> ".augmentrc"
echo     }, >> ".augmentrc"
echo     "network": { >> ".augmentrc"
echo         "timeout": 30000, >> ".augmentrc"
echo         "retries": 3 >> ".augmentrc"
echo     } >> ".augmentrc"
echo } >> ".augmentrc"
echo ✅ فایل .augmentrc ایجاد شد

echo.
echo 🎉 تنظیمات کامل شد!
echo.
echo 📋 مراحل بعدی:
echo 1. فیلترشکن خود را روشن کنید
echo 2. مطمئن شوید پورت 10809 فعال است
echo 3. VS Code را restart کنید
echo 4. Augment Extension را reload کنید
echo.
echo 💡 اگر پورت فیلترشکن شما متفاوت است:
echo    - فایل .augmentrc را ویرایش کنید
echo    - پورت 10809 را با پورت خود جایگزین کنید
echo.
echo 🚀 ربات VIP BIG BANG شما کاملاً مستقل کار می‌کند!
echo.
pause
