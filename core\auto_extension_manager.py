"""
🔌 AUTO EXTENSION MANAGER
🚀 AUTOMATICALLY INSTALLS AND <PERSON>NAGES CHROME EXTENSION
🛡️ INTEGRATED WITH MAIN ROBOT SYSTEM
"""

import os
import subprocess
import time
import logging
import json
import shutil
from pathlib import Path

class AutoExtensionManager:
    """
    🔌 AUTO EXTENSION MANAGER
    🚀 Automatically manages Chrome extension
    """
    
    def __init__(self):
        self.logger = logging.getLogger("AutoExtensionManager")
        
        # Paths
        self.extension_dir = "vip_extension"
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        # Extension state
        self.extension_installed = False
        self.chrome_process = None
        
        self.logger.info("🔌 Auto Extension Manager initialized")
    
    def find_chrome(self):
        """🔍 Find Chrome executable"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                return path
        return None
    
    def ensure_extension_files(self):
        """📁 Ensure extension files exist"""
        try:
            if not os.path.exists(self.extension_dir):
                os.makedirs(self.extension_dir)
            
            # Check if all required files exist
            required_files = [
                'manifest.json', 'content.js', 'injected.js', 
                'background.js', 'popup.html', 'popup.js'
            ]
            
            missing_files = []
            for file in required_files:
                if not os.path.exists(os.path.join(self.extension_dir, file)):
                    missing_files.append(file)
            
            if missing_files:
                self.logger.warning(f"⚠️ Missing extension files: {missing_files}")
                return False
            
            # Create simple icons if they don't exist
            self.create_simple_icons()
            
            self.logger.info("✅ Extension files verified")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Extension files check error: {e}")
            return False
    
    def create_simple_icons(self):
        """🎨 Create simple extension icons"""
        try:
            icon_sizes = [16, 48, 128]
            
            for size in icon_sizes:
                icon_path = os.path.join(self.extension_dir, f"icon{size}.png")
                
                if not os.path.exists(icon_path):
                    # Create minimal PNG (1x1 transparent pixel)
                    with open(icon_path, 'wb') as f:
                        # Minimal PNG data
                        png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'
                        f.write(png_data)
            
            self.logger.info("✅ Extension icons created")
            
        except Exception as e:
            self.logger.error(f"❌ Icon creation error: {e}")
    
    def launch_chrome_with_extension(self, url="https://quotex.io"):
        """🚀 Launch Chrome with extension automatically"""
        try:
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                self.logger.error("❌ Chrome not found")
                return False
            
            # Ensure extension files exist
            if not self.ensure_extension_files():
                self.logger.error("❌ Extension files missing")
                return False
            
            # Get absolute path to extension
            extension_path = os.path.abspath(self.extension_dir)
            
            # Professional Chrome flags
            chrome_flags = [
                f"--load-extension={extension_path}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-client-side-phishing-detection",
                "--disable-sync",
                "--disable-background-networking",
                "--disable-extensions-http-throttling",
                "--disable-extensions-file-access-check",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-infobars",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--ignore-certificate-errors",
                "--ignore-ssl-errors",
                "--disable-features=VizDisplayCompositor",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-domain-reliability",
                "--disable-component-update",
                "--disable-background-mode",
                "--disable-save-password-bubble",
                "--disable-single-click-autofill",
                "--disable-dev-shm-usage",
                "--disable-logging",
                "--disable-login-animations",
                "--disable-notifications",
                "--disable-gpu-sandbox",
                "--disable-software-rasterizer",
                "--disable-field-trial-config",
                "--disable-back-forward-cache",
                "--metrics-recording-only",
                "--no-report-upload",
                "--disable-breakpad",
                "--disable-crash-reporter",
                "--password-store=basic",
                "--use-mock-keychain",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--remote-debugging-port=9222",
                "--enable-automation=false"
            ]
            
            # Build command
            cmd = [chrome_exe] + chrome_flags + [url]
            
            self.logger.info("🚀 Launching Chrome with VIP BIG BANG Extension...")
            
            # Launch Chrome
            self.chrome_process = subprocess.Popen(cmd, shell=False)
            
            # Wait for Chrome to start
            time.sleep(3)
            
            self.extension_installed = True
            self.logger.info("✅ Chrome launched with extension!")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to launch Chrome with extension: {e}")
            return False
    
    def check_extension_status(self):
        """📊 Check extension status"""
        try:
            if not self.chrome_process:
                return {"status": "not_running", "message": "Chrome not launched"}
            
            # Check if Chrome process is still running
            if self.chrome_process.poll() is not None:
                return {"status": "chrome_closed", "message": "Chrome process ended"}
            
            # Try to connect to DevTools to verify Chrome is running
            try:
                import requests
                response = requests.get("http://localhost:9222/json", timeout=2)
                if response.status_code == 200:
                    tabs = response.json()
                    quotex_tabs = [tab for tab in tabs if 'quotex' in tab.get('url', '').lower()]
                    
                    if quotex_tabs:
                        return {
                            "status": "active", 
                            "message": f"Extension active on {len(quotex_tabs)} Quotex tab(s)",
                            "tabs": len(quotex_tabs)
                        }
                    else:
                        return {
                            "status": "chrome_running", 
                            "message": "Chrome running, please open Quotex.io"
                        }
                else:
                    return {"status": "devtools_error", "message": "DevTools not accessible"}
                    
            except Exception:
                return {"status": "connection_error", "message": "Cannot connect to Chrome"}
            
        except Exception as e:
            self.logger.error(f"❌ Status check error: {e}")
            return {"status": "error", "message": str(e)}
    
    def stop_chrome(self):
        """🛑 Stop Chrome process"""
        try:
            if self.chrome_process:
                self.chrome_process.terminate()
                self.chrome_process = None
                self.extension_installed = False
                self.logger.info("🛑 Chrome process stopped")
                return True
        except Exception as e:
            self.logger.error(f"❌ Stop Chrome error: {e}")
        return False
    
    def restart_chrome(self):
        """🔄 Restart Chrome with extension"""
        try:
            self.stop_chrome()
            time.sleep(2)
            return self.launch_chrome_with_extension()
        except Exception as e:
            self.logger.error(f"❌ Restart Chrome error: {e}")
            return False
    
    def get_extension_info(self):
        """ℹ️ Get extension information"""
        return {
            "extension_dir": self.extension_dir,
            "extension_installed": self.extension_installed,
            "chrome_running": self.chrome_process is not None,
            "files_exist": self.ensure_extension_files()
        }
