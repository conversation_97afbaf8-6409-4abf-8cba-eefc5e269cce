#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 CONNECT TO QUOTEX NOW - WORKING METHODS
💎 همه روش‌های کاری برای اتصال فوری به Quotex
⚡ بر اساس متدهای قبلی که کار می‌کردند
"""

import os
import sys
import time
import subprocess
import logging
import requests
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import setup_logger

class QuotexConnectorNow:
    """🚀 اتصال فوری به Quotex با تمام روش‌های کاری"""
    
    def __init__(self):
        self.logger = setup_logger("QuotexConnectorNow")
        
        # Chrome paths
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        # Working Quotex URLs
        self.quotex_urls = [
            "https://quotex.io/en/sign-in",
            "https://quotex.io/",
            "https://quotex.io/en/",
            "https://qxbroker.com/en/sign-in"
        ]
        
        self.logger.info("🚀 Quotex Connector Now initialized")
    
    def find_chrome(self):
        """🔍 Find Chrome executable"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                self.logger.info(f"✅ Chrome found: {path}")
                return path
        
        self.logger.error("❌ Chrome not found")
        return None
    
    def method_1_persistent_chrome(self):
        """🔥 Method 1: Persistent Chrome Manager (BEST)"""
        try:
            self.logger.info("🔥 Method 1: Launching Persistent Chrome...")
            
            # Check if persistent_chrome_manager.py exists
            if not os.path.exists("persistent_chrome_manager.py"):
                self.logger.error("❌ persistent_chrome_manager.py not found")
                return False
            
            # Run persistent Chrome manager
            result = subprocess.run([
                sys.executable, "persistent_chrome_manager.py"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.logger.info("✅ Persistent Chrome launched successfully!")
                print("🏆 SUCCESS! Persistent Chrome with Quotex launched!")
                print("📱 Chrome should open with Quotex automatically")
                print("🔐 Login will be remembered for next time")
                return True
            else:
                self.logger.error(f"❌ Persistent Chrome failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Method 1 failed: {e}")
            return False
    
    def method_2_direct_chrome_launch(self):
        """🚀 Method 2: Direct Chrome Launch"""
        try:
            self.logger.info("🚀 Method 2: Direct Chrome Launch...")
            
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                return False
            
            # Advanced Chrome flags for Quotex
            chrome_flags = [
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-infobars",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--ignore-certificate-errors",
                "--remote-debugging-port=9222",
                "--user-data-dir=" + os.path.join(os.path.expanduser("~"), "ChromeQuotexProfile"),
                "--window-size=1366,768",
                "--start-maximized"
            ]
            
            # Build command
            cmd = [chrome_exe] + chrome_flags + ["https://quotex.io/en/sign-in"]
            
            # Launch Chrome
            process = subprocess.Popen(cmd, shell=False)
            time.sleep(3)
            
            self.logger.info("✅ Chrome launched directly to Quotex!")
            print("🏆 SUCCESS! Chrome launched to Quotex!")
            print("🌐 Quotex should be loading now...")
            print("🔐 You can login manually")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Method 2 failed: {e}")
            return False
    
    def method_3_devtools_connection(self):
        """🔗 Method 3: Connect to existing Chrome via DevTools"""
        try:
            self.logger.info("🔗 Method 3: DevTools Connection...")
            
            # Check if Chrome DevTools is available
            try:
                response = requests.get("http://localhost:9222/json", timeout=5)
                if response.status_code == 200:
                    tabs = response.json()
                    
                    # Check if Quotex tab exists
                    quotex_tab = None
                    for tab in tabs:
                        if 'quotex' in tab.get('url', '').lower():
                            quotex_tab = tab
                            break
                    
                    if quotex_tab:
                        # Activate existing Quotex tab
                        tab_id = quotex_tab['id']
                        activate_url = f"http://localhost:9222/json/activate/{tab_id}"
                        requests.get(activate_url, timeout=2)
                        
                        self.logger.info("✅ Connected to existing Quotex tab!")
                        print("🏆 SUCCESS! Connected to existing Quotex tab!")
                        print("📱 Quotex tab should be active now")
                        return True
                    else:
                        # Create new Quotex tab
                        new_tab_url = "http://localhost:9222/json/new?https://quotex.io/en/sign-in"
                        response = requests.get(new_tab_url, timeout=5)
                        
                        if response.status_code == 200:
                            self.logger.info("✅ Created new Quotex tab!")
                            print("🏆 SUCCESS! New Quotex tab created!")
                            print("🌐 Quotex should be loading in new tab...")
                            return True
                        
            except requests.exceptions.RequestException:
                self.logger.warning("⚠️ Chrome DevTools not available")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Method 3 failed: {e}")
            return False
    
    def method_4_simple_launcher(self):
        """🎯 Method 4: Simple Quotex Launcher"""
        try:
            self.logger.info("🎯 Method 4: Simple Launcher...")
            
            # Check if simple_quotex_launcher.py exists
            if not os.path.exists("simple_quotex_launcher.py"):
                self.logger.error("❌ simple_quotex_launcher.py not found")
                return False
            
            # Run simple launcher
            result = subprocess.run([
                sys.executable, "simple_quotex_launcher.py"
            ], capture_output=True, text=True, timeout=20)
            
            if result.returncode == 0:
                self.logger.info("✅ Simple launcher successful!")
                print("🏆 SUCCESS! Simple launcher worked!")
                print("🌐 Quotex should be opening...")
                return True
            else:
                self.logger.error(f"❌ Simple launcher failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Method 4 failed: {e}")
            return False
    
    def method_5_extension_install(self):
        """🔧 Method 5: Install Chrome Extension"""
        try:
            self.logger.info("🔧 Method 5: Installing Chrome Extension...")
            
            # Check if extension exists
            extension_path = "chrome_extension"
            if not os.path.exists(extension_path):
                self.logger.error("❌ Chrome extension not found")
                return False
            
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                return False
            
            # Launch Chrome with extension
            cmd = [
                chrome_exe,
                f"--load-extension={os.path.abspath(extension_path)}",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--remote-debugging-port=9222",
                "https://quotex.io/en/sign-in"
            ]
            
            process = subprocess.Popen(cmd, shell=False)
            time.sleep(3)
            
            self.logger.info("✅ Chrome with extension launched!")
            print("🏆 SUCCESS! Chrome with VIP extension launched!")
            print("🔧 Extension should be active")
            print("🌐 Quotex should be loading...")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Method 5 failed: {e}")
            return False
    
    def connect_now(self):
        """🚀 Try all methods to connect to Quotex NOW"""
        print("🚀" + "=" * 60 + "🚀")
        print("🔥" + " " * 15 + "CONNECTING TO QUOTEX NOW" + " " * 15 + "🔥")
        print("💎" + " " * 12 + "Using All Working Methods" + " " * 12 + "💎")
        print("🚀" + "=" * 60 + "🚀")
        
        methods = [
            ("🔥 Persistent Chrome (BEST)", self.method_1_persistent_chrome),
            ("🚀 Direct Chrome Launch", self.method_2_direct_chrome_launch),
            ("🔗 DevTools Connection", self.method_3_devtools_connection),
            ("🎯 Simple Launcher", self.method_4_simple_launcher),
            ("🔧 Extension Install", self.method_5_extension_install)
        ]
        
        for method_name, method_func in methods:
            print(f"\n🔄 Trying: {method_name}")
            print("-" * 50)
            
            try:
                if method_func():
                    print(f"\n🏆 SUCCESS! {method_name} worked!")
                    print("✅ Quotex connection established!")
                    print("\n💡 Next steps:")
                    print("1️⃣ Wait for Quotex to load completely")
                    print("2️⃣ Login to your account (if not already logged in)")
                    print("3️⃣ Start trading!")
                    print("\n🔥 Connection successful!")
                    return True
                else:
                    print(f"❌ {method_name} failed, trying next method...")
                    
            except Exception as e:
                print(f"❌ {method_name} error: {e}")
                continue
        
        print("\n❌ All methods failed!")
        print("🔧 Possible solutions:")
        print("1️⃣ Check internet connection")
        print("2️⃣ Make sure Chrome is installed")
        print("3️⃣ Try running as administrator")
        print("4️⃣ Disable antivirus temporarily")
        return False

def main():
    """🚀 Main function"""
    connector = QuotexConnectorNow()
    connector.connect_now()

if __name__ == "__main__":
    main()
