// VIP BIG BANG Background Script

console.log('🚀 VIP BIG BANG Background Service Worker Started');

// Store connections
const connections = new Map();
let latestData = null;

// Handle extension connections
chrome.runtime.onConnect.addListener((port) => {
    if (port.name === 'quotex-bridge') {
        console.log('✅ New bridge connection established');
        
        connections.set(port.sender.tab.id, port);
        
        // Handle messages from content script
        port.onMessage.addListener((message) => {
            if (message.type === 'quotex-data') {
                latestData = message.data;
                console.log('📡 Data received:', message.data);
                
                // Broadcast to all connections
                broadcastData(message.data);
            } else if (message.type === 'pong') {
                console.log('🏓 Pong received from tab:', port.sender.tab.id);
            }
        });
        
        // Handle disconnection
        port.onDisconnect.addListener(() => {
            connections.delete(port.sender.tab.id);
            console.log('❌ Bridge connection closed');
        });
        
        // Send ping to test connection
        port.postMessage({ type: 'ping' });
    }
});

// Broadcast data to all connections
function broadcastData(data) {
    connections.forEach((port, tabId) => {
        try {
            port.postMessage({
                type: 'broadcast-data',
                data: data
            });
        } catch (error) {
            // Remove dead connection
            connections.delete(tabId);
        }
    });
}

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    // Check if we're on Quotex
    if (tab.url.includes('quotex.io') || tab.url.includes('qxbroker.com')) {
        // Inject content script if not already injected
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        }).catch(() => {
            // Script might already be injected
        });
    } else {
        // Open Quotex
        chrome.tabs.create({ url: 'https://quotex.io' });
    }
});

// Handle tab updates
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && 
        (tab.url.includes('quotex.io') || tab.url.includes('qxbroker.com'))) {
        
        // Ensure content script is injected
        setTimeout(() => {
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js']
            }).catch(() => {
                // Script might already be injected
            });
        }, 1000);
    }
});

// Periodic ping to keep connections alive
setInterval(() => {
    connections.forEach((port, tabId) => {
        try {
            port.postMessage({ type: 'ping' });
        } catch (error) {
            connections.delete(tabId);
        }
    });
}, 30000);

// API for external access
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'get-latest-data') {
        sendResponse({ data: latestData });
    } else if (request.type === 'get-connection-status') {
        sendResponse({ 
            connected: connections.size > 0,
            connections: connections.size,
            hasData: latestData !== null
        });
    }
    return true;
});

console.log('✅ VIP BIG BANG Background Service Worker Ready');
