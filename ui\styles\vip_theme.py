"""
🎨 VIP BIG BANG - Professional Theme System
Gaming-style theme with purple, white, pink, and soft blue colors
"""

from PySide6.QtCore import QObject
from PySide6.QtGui import QFont, QFontDatabase

class VIPTheme(QObject):
    """
    Professional VIP BIG BANG theme system
    
    Color Palette:
    - Primary: Purple (#8B5CF6)
    - Secondary: Pink (#EC4899) 
    - Accent: Soft Blue (#60A5FA)
    - Background: Dark gradients
    - Text: White (#FFFFFF)
    """
    
    def __init__(self):
        super().__init__()
        
        # Color definitions
        self.colors = {
            # Primary colors
            "primary": "#8B5CF6",           # Purple
            "primary_light": "#A78BFA",     # Light purple
            "primary_dark": "#7C3AED",      # Dark purple
            
            # Secondary colors
            "secondary": "#EC4899",         # Pink
            "secondary_light": "#F472B6",   # Light pink
            "secondary_dark": "#DB2777",    # Dark pink
            
            # Accent colors
            "accent": "#60A5FA",            # Soft blue
            "accent_light": "#93C5FD",      # Light blue
            "accent_dark": "#3B82F6",       # Blue
            
            # Success/Error colors
            "success": "#10B981",           # Green
            "warning": "#F59E0B",           # Orange
            "error": "#EF4444",             # Red
            
            # Background colors
            "bg_primary": "#1F2937",        # Dark gray
            "bg_secondary": "#374151",      # Medium gray
            "bg_tertiary": "#4B5563",       # Light gray
            
            # Text colors
            "text_primary": "#FFFFFF",      # White
            "text_secondary": "#F3F4F6",    # Light gray
            "text_muted": "#9CA3AF",        # Muted gray
            
            # Border colors
            "border_primary": "rgba(139, 92, 246, 0.3)",
            "border_secondary": "rgba(255, 255, 255, 0.1)",
            "border_accent": "rgba(96, 165, 250, 0.3)",
        }
        
        # Font settings
        self.fonts = {
            "primary": "Segoe UI",
            "secondary": "Arial",
            "monospace": "Consolas"
        }
        
        # Load custom fonts if available
        self._load_custom_fonts()
    
    def _load_custom_fonts(self):
        """Load custom fonts for better Persian support"""
        try:
            # Try to load Persian fonts
            font_db = QFontDatabase()
            
            # Check for Persian fonts
            persian_fonts = ["Vazir", "IRANSans", "Tahoma"]
            for font_name in persian_fonts:
                if font_name in font_db.families():
                    self.fonts["persian"] = font_name
                    break
            else:
                self.fonts["persian"] = "Tahoma"  # Fallback
                
        except Exception:
            self.fonts["persian"] = "Tahoma"
    
    def get_stylesheet(self) -> str:
        """Get complete stylesheet for the application"""
        return f"""
        /* Main Application Styling */
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {self.colors["bg_primary"]},
                stop:1 #0F172A);
            color: {self.colors["text_primary"]};
            font-family: '{self.fonts["primary"]}', Arial, sans-serif;
            font-size: 12px;
        }}
        
        /* Header Styling */
        QFrame#header {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(139, 92, 246, 0.15),
                stop:1 rgba(139, 92, 246, 0.05));
            border: 2px solid {self.colors["border_primary"]};
            border-radius: 15px;
            padding: 10px;
        }}
        
        QLabel#logo {{
            font-size: 32px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {self.colors["primary"]},
                stop:1 {self.colors["secondary"]});
            border-radius: 25px;
            padding: 8px;
        }}
        
        QLabel#title {{
            font-size: 18px;
            font-weight: bold;
            color: {self.colors["primary"]};
        }}
        
        QLabel#subtitle {{
            font-size: 11px;
            color: {self.colors["text_secondary"]};
        }}
        
        /* Combo Boxes */
        QComboBox#assetCombo, QComboBox#timeframeCombo {{
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid {self.colors["border_primary"]};
            border-radius: 8px;
            padding: 8px 12px;
            color: {self.colors["text_primary"]};
            font-weight: bold;
            min-width: 100px;
        }}
        
        QComboBox#assetCombo:hover, QComboBox#timeframeCombo:hover {{
            border-color: {self.colors["primary"]};
            background: rgba(139, 92, 246, 0.1);
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {self.colors["primary"]};
        }}
        
        /* Status Labels */
        QLabel#connectionStatus {{
            font-size: 12px;
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #EF4444;
        }}
        
        QLabel#modeLabel {{
            font-size: 12px;
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid {self.colors["success"]};
            color: {self.colors["success"]};
        }}
        
        QLabel#balanceLabel {{
            font-size: 14px;
            font-weight: bold;
            color: {self.colors["primary"]};
        }}
        
        QPushButton#settingsBtn {{
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid {self.colors["border_primary"]};
            border-radius: 20px;
            color: {self.colors["primary"]};
            font-size: 16px;
            font-weight: bold;
        }}
        
        QPushButton#settingsBtn:hover {{
            background: rgba(139, 92, 246, 0.2);
            border-color: {self.colors["primary"]};
        }}
        
        /* Left Panel - Analysis Modules */
        QFrame#leftPanel {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(139, 92, 246, 0.08),
                stop:1 rgba(139, 92, 246, 0.03));
            border: 2px solid {self.colors["border_primary"]};
            border-radius: 15px;
            padding: 10px;
        }}
        
        /* Center Panel - Chart */
        QFrame#centerPanel {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.05),
                stop:1 rgba(255, 255, 255, 0.02));
            border: 2px solid {self.colors["border_secondary"]};
            border-radius: 15px;
            padding: 10px;
        }}
        
        /* Indicators Panel */
        QFrame#indicatorsPanel {{
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid {self.colors["border_secondary"]};
            border-radius: 10px;
            padding: 8px;
        }}
        
        QLabel#indicatorTitle {{
            font-size: 11px;
            font-weight: bold;
            color: {self.colors["primary"]};
        }}
        
        QLabel#indicatorValue {{
            font-size: 13px;
            font-weight: bold;
            color: {self.colors["text_primary"]};
        }}
        
        QFrame#indicatorFrame {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 5px;
        }}
        
        QFrame#vortexVisual {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {self.colors["accent"]},
                stop:0.5 {self.colors["secondary"]},
                stop:1 {self.colors["accent"]});
            border-radius: 5px;
        }}
        
        /* Right Panel - Controls */
        QFrame#rightPanel {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(96, 165, 250, 0.08),
                stop:1 rgba(96, 165, 250, 0.03));
            border: 2px solid {self.colors["border_accent"]};
            border-radius: 15px;
            padding: 10px;
        }}
        
        /* Trading Controls */
        QFrame#tradingControls {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid {self.colors["border_secondary"]};
            border-radius: 10px;
            padding: 8px;
        }}
        
        QLabel#sectionTitle {{
            font-size: 13px;
            font-weight: bold;
            color: {self.colors["primary"]};
            padding: 5px 0;
        }}
        
        QLabel#controlLabel {{
            font-size: 11px;
            color: {self.colors["text_secondary"]};
        }}
        
        QSpinBox#volumeSpinbox {{
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid {self.colors["border_primary"]};
            border-radius: 5px;
            padding: 5px;
            color: {self.colors["text_primary"]};
            font-weight: bold;
        }}
        
        /* Trading Buttons */
        QPushButton#buyBtn {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {self.colors["success"]},
                stop:1 #059669);
            border: none;
            border-radius: 8px;
            padding: 10px 15px;
            color: white;
            font-weight: bold;
            font-size: 13px;
        }}
        
        QPushButton#buyBtn:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #059669,
                stop:1 #047857);
        }}
        
        QPushButton#sellBtn {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {self.colors["error"]},
                stop:1 #DC2626);
            border: none;
            border-radius: 8px;
            padding: 10px 15px;
            color: white;
            font-weight: bold;
            font-size: 13px;
        }}
        
        QPushButton#sellBtn:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #DC2626,
                stop:1 #B91C1C);
        }}
        
        QPushButton#emergencyBtn {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #DC2626,
                stop:1 #991B1B);
            border: 2px solid #EF4444;
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }}
        
        QPushButton#emergencyBtn:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #991B1B,
                stop:1 #7F1D1D);
            border-color: #DC2626;
        }}
        
        /* Status Sections */
        QFrame#autotradeStatus, QFrame#accountInfo, QFrame#alertCenter {{
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid {self.colors["border_secondary"]};
            border-radius: 8px;
            padding: 8px;
        }}
        
        QLabel#statusIndicator {{
            font-size: 12px;
            font-weight: bold;
        }}
        
        QPushButton#toggleBtn {{
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid {self.colors["border_primary"]};
            border-radius: 6px;
            padding: 6px 12px;
            color: {self.colors["text_primary"]};
            font-weight: bold;
        }}
        
        QPushButton#toggleBtn:hover {{
            background: rgba(139, 92, 246, 0.2);
            border-color: {self.colors["primary"]};
        }}
        
        QLabel#statValue {{
            font-size: 11px;
            font-weight: bold;
            color: {self.colors["primary"]};
        }}
        
        QLabel#accountMode {{
            font-size: 11px;
            font-weight: bold;
            color: {self.colors["success"]};
        }}
        
        QLabel#accountBalance, QLabel#accountEquity {{
            font-size: 12px;
            font-weight: bold;
            color: {self.colors["primary"]};
        }}
        
        /* Alert Center */
        QListWidget#alertsList {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid {self.colors["border_secondary"]};
            border-radius: 5px;
            color: {self.colors["text_secondary"]};
            font-size: 10px;
        }}
        
        QListWidget#alertsList::item {{
            padding: 3px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }}
        
        QListWidget#alertsList::item:selected {{
            background: rgba(139, 92, 246, 0.3);
        }}
        
        /* Footer */
        QFrame#footer {{
            background: rgba(255, 255, 255, 0.03);
            border-top: 1px solid {self.colors["border_secondary"]};
            padding: 5px;
        }}
        
        QLabel#systemStatus {{
            font-size: 11px;
            font-weight: bold;
            color: {self.colors["success"]};
        }}
        
        QLabel#performanceLabel {{
            font-size: 11px;
            color: {self.colors["text_secondary"]};
        }}
        
        QLabel#timeLabel {{
            font-size: 11px;
            font-weight: bold;
            color: {self.colors["primary"]};
        }}
        
        /* Scrollbars */
        QScrollBar:vertical {{
            background: rgba(255, 255, 255, 0.1);
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background: {self.colors["primary"]};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: {self.colors["primary_light"]};
        }}
        
        /* Tooltips */
        QToolTip {{
            background: {self.colors["bg_secondary"]};
            border: 1px solid {self.colors["primary"]};
            border-radius: 5px;
            padding: 5px;
            color: {self.colors["text_primary"]};
            font-size: 11px;
        }}
        """
    
    def get_color(self, color_name: str) -> str:
        """Get color by name"""
        return self.colors.get(color_name, "#FFFFFF")
    
    def get_font(self, font_type: str = "primary") -> str:
        """Get font by type"""
        return self.fonts.get(font_type, "Segoe UI")
    
    def create_gradient_style(self, start_color: str, end_color: str, direction: str = "horizontal") -> str:
        """Create gradient style string"""
        if direction == "horizontal":
            return f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {start_color}, stop:1 {end_color})"
        else:
            return f"qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {start_color}, stop:1 {end_color})"
