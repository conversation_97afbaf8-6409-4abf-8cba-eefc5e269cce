console.log('🚀 VIP BIG BANG TRUE QUOTEX EXTRACTOR - Version 4.0');

// True Quotex Data Extractor - NO FAKE DATA
class TrueQuotexExtractor {
    constructor() {
        this.isRunning = false;
        this.extractionInterval = null;
        this.websocket = null;
        this.lastData = {};
        this.debugMode = true;
        
        console.log('📊 True Quotex Extractor initialized - REAL DATA ONLY');
    }
    
    // Check if we're on Quotex
    isQuotexPage() {
        const url = window.location.href.toLowerCase();
        const title = document.title.toLowerCase();
        
        const isQuotex = url.includes('quotex.io') || 
                        url.includes('quotex.com') ||
                        url.includes('qxbroker.com') ||
                        title.includes('quotex');
        
        if (this.debugMode) {
            console.log('🌐 URL Check:', url);
            console.log('📄 Title Check:', title);
            console.log('✅ Is Quotex Page:', isQuotex);
        }
        
        return isQuotex;
    }
    
    // Extract ONLY real data from Quotex
    extractTrueData() {
        try {
            if (!this.isQuotexPage()) {
                console.log('⚠️ Not on Quotex page - skipping extraction');
                return null;
            }
            
            console.log('🔍 Extracting TRUE Quotex data - NO FAKE DATA...');
            
            const data = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                title: document.title,
                extraction_method: 'true_quotex_extractor_v4_real_only'
            };
            
            // Debug page structure first
            if (this.debugMode) {
                this.debugPageStructure();
            }
            
            // Extract REAL asset
            data.currentAsset = this.extractRealAsset();
            
            // Extract REAL price
            data.currentPrice = this.extractRealPrice();
            
            // Extract REAL balance
            data.balance = this.extractRealBalance();
            
            // Extract REAL account type
            data.accountType = this.extractRealAccountType();
            
            // Extract additional REAL data
            data.payout = this.extractRealPayout();
            data.timeframe = this.extractRealTimeframe();
            
            // ONLY send if we have REAL data - STRICT CHECK
            if (this.hasRealData(data)) {
                console.log('✅ TRUE REAL data extracted:', data);
                this.sendToRobot(data);
                this.lastData = data;
                return data;
            } else {
                console.log('❌ NO REAL DATA FOUND - not sending fake data');
                console.log('🚫 BLOCKING FAKE DATA:', data);

                // Send a message that no real data was found
                this.sendNoDataMessage();
                return null;
            }
            
        } catch (error) {
            console.error('❌ True data extraction error:', error);
            return null;
        }
    }
    
    // Check if data is actually real - VERY STRICT
    hasRealData(data) {
        // List of known fake values to reject
        const fakeAssets = ['OTC EUR/USD', 'Market', 'EUR/USD', 'USD/BRL'];
        const fakePrices = ['0.85000', '4', '684.0213', '1.07000'];
        const fakeBalances = ['$10,000.00', '$0.85', '$9,684.02'];

        // Check if asset is fake
        const hasRealAsset = data.currentAsset &&
                            !fakeAssets.includes(data.currentAsset) &&
                            data.currentAsset.length > 3;

        // Check if price is fake
        const hasRealPrice = data.currentPrice &&
                            !fakePrices.includes(data.currentPrice) &&
                            parseFloat(data.currentPrice) > 0 &&
                            parseFloat(data.currentPrice) !== 0.85 &&
                            parseFloat(data.currentPrice) !== 4 &&
                            parseFloat(data.currentPrice) !== 684.0213;

        // Check if balance is fake
        const hasRealBalance = data.balance &&
                              !fakeBalances.includes(data.balance) &&
                              !data.balance.includes('$0.85') &&
                              !data.balance.includes('$10,000.00') &&
                              !data.balance.includes('$9,684.02');

        console.log('🔍 STRICT Real data check:');
        console.log('  - Real Asset:', hasRealAsset, data.currentAsset);
        console.log('  - Real Price:', hasRealPrice, data.currentPrice);
        console.log('  - Real Balance:', hasRealBalance, data.balance);
        console.log('  - Overall Real:', hasRealAsset && hasRealPrice && hasRealBalance);

        // ALL must be real, not just one
        return hasRealAsset && hasRealPrice && hasRealBalance;
    }
    
    // Extract REAL asset from page
    extractRealAsset() {
        console.log('🔍 Extracting REAL asset...');
        
        // Look for actual asset elements on the page
        const assetSelectors = [
            // Try to find actual trading interface elements
            '.trading-header .asset-name',
            '.chart-header .symbol',
            '.asset-selector .selected',
            '.current-asset',
            '.trading-symbol',
            '.pair-name',
            
            // Look for any element containing asset-like text
            '*[class*="asset"]',
            '*[class*="symbol"]',
            '*[class*="pair"]'
        ];
        
        for (const selector of assetSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`🔍 Checking ${selector}: found ${elements.length} elements`);
                
                for (const element of elements) {
                    const text = element.textContent.trim();
                    console.log(`  - Text: "${text}"`);
                    
                    if (this.isRealAsset(text)) {
                        console.log(`✅ REAL asset found: ${text}`);
                        return text;
                    }
                }
            } catch (e) {
                console.log(`❌ Error with selector ${selector}:`, e.message);
            }
        }
        
        // Scan all text on page for asset patterns
        console.log('🔍 Scanning page text for assets...');
        const pageText = document.body.textContent;
        
        // Look for common asset patterns
        const assetPatterns = [
            /\b[A-Z]{3}\/[A-Z]{3}\b/g,  // EUR/USD
            /\bOTC\s+[A-Z]{3}\/[A-Z]{3}\b/g,  // OTC EUR/USD
            /\b[A-Z]{3}[A-Z]{3}\b/g,    // EURUSD
            /\b#[A-Z]+\b/g              // #AAPL
        ];
        
        for (const pattern of assetPatterns) {
            const matches = pageText.match(pattern);
            if (matches) {
                for (const match of matches) {
                    if (this.isRealAsset(match)) {
                        console.log(`✅ REAL asset found via pattern: ${match}`);
                        return match;
                    }
                }
            }
        }
        
        console.log('❌ NO REAL ASSET FOUND');
        return null;
    }
    
    // Extract REAL price from page
    extractRealPrice() {
        console.log('💰 Extracting REAL price...');
        
        const priceSelectors = [
            '.price-display',
            '.current-price',
            '.live-price',
            '.chart-price',
            '.quote-price',
            '*[class*="price"]',
            '*[class*="quote"]',
            '*[class*="rate"]'
        ];
        
        for (const selector of priceSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`💰 Checking ${selector}: found ${elements.length} elements`);
                
                for (const element of elements) {
                    const text = element.textContent.trim();
                    console.log(`  - Text: "${text}"`);
                    
                    if (this.isRealPrice(text)) {
                        console.log(`✅ REAL price found: ${text}`);
                        return text;
                    }
                }
            } catch (e) {
                console.log(`❌ Error with selector ${selector}:`, e.message);
            }
        }
        
        // Scan for price patterns in page text
        console.log('💰 Scanning page text for prices...');
        const pageText = document.body.textContent;
        
        const pricePatterns = [
            /\b\d+\.\d{4,5}\b/g,        // 1.23456
            /\b\d{1,3}(,\d{3})*\.\d{2,5}\b/g,  // 1,234.56789
            /\b\d+,\d{4,5}\b/g          // European format
        ];
        
        for (const pattern of pricePatterns) {
            const matches = pageText.match(pattern);
            if (matches) {
                for (const match of matches) {
                    if (this.isRealPrice(match)) {
                        console.log(`✅ REAL price found via pattern: ${match}`);
                        return match;
                    }
                }
            }
        }
        
        console.log('❌ NO REAL PRICE FOUND');
        return null;
    }
    
    // Extract REAL balance from page
    extractRealBalance() {
        console.log('💵 Extracting REAL balance...');
        
        const balanceSelectors = [
            '.balance',
            '.account-balance',
            '.user-balance',
            '.wallet-balance',
            '*[class*="balance"]',
            '*[class*="money"]',
            '*[class*="funds"]'
        ];
        
        for (const selector of balanceSelectors) {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`💵 Checking ${selector}: found ${elements.length} elements`);
                
                for (const element of elements) {
                    const text = element.textContent.trim();
                    console.log(`  - Text: "${text}"`);
                    
                    if (this.isRealBalance(text)) {
                        console.log(`✅ REAL balance found: ${text}`);
                        return text;
                    }
                }
            } catch (e) {
                console.log(`❌ Error with selector ${selector}:`, e.message);
            }
        }
        
        // Scan for balance patterns
        console.log('💵 Scanning page text for balances...');
        const pageText = document.body.textContent;
        
        const balancePatterns = [
            /\$\d{1,3}(,\d{3})*\.\d{2}/g,  // $1,234.56
            /\d{1,3}(,\d{3})*\.\d{2}\s*\$/g,  // 1,234.56 $
            /€\d{1,3}(,\d{3})*\.\d{2}/g,   // €1,234.56
            /£\d{1,3}(,\d{3})*\.\d{2}/g    // £1,234.56
        ];
        
        for (const pattern of balancePatterns) {
            const matches = pageText.match(pattern);
            if (matches) {
                for (const match of matches) {
                    if (this.isRealBalance(match)) {
                        console.log(`✅ REAL balance found via pattern: ${match}`);
                        return match;
                    }
                }
            }
        }
        
        console.log('❌ NO REAL BALANCE FOUND');
        return null;
    }
    
    // Extract REAL account type
    extractRealAccountType() {
        console.log('🏦 Extracting REAL account type...');
        
        const url = window.location.href.toLowerCase();
        const title = document.title.toLowerCase();
        const pageText = document.body.textContent.toLowerCase();
        
        // Check URL for account type
        if (url.includes('demo')) {
            console.log('✅ DEMO account detected from URL');
            return 'DEMO ACCOUNT';
        } else if (url.includes('real') || url.includes('live')) {
            console.log('✅ REAL account detected from URL');
            return 'REAL ACCOUNT';
        }
        
        // Check title
        if (title.includes('demo')) {
            console.log('✅ DEMO account detected from title');
            return 'DEMO ACCOUNT';
        } else if (title.includes('live') || title.includes('real')) {
            console.log('✅ REAL account detected from title');
            return 'REAL ACCOUNT';
        }
        
        // Check page content
        if (pageText.includes('demo account') || pageText.includes('practice')) {
            console.log('✅ DEMO account detected from content');
            return 'DEMO ACCOUNT';
        } else if (pageText.includes('real account') || pageText.includes('live account')) {
            console.log('✅ REAL account detected from content');
            return 'REAL ACCOUNT';
        }
        
        console.log('❌ NO REAL ACCOUNT TYPE FOUND');
        return null;
    }
    
    // Extract REAL payout
    extractRealPayout() {
        const pageText = document.body.textContent;
        const payoutMatch = pageText.match(/(\d{1,3})%/);
        
        if (payoutMatch && parseInt(payoutMatch[1]) >= 70 && parseInt(payoutMatch[1]) <= 100) {
            console.log(`✅ REAL payout found: ${payoutMatch[0]}`);
            return payoutMatch[0];
        }
        
        return null;
    }
    
    // Extract REAL timeframe
    extractRealTimeframe() {
        const pageText = document.body.textContent;
        const timeframeMatch = pageText.match(/(\d+[smh])/);
        
        if (timeframeMatch) {
            console.log(`✅ REAL timeframe found: ${timeframeMatch[0]}`);
            return timeframeMatch[0];
        }
        
        return null;
    }
    
    // Validation functions - ULTRA STRICT
    isRealAsset(text) {
        if (!text || text.length < 3) return false;

        // Reject ALL known fake values
        const fakeAssets = [
            'OTC EUR/USD', 'EUR/USD', 'USD/BRL', 'Market',
            'GBP/USD', 'USD/JPY', 'AUD/USD', 'USD/CAD'
        ];

        if (fakeAssets.includes(text)) {
            console.log(`🚫 REJECTED FAKE ASSET: ${text}`);
            return false;
        }

        // Must be a real trading pair format
        const isValidFormat = /^[A-Z]{3}\/[A-Z]{3}$/.test(text) ||
                             /^OTC\s+[A-Z]{3}\/[A-Z]{3}$/.test(text) ||
                             /^#[A-Z]+$/.test(text);

        console.log(`🔍 Asset validation: ${text} -> ${isValidFormat}`);
        return isValidFormat;
    }

    isRealPrice(text) {
        if (!text) return false;

        // Reject ALL known fake values
        const fakePrices = ['0.85000', '1.07000', '4', '684.0213', '0.85', '1.07'];

        if (fakePrices.includes(text)) {
            console.log(`🚫 REJECTED FAKE PRICE: ${text}`);
            return false;
        }

        const numValue = parseFloat(text.replace(/[^0-9.]/g, ''));
        const isValid = numValue > 0 &&
                       numValue !== 0.85 &&
                       numValue !== 1.07 &&
                       numValue !== 4 &&
                       numValue !== 684.0213;

        console.log(`🔍 Price validation: ${text} (${numValue}) -> ${isValid}`);
        return isValid;
    }

    isRealBalance(text) {
        if (!text) return false;

        // Reject ALL known fake values
        const fakeBalances = ['$10,000.00', '$0.85', '$9,684.02', '$4259.89'];

        if (fakeBalances.some(fake => text.includes(fake.replace('$', '').replace(',', '')))) {
            console.log(`🚫 REJECTED FAKE BALANCE: ${text}`);
            return false;
        }

        const numValue = parseFloat(text.replace(/[^0-9.]/g, ''));
        const isValid = numValue >= 0 &&
                       numValue !== 10000 &&
                       numValue !== 0.85 &&
                       numValue !== 9684.02 &&
                       numValue !== 4259.89;

        console.log(`🔍 Balance validation: ${text} (${numValue}) -> ${isValid}`);
        return isValid;
    }
    
    // Debug page structure
    debugPageStructure() {
        console.log('🔍 DEBUG: Page structure analysis...');
        console.log('🌐 URL:', window.location.href);
        console.log('📄 Title:', document.title);
        console.log('📊 Total elements:', document.querySelectorAll('*').length);
        
        // Look for trading-related elements
        const tradingKeywords = ['price', 'balance', 'asset', 'trading', 'chart'];
        
        tradingKeywords.forEach(keyword => {
            const elements = document.querySelectorAll(`[class*="${keyword}"], [id*="${keyword}"]`);
            if (elements.length > 0) {
                console.log(`🎯 Found ${elements.length} elements with "${keyword}"`);
                elements.forEach((el, i) => {
                    if (i < 3) { // Show first 3
                        console.log(`  - ${el.tagName}.${el.className}: "${el.textContent.trim().substring(0, 30)}"`);
                    }
                });
            }
        });
    }
    
    // Send "no data" message to robot
    sendNoDataMessage() {
        try {
            console.log('📡 Sending NO REAL DATA message to VIP BIG BANG robot...');

            const noDataMessage = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                title: document.title,
                extraction_method: 'true_quotex_extractor_v4_no_data',
                message: 'NO_REAL_DATA_FOUND',
                status: 'WAITING_FOR_REAL_DATA'
            };

            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'no_real_data',
                    data: noDataMessage
                }));
                console.log('✅ No data message sent via existing WebSocket');
                return;
            }

            // Create new WebSocket connection for no data message
            this.websocket = new WebSocket('ws://localhost:8765');

            this.websocket.onopen = () => {
                console.log('✅ WebSocket connected for no data message');
                this.websocket.send(JSON.stringify({
                    type: 'no_real_data',
                    data: noDataMessage
                }));
                console.log('✅ No data message sent to robot');
            };

        } catch (error) {
            console.error('❌ Send no data message error:', error);
        }
    }

    // Send data to VIP BIG BANG robot
    sendToRobot(data) {
        try {
            console.log('📡 Sending TRUE REAL data to VIP BIG BANG robot...');

            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'quotex_data',
                    data: data
                }));
                console.log('✅ TRUE data sent via existing WebSocket');
                return;
            }
            
            // Create new WebSocket connection
            this.websocket = new WebSocket('ws://localhost:8765');
            
            this.websocket.onopen = () => {
                console.log('✅ WebSocket connected to VIP BIG BANG robot');
                this.websocket.send(JSON.stringify({
                    type: 'quotex_data',
                    data: data
                }));
                console.log('✅ TRUE REAL data sent to robot');
            };
            
            this.websocket.onmessage = (event) => {
                console.log('📨 Response from robot:', event.data);
            };
            
            this.websocket.onclose = () => {
                console.log('🔌 WebSocket connection closed');
                this.websocket = null;
            };
            
            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
                this.websocket = null;
            };
            
        } catch (error) {
            console.error('❌ Send to robot error:', error);
        }
    }
    
    // Start extraction with DOM monitoring
    start() {
        if (this.isRunning) {
            console.log('⚠️ Extractor already running');
            return;
        }

        console.log('🚀 Starting True Quotex Extractor - REAL DATA ONLY...');
        this.isRunning = true;

        // Extract immediately
        this.extractTrueData();

        // Set up periodic extraction (less frequent)
        this.extractionInterval = setInterval(() => {
            this.extractTrueData();
        }, 15000); // Every 15 seconds

        // Set up DOM monitoring for real-time changes
        this.startDOMMonitoring();

        console.log('✅ True Quotex Extractor started with DOM monitoring');
    }

    // Monitor DOM changes for real-time data
    startDOMMonitoring() {
        console.log('🔍 Starting DOM monitoring for real-time changes...');

        // Monitor for text changes that might indicate price updates
        const observer = new MutationObserver((mutations) => {
            let hasRelevantChange = false;

            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    // Check if the change is in a potentially relevant element
                    const target = mutation.target;
                    const text = target.textContent || '';

                    // Look for price-like or balance-like changes
                    if (text.match(/\d+\.\d{2,5}/) || text.includes('$') || text.includes('%')) {
                        hasRelevantChange = true;
                        console.log('🔄 DOM change detected:', text.substring(0, 50));
                    }
                }
            });

            // If relevant change detected, extract data
            if (hasRelevantChange) {
                console.log('🔄 Relevant DOM change detected, extracting data...');
                setTimeout(() => {
                    this.extractTrueData();
                }, 1000); // Wait 1 second for changes to settle
            }
        });

        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        console.log('✅ DOM monitoring started');
    }
    
    // Stop extraction
    stop() {
        console.log('⏹️ Stopping True Quotex Extractor...');
        this.isRunning = false;
        
        if (this.extractionInterval) {
            clearInterval(this.extractionInterval);
            this.extractionInterval = null;
        }
        
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        
        console.log('✅ True Quotex Extractor stopped');
    }
}

// Initialize and start the TRUE extractor
const trueExtractor = new TrueQuotexExtractor();

// Wait for page to load
function waitForPageLoad() {
    if (document.readyState === 'complete') {
        console.log('✅ Page loaded, starting True Quotex Extractor...');
        setTimeout(() => {
            trueExtractor.start();
        }, 5000); // Wait 5 seconds after page load
    } else {
        console.log('⏳ Waiting for page to load...');
        window.addEventListener('load', () => {
            console.log('✅ Page loaded, starting True Quotex Extractor...');
            setTimeout(() => {
                trueExtractor.start();
            }, 5000);
        });
    }
}

// Start when script loads
console.log('🚀 VIP BIG BANG True Quotex Extractor: Initializing...');
waitForPageLoad();
