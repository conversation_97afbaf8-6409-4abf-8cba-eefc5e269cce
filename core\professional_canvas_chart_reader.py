"""
VIP BIG BANG Enterprise - Professional Canvas Chart Reader
Real-time Chart Analysis with Computer Vision & OCR
"""

import asyncio
import cv2
import numpy as np
import base64
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from pathlib import Path

try:
    import pytesseract
    from PIL import Image, ImageEnhance, ImageFilter
    from skimage import measure, morphology
    import scipy.ndimage as ndimage
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("⚠️ OCR libraries not available - install with: pip install pytesseract pillow scikit-image scipy")

class ProfessionalCanvasChartReader:
    """
    📊 Professional Canvas Chart Reader
    
    Features:
    - Real-time Canvas Chart Analysis
    - Advanced Computer Vision for Candle Detection
    - Professional OCR for Price Reading
    - Pattern Recognition (Support/Resistance)
    - Volume Analysis from Visual Data
    - Trend Detection
    - Signal Generation
    """
    
    def __init__(self, settings: Optional[Dict] = None):
        self.settings = settings or {}
        self.logger = logging.getLogger(__name__)
        
        # Chart analysis state
        self.current_chart_data = {}
        self.candle_history = []
        self.price_levels = []
        self.trend_data = {}
        
        # Computer vision settings
        self.cv_config = {
            'candle_detection': {
                'min_candle_width': 3,
                'max_candle_width': 50,
                'min_candle_height': 5,
                'color_tolerance': 30
            },
            'price_detection': {
                'text_threshold': 127,
                'min_confidence': 60,
                'scale_factor': 2.0
            },
            'pattern_detection': {
                'support_resistance_threshold': 5,
                'trend_line_threshold': 10,
                'pattern_confidence_min': 0.7
            }
        }
        
        # Color definitions for candle detection
        self.candle_colors = {
            'bullish': {
                'green': [(0, 100, 0), (100, 255, 100)],
                'blue': [(0, 0, 100), (100, 100, 255)],
                'white': [(200, 200, 200), (255, 255, 255)]
            },
            'bearish': {
                'red': [(100, 0, 0), (255, 100, 100)],
                'orange': [(200, 100, 0), (255, 200, 100)],
                'black': [(0, 0, 0), (50, 50, 50)]
            }
        }
        
        # OCR configuration
        if OCR_AVAILABLE:
            self.tesseract_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789.,$+-'
        
        self.logger.info("📊 Professional Canvas Chart Reader initialized")
    
    async def analyze_chart_canvas(self, page, canvas_selector: str = 'canvas') -> Dict[str, Any]:
        """📊 Analyze Chart Canvas with Professional Computer Vision"""
        try:
            if not OCR_AVAILABLE:
                self.logger.error("❌ OCR libraries not available")
                return {}
            
            self.logger.info("📊 Starting professional canvas chart analysis...")
            
            # Capture canvas screenshot
            canvas_data = await self.capture_canvas_screenshot(page, canvas_selector)
            if not canvas_data:
                return {}
            
            # Convert to OpenCV format
            image = self.base64_to_opencv(canvas_data['screenshot'])
            if image is None:
                return {}
            
            # Perform comprehensive analysis
            analysis_results = {
                'timestamp': datetime.now().isoformat(),
                'canvas_info': canvas_data['info'],
                'candles': await self.detect_candles(image),
                'price_levels': await self.detect_price_levels(image),
                'support_resistance': await self.detect_support_resistance(image),
                'trend_analysis': await self.analyze_trend(image),
                'volume_analysis': await self.analyze_volume_visual(image),
                'patterns': await self.detect_chart_patterns(image)
            }
            
            # Update internal state
            self.current_chart_data = analysis_results
            self.update_candle_history(analysis_results['candles'])
            
            self.logger.info("✅ Professional canvas chart analysis completed")
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"❌ Canvas chart analysis error: {e}")
            return {}
    
    async def capture_canvas_screenshot(self, page, canvas_selector: str) -> Optional[Dict]:
        """📸 Capture Canvas Screenshot with Metadata"""
        try:
            # Find canvas element
            canvas_element = await page.query_selector(canvas_selector)
            if not canvas_element:
                self.logger.warning("⚠️ Canvas element not found")
                return None
            
            # Get canvas information
            canvas_info = await canvas_element.evaluate("""
                (canvas) => {
                    return {
                        width: canvas.width,
                        height: canvas.height,
                        clientWidth: canvas.clientWidth,
                        clientHeight: canvas.clientHeight,
                        id: canvas.id,
                        className: canvas.className
                    };
                }
            """)
            
            # Capture screenshot
            screenshot_buffer = await canvas_element.screenshot()
            screenshot_base64 = base64.b64encode(screenshot_buffer).decode('utf-8')
            
            return {
                'info': canvas_info,
                'screenshot': screenshot_base64,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Canvas screenshot error: {e}")
            return None
    
    def base64_to_opencv(self, base64_string: str) -> Optional[np.ndarray]:
        """🔄 Convert Base64 to OpenCV Image"""
        try:
            # Decode base64
            image_data = base64.b64decode(base64_string)
            
            # Convert to numpy array
            nparr = np.frombuffer(image_data, np.uint8)
            
            # Decode image
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            return image
            
        except Exception as e:
            self.logger.error(f"❌ Base64 to OpenCV conversion error: {e}")
            return None
    
    async def detect_candles(self, image: np.ndarray) -> List[Dict]:
        """🕯️ Detect Candles with Advanced Computer Vision"""
        try:
            candles = []
            height, width = image.shape[:2]
            
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Detect bullish candles (green/blue/white)
            bullish_candles = await self.detect_candles_by_color(image, hsv, 'bullish')
            
            # Detect bearish candles (red/orange/black)
            bearish_candles = await self.detect_candles_by_color(image, hsv, 'bearish')
            
            # Combine and sort by x position
            all_candles = bullish_candles + bearish_candles
            all_candles.sort(key=lambda c: c['x'])
            
            # Calculate candle metrics
            for i, candle in enumerate(all_candles):
                candle['index'] = i
                candle['timestamp'] = datetime.now().isoformat()
                
                # Calculate price levels (relative to chart height)
                candle['high_ratio'] = candle['high'] / height
                candle['low_ratio'] = candle['low'] / height
                candle['open_ratio'] = candle['open'] / height
                candle['close_ratio'] = candle['close'] / height
                
                # Calculate candle properties
                candle['body_size'] = abs(candle['close'] - candle['open'])
                candle['upper_shadow'] = candle['high'] - max(candle['open'], candle['close'])
                candle['lower_shadow'] = min(candle['open'], candle['close']) - candle['low']
                candle['total_range'] = candle['high'] - candle['low']
                
                # Candle pattern classification
                candle['pattern'] = self.classify_candle_pattern(candle)
            
            self.logger.info(f"🕯️ Detected {len(all_candles)} candles")
            return all_candles
            
        except Exception as e:
            self.logger.error(f"❌ Candle detection error: {e}")
            return []
    
    async def detect_candles_by_color(self, image: np.ndarray, hsv: np.ndarray, candle_type: str) -> List[Dict]:
        """🎨 Detect Candles by Color Type"""
        try:
            candles = []
            colors = self.candle_colors[candle_type]
            
            for color_name, (lower, upper) in colors.items():
                # Create color mask
                lower_bound = np.array(lower)
                upper_bound = np.array(upper)
                mask = cv2.inRange(image, lower_bound, upper_bound)
                
                # Find contours
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    # Get bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Filter by size
                    if (self.cv_config['candle_detection']['min_candle_width'] <= w <= 
                        self.cv_config['candle_detection']['max_candle_width'] and
                        h >= self.cv_config['candle_detection']['min_candle_height']):
                        
                        # Extract candle data
                        candle = {
                            'x': x,
                            'y': y,
                            'width': w,
                            'height': h,
                            'type': candle_type,
                            'color': color_name,
                            'area': cv2.contourArea(contour),
                            'high': y,
                            'low': y + h,
                            'open': y + h * 0.3 if candle_type == 'bullish' else y + h * 0.7,
                            'close': y + h * 0.7 if candle_type == 'bullish' else y + h * 0.3
                        }
                        
                        candles.append(candle)
            
            return candles
            
        except Exception as e:
            self.logger.error(f"❌ Color-based candle detection error: {e}")
            return []
    
    def classify_candle_pattern(self, candle: Dict) -> str:
        """📊 Classify Candle Pattern"""
        try:
            body_ratio = candle['body_size'] / candle['total_range'] if candle['total_range'] > 0 else 0
            upper_shadow_ratio = candle['upper_shadow'] / candle['total_range'] if candle['total_range'] > 0 else 0
            lower_shadow_ratio = candle['lower_shadow'] / candle['total_range'] if candle['total_range'] > 0 else 0
            
            # Doji patterns
            if body_ratio < 0.1:
                if upper_shadow_ratio > 0.4 and lower_shadow_ratio < 0.1:
                    return 'DRAGONFLY_DOJI'
                elif lower_shadow_ratio > 0.4 and upper_shadow_ratio < 0.1:
                    return 'GRAVESTONE_DOJI'
                else:
                    return 'DOJI'
            
            # Hammer patterns
            elif body_ratio < 0.3 and lower_shadow_ratio > 0.5:
                return 'HAMMER' if candle['type'] == 'bullish' else 'HANGING_MAN'
            
            # Shooting star
            elif body_ratio < 0.3 and upper_shadow_ratio > 0.5:
                return 'SHOOTING_STAR'
            
            # Strong candles
            elif body_ratio > 0.7:
                return 'STRONG_BULLISH' if candle['type'] == 'bullish' else 'STRONG_BEARISH'
            
            # Normal candles
            else:
                return 'NORMAL_BULLISH' if candle['type'] == 'bullish' else 'NORMAL_BEARISH'
                
        except Exception as e:
            self.logger.error(f"❌ Candle pattern classification error: {e}")
            return 'UNKNOWN'
    
    async def detect_price_levels(self, image: np.ndarray) -> List[Dict]:
        """💰 Detect Price Levels with OCR"""
        try:
            if not OCR_AVAILABLE:
                return []
            
            price_levels = []
            
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Enhance image for OCR
            enhanced = self.enhance_image_for_ocr(gray)
            
            # Apply OCR
            ocr_data = pytesseract.image_to_data(enhanced, config=self.tesseract_config, output_type=pytesseract.Output.DICT)
            
            # Extract price-like text
            for i, text in enumerate(ocr_data['text']):
                if text.strip():
                    confidence = int(ocr_data['conf'][i])
                    if confidence > self.cv_config['price_detection']['min_confidence']:
                        # Check if text looks like a price
                        if self.is_price_like(text):
                            price_level = {
                                'text': text,
                                'confidence': confidence,
                                'x': ocr_data['left'][i],
                                'y': ocr_data['top'][i],
                                'width': ocr_data['width'][i],
                                'height': ocr_data['height'][i],
                                'price_value': self.parse_price(text)
                            }
                            price_levels.append(price_level)
            
            self.logger.info(f"💰 Detected {len(price_levels)} price levels")
            return price_levels
            
        except Exception as e:
            self.logger.error(f"❌ Price level detection error: {e}")
            return []
    
    def enhance_image_for_ocr(self, gray_image: np.ndarray) -> np.ndarray:
        """🔍 Enhance Image for OCR"""
        try:
            # Scale up for better OCR
            scale_factor = self.cv_config['price_detection']['scale_factor']
            height, width = gray_image.shape
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            scaled = cv2.resize(gray_image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Apply threshold
            _, thresh = cv2.threshold(scaled, self.cv_config['price_detection']['text_threshold'], 255, cv2.THRESH_BINARY)
            
            # Noise reduction
            denoised = cv2.medianBlur(thresh, 3)
            
            # Morphological operations
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(denoised, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            self.logger.error(f"❌ Image enhancement error: {e}")
            return gray_image
    
    def is_price_like(self, text: str) -> bool:
        """💰 Check if Text Looks Like a Price"""
        try:
            # Remove common price symbols
            clean_text = text.replace('$', '').replace(',', '').replace('+', '').replace('-', '').strip()
            
            # Check if it's a valid number
            try:
                float(clean_text)
                return True
            except ValueError:
                return False
                
        except Exception:
            return False
    
    def parse_price(self, text: str) -> Optional[float]:
        """💰 Parse Price from Text"""
        try:
            # Clean and parse
            clean_text = text.replace('$', '').replace(',', '').replace('+', '').replace('-', '').strip()
            return float(clean_text)
        except ValueError:
            return None

    async def detect_support_resistance(self, image: np.ndarray) -> Dict[str, List]:
        """📊 Detect Support and Resistance Levels"""
        try:
            height, width = image.shape[:2]

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Detect horizontal lines (potential support/resistance)
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width//4, 1))
            horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)

            # Find contours of horizontal lines
            contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            support_levels = []
            resistance_levels = []

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # Filter by minimum width
                if w > width * 0.1:  # At least 10% of chart width
                    level = {
                        'y': y,
                        'x_start': x,
                        'x_end': x + w,
                        'width': w,
                        'strength': w / width,  # Strength based on width
                        'price_ratio': y / height
                    }

                    # Classify as support or resistance based on position
                    if y > height * 0.6:  # Lower part of chart
                        support_levels.append(level)
                    elif y < height * 0.4:  # Upper part of chart
                        resistance_levels.append(level)

            # Sort by strength
            support_levels.sort(key=lambda x: x['strength'], reverse=True)
            resistance_levels.sort(key=lambda x: x['strength'], reverse=True)

            self.logger.info(f"📊 Detected {len(support_levels)} support and {len(resistance_levels)} resistance levels")

            return {
                'support': support_levels[:5],  # Top 5 support levels
                'resistance': resistance_levels[:5]  # Top 5 resistance levels
            }

        except Exception as e:
            self.logger.error(f"❌ Support/Resistance detection error: {e}")
            return {'support': [], 'resistance': []}

    async def analyze_trend(self, image: np.ndarray) -> Dict[str, Any]:
        """📈 Analyze Trend from Chart"""
        try:
            if not self.candle_history:
                return {'trend': 'UNKNOWN', 'strength': 0, 'confidence': 0}

            # Get recent candles
            recent_candles = self.candle_history[-20:] if len(self.candle_history) >= 20 else self.candle_history

            if len(recent_candles) < 5:
                return {'trend': 'INSUFFICIENT_DATA', 'strength': 0, 'confidence': 0}

            # Calculate trend based on highs and lows
            highs = [candle['high'] for candle in recent_candles]
            lows = [candle['low'] for candle in recent_candles]

            # Linear regression for trend detection
            x = np.arange(len(highs))

            # Trend of highs
            highs_slope = np.polyfit(x, highs, 1)[0]

            # Trend of lows
            lows_slope = np.polyfit(x, lows, 1)[0]

            # Overall trend
            overall_slope = (highs_slope + lows_slope) / 2

            # Determine trend direction
            if overall_slope > 2:
                trend = 'STRONG_UPTREND'
                strength = min(abs(overall_slope) / 10, 1.0)
            elif overall_slope > 0.5:
                trend = 'UPTREND'
                strength = min(abs(overall_slope) / 5, 1.0)
            elif overall_slope < -2:
                trend = 'STRONG_DOWNTREND'
                strength = min(abs(overall_slope) / 10, 1.0)
            elif overall_slope < -0.5:
                trend = 'DOWNTREND'
                strength = min(abs(overall_slope) / 5, 1.0)
            else:
                trend = 'SIDEWAYS'
                strength = 0.1

            # Calculate confidence based on consistency
            bullish_candles = sum(1 for candle in recent_candles if candle['type'] == 'bullish')
            bearish_candles = len(recent_candles) - bullish_candles

            if trend in ['UPTREND', 'STRONG_UPTREND']:
                confidence = bullish_candles / len(recent_candles)
            elif trend in ['DOWNTREND', 'STRONG_DOWNTREND']:
                confidence = bearish_candles / len(recent_candles)
            else:
                confidence = 0.5

            trend_analysis = {
                'trend': trend,
                'strength': strength,
                'confidence': confidence,
                'highs_slope': highs_slope,
                'lows_slope': lows_slope,
                'overall_slope': overall_slope,
                'bullish_ratio': bullish_candles / len(recent_candles),
                'bearish_ratio': bearish_candles / len(recent_candles)
            }

            self.trend_data = trend_analysis
            self.logger.info(f"📈 Trend analysis: {trend} (strength: {strength:.2f}, confidence: {confidence:.2f})")

            return trend_analysis

        except Exception as e:
            self.logger.error(f"❌ Trend analysis error: {e}")
            return {'trend': 'ERROR', 'strength': 0, 'confidence': 0}

    async def analyze_volume_visual(self, image: np.ndarray) -> Dict[str, Any]:
        """📊 Analyze Volume from Visual Indicators"""
        try:
            # Since Quotex doesn't show traditional volume, we analyze visual cues
            height, width = image.shape[:2]

            # Look for volume-like indicators (bars, colors, etc.)
            volume_indicators = []

            # Analyze color intensity as volume proxy
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Calculate average saturation and value (brightness)
            avg_saturation = np.mean(hsv[:, :, 1])
            avg_brightness = np.mean(hsv[:, :, 2])

            # Analyze color distribution
            color_hist = cv2.calcHist([image], [0, 1, 2], None, [50, 50, 50], [0, 256, 0, 256, 0, 256])
            color_diversity = np.count_nonzero(color_hist)

            # Estimate volume based on visual activity
            if avg_saturation > 100 and color_diversity > 1000:
                volume_estimate = 'HIGH'
                volume_score = 0.8
            elif avg_saturation > 50 and color_diversity > 500:
                volume_estimate = 'MEDIUM'
                volume_score = 0.5
            else:
                volume_estimate = 'LOW'
                volume_score = 0.2

            volume_analysis = {
                'estimate': volume_estimate,
                'score': volume_score,
                'avg_saturation': avg_saturation,
                'avg_brightness': avg_brightness,
                'color_diversity': color_diversity,
                'visual_activity': avg_saturation * color_diversity / 10000
            }

            self.logger.info(f"📊 Volume analysis: {volume_estimate} (score: {volume_score:.2f})")
            return volume_analysis

        except Exception as e:
            self.logger.error(f"❌ Volume analysis error: {e}")
            return {'estimate': 'UNKNOWN', 'score': 0}

    async def detect_chart_patterns(self, image: np.ndarray) -> List[Dict]:
        """📊 Detect Chart Patterns"""
        try:
            patterns = []

            if len(self.candle_history) < 10:
                return patterns

            recent_candles = self.candle_history[-20:] if len(self.candle_history) >= 20 else self.candle_history

            # Double Top/Bottom detection
            double_patterns = self.detect_double_patterns(recent_candles)
            patterns.extend(double_patterns)

            # Head and Shoulders detection
            hs_patterns = self.detect_head_shoulders(recent_candles)
            patterns.extend(hs_patterns)

            # Triangle patterns
            triangle_patterns = self.detect_triangles(recent_candles)
            patterns.extend(triangle_patterns)

            # Flag and Pennant patterns
            flag_patterns = self.detect_flags_pennants(recent_candles)
            patterns.extend(flag_patterns)

            self.logger.info(f"📊 Detected {len(patterns)} chart patterns")
            return patterns

        except Exception as e:
            self.logger.error(f"❌ Chart pattern detection error: {e}")
            return []

    def detect_double_patterns(self, candles: List[Dict]) -> List[Dict]:
        """📊 Detect Double Top/Bottom Patterns"""
        patterns = []

        try:
            if len(candles) < 10:
                return patterns

            highs = [candle['high'] for candle in candles]
            lows = [candle['low'] for candle in candles]

            # Find peaks and valleys
            from scipy.signal import find_peaks

            peaks, _ = find_peaks(highs, distance=3)
            valleys, _ = find_peaks([-x for x in lows], distance=3)

            # Check for double tops
            if len(peaks) >= 2:
                for i in range(len(peaks) - 1):
                    peak1_height = highs[peaks[i]]
                    peak2_height = highs[peaks[i + 1]]

                    # Check if peaks are similar height
                    if abs(peak1_height - peak2_height) / max(peak1_height, peak2_height) < 0.02:
                        patterns.append({
                            'type': 'DOUBLE_TOP',
                            'confidence': 0.7,
                            'peak1_index': peaks[i],
                            'peak2_index': peaks[i + 1],
                            'resistance_level': (peak1_height + peak2_height) / 2
                        })

            # Check for double bottoms
            if len(valleys) >= 2:
                for i in range(len(valleys) - 1):
                    valley1_depth = lows[valleys[i]]
                    valley2_depth = lows[valleys[i + 1]]

                    # Check if valleys are similar depth
                    if abs(valley1_depth - valley2_depth) / max(valley1_depth, valley2_depth) < 0.02:
                        patterns.append({
                            'type': 'DOUBLE_BOTTOM',
                            'confidence': 0.7,
                            'valley1_index': valleys[i],
                            'valley2_index': valleys[i + 1],
                            'support_level': (valley1_depth + valley2_depth) / 2
                        })

        except Exception as e:
            self.logger.error(f"❌ Double pattern detection error: {e}")

        return patterns

    def detect_head_shoulders(self, candles: List[Dict]) -> List[Dict]:
        """📊 Detect Head and Shoulders Patterns"""
        patterns = []

        try:
            if len(candles) < 15:
                return patterns

            highs = [candle['high'] for candle in candles]

            # Find three consecutive peaks
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(highs, distance=3)

            if len(peaks) >= 3:
                for i in range(len(peaks) - 2):
                    left_shoulder = highs[peaks[i]]
                    head = highs[peaks[i + 1]]
                    right_shoulder = highs[peaks[i + 2]]

                    # Check head and shoulders pattern
                    if (head > left_shoulder and head > right_shoulder and
                        abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder) < 0.05):

                        patterns.append({
                            'type': 'HEAD_AND_SHOULDERS',
                            'confidence': 0.8,
                            'left_shoulder': left_shoulder,
                            'head': head,
                            'right_shoulder': right_shoulder,
                            'neckline': (left_shoulder + right_shoulder) / 2
                        })

        except Exception as e:
            self.logger.error(f"❌ Head and shoulders detection error: {e}")

        return patterns

    def detect_triangles(self, candles: List[Dict]) -> List[Dict]:
        """📊 Detect Triangle Patterns"""
        patterns = []

        try:
            if len(candles) < 10:
                return patterns

            highs = [candle['high'] for candle in candles]
            lows = [candle['low'] for candle in candles]

            # Calculate trend lines
            x = np.arange(len(highs))

            # Upper trend line (resistance)
            upper_slope = np.polyfit(x, highs, 1)[0]

            # Lower trend line (support)
            lower_slope = np.polyfit(x, lows, 1)[0]

            # Ascending triangle
            if abs(upper_slope) < 0.1 and lower_slope > 0.5:
                patterns.append({
                    'type': 'ASCENDING_TRIANGLE',
                    'confidence': 0.6,
                    'upper_slope': upper_slope,
                    'lower_slope': lower_slope
                })

            # Descending triangle
            elif abs(lower_slope) < 0.1 and upper_slope < -0.5:
                patterns.append({
                    'type': 'DESCENDING_TRIANGLE',
                    'confidence': 0.6,
                    'upper_slope': upper_slope,
                    'lower_slope': lower_slope
                })

            # Symmetrical triangle
            elif upper_slope < -0.3 and lower_slope > 0.3:
                patterns.append({
                    'type': 'SYMMETRICAL_TRIANGLE',
                    'confidence': 0.5,
                    'upper_slope': upper_slope,
                    'lower_slope': lower_slope
                })

        except Exception as e:
            self.logger.error(f"❌ Triangle detection error: {e}")

        return patterns

    def detect_flags_pennants(self, candles: List[Dict]) -> List[Dict]:
        """📊 Detect Flag and Pennant Patterns"""
        patterns = []

        try:
            if len(candles) < 8:
                return patterns

            # Look for consolidation after strong move
            recent_range = max([candle['high'] for candle in candles[-5:]]) - min([candle['low'] for candle in candles[-5:]])
            previous_range = max([candle['high'] for candle in candles[-10:-5]]) - min([candle['low'] for candle in candles[-10:-5]])

            # Flag pattern: consolidation after strong move
            if recent_range < previous_range * 0.5:
                # Determine flag direction
                recent_trend = sum(1 for candle in candles[-5:] if candle['type'] == 'bullish')

                if recent_trend >= 3:
                    patterns.append({
                        'type': 'BULL_FLAG',
                        'confidence': 0.6,
                        'consolidation_range': recent_range,
                        'previous_range': previous_range
                    })
                elif recent_trend <= 2:
                    patterns.append({
                        'type': 'BEAR_FLAG',
                        'confidence': 0.6,
                        'consolidation_range': recent_range,
                        'previous_range': previous_range
                    })

        except Exception as e:
            self.logger.error(f"❌ Flag/Pennant detection error: {e}")

        return patterns

    def update_candle_history(self, new_candles: List[Dict]):
        """📊 Update Candle History"""
        try:
            # Add new candles to history
            self.candle_history.extend(new_candles)

            # Keep only last 100 candles for performance
            if len(self.candle_history) > 100:
                self.candle_history = self.candle_history[-100:]

            self.logger.debug(f"📊 Updated candle history: {len(self.candle_history)} candles")

        except Exception as e:
            self.logger.error(f"❌ Candle history update error: {e}")

    def get_latest_analysis(self) -> Dict[str, Any]:
        """📊 Get Latest Chart Analysis"""
        return {
            'chart_data': self.current_chart_data,
            'candle_count': len(self.candle_history),
            'trend_data': self.trend_data,
            'price_levels_count': len(self.price_levels),
            'last_update': datetime.now().isoformat()
        }
