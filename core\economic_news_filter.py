"""
VIP BIG BANG Enterprise - Economic News Filter
Filter trading during high-impact economic news events
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import logging
from datetime import datetime, timedelta
import json

class EconomicNewsFilter:
    """
    Economic News Filter - VIP BIG BANG complementary analysis
    Prevents trading during high-impact economic news events
    Filters events like NFP, CPI, FOMC, etc.
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("EconomicNewsFilter")
        
        # News filter parameters
        self.high_impact_buffer = 30  # 30 minutes before/after high impact news
        self.medium_impact_buffer = 15  # 15 minutes before/after medium impact news
        self.low_impact_buffer = 5     # 5 minutes before/after low impact news
        
        # High impact news events
        self.high_impact_events = [
            'NFP', 'Non-Farm Payrolls', 'Employment Change',
            'CPI', 'Consumer Price Index', 'Inflation Rate',
            'FOMC', 'Federal Reserve', 'Interest Rate Decision',
            'GDP', 'Gross Domestic Product',
            'Unemployment Rate', 'Jobless Claims',
            'Retail Sales', 'Manufacturing PMI',
            'Central Bank Speech', 'ECB Decision',
            'Bank of England', 'Bank of Japan'
        ]
        
        # Medium impact events
        self.medium_impact_events = [
            'PPI', 'Producer Price Index',
            'Housing Data', 'Building Permits',
            'Trade Balance', 'Current Account',
            'Industrial Production',
            'Consumer Confidence', 'Business Confidence',
            'Durable Goods Orders'
        ]
        
        # News schedule (example - should be updated from real news API)
        self.news_schedule = []
        
        self.logger.debug("Economic News Filter initialized")
    
    def load_news_schedule(self, news_data: List[Dict] = None) -> bool:
        """Load economic news schedule from external source"""
        try:
            if news_data:
                self.news_schedule = news_data
            else:
                # Default schedule for testing (should be replaced with real API)
                self.news_schedule = [
                    {
                        'time': datetime.now() + timedelta(hours=2),
                        'event': 'NFP',
                        'currency': 'USD',
                        'impact': 'HIGH',
                        'forecast': '200K',
                        'previous': '180K'
                    },
                    {
                        'time': datetime.now() + timedelta(days=1),
                        'event': 'CPI',
                        'currency': 'USD',
                        'impact': 'HIGH',
                        'forecast': '3.2%',
                        'previous': '3.1%'
                    }
                ]
            
            self.logger.info(f"Loaded {len(self.news_schedule)} news events")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load news schedule: {e}")
            return False
    
    def classify_news_impact(self, event_name: str) -> str:
        """Classify news event impact level"""
        event_upper = event_name.upper()
        
        # Check high impact
        for high_event in self.high_impact_events:
            if high_event.upper() in event_upper:
                return 'HIGH'
        
        # Check medium impact
        for medium_event in self.medium_impact_events:
            if medium_event.upper() in event_upper:
                return 'MEDIUM'
        
        # Default to low impact
        return 'LOW'
    
    def get_buffer_time(self, impact_level: str) -> int:
        """Get buffer time in minutes based on impact level"""
        if impact_level == 'HIGH':
            return self.high_impact_buffer
        elif impact_level == 'MEDIUM':
            return self.medium_impact_buffer
        else:
            return self.low_impact_buffer
    
    def check_upcoming_news(self, current_time: datetime = None) -> Dict:
        """Check for upcoming news events that might affect trading"""
        if current_time is None:
            current_time = datetime.now()
        
        upcoming_news = []
        blocking_news = []
        
        for news_event in self.news_schedule:
            event_time = news_event['time']
            time_diff = (event_time - current_time).total_seconds() / 60  # minutes
            
            impact_level = news_event.get('impact', self.classify_news_impact(news_event['event']))
            buffer_time = self.get_buffer_time(impact_level)
            
            # Check if event is within buffer time
            if -buffer_time <= time_diff <= buffer_time:
                blocking_news.append({
                    'event': news_event['event'],
                    'time': event_time,
                    'impact': impact_level,
                    'currency': news_event.get('currency', 'USD'),
                    'time_diff_minutes': time_diff,
                    'buffer_time': buffer_time
                })
            elif 0 <= time_diff <= 120:  # Next 2 hours
                upcoming_news.append({
                    'event': news_event['event'],
                    'time': event_time,
                    'impact': impact_level,
                    'currency': news_event.get('currency', 'USD'),
                    'time_diff_minutes': time_diff
                })
        
        return {
            'upcoming_news': upcoming_news,
            'blocking_news': blocking_news,
            'is_blocked': len(blocking_news) > 0,
            'next_clear_time': self.calculate_next_clear_time(current_time, blocking_news)
        }
    
    def calculate_next_clear_time(self, current_time: datetime, blocking_news: List[Dict]) -> datetime:
        """Calculate when trading will be clear of news events"""
        if not blocking_news:
            return current_time
        
        latest_clear_time = current_time
        
        for news in blocking_news:
            event_time = news['time']
            buffer_time = news['buffer_time']
            
            # Calculate when this event's buffer period ends
            clear_time = event_time + timedelta(minutes=buffer_time)
            
            if clear_time > latest_clear_time:
                latest_clear_time = clear_time
        
        return latest_clear_time
    
    def get_trading_recommendation(self, current_time: datetime = None) -> Dict:
        """Get trading recommendation based on news events"""
        news_check = self.check_upcoming_news(current_time)
        
        if news_check['is_blocked']:
            # Find the highest impact blocking event
            highest_impact = 'LOW'
            critical_event = None
            
            for news in news_check['blocking_news']:
                if news['impact'] == 'HIGH':
                    highest_impact = 'HIGH'
                    critical_event = news
                    break
                elif news['impact'] == 'MEDIUM' and highest_impact != 'HIGH':
                    highest_impact = 'MEDIUM'
                    critical_event = news
            
            if not critical_event:
                critical_event = news_check['blocking_news'][0]
            
            recommendation = {
                'allow_trading': False,
                'reason': f"High-impact news: {critical_event['event']} in {abs(critical_event['time_diff_minutes']):.0f} minutes",
                'risk_level': 'HIGH',
                'next_clear_time': news_check['next_clear_time'],
                'critical_event': critical_event
            }
        else:
            # Check for upcoming news in next hour
            upcoming_high_impact = [n for n in news_check['upcoming_news'] if n['impact'] == 'HIGH' and n['time_diff_minutes'] <= 60]
            
            if upcoming_high_impact:
                next_event = min(upcoming_high_impact, key=lambda x: x['time_diff_minutes'])
                recommendation = {
                    'allow_trading': True,
                    'reason': f"Caution: {next_event['event']} in {next_event['time_diff_minutes']:.0f} minutes",
                    'risk_level': 'MEDIUM',
                    'next_clear_time': current_time or datetime.now(),
                    'warning_event': next_event
                }
            else:
                recommendation = {
                    'allow_trading': True,
                    'reason': "No high-impact news events detected",
                    'risk_level': 'LOW',
                    'next_clear_time': current_time or datetime.now()
                }
        
        return recommendation
    
    def analyze(self, data: pd.DataFrame = None) -> Dict:
        """
        Main economic news filter analysis
        Returns trading permission based on news events
        """
        try:
            current_time = datetime.now()
            
            # Get trading recommendation
            recommendation = self.get_trading_recommendation(current_time)
            
            # Calculate score based on recommendation
            if recommendation['allow_trading']:
                if recommendation['risk_level'] == 'LOW':
                    score = 1.0  # Full permission
                    confidence = 1.0
                elif recommendation['risk_level'] == 'MEDIUM':
                    score = 0.7  # Cautious permission
                    confidence = 0.7
                else:
                    score = 0.5  # Neutral
                    confidence = 0.5
            else:
                score = 0.0  # No permission
                confidence = 1.0  # High confidence in blocking
            
            # Direction is always neutral for news filter
            direction = 'NEUTRAL'
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'allow_trading': recommendation['allow_trading'],
                'risk_level': recommendation['risk_level'],
                'reason': recommendation['reason'],
                'next_clear_time': recommendation['next_clear_time'],
                'news_check': self.check_upcoming_news(current_time),
                'details': f"News Filter: {recommendation['reason']}"
            }
            
        except Exception as e:
            self.logger.error(f"Economic news filter error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'allow_trading': True,  # Default to allow if filter fails
                'details': f'News filter failed: {str(e)}'
            }
