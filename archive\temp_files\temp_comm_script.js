
            // 🔗 VIP BIG BANG Chrome Robot Communication
            (function() {
                console.log('🔗 VIP BIG BANG Chrome Communication Loading...');
                
                // WebSocket connection to robot
                let robotSocket = null;
                let reconnectInterval = null;
                
                function connectToRobot() {
                    try {
                        robotSocket = new WebSocket('ws://localhost:8765');
                        
                        robotSocket.onopen = function() {
                            console.log('🤖 Connected to VIP BIG BANG Robot!');
                            
                            // Send connection status
                            robotSocket.send(JSON.stringify({
                                type: 'connection_status',
                                connected: true,
                                timestamp: Date.now()
                            }));
                            
                            // Start monitoring
                            startQuotexMonitoring();
                        };
                        
                        robotSocket.onclose = function() {
                            console.log('🔌 Disconnected from robot, attempting reconnect...');
                            setTimeout(connectToRobot, 3000);
                        };
                        
                        robotSocket.onerror = function(error) {
                            console.error('❌ Robot connection error:', error);
                        };
                        
                    } catch (error) {
                        console.error('❌ Failed to connect to robot:', error);
                        setTimeout(connectToRobot, 5000);
                    }
                }
                
                function startQuotexMonitoring() {
                    console.log('📊 Starting Quotex monitoring for robot...');
                    
                    // Price monitoring
                    setInterval(() => {
                        try {
                            const prices = extractQuotexPrices();
                            if (Object.keys(prices).length > 0 && robotSocket && robotSocket.readyState === WebSocket.OPEN) {
                                robotSocket.send(JSON.stringify({
                                    type: 'price_update',
                                    data: prices,
                                    timestamp: Date.now()
                                }));
                            }
                        } catch (error) {
                            console.error('Price monitoring error:', error);
                        }
                    }, 1000);
                    
                    // Balance monitoring
                    setInterval(() => {
                        try {
                            const balance = extractQuotexBalance();
                            if (balance > 0 && robotSocket && robotSocket.readyState === WebSocket.OPEN) {
                                robotSocket.send(JSON.stringify({
                                    type: 'balance_update',
                                    balance: balance,
                                    timestamp: Date.now()
                                }));
                            }
                        } catch (error) {
                            console.error('Balance monitoring error:', error);
                        }
                    }, 2000);
                    
                    // Trade monitoring
                    monitorTrades();
                }
                
                function extractQuotexPrices() {
                    const prices = {};
                    
                    // Enhanced price selectors for Quotex
                    const selectors = [
                        '.chart-price',
                        '.current-rate', 
                        '[data-testid="current-price"]',
                        '.asset-price',
                        '.price-display',
                        '.rate-value',
                        '.trading-chart__price',
                        '.chart__price',
                        '.quote-value',
                        '.price-current',
                        '.current-price',
                        '.asset-current-price',
                        '.chart-current-price'
                    ];
                    
                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(element => {
                                const text = element.textContent || element.innerText || '';
                                const priceMatch = text.match(/\d+\.\d{3,5}/);
                                
                                if (priceMatch) {
                                    const price = parseFloat(priceMatch[0]);
                                    if (price > 0 && price < 1000) {
                                        const asset = determineAssetFromElement(element) || 'EUR/USD';
                                        prices[asset] = {
                                            price: price,
                                            timestamp: Date.now(),
                                            source: selector
                                        };
                                    }
                                }
                            });
                        } catch (e) {}
                    });
                    
                    return prices;
                }
                
                function extractQuotexBalance() {
                    const selectors = [
                        '.balance__value',
                        '.user-balance',
                        '[data-testid="balance"]',
                        '.account-balance',
                        '.header-balance',
                        '.balance-amount',
                        '.wallet-balance',
                        '.current-balance',
                        '.balance-display',
                        '.user-balance-amount'
                    ];
                    
                    for (const selector of selectors) {
                        try {
                            const element = document.querySelector(selector);
                            if (element) {
                                const text = element.textContent || element.innerText || '';
                                const balanceMatch = text.match(/\d+(?:\.\d+)?/);
                                
                                if (balanceMatch) {
                                    const balance = parseFloat(balanceMatch[0]);
                                    if (balance > 0) {
                                        return balance;
                                    }
                                }
                            }
                        } catch (e) {}
                    }
                    
                    return 0;
                }
                
                function determineAssetFromElement(element) {
                    try {
                        // Look for asset name in parent elements
                        let parent = element.parentElement;
                        let depth = 0;
                        
                        while (parent && depth < 5) {
                            const text = parent.textContent || parent.innerText || '';
                            
                            // Common forex pairs
                            const assetMatch = text.match(/(EUR\/USD|GBP\/USD|USD\/JPY|AUD\/USD|USD\/CAD|USD\/CHF|NZD\/USD)/i);
                            if (assetMatch) {
                                return assetMatch[1].toUpperCase();
                            }
                            
                            parent = parent.parentElement;
                            depth++;
                        }
                        
                        return 'EUR/USD'; // Default
                    } catch (e) {
                        return 'EUR/USD';
                    }
                }
                
                function monitorTrades() {
                    // Monitor for trade results
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.addedNodes.length > 0) {
                                mutation.addedNodes.forEach(node => {
                                    if (node.nodeType === 1) {
                                        const text = node.textContent || '';
                                        
                                        // Look for trade result indicators
                                        if (text.includes('Win') || text.includes('Loss') || 
                                            text.includes('Profit') || text.includes('Lost')) {
                                            
                                            if (robotSocket && robotSocket.readyState === WebSocket.OPEN) {
                                                robotSocket.send(JSON.stringify({
                                                    type: 'trade_result',
                                                    data: {
                                                        result: text,
                                                        timestamp: Date.now()
                                                    }
                                                }));
                                            }
                                        }
                                    }
                                });
                            }
                        });
                    });
                    
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                }
                
                // Start connection
                connectToRobot();
                
                // Set global flag
                window.VIP_BIG_BANG_ROBOT_CONNECTED = true;
                
                console.log('🏆 VIP BIG BANG Chrome Robot Communication Ready!');
            })();
            