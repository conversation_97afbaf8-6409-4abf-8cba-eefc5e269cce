"""
VIP BIG BANG Professional Theme System
Enterprise-grade styling with proper CSS architecture
"""

class VIPTheme:
    """Professional theme system for VIP BIG BANG"""
    
    # Color Palette
    COLORS = {
        # Primary Colors
        'primary_dark': '#1A0F3D',
        'primary_medium': '#2D1B69', 
        'primary_light': '#4B3296',
        'primary_accent': '#7850C8',
        
        # Success/Buy Colors
        'success_primary': '#32C832',
        'success_secondary': '#228B22',
        'success_light': '#4AFF4A',
        
        # Danger/Sell Colors
        'danger_primary': '#FF4444',
        'danger_secondary': '#CC2222',
        'danger_light': '#FF6666',
        
        # Warning Colors
        'warning_primary': '#FF8844',
        'warning_secondary': '#CC6622',
        
        # Neutral Colors
        'white': '#FFFFFF',
        'gray_light': '#CCCCCC',
        'gray_medium': '#888888',
        'gray_dark': '#444444',
        'black': '#000000',
        
        # Special Colors
        'gold': '#FFD700',
        'blue': '#4488FF',
        'transparent': 'transparent'
    }
    
    # Typography
    FONTS = {
        'primary': "'Segoe UI', 'Arial', sans-serif",
        'secondary': "'Roboto', 'Arial', sans-serif",
        'monospace': "'Consolas', 'Monaco', monospace"
    }
    
    FONT_SIZES = {
        'xs': '10px',
        'sm': '12px', 
        'md': '14px',
        'lg': '16px',
        'xl': '18px',
        'xxl': '24px',
        'xxxl': '32px'
    }
    
    # Spacing
    SPACING = {
        'xs': '4px',
        'sm': '8px',
        'md': '12px',
        'lg': '16px',
        'xl': '20px',
        'xxl': '24px',
        'xxxl': '32px'
    }
    
    # Border Radius
    RADIUS = {
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        'xxl': '20px',
        'round': '50%'
    }
    
    # Shadows
    SHADOWS = {
        'sm': '0 2px 4px rgba(0,0,0,0.1)',
        'md': '0 4px 8px rgba(0,0,0,0.15)',
        'lg': '0 8px 16px rgba(0,0,0,0.2)',
        'xl': '0 12px 24px rgba(0,0,0,0.25)'
    }
    
    @classmethod
    def get_main_stylesheet(cls):
        """Get the main application stylesheet"""
        return f"""
        /* === GLOBAL STYLES === */
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 {cls.COLORS['primary_medium']}, 
                stop:1 {cls.COLORS['primary_dark']});
            color: {cls.COLORS['white']};
            font-family: {cls.FONTS['primary']};
            font-size: {cls.FONT_SIZES['sm']};
        }}
        
        QWidget {{
            background: {cls.COLORS['transparent']};
            color: {cls.COLORS['white']};
            font-family: {cls.FONTS['primary']};
        }}
        
        /* === PANEL STYLES === */
        .vip-panel {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 rgba(75, 50, 150, 0.8), 
                stop:1 rgba(45, 27, 105, 0.9));
            border: 2px solid rgba(120, 80, 200, 0.5);
            border-radius: {cls.RADIUS['lg']};
            padding: {cls.SPACING['md']};
        }}
        
        .vip-panel:hover {{
            border-color: rgba(120, 80, 200, 0.8);
        }}
        
        /* === BUTTON STYLES === */
        .vip-btn {{
            background: rgba(75, 50, 150, 0.6);
            border: 1px solid rgba(120, 80, 200, 0.8);
            border-radius: {cls.RADIUS['xxl']};
            padding: {cls.SPACING['sm']} {cls.SPACING['lg']};
            color: {cls.COLORS['white']};
            font-weight: bold;
            font-size: {cls.FONT_SIZES['sm']};
        }}
        
        .vip-btn:hover {{
            background: rgba(120, 80, 200, 0.8);
            border-color: rgba(150, 100, 250, 0.9);
        }}
        
        .vip-btn:pressed {{
            background: rgba(45, 27, 105, 0.9);
        }}
        
        .vip-btn-active {{
            background: {cls.COLORS['success_primary']};
            border-color: {cls.COLORS['success_secondary']};
        }}
        
        .vip-btn-buy {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 {cls.COLORS['success_primary']}, 
                stop:1 {cls.COLORS['success_secondary']});
            border: none;
            border-radius: {cls.RADIUS['xxl']};
            padding: {cls.SPACING['md']} {cls.SPACING['xxl']};
            font-size: {cls.FONT_SIZES['md']};
        }}
        
        .vip-btn-sell {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 {cls.COLORS['danger_primary']}, 
                stop:1 {cls.COLORS['danger_secondary']});
            border: none;
            border-radius: {cls.RADIUS['xxl']};
            padding: {cls.SPACING['md']} {cls.SPACING['xxl']};
            font-size: {cls.FONT_SIZES['md']};
        }}
        
        .vip-btn-control {{
            background: rgba(75, 50, 150, 0.8);
            border: 2px solid rgba(120, 80, 200, 0.6);
            border-radius: {cls.RADIUS['lg']};
            color: {cls.COLORS['white']};
            font-weight: bold;
            min-width: 80px;
            min-height: 80px;
        }}
        
        .vip-btn-control:hover {{
            background: rgba(120, 80, 200, 0.9);
            border-color: rgba(150, 100, 250, 0.8);
        }}
        
        /* === TEXT STYLES === */
        .vip-title {{
            color: {cls.COLORS['white']};
            font-size: {cls.FONT_SIZES['lg']};
            font-weight: bold;
        }}
        
        .vip-subtitle {{
            color: {cls.COLORS['gray_light']};
            font-size: {cls.FONT_SIZES['md']};
            font-weight: 600;
        }}
        
        .vip-value {{
            color: {cls.COLORS['success_primary']};
            font-size: {cls.FONT_SIZES['xl']};
            font-weight: bold;
        }}
        
        .vip-value-large {{
            color: {cls.COLORS['success_primary']};
            font-size: {cls.FONT_SIZES['xxl']};
            font-weight: bold;
        }}
        
        .vip-percentage {{
            font-size: {cls.FONT_SIZES['xxl']};
            font-weight: bold;
        }}
        
        /* === PROGRESS BAR STYLES === */
        QProgressBar {{
            background: rgba(50, 50, 50, 0.8);
            border: 1px solid rgba(120, 80, 200, 0.5);
            border-radius: {cls.RADIUS['md']};
            text-align: center;
            color: {cls.COLORS['white']};
            font-weight: bold;
            height: 20px;
        }}
        
        QProgressBar::chunk {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 {cls.COLORS['success_primary']}, 
                stop:1 {cls.COLORS['success_secondary']});
            border-radius: {cls.RADIUS['sm']};
        }}
        
        /* === TOGGLE SWITCH === */
        .vip-toggle-on {{
            background: {cls.COLORS['success_primary']};
            border-radius: {cls.RADIUS['lg']};
            padding: {cls.SPACING['sm']} {cls.SPACING['lg']};
        }}
        
        .vip-toggle-off {{
            background: {cls.COLORS['gray_dark']};
            border-radius: {cls.RADIUS['lg']};
            padding: {cls.SPACING['sm']} {cls.SPACING['lg']};
        }}
        
        /* === PULSE BAR === */
        .vip-pulse-bar {{
            border-radius: {cls.RADIUS['sm']};
            margin: 1px;
            height: 8px;
        }}
        """
