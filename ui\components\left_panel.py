"""
Professional Left Panel Component for VIP BIG BANG
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from .base_component import BaseComponent, VIPPanel, VIPLabel, VIPToggleSwitch, VIPPulseBar

class VIPLeftPanel(BaseComponent):
    """Professional left panel component exactly matching the design"""
    
    # Signals
    manual_trading_toggled = Signal(bool)
    
    def __init__(self, parent=None):
        self.balance = 1251.76
        self.autotrade_enabled = True
        self.trade_profit = 5
        self.profit_loss = 10
        super().__init__(parent)
    
    def _setup_component(self):
        """Setup the left panel component"""
        self.apply_style("vip-panel")
        self.setFixedWidth(180)
        
        # Main vertical layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Manual Trading section
        manual_section = self._create_manual_trading_section()
        layout.addWidget(manual_section)
        
        # Account Summary section
        account_section = self._create_account_summary_section()
        layout.addWidget(account_section)
        
        # AutoTrade section
        autotrade_section = self._create_autotrade_section()
        layout.addWidget(autotrade_section)
        
        # PulseBar section
        pulsebar_section = self._create_pulsebar_section()
        layout.addWidget(pulsebar_section)
        
        # Stretch to fill remaining space
        layout.addStretch()
    
    def _create_manual_trading_section(self):
        """Create manual trading section"""
        panel = VIPPanel()
        
        # Mouse cursor icon
        cursor_icon = VIPLabel("🖱️")
        cursor_icon.setAlignment(Qt.AlignCenter)
        cursor_icon.setStyleSheet("font-size: 24px; margin-bottom: 5px; background: transparent;")
        panel.add_widget(cursor_icon)
        
        # Manual Trading text
        manual_text = VIPLabel("Manual Trading", "subtitle")
        manual_text.setAlignment(Qt.AlignCenter)
        manual_text.setStyleSheet("color: white; font-weight: bold; font-size: 12px; background: transparent;")
        panel.add_widget(manual_text)
        
        # Toggle switch
        self.manual_toggle = VIPToggleSwitch(initial_state=True)
        self.manual_toggle.toggled.connect(self.manual_trading_toggled.emit)
        panel.add_widget(self.manual_toggle)
        
        return panel
    
    def _create_account_summary_section(self):
        """Create account summary section"""
        panel = VIPPanel()
        
        # Title
        title = VIPLabel("Account Summary", "subtitle")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white; font-weight: bold; font-size: 12px; margin-bottom: 10px; background: transparent;")
        panel.add_widget(title)
        
        # Balance - exactly like in image
        self.balance_label = VIPLabel(f"${self.balance:,.2f}", "value-large")
        self.balance_label.setAlignment(Qt.AlignCenter)
        self.balance_label.setStyleSheet("""
            font-size: 24px; 
            color: #32C832; 
            font-weight: bold; 
            background: transparent;
        """)
        panel.add_widget(self.balance_label)
        
        return panel
    
    def _create_autotrade_section(self):
        """Create autotrade section"""
        panel = VIPPanel()
        
        # AutoTrade status
        status_text = "AutoTrade ON" if self.autotrade_enabled else "AutoTrade OFF"
        status_color = "#32C832" if self.autotrade_enabled else "#FF4444"
        
        self.autotrade_status = VIPLabel(status_text)
        self.autotrade_status.setStyleSheet(f"""
            color: {status_color}; 
            font-weight: bold; 
            font-size: 12px; 
            background: transparent;
        """)
        panel.add_widget(self.autotrade_status)
        
        # Trade statistics
        self.trade_stats = VIPLabel(f"Trade $ +{self.trade_profit}\\nProfit / Loss +{self.profit_loss}")
        self.trade_stats.setStyleSheet("""
            color: white; 
            font-size: 10px; 
            margin-top: 5px; 
            background: transparent;
        """)
        panel.add_widget(self.trade_stats)
        
        return panel
    
    def _create_pulsebar_section(self):
        """Create pulsebar section"""
        panel = VIPPanel()
        
        # Title
        title = VIPLabel("PulseBar", "subtitle")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            color: white; 
            font-weight: bold; 
            font-size: 12px; 
            margin-bottom: 10px; 
            background: transparent;
        """)
        panel.add_widget(title)
        
        # Pulse bar with colors exactly like in image
        self.pulse_bar = VIPPulseBar(["#FF4444", "#FF8844", "#FFFF44", "#88FF44", "#44FF44"])
        panel.add_widget(self.pulse_bar)
        
        return panel
    
    def update_balance(self, balance: float):
        """Update the account balance"""
        self.balance = balance
        self.balance_label.setText(f"${balance:,.2f}")

    def update_from_extension_data(self, data: dict):
        """Update UI from Chrome Extension data"""
        try:
            # Update balance from extension
            if 'balance' in data:
                balance_str = data['balance']
                if balance_str and balance_str != 'N/A':
                    # Extract numeric value from balance string like "$0.85"
                    balance_clean = balance_str.replace('$', '').replace(',', '')
                    try:
                        balance_value = float(balance_clean)
                        self.update_balance(balance_value)
                        print(f"✅ Balance updated from extension: ${balance_value}")
                    except ValueError:
                        print(f"⚠️ Could not parse balance: {balance_str}")

            # Update asset information
            if 'currentAsset' in data and data['currentAsset']:
                asset = data['currentAsset']
                if asset != 'Market' and asset != 'None':  # Filter fake data
                    print(f"✅ Asset updated from extension: {asset}")

            # Update extraction count
            if 'extractionCount' in data:
                count = data['extractionCount']
                print(f"✅ Extraction count: {count}")

        except Exception as e:
            print(f"❌ Error updating UI from extension data: {e}")
    
    def update_autotrade_status(self, enabled: bool):
        """Update autotrade status"""
        self.autotrade_enabled = enabled
        status_text = "AutoTrade ON" if enabled else "AutoTrade OFF"
        status_color = "#32C832" if enabled else "#FF4444"
        
        self.autotrade_status.setText(status_text)
        self.autotrade_status.setStyleSheet(f"""
            color: {status_color}; 
            font-weight: bold; 
            font-size: 12px; 
            background: transparent;
        """)
    
    def update_trade_stats(self, trade_profit: int, profit_loss: int):
        """Update trade statistics"""
        self.trade_profit = trade_profit
        self.profit_loss = profit_loss
        self.trade_stats.setText(f"Trade $ +{trade_profit}\\nProfit / Loss +{profit_loss}")
    
    def update_pulse_colors(self, colors: list):
        """Update pulse bar colors"""
        self.pulse_bar.update_colors(colors)
    
    def set_manual_trading(self, enabled: bool):
        """Set manual trading state"""
        self.manual_toggle.set_state(enabled)
    
    def get_manual_trading_state(self) -> bool:
        """Get manual trading state"""
        return self.manual_toggle.state
    
    def get_balance(self) -> float:
        """Get current balance"""
        return self.balance
    
    def get_autotrade_status(self) -> bool:
        """Get autotrade status"""
        return self.autotrade_enabled
