// 🚀 VIP BIG BANG - Enhanced Content Script
console.log('🚀 VIP BIG BANG Extension Loaded on:', window.location.href);

class VIPBigBangExtractor {
    constructor() {
        this.isActive = false;
        this.extractionInterval = null;
        this.websocket = null;
        this.extractionCount = 0;
        this.lastData = null;

        this.init();
    }

    init() {
        // Check if we're on Quotex page
        if (!this.isQuotexPage()) {
            console.log('❌ Not on Quotex page');
            return;
        }

        console.log('✅ Quotex page detected');

        // Setup message listener
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open
        });

        // Connect to VIP BIG BANG server
        this.connectWebSocket();

        // Send ready signal
        this.sendStatusUpdate('ready');

        console.log('🎯 VIP BIG BANG Extractor initialized');
    }

    isQuotexPage() {
        const url = window.location.href.toLowerCase();
        return url.includes('qxbroker.com') || url.includes('quotex.io') || url.includes('quotex.com');
    }

    handleMessage(request, sender, sendResponse) {
        console.log('📨 Message received:', request);

        try {
            switch (request.action) {
                case 'startExtraction':
                    this.startExtraction();
                    sendResponse({ status: 'started', active: this.isActive });
                    break;

                case 'stopExtraction':
                    this.stopExtraction();
                    sendResponse({ status: 'stopped', active: this.isActive });
                    break;

                case 'getStatus':
                    const status = {
                        isActive: this.isActive,
                        extractionCount: this.extractionCount,
                        lastData: this.lastData,
                        activeWebSockets: 0,
                        isQuotexPage: this.isQuotexPage(),
                        professionalExtractor: window.professionalQuotexExtractor ?
                            window.professionalQuotexExtractor.getStatus() : null
                    };
                    sendResponse(status);
                    break;

                case 'extractNow':
                    // Immediate extraction request
                    const data = this.getQuotexData();
                    this.sendToServer(data);
                    sendResponse({ status: 'extracted', data: data });
                    break;

                default:
                    sendResponse({ error: 'Unknown action', action: request.action });
            }
        } catch (error) {
            console.error('❌ Message handling error:', error);
            sendResponse({ error: error.message, action: request.action });
        }
    }

    startExtraction() {
        if (this.isActive) {
            console.log('⚠️ Extraction already active');
            return;
        }

        if (!this.isQuotexPage()) {
            console.log('❌ Cannot start: Not on Quotex page');
            return;
        }

        console.log('🚀 Starting data extraction...');
        this.isActive = true;

        // Start extraction interval
        this.extractionInterval = setInterval(() => {
            this.extractData();
        }, 2000);

        // Extract immediately
        this.extractData();

        this.sendStatusUpdate('extraction_started');
    }

    stopExtraction() {
        console.log('⏹️ Stopping data extraction...');
        this.isActive = false;

        if (this.extractionInterval) {
            clearInterval(this.extractionInterval);
            this.extractionInterval = null;
        }

        this.sendStatusUpdate('extraction_stopped');
    }

    extractData() {
        try {
            this.extractionCount++;

            const data = this.getQuotexData();
            this.lastData = data;

            // Send to VIP BIG BANG server
            this.sendToServer(data);

            // Send status to popup
            this.sendStatusUpdate('data_extracted', data);

            console.log(`📊 Extraction #${this.extractionCount}:`, data);

        } catch (error) {
            console.error('❌ Extraction error:', error);
        }
    }

    getQuotexData() {
        // Enhanced selectors for Quotex
        const selectors = {
            balance: [
                '[data-testid="balance"]',
                '.balance-value',
                '[class*="balance"]',
                '.header-balance',
                '.user-balance',
                '[class*="Balance"]'
            ],
            asset: [
                '[data-testid="asset-name"]',
                '.asset-name',
                '[class*="asset-name"]',
                '.current-asset',
                '[class*="Asset"]'
            ],
            price: [
                '[data-testid="current-price"]',
                '.current-price',
                '[class*="price"]',
                '.quote-price',
                '[class*="Price"]'
            ],
            payout: [
                '[class*="payout"]',
                '[class*="Payout"]',
                '.payout-value'
            ]
        };

        const extractText = (selectorArray) => {
            for (const selector of selectorArray) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    return element.textContent.trim();
                }
            }
            return null;
        };

        return {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            balance: extractText(selectors.balance),
            currentAsset: extractText(selectors.asset),
            currentPrice: extractText(selectors.price),
            payout: extractText(selectors.payout),
            pageTitle: document.title,
            extractionCount: this.extractionCount,
            source: 'VIP_BIG_BANG_CONTENT_SCRIPT'
        };
    }

    connectWebSocket() {
        try {
            // Close existing connection if any
            if (this.websocket) {
                try {
                    this.websocket.close();
                } catch (e) {
                    // Ignore close errors
                }
                this.websocket = null;
            }

            console.log('🔌 Attempting WebSocket connection to VIP BIG BANG server...');
            this.websocket = new WebSocket('ws://localhost:8765');

            this.websocket.onopen = () => {
                console.log('✅ Connected to VIP BIG BANG server');
                this.sendToServer({
                    type: 'connection',
                    message: 'VIP BIG BANG Extension connected',
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                });
            };

            this.websocket.onclose = (event) => {
                console.log('🔌 Disconnected from VIP BIG BANG server', {
                    code: event.code,
                    reason: event.reason,
                    wasClean: event.wasClean
                });

                // Only reconnect if not a clean close
                if (!event.wasClean && event.code !== 1000) {
                    console.log('🔄 Attempting reconnection in 3 seconds...');
                    setTimeout(() => this.connectWebSocket(), 3000);
                }
            };

            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket connection error:', {
                    type: error.type,
                    target: error.target ? error.target.readyState : 'unknown',
                    timestamp: new Date().toISOString()
                });
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    console.log('📨 Received from server:', message);
                } catch (e) {
                    console.log('📨 Received non-JSON message:', event.data);
                }
            };

        } catch (error) {
            console.error('❌ WebSocket setup failed:', {
                message: error.message,
                stack: error.stack
            });

            // Retry after delay
            setTimeout(() => {
                console.log('🔄 Retrying WebSocket connection...');
                this.connectWebSocket();
            }, 5000);
        }
    }

    sendToServer(data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            try {
                this.websocket.send(JSON.stringify({
                    type: 'quotex_data',
                    data: data
                }));
            } catch (error) {
                console.error('❌ Failed to send data:', error);
            }
        }
    }

    sendStatusUpdate(status, data = null) {
        try {
            chrome.runtime.sendMessage({
                type: 'status_update',
                status: status,
                data: data,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.log('Status update failed:', error);
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.vipBigBangExtractor = new VIPBigBangExtractor();
    });
} else {
    window.vipBigBangExtractor = new VIPBigBangExtractor();
}

// Also initialize immediately
window.vipBigBangExtractor = new VIPBigBangExtractor();
