@echo off
title VIP BIG BANG - Fix Extension and Start
color 0A

echo.
echo ===============================================
echo 🔧 VIP BIG BANG - Extension Fix and Start
echo 📦 Creating Extension Files
echo 🚀 Starting Robot with Fixed Extension
echo ===============================================
echo.

echo 🔧 Step 1: Creating chrome_extension directory...
if not exist "chrome_extension" mkdir chrome_extension

echo 📦 Step 2: Creating manifest.json...
echo {> chrome_extension\manifest.json
echo   "manifest_version": 3,>> chrome_extension\manifest.json
echo   "name": "VIP BIG BANG Quotex Reader",>> chrome_extension\manifest.json
echo   "version": "1.0.0",>> chrome_extension\manifest.json
echo   "description": "Read real data from Quotex",>> chrome_extension\manifest.json
echo   "permissions": ["activeTab", "storage", "tabs", "scripting"],>> chrome_extension\manifest.json
echo   "host_permissions": ["https://qxbroker.com/*", "https://quotex.io/*"],>> chrome_extension\manifest.json
echo   "background": {"service_worker": "background.js"},>> chrome_extension\manifest.json
echo   "content_scripts": [{"matches": ["https://qxbroker.com/*"], "js": ["content.js"]}],>> chrome_extension\manifest.json
echo   "action": {"default_popup": "popup.html"}>> chrome_extension\manifest.json
echo }>> chrome_extension\manifest.json

echo 📝 Step 3: Creating content.js...
echo console.log('VIP BIG BANG Extension Loaded');> chrome_extension\content.js
echo function extractData() {>> chrome_extension\content.js
echo   const balance = document.querySelector('.balance') ^|^| document.querySelector('[class*="balance"]');>> chrome_extension\content.js
echo   const price = document.querySelector('.price') ^|^| document.querySelector('[class*="price"]');>> chrome_extension\content.js
echo   const data = {>> chrome_extension\content.js
echo     balance: balance ? balance.textContent : 'Not found',>> chrome_extension\content.js
echo     price: price ? price.textContent : 'Not found',>> chrome_extension\content.js
echo     timestamp: new Date().toISOString()>> chrome_extension\content.js
echo   };>> chrome_extension\content.js
echo   try {>> chrome_extension\content.js
echo     const ws = new WebSocket('ws://localhost:8765');>> chrome_extension\content.js
echo     ws.onopen = function() {>> chrome_extension\content.js
echo       ws.send(JSON.stringify({type: 'quotex_data', data: data}));>> chrome_extension\content.js
echo       ws.close();>> chrome_extension\content.js
echo     };>> chrome_extension\content.js
echo   } catch(e) { console.log('WebSocket failed:', e); }>> chrome_extension\content.js
echo }>> chrome_extension\content.js
echo setInterval(extractData, 2000);>> chrome_extension\content.js

echo 🔧 Step 4: Creating background.js...
echo console.log('VIP BIG BANG Background Script');> chrome_extension\background.js
echo chrome.runtime.onMessage.addListener((msg, sender, response) =^> {>> chrome_extension\background.js
echo   console.log('Message:', msg);>> chrome_extension\background.js
echo   response({status: 'ok'});>> chrome_extension\background.js
echo });>> chrome_extension\background.js

echo 📄 Step 5: Creating popup.html...
echo ^<!DOCTYPE html^>> chrome_extension\popup.html
echo ^<html^>^<head^>^<style^>body{width:300px;padding:20px;}^</style^>^</head^>>> chrome_extension\popup.html
echo ^<body^>^<h3^>🚀 VIP BIG BANG^</h3^>^<p^>Extension Active^</p^>^</body^>^</html^>>> chrome_extension\popup.html

echo ✅ Extension files created successfully!
echo.

echo 🚀 Step 6: Starting VIP BIG BANG Robot...
echo.

python vip_real_quotex_main.py

echo.
echo 🔌 VIP BIG BANG stopped.
pause
