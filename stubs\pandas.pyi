"""
Pandas type stubs for VIP BIG BANG
"""

from typing import Any, Dict, Iterator, Tuple

class Series:
    def __init__(self, data: Any = None, index: Any = None, dtype: Any = None, name: Any = None) -> None: ...
    def to_dict(self) -> Dict[Any, Any]: ...
    def __getitem__(self, key: Any) -> Any: ...
    def __setitem__(self, key: Any, value: Any) -> None: ...
    def mean(self) -> float: ...
    def tail(self, n: int = 5) -> 'Series': ...
    def pct_change(self) -> 'Series': ...
    def dropna(self) -> 'Series': ...
    def std(self) -> float: ...

class DataFrame:
    def __init__(self, data: Any = None, index: Any = None, columns: Any = None, dtype: Any = None) -> None: ...
    def iterrows(self) -> Iterator[Tuple[Any, Series]]: ...
    def __len__(self) -> int: ...
    def __getitem__(self, key: Any) -> Any: ...
    def __setitem__(self, key: Any, value: Any) -> None: ...
    def head(self, n: int = 5) -> 'DataFrame': ...
    def tail(self, n: int = 5) -> 'DataFrame': ...
    def iloc(self) -> Any: ...
    def loc(self) -> Any: ...
    @property
    def columns(self) -> Any: ...

def read_csv(filepath_or_buffer: Any, **kwargs: Any) -> DataFrame: ...
