#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
VIP BIG BANG - Simple Comprehensive Dashboard
Unified Interface for All Trading Components
Real-time System Status & Live Data Integration
"""

import sys
import os
import json
import time
import threading
from datetime import datetime
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")
    except:
        pass

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("Installing PySide6...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True

class SimpleComprehensiveDashboard(QMainWindow):
    """Simple VIP BIG BANG Comprehensive Dashboard"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VIP BIG BANG - Comprehensive Trading Dashboard")
        
        # Initialize data
        self.system_data = {
            'websocket_active': True,
            'data_extractor_active': True,
            'analysis_engine_active': True,
            'autotrade_active': True,
            'extension_connected': False
        }
        
        self.live_data = {
            'balance': 0.0,
            'current_asset': 'Waiting...',
            'current_price': 0.0
        }
        
        # Setup window
        self.setup_window()
        
        # Setup UI
        self.setup_ui()
        
        # Setup timers
        self.setup_timers()
        
        # Load shared data
        self.load_shared_data()
        
    def setup_window(self):
        """Setup main window"""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        width = int(screen_geometry.width() * 0.9)
        height = int(screen_geometry.height() * 0.9)
        
        self.setGeometry(
            (screen_geometry.width() - width) // 2,
            (screen_geometry.height() - height) // 2,
            width,
            height
        )
        
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f172a, stop:1 #1e293b);
                color: #e2e8f0;
            }
        """)
        
    def setup_ui(self):
        """Setup main UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header
        header = self.create_header()
        main_layout.addWidget(header)
        
        # Content area
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # Left panel - System Status
        left_panel = self.create_system_status_panel()
        content_layout.addWidget(left_panel)
        
        # Center panel - Live Data
        center_panel = self.create_live_data_panel()
        content_layout.addWidget(center_panel)
        
        # Right panel - Controls
        right_panel = self.create_controls_panel()
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
        
        # Footer
        footer = self.create_footer()
        main_layout.addWidget(footer)
        
    def create_header(self):
        """Create header"""
        header = QFrame()
        header.setFrameStyle(QFrame.Box)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:1 #3b82f6);
                border: 2px solid #60a5fa;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header)
        
        title = QLabel("VIP BIG BANG - COMPREHENSIVE TRADING DASHBOARD")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(title)
        
        layout.addStretch()
        
        self.time_label = QLabel()
        self.time_label.setFont(QFont("Consolas", 12, QFont.Bold))
        self.time_label.setStyleSheet("color: #fbbf24; font-weight: bold;")
        layout.addWidget(self.time_label)
        
        return header
        
    def create_system_status_panel(self):
        """Create system status panel"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Box)
        panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e293b, stop:1 #334155);
                border: 2px solid #3b82f6;
                border-radius: 15px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        title = QLabel("SYSTEM STATUS")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #3b82f6; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Status indicators
        self.websocket_status = QLabel("WebSocket Server: Active")
        self.data_extractor_status = QLabel("Data Extractor: Active")
        self.analysis_engine_status = QLabel("Analysis Engine: 20 Indicators")
        self.autotrade_status = QLabel("AutoTrade Engine: Ready")
        self.chrome_extension_status = QLabel("Chrome Extension: Waiting")
        
        for status_label in [self.websocket_status, self.data_extractor_status, 
                           self.analysis_engine_status, self.autotrade_status, 
                           self.chrome_extension_status]:
            status_label.setFont(QFont("Consolas", 10))
            status_label.setStyleSheet("color: #e2e8f0; margin: 5px 0;")
            layout.addWidget(status_label)
            
        # Performance
        self.performance_label = QLabel("Performance: 0.150s read time")
        self.performance_label.setFont(QFont("Consolas", 10))
        self.performance_label.setStyleSheet("color: #10b981; margin-top: 15px;")
        layout.addWidget(self.performance_label)
        
        layout.addStretch()
        return panel
        
    def create_live_data_panel(self):
        """Create live data panel"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Box)
        panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f172a, stop:1 #1e293b);
                border: 2px solid #10b981;
                border-radius: 15px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        title = QLabel("LIVE QUOTEX DATA")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #10b981; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Account info
        account_group = QGroupBox("REAL ACCOUNT INFORMATION")
        account_group.setStyleSheet("""
            QGroupBox {
                color: #fbbf24;
                font-weight: bold;
                border: 1px solid #fbbf24;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        account_layout = QVBoxLayout(account_group)
        
        self.balance_label = QLabel("Balance: Waiting for connection...")
        self.account_type_label = QLabel("Type: Waiting for connection...")
        self.profit_label = QLabel("Today's P&L: Waiting for connection...")
        
        for label in [self.balance_label, self.account_type_label, self.profit_label]:
            label.setFont(QFont("Consolas", 10))
            label.setStyleSheet("color: #e2e8f0; margin: 3px 0;")
            account_layout.addWidget(label)
            
        layout.addWidget(account_group)
        
        # Trading info
        trading_group = QGroupBox("CURRENT TRADING")
        trading_group.setStyleSheet("""
            QGroupBox {
                color: #8b5cf6;
                font-weight: bold;
                border: 1px solid #8b5cf6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        trading_layout = QVBoxLayout(trading_group)
        
        self.asset_label = QLabel("Asset: Waiting for connection...")
        self.price_label = QLabel("Price: Waiting for connection...")
        self.payout_label = QLabel("Payout: Waiting for connection...")
        
        for label in [self.asset_label, self.price_label, self.payout_label]:
            label.setFont(QFont("Consolas", 10))
            label.setStyleSheet("color: #e2e8f0; margin: 3px 0;")
            trading_layout.addWidget(label)
            
        layout.addWidget(trading_group)
        
        # Logs
        logs_group = QGroupBox("SYSTEM LOGS")
        logs_group.setStyleSheet("""
            QGroupBox {
                color: #6b7280;
                font-weight: bold;
                border: 1px solid #6b7280;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        logs_layout = QVBoxLayout(logs_group)
        
        self.logs_text = QTextEdit()
        self.logs_text.setFont(QFont("Consolas", 9))
        self.logs_text.setStyleSheet("""
            QTextEdit {
                background: #111827;
                color: #e5e7eb;
                border: 1px solid #4b5563;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        self.logs_text.setMaximumHeight(120)
        logs_layout.addWidget(self.logs_text)
        
        layout.addWidget(logs_group)

        return panel

    def create_controls_panel(self):
        """Create controls panel"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Box)
        panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e1b4b, stop:1 #312e81);
                border: 2px solid #8b5cf6;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        layout = QVBoxLayout(panel)

        title = QLabel("TRADING CONTROLS")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #8b5cf6; margin-bottom: 15px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Trading buttons
        buttons_layout = QHBoxLayout()

        self.call_button = QPushButton("CALL")
        self.call_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.call_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ef4444, stop:1 #dc2626);
            }
        """)
        self.call_button.clicked.connect(lambda: self.place_trade('CALL'))

        self.put_button = QPushButton("PUT")
        self.put_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.put_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
            }
        """)
        self.put_button.clicked.connect(lambda: self.place_trade('PUT'))

        buttons_layout.addWidget(self.call_button)
        buttons_layout.addWidget(self.put_button)
        layout.addLayout(buttons_layout)

        # Quick actions
        actions_group = QGroupBox("QUICK ACTIONS")
        actions_group.setStyleSheet("""
            QGroupBox {
                color: #ea580c;
                font-weight: bold;
                border: 1px solid #ea580c;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        actions_layout = QVBoxLayout(actions_group)

        self.extension_btn = QPushButton("Extension Manager")
        self.quotex_btn = QPushButton("Open Quotex")
        self.emergency_btn = QPushButton("Emergency Stop")

        button_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #374151, stop:1 #4b5563);
                color: white;
                border: 1px solid #6b7280;
                padding: 8px;
                border-radius: 8px;
                font-weight: bold;
                margin: 2px 0;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4b5563, stop:1 #6b7280);
            }
        """

        for btn in [self.extension_btn, self.quotex_btn, self.emergency_btn]:
            btn.setStyleSheet(button_style)
            btn.setFont(QFont("Arial", 10, QFont.Bold))
            actions_layout.addWidget(btn)

        # Connect signals
        self.extension_btn.clicked.connect(self.launch_extension_manager)
        self.quotex_btn.clicked.connect(self.open_quotex)
        self.emergency_btn.clicked.connect(self.emergency_stop)

        layout.addWidget(actions_group)

        # Chrome extension status
        extension_group = QGroupBox("CHROME EXTENSION")
        extension_group.setStyleSheet("""
            QGroupBox {
                color: #a855f7;
                font-weight: bold;
                border: 1px solid #a855f7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        extension_layout = QVBoxLayout(extension_group)

        self.extension_status_label = QLabel("Not Connected")
        self.extension_status_label.setFont(QFont("Consolas", 11, QFont.Bold))
        self.extension_status_label.setStyleSheet("color: #fbbf24; margin: 5px 0;")
        self.extension_status_label.setAlignment(Qt.AlignCenter)
        extension_layout.addWidget(self.extension_status_label)

        instructions = QLabel("1. Open Chrome\n2. Go to chrome://extensions/\n3. Enable Developer mode\n4. Load unpacked: chrome_extension\n5. Go to qxbroker.com\n6. Start Extraction")
        instructions.setFont(QFont("Consolas", 8))
        instructions.setStyleSheet("color: #e2e8f0; margin: 5px 0;")
        instructions.setWordWrap(True)
        extension_layout.addWidget(instructions)

        layout.addWidget(extension_group)
        layout.addStretch()

        return panel
