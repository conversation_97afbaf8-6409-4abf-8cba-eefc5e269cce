"""
VIP BIG BANG Enterprise - Risk Manager
Advanced risk management system for capital protection
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from collections import deque
import threading
import math

class RiskManager:
    """
    Enterprise-level risk management system
    Protects capital through advanced risk controls
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("RiskManager")
        
        # Risk parameters
        self.max_trade_amount = settings.risk_management.max_trade_amount
        self.min_trade_amount = settings.risk_management.min_trade_amount
        self.daily_loss_limit = settings.risk_management.daily_loss_limit
        self.max_consecutive_losses = settings.risk_management.max_consecutive_losses
        self.risk_per_trade = settings.risk_management.risk_per_trade / 100  # Convert to decimal
        
        # Current risk state
        self.current_balance = 1000.0  # Starting balance
        self.daily_pnl = 0.0
        self.consecutive_losses = 0
        self.consecutive_wins = 0
        self.current_drawdown = 0.0
        self.peak_balance = 1000.0
        
        # Trade tracking
        self.trade_history = deque(maxlen=1000)
        self.daily_trades = deque(maxlen=100)
        self.hourly_trades = deque(maxlen=50)
        
        # Risk metrics
        self.risk_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'average_win': 0.0,
            'average_loss': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'recovery_factor': 0.0
        }
        
        # Risk alerts
        self.risk_alerts = deque(maxlen=100)
        
        # Thread safety
        self.lock = threading.Lock()
        
        self.logger.info("Risk Manager initialized")
    
    def evaluate_trade_risk(self, signal: Dict, proposed_amount: float) -> Dict:
        """
        Evaluate risk for a proposed trade
        Returns risk assessment and recommended amount
        """
        try:
            with self.lock:
                # Basic risk checks
                risk_checks = self._perform_risk_checks(proposed_amount)
                
                if not risk_checks['allowed']:
                    return {
                        'approved': False,
                        'reason': risk_checks['reason'],
                        'recommended_amount': 0,
                        'risk_level': 'HIGH'
                    }
                
                # Calculate optimal trade amount
                optimal_amount = self._calculate_optimal_amount(signal, proposed_amount)
                
                # Assess risk level
                risk_level = self._assess_risk_level(optimal_amount, signal)
                
                # Position sizing based on Kelly Criterion
                kelly_amount = self._kelly_criterion_sizing(signal)
                
                # Final amount (conservative approach)
                final_amount = min(optimal_amount, kelly_amount, proposed_amount)
                final_amount = max(self.min_trade_amount, min(self.max_trade_amount, final_amount))
                
                return {
                    'approved': True,
                    'recommended_amount': final_amount,
                    'original_amount': proposed_amount,
                    'optimal_amount': optimal_amount,
                    'kelly_amount': kelly_amount,
                    'risk_level': risk_level,
                    'risk_percentage': (final_amount / self.current_balance) * 100,
                    'expected_loss': final_amount,
                    'expected_profit': final_amount * 0.8,  # 80% payout
                    'risk_reward_ratio': 0.8,
                    'confidence_adjustment': self._get_confidence_adjustment(signal)
                }
                
        except Exception as e:
            self.logger.error(f"Risk evaluation error: {e}")
            return {
                'approved': False,
                'reason': f'Risk evaluation failed: {str(e)}',
                'recommended_amount': 0,
                'risk_level': 'HIGH'
            }
    
    def _perform_risk_checks(self, amount: float) -> Dict:
        """Perform basic risk checks"""
        # Check daily loss limit
        if abs(self.daily_pnl) >= self.daily_loss_limit:
            return {'allowed': False, 'reason': 'Daily loss limit reached'}
        
        # Check consecutive losses
        if self.consecutive_losses >= self.max_consecutive_losses:
            return {'allowed': False, 'reason': 'Maximum consecutive losses reached'}
        
        # Check minimum balance
        if self.current_balance < self.min_trade_amount * 10:
            return {'allowed': False, 'reason': 'Insufficient balance for safe trading'}
        
        # Check amount limits
        if amount < self.min_trade_amount:
            return {'allowed': False, 'reason': 'Amount below minimum trade size'}
        
        if amount > self.max_trade_amount:
            return {'allowed': False, 'reason': 'Amount exceeds maximum trade size'}
        
        # Check if amount exceeds risk per trade
        max_risk_amount = self.current_balance * self.risk_per_trade
        if amount > max_risk_amount:
            return {'allowed': False, 'reason': f'Amount exceeds risk per trade limit ({self.risk_per_trade*100}%)'}
        
        # Check daily trade frequency
        today_trades = self._count_today_trades()
        if today_trades >= self.settings.trading.max_daily_trades:
            return {'allowed': False, 'reason': 'Daily trade limit reached'}
        
        # Check hourly trade frequency
        hour_trades = self._count_hour_trades()
        if hour_trades >= self.settings.trading.max_trades_per_hour:
            return {'allowed': False, 'reason': 'Hourly trade limit reached'}
        
        return {'allowed': True, 'reason': 'All risk checks passed'}
    
    def _calculate_optimal_amount(self, signal: Dict, proposed_amount: float) -> float:
        """Calculate optimal trade amount based on signal strength and market conditions"""
        base_amount = self.current_balance * self.risk_per_trade
        
        # Adjust based on signal confidence
        confidence = signal.get('confidence', 0.5)
        confidence_multiplier = 0.5 + (confidence * 1.0)  # 0.5x to 1.5x based on confidence
        
        # Adjust based on recent performance
        performance_multiplier = self._get_performance_multiplier()
        
        # Adjust based on market volatility (if available)
        volatility_multiplier = self._get_volatility_multiplier()
        
        # Adjust based on time of day (if applicable)
        time_multiplier = self._get_time_multiplier()
        
        optimal_amount = base_amount * confidence_multiplier * performance_multiplier * volatility_multiplier * time_multiplier
        
        return round(optimal_amount, 2)
    
    def _kelly_criterion_sizing(self, signal: Dict) -> float:
        """Calculate position size using Kelly Criterion"""
        if self.risk_metrics['total_trades'] < 10:
            # Not enough data for Kelly, use conservative sizing
            return self.current_balance * 0.01  # 1% of balance
        
        win_rate = self.risk_metrics['win_rate']
        avg_win = self.risk_metrics['average_win']
        avg_loss = self.risk_metrics['average_loss']
        
        if avg_loss == 0 or win_rate == 0:
            return self.current_balance * 0.01
        
        # Kelly formula: f = (bp - q) / b
        # where b = odds received (avg_win/avg_loss), p = win_rate, q = loss_rate
        b = avg_win / avg_loss
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b
        
        # Apply safety factor (use 25% of Kelly)
        safe_kelly = kelly_fraction * 0.25
        
        # Ensure it's positive and reasonable
        safe_kelly = max(0.005, min(0.05, safe_kelly))  # Between 0.5% and 5%
        
        kelly_amount = self.current_balance * safe_kelly
        
        return round(kelly_amount, 2)
    
    def _assess_risk_level(self, amount: float, signal: Dict) -> str:
        """Assess overall risk level for the trade"""
        risk_factors = []
        
        # Amount risk
        amount_risk = amount / self.current_balance
        if amount_risk > 0.05:
            risk_factors.append('HIGH_AMOUNT')
        elif amount_risk > 0.02:
            risk_factors.append('MEDIUM_AMOUNT')
        
        # Consecutive losses risk
        if self.consecutive_losses > 2:
            risk_factors.append('CONSECUTIVE_LOSSES')
        
        # Drawdown risk
        if self.current_drawdown > 0.1:  # 10% drawdown
            risk_factors.append('HIGH_DRAWDOWN')
        
        # Signal confidence risk
        confidence = signal.get('confidence', 0.5)
        if confidence < 0.7:
            risk_factors.append('LOW_CONFIDENCE')
        
        # Daily PnL risk
        if abs(self.daily_pnl) > self.daily_loss_limit * 0.7:
            risk_factors.append('HIGH_DAILY_LOSS')
        
        # Determine overall risk level
        if len(risk_factors) >= 3:
            return 'HIGH'
        elif len(risk_factors) >= 1:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _get_performance_multiplier(self) -> float:
        """Get performance-based multiplier"""
        if self.risk_metrics['total_trades'] < 5:
            return 1.0  # Neutral for insufficient data
        
        win_rate = self.risk_metrics['win_rate']
        
        # Reduce size after losses, increase after wins (but conservatively)
        if self.consecutive_losses > 0:
            return max(0.5, 1.0 - (self.consecutive_losses * 0.1))
        elif self.consecutive_wins > 2:
            return min(1.3, 1.0 + (self.consecutive_wins * 0.05))
        elif win_rate > 0.7:
            return 1.2
        elif win_rate < 0.4:
            return 0.8
        else:
            return 1.0
    
    def _get_volatility_multiplier(self) -> float:
        """Get volatility-based multiplier"""
        # This would analyze recent price volatility
        # For now, return neutral
        return 1.0
    
    def _get_time_multiplier(self) -> float:
        """Get time-based multiplier"""
        # Reduce size during high-volatility periods (market open/close)
        current_hour = datetime.now().hour
        
        # Assuming UTC time, adjust for market hours
        if 13 <= current_hour <= 15:  # European market open
            return 0.9
        elif 8 <= current_hour <= 10:  # US market open
            return 0.9
        else:
            return 1.0
    
    def _get_confidence_adjustment(self, signal: Dict) -> float:
        """Get confidence-based adjustment factor"""
        confidence = signal.get('confidence', 0.5)
        agreement = signal.get('agreement', 0.5)
        
        # Combine confidence and agreement
        combined_confidence = (confidence + agreement) / 2
        
        # Return adjustment factor
        return combined_confidence
    
    def record_trade_result(self, trade_result: Dict):
        """Record trade result and update risk metrics"""
        try:
            with self.lock:
                # Extract trade data
                amount = trade_result.get('amount', 0)
                profit = trade_result.get('profit', 0)
                is_win = profit > 0
                
                # Update balance
                self.current_balance += profit
                self.daily_pnl += profit
                
                # Update peak balance and drawdown
                if self.current_balance > self.peak_balance:
                    self.peak_balance = self.current_balance
                
                self.current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
                
                # Update consecutive counters
                if is_win:
                    self.consecutive_wins += 1
                    self.consecutive_losses = 0
                else:
                    self.consecutive_losses += 1
                    self.consecutive_wins = 0
                
                # Add to trade history
                trade_record = {
                    'timestamp': datetime.now(),
                    'amount': amount,
                    'profit': profit,
                    'is_win': is_win,
                    'balance_after': self.current_balance,
                    'drawdown': self.current_drawdown
                }
                
                self.trade_history.append(trade_record)
                self.daily_trades.append(trade_record)
                self.hourly_trades.append(trade_record)
                
                # Update risk metrics
                self._update_risk_metrics()
                
                # Check for risk alerts
                self._check_risk_alerts()
                
                self.logger.info(f"Trade recorded: ${profit:.2f} profit, Balance: ${self.current_balance:.2f}")
                
        except Exception as e:
            self.logger.error(f"Trade recording error: {e}")
    
    def _update_risk_metrics(self):
        """Update comprehensive risk metrics"""
        if not self.trade_history:
            return
        
        trades = list(self.trade_history)
        
        # Basic metrics
        self.risk_metrics['total_trades'] = len(trades)
        winning_trades = [t for t in trades if t['is_win']]
        losing_trades = [t for t in trades if not t['is_win']]
        
        self.risk_metrics['winning_trades'] = len(winning_trades)
        self.risk_metrics['losing_trades'] = len(losing_trades)
        
        if len(trades) > 0:
            self.risk_metrics['win_rate'] = len(winning_trades) / len(trades)
        
        # Average win/loss
        if winning_trades:
            self.risk_metrics['average_win'] = sum(t['profit'] for t in winning_trades) / len(winning_trades)
        
        if losing_trades:
            self.risk_metrics['average_loss'] = abs(sum(t['profit'] for t in losing_trades) / len(losing_trades))
        
        # Profit factor
        total_wins = sum(t['profit'] for t in winning_trades)
        total_losses = abs(sum(t['profit'] for t in losing_trades))
        
        if total_losses > 0:
            self.risk_metrics['profit_factor'] = total_wins / total_losses
        
        # Maximum drawdown
        self.risk_metrics['max_drawdown'] = max(t['drawdown'] for t in trades)
        
        # Recovery factor
        if self.risk_metrics['max_drawdown'] > 0:
            total_profit = sum(t['profit'] for t in trades)
            self.risk_metrics['recovery_factor'] = total_profit / (self.risk_metrics['max_drawdown'] * 1000)  # Assuming $1000 starting balance
    
    def _check_risk_alerts(self):
        """Check for risk alert conditions"""
        alerts = []
        
        # High drawdown alert
        if self.current_drawdown > 0.15:  # 15% drawdown
            alerts.append({
                'type': 'HIGH_DRAWDOWN',
                'message': f'High drawdown detected: {self.current_drawdown*100:.1f}%',
                'severity': 'HIGH',
                'timestamp': datetime.now()
            })
        
        # Consecutive losses alert
        if self.consecutive_losses >= 3:
            alerts.append({
                'type': 'CONSECUTIVE_LOSSES',
                'message': f'Consecutive losses: {self.consecutive_losses}',
                'severity': 'MEDIUM',
                'timestamp': datetime.now()
            })
        
        # Daily loss alert
        if abs(self.daily_pnl) > self.daily_loss_limit * 0.8:
            alerts.append({
                'type': 'DAILY_LOSS_WARNING',
                'message': f'Approaching daily loss limit: ${abs(self.daily_pnl):.2f}',
                'severity': 'HIGH',
                'timestamp': datetime.now()
            })
        
        # Add alerts to queue
        for alert in alerts:
            self.risk_alerts.append(alert)
            self.logger.warning(f"Risk Alert: {alert['message']}")
    
    def _count_today_trades(self) -> int:
        """Count trades made today"""
        today = datetime.now().date()
        return sum(1 for trade in self.daily_trades 
                  if trade['timestamp'].date() == today)
    
    def _count_hour_trades(self) -> int:
        """Count trades made in the last hour"""
        one_hour_ago = datetime.now() - timedelta(hours=1)
        return sum(1 for trade in self.hourly_trades 
                  if trade['timestamp'] > one_hour_ago)
    
    def get_risk_status(self) -> Dict:
        """Get current risk status"""
        with self.lock:
            return {
                'current_balance': self.current_balance,
                'daily_pnl': self.daily_pnl,
                'current_drawdown': self.current_drawdown,
                'consecutive_losses': self.consecutive_losses,
                'consecutive_wins': self.consecutive_wins,
                'risk_metrics': self.risk_metrics.copy(),
                'recent_alerts': list(self.risk_alerts)[-5:],  # Last 5 alerts
                'trading_allowed': self._is_trading_allowed()
            }
    
    def _is_trading_allowed(self) -> bool:
        """Check if trading is currently allowed"""
        checks = self._perform_risk_checks(self.min_trade_amount)
        return checks['allowed']
    
    def reset_daily_metrics(self):
        """Reset daily metrics (called at start of new day)"""
        with self.lock:
            self.daily_pnl = 0.0
            self.daily_trades.clear()
            self.logger.info("Daily risk metrics reset")
