"""
VIP BIG BANG Enterprise - Adaptive Decision System
سیستم تصمیم‌گیری انطباقی و کاملاً قابل تنظیم
"""

from datetime import datetime
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s: %(message)s', datefmt='%H:%M:%S')

class AdaptiveDecisionSystem:
    """سیستم تصمیم‌گیری انطباقی"""
    
    def __init__(self):
        self.logger = logging.getLogger("AdaptiveDecision")
        
        # پروفایل‌های مختلف تصمیم‌گیری
        self.decision_profiles = {
            'conservative': {
                'name': 'محافظه‌کار',
                'min_signal_strength': 0.95,
                'min_alignment': 0.90,
                'required_confirmations': 8,
                'max_daily_trades': 5,
                'cooldown_minutes': 60,
                'win_rate_target': 0.95
            },
            'balanced': {
                'name': 'متعادل',
                'min_signal_strength': 0.85,
                'min_alignment': 0.80,
                'required_confirmations': 6,
                'max_daily_trades': 15,
                'cooldown_minutes': 30,
                'win_rate_target': 0.85
            },
            'aggressive': {
                'name': 'تهاجمی',
                'min_signal_strength': 0.75,
                'min_alignment': 0.70,
                'required_confirmations': 4,
                'max_daily_trades': 30,
                'cooldown_minutes': 15,
                'win_rate_target': 0.75
            },
            'scalping': {
                'name': 'اسکالپینگ',
                'min_signal_strength': 0.70,
                'min_alignment': 0.65,
                'required_confirmations': 3,
                'max_daily_trades': 50,
                'cooldown_minutes': 5,
                'win_rate_target': 0.70
            }
        }
        
        # تنظیمات بر اساس تایم‌فریم
        self.timeframe_configs = {
            '15s': {
                'analysis_period': 15,
                'trade_duration': 1,  # 1 minute
                'lookback_candles': 20,
                'volatility_threshold': 0.001,
                'volume_multiplier': 1.5,
                'trend_sensitivity': 'high'
            },
            '30s': {
                'analysis_period': 30,
                'trade_duration': 2,  # 2 minutes
                'lookback_candles': 30,
                'volatility_threshold': 0.002,
                'volume_multiplier': 1.3,
                'trend_sensitivity': 'high'
            },
            '1m': {
                'analysis_period': 60,
                'trade_duration': 5,  # 5 minutes
                'lookback_candles': 50,
                'volatility_threshold': 0.003,
                'volume_multiplier': 1.2,
                'trend_sensitivity': 'medium'
            },
            '5m': {
                'analysis_period': 300,
                'trade_duration': 15,  # 15 minutes
                'lookback_candles': 100,
                'volatility_threshold': 0.005,
                'volume_multiplier': 1.1,
                'trend_sensitivity': 'medium'
            },
            '15m': {
                'analysis_period': 900,
                'trade_duration': 60,  # 1 hour
                'lookback_candles': 200,
                'volatility_threshold': 0.008,
                'volume_multiplier': 1.0,
                'trend_sensitivity': 'low'
            }
        }
        
        # تنظیمات فعلی
        self.current_profile = 'balanced'
        self.current_timeframe = '1m'
        self.custom_settings = {}
        
    def set_decision_profile(self, profile_name):
        """تنظیم پروفایل تصمیم‌گیری"""
        if profile_name in self.decision_profiles:
            self.current_profile = profile_name
            self.logger.info(f"پروفایل تصمیم‌گیری تغییر کرد: {self.decision_profiles[profile_name]['name']}")
            return True
        return False
    
    def set_timeframe(self, timeframe):
        """تنظیم تایم‌فریم"""
        if timeframe in self.timeframe_configs:
            self.current_timeframe = timeframe
            self.logger.info(f"تایم‌فریم تغییر کرد: {timeframe}")
            return True
        return False
    
    def create_custom_profile(self, name, settings):
        """ایجاد پروفایل سفارشی"""
        self.decision_profiles[name] = {
            'name': settings.get('name', name),
            'min_signal_strength': settings.get('min_signal_strength', 0.80),
            'min_alignment': settings.get('min_alignment', 0.75),
            'required_confirmations': settings.get('required_confirmations', 5),
            'max_daily_trades': settings.get('max_daily_trades', 20),
            'cooldown_minutes': settings.get('cooldown_minutes', 30),
            'win_rate_target': settings.get('win_rate_target', 0.80)
        }
        self.logger.info(f"پروفایل سفارشی ایجاد شد: {name}")
    
    def get_adaptive_settings(self):
        """دریافت تنظیمات انطباقی فعلی"""
        profile = self.decision_profiles[self.current_profile]
        timeframe = self.timeframe_configs[self.current_timeframe]
        
        # ترکیب تنظیمات
        adaptive_settings = {
            **profile,
            **timeframe,
            'profile_name': self.current_profile,
            'timeframe': self.current_timeframe
        }
        
        # اعمال تنظیمات سفارشی
        adaptive_settings.update(self.custom_settings)
        
        return adaptive_settings
    
    def auto_adjust_for_timeframe(self, timeframe, trade_duration_minutes):
        """تنظیم خودکار بر اساس تایم‌فریم و مدت ترید"""
        
        print(f"🔄 تنظیم خودکار برای {timeframe} و ترید {trade_duration_minutes} دقیقه‌ای")
        
        # تنظیمات خودکار بر اساس نسبت تایم‌فریم به مدت ترید
        if timeframe == '15s':
            if trade_duration_minutes <= 1:
                suggested_profile = 'scalping'
            elif trade_duration_minutes <= 5:
                suggested_profile = 'aggressive'
            else:
                suggested_profile = 'balanced'
        
        elif timeframe == '30s':
            if trade_duration_minutes <= 2:
                suggested_profile = 'aggressive'
            elif trade_duration_minutes <= 10:
                suggested_profile = 'balanced'
            else:
                suggested_profile = 'conservative'
        
        elif timeframe == '1m':
            if trade_duration_minutes <= 5:
                suggested_profile = 'balanced'
            elif trade_duration_minutes <= 15:
                suggested_profile = 'conservative'
            else:
                suggested_profile = 'conservative'
        
        elif timeframe == '5m':
            if trade_duration_minutes <= 15:
                suggested_profile = 'balanced'
            else:
                suggested_profile = 'conservative'
        
        else:  # 15m+
            suggested_profile = 'conservative'
        
        # اعمال تنظیمات پیشنهادی
        self.set_decision_profile(suggested_profile)
        self.set_timeframe(timeframe)
        
        # تنظیمات اضافی بر اساس مدت ترید
        ratio = trade_duration_minutes / int(timeframe.replace('s', '').replace('m', ''))
        
        if timeframe.endswith('s'):
            ratio = trade_duration_minutes / (int(timeframe.replace('s', '')) / 60)
        
        # تنظیم دینامیک پارامترها
        if ratio < 5:  # ترید کوتاه‌مدت
            self.custom_settings.update({
                'min_signal_strength': min(0.95, self.decision_profiles[suggested_profile]['min_signal_strength'] + 0.05),
                'required_confirmations': self.decision_profiles[suggested_profile]['required_confirmations'] + 1,
                'cooldown_minutes': max(5, self.decision_profiles[suggested_profile]['cooldown_minutes'] // 2)
            })
        elif ratio > 20:  # ترید بلندمدت
            self.custom_settings.update({
                'min_signal_strength': max(0.70, self.decision_profiles[suggested_profile]['min_signal_strength'] - 0.05),
                'required_confirmations': max(3, self.decision_profiles[suggested_profile]['required_confirmations'] - 1),
                'cooldown_minutes': self.decision_profiles[suggested_profile]['cooldown_minutes'] * 2
            })
        
        return self.get_adaptive_settings()
    
    def validate_signal_adaptive(self, primary_results, complementary_results, market_data=None):
        """اعتبارسنجی انطباقی سیگنال"""
        
        settings = self.get_adaptive_settings()
        
        print(f"\n🎯 اعتبارسنجی انطباقی - پروفایل: {settings['name']}")
        print(f"📊 تایم‌فریم: {settings['timeframe']} | مدت ترید: {settings['trade_duration']} دقیقه")
        print("-" * 60)
        
        validation_score = 0
        max_score = 100
        details = []
        
        # 1. بررسی امتیاز کلی (25 امتیاز)
        scores = [r.get('score', 0) for r in primary_results.values() if isinstance(r, dict)]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        if avg_score >= settings['min_signal_strength']:
            score_points = 25
            validation_score += score_points
            details.append(f"✅ امتیاز کلی: {avg_score:.1%} ({score_points}/25)")
        else:
            score_points = (avg_score / settings['min_signal_strength']) * 25
            validation_score += score_points
            details.append(f"⚠️ امتیاز کلی: {avg_score:.1%} ({score_points:.0f}/25)")
        
        # 2. بررسی هم‌راستایی (20 امتیاز)
        directions = [r.get('direction') for r in primary_results.values() if isinstance(r, dict)]
        if directions:
            main_direction = max(set(directions), key=directions.count)
            alignment = directions.count(main_direction) / len(directions)
            
            if alignment >= settings['min_alignment']:
                align_points = 20
                validation_score += align_points
                details.append(f"✅ هم‌راستایی: {alignment:.1%} ({align_points}/20)")
            else:
                align_points = (alignment / settings['min_alignment']) * 20
                validation_score += align_points
                details.append(f"⚠️ هم‌راستایی: {alignment:.1%} ({align_points:.0f}/20)")
        
        # 3. بررسی تأیید چندگانه (25 امتیاز)
        confirmations = self.count_adaptive_confirmations(primary_results, settings)
        if confirmations >= settings['required_confirmations']:
            conf_points = 25
            validation_score += conf_points
            details.append(f"✅ تأیید: {confirmations}/{settings['required_confirmations']} ({conf_points}/25)")
        else:
            conf_points = (confirmations / settings['required_confirmations']) * 25
            validation_score += conf_points
            details.append(f"⚠️ تأیید: {confirmations}/{settings['required_confirmations']} ({conf_points:.0f}/25)")
        
        # 4. بررسی شرایط تایم‌فریم (15 امتیاز)
        timeframe_score = self.evaluate_timeframe_conditions(market_data, settings)
        validation_score += timeframe_score
        details.append(f"{'✅' if timeframe_score >= 12 else '⚠️'} شرایط تایم‌فریم: ({timeframe_score:.0f}/15)")
        
        # 5. بررسی فیلترهای مکمل (15 امتیاز)
        filter_score = self.evaluate_adaptive_filters(complementary_results, settings)
        validation_score += filter_score
        details.append(f"{'✅' if filter_score >= 12 else '⚠️'} فیلترها: ({filter_score:.0f}/15)")
        
        # نمایش جزئیات
        for detail in details:
            print(f"   {detail}")
        
        # نتیجه نهایی
        final_percentage = validation_score
        print(f"\n📊 امتیاز نهایی: {validation_score:.0f}/100 ({final_percentage:.1f}%)")
        
        # تصمیم‌گیری بر اساس هدف وین ریت
        target_threshold = settings['win_rate_target'] * 100
        
        if final_percentage >= target_threshold:
            decision = "APPROVED"
            allow_trading = True
            win_probability = settings['win_rate_target']
        else:
            decision = "REJECTED"
            allow_trading = False
            win_probability = final_percentage / 100
        
        print(f"🎯 هدف وین ریت: {settings['win_rate_target']:.1%}")
        print(f"🚦 تصمیم: {decision}")
        
        return {
            'decision': decision,
            'allow_trading': allow_trading,
            'score': final_percentage,
            'win_probability': win_probability,
            'settings_used': settings,
            'details': details
        }
    
    def count_adaptive_confirmations(self, primary_results, settings):
        """شمارش تأیید‌های انطباقی"""
        confirmations = 0
        
        # تنظیم وزن‌ها بر اساس تایم‌فریم
        if settings['timeframe'] in ['15s', '30s']:
            # تایم‌فریم‌های کوتاه - تأکید بر momentum و volume
            priority_indicators = ['momentum', 'volume_per_candle', 'vortex']
        elif settings['timeframe'] in ['1m', '5m']:
            # تایم‌فریم‌های متوسط - تعادل
            priority_indicators = ['ma6', 'trend_analyzer', 'strong_level']
        else:
            # تایم‌فریم‌های بلند - تأکید بر trend
            priority_indicators = ['trend_analyzer', 'ma6', 'strong_level']
        
        # شمارش تأیید‌ها با وزن
        for indicator, result in primary_results.items():
            if isinstance(result, dict):
                score = result.get('score', 0)
                direction = result.get('direction', 'NEUTRAL')
                
                if direction in ['UP', 'DOWN'] and score >= 0.7:
                    if indicator in priority_indicators:
                        confirmations += 1.5  # وزن بیشتر
                    else:
                        confirmations += 1.0  # وزن عادی
        
        return int(confirmations)
    
    def evaluate_timeframe_conditions(self, market_data, settings):
        """ارزیابی شرایط مخصوص تایم‌فریم"""
        if not market_data:
            return 10  # امتیاز متوسط
        
        score = 0
        
        # شبیه‌سازی ارزیابی
        if settings['timeframe'] in ['15s', '30s']:
            # تایم‌فریم کوتاه - بررسی نوسانات
            score += 8  # فرض نوسان مناسب
        elif settings['timeframe'] in ['1m', '5m']:
            # تایم‌فریم متوسط - بررسی ترند
            score += 12  # فرض ترند مناسب
        else:
            # تایم‌فریم بلند - بررسی استحکام
            score += 15  # فرض شرایط عالی
        
        return min(score, 15)
    
    def evaluate_adaptive_filters(self, complementary_results, settings):
        """ارزیابی فیلترهای انطباقی"""
        score = 0
        
        # فیلترهای کلیدی بر اساس پروفایل
        if settings['profile_name'] == 'conservative':
            key_filters = ['economic_news_filter', 'account_safety', 'otc_mode_detector']
            required_score = 0.9
        elif settings['profile_name'] == 'scalping':
            key_filters = ['live_signal_scanner', 'otc_mode_detector']
            required_score = 0.7
        else:
            key_filters = ['economic_news_filter', 'account_safety', 'live_signal_scanner']
            required_score = 0.8
        
        for filter_name in key_filters:
            if filter_name in complementary_results:
                result = complementary_results[filter_name]
                if isinstance(result, dict):
                    if result.get('allow_trading', True) and result.get('score', 0) >= required_score:
                        score += 15 / len(key_filters)
        
        return score
    
    def save_configuration(self, filename):
        """ذخیره تنظیمات"""
        config = {
            'current_profile': self.current_profile,
            'current_timeframe': self.current_timeframe,
            'custom_settings': self.custom_settings,
            'decision_profiles': self.decision_profiles,
            'timeframe_configs': self.timeframe_configs
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تنظیمات در {filename} ذخیره شد")
    
    def load_configuration(self, filename):
        """بارگذاری تنظیمات"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.current_profile = config.get('current_profile', 'balanced')
            self.current_timeframe = config.get('current_timeframe', '1m')
            self.custom_settings = config.get('custom_settings', {})
            
            if 'decision_profiles' in config:
                self.decision_profiles.update(config['decision_profiles'])
            
            if 'timeframe_configs' in config:
                self.timeframe_configs.update(config['timeframe_configs'])
            
            print(f"✅ تنظیمات از {filename} بارگذاری شد")
            return True
            
        except Exception as e:
            print(f"❌ خطا در بارگذاری: {e}")
            return False

def demo_adaptive_system():
    """نمایش سیستم انطباقی"""
    
    print("🚀 VIP BIG BANG - سیستم تصمیم‌گیری انطباقی")
    print("=" * 60)
    
    system = AdaptiveDecisionSystem()
    
    # نمایش پروفایل‌های موجود
    print("📋 پروفایل‌های تصمیم‌گیری موجود:")
    for key, profile in system.decision_profiles.items():
        print(f"   {key}: {profile['name']} (هدف: {profile['win_rate_target']:.0%})")
    
    print(f"\n⏰ تایم‌فریم‌های پشتیبانی شده:")
    for tf in system.timeframe_configs.keys():
        print(f"   {tf}")
    
    # تست تنظیم خودکار
    print(f"\n🔄 تست تنظیم خودکار:")
    
    scenarios = [
        ('15s', 1, 'اسکالپینگ سریع'),
        ('30s', 5, 'ترید کوتاه‌مدت'),
        ('1m', 15, 'ترید متوسط‌مدت'),
        ('5m', 60, 'ترید بلندمدت')
    ]
    
    for timeframe, trade_duration, description in scenarios:
        print(f"\n📊 سناریو: {description}")
        print(f"   تایم‌فریم: {timeframe} | مدت ترید: {trade_duration} دقیقه")
        
        settings = system.auto_adjust_for_timeframe(timeframe, trade_duration)
        
        print(f"   پروفایل انتخابی: {settings['name']}")
        print(f"   حداقل امتیاز: {settings['min_signal_strength']:.1%}")
        print(f"   تأیید لازم: {settings['required_confirmations']}")
        print(f"   حداکثر ترید/روز: {settings['max_daily_trades']}")
    
    # ذخیره تنظیمات
    system.save_configuration('adaptive_config.json')

def main():
    """تست اصلی"""
    demo_adaptive_system()

if __name__ == "__main__":
    main()
