"""
VIP BIG BANG Enterprise - Trap Candle Analyzer
Advanced candlestick pattern recognition for trap detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import logging
from datetime import datetime

class TrapCandleAnalyzer:
    """
    Enterprise-level trap candle detection
    Identifies false breakouts and reversal patterns
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("TrapCandleAnalyzer")
        
        # Trap detection parameters
        self.lookback_period = 20
        self.wick_ratio_threshold = 0.6  # Wick should be 60% of total candle
        self.body_ratio_threshold = 0.3   # Body should be less than 30%
        self.volume_spike_threshold = 1.5  # Volume spike multiplier
        
        # Pattern recognition parameters
        self.min_pattern_strength = 0.7
        self.confirmation_candles = 2
        
        self.logger.debug("Trap Candle Analyzer initialized")
    
    def calculate_candle_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate comprehensive candle metrics"""
        df = data.copy()
        
        # Basic OHLC
        df['open'] = df.get('open', df.get('price', 0))
        df['high'] = df.get('high', df.get('price', 0))
        df['low'] = df.get('low', df.get('price', 0))
        df['close'] = df.get('close', df.get('price', 0))
        
        # Candle body and wicks
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_wick'] = df['high'] - df[['open', 'close']].max(axis=1)
        df['lower_wick'] = df[['open', 'close']].min(axis=1) - df['low']
        df['total_range'] = df['high'] - df['low']
        
        # Ratios
        df['body_ratio'] = df['body_size'] / df['total_range'].replace(0, 1)
        df['upper_wick_ratio'] = df['upper_wick'] / df['total_range'].replace(0, 1)
        df['lower_wick_ratio'] = df['lower_wick'] / df['total_range'].replace(0, 1)
        
        # Candle direction
        df['is_bullish'] = df['close'] > df['open']
        df['is_bearish'] = df['close'] < df['open']
        df['is_doji'] = abs(df['close'] - df['open']) < (df['total_range'] * 0.1)
        
        return df
    
    def detect_hammer_patterns(self, df: pd.DataFrame) -> Dict:
        """Detect hammer and hanging man patterns"""
        if len(df) < 3:
            return {'pattern': 'NONE', 'strength': 0}
        
        current = df.iloc[-1]
        
        # Hammer/Hanging Man criteria
        is_hammer_shape = (
            current['lower_wick_ratio'] > 0.6 and  # Long lower wick
            current['upper_wick_ratio'] < 0.1 and  # Short upper wick
            current['body_ratio'] < 0.3             # Small body
        )
        
        if not is_hammer_shape:
            return {'pattern': 'NONE', 'strength': 0}
        
        # Determine if it's bullish or bearish based on context
        prev_trend = self.determine_trend(df.iloc[-5:-1])
        
        pattern = 'NONE'
        strength = current['lower_wick_ratio']
        
        if prev_trend == 'DOWN' and current['is_bullish']:
            pattern = 'BULLISH_HAMMER'
        elif prev_trend == 'UP' and current['is_bearish']:
            pattern = 'BEARISH_HANGING_MAN'
        
        return {
            'pattern': pattern,
            'strength': strength,
            'wick_ratio': current['lower_wick_ratio'],
            'body_ratio': current['body_ratio']
        }
    
    def detect_shooting_star_patterns(self, df: pd.DataFrame) -> Dict:
        """Detect shooting star and inverted hammer patterns"""
        if len(df) < 3:
            return {'pattern': 'NONE', 'strength': 0}
        
        current = df.iloc[-1]
        
        # Shooting Star criteria
        is_shooting_star_shape = (
            current['upper_wick_ratio'] > 0.6 and  # Long upper wick
            current['lower_wick_ratio'] < 0.1 and  # Short lower wick
            current['body_ratio'] < 0.3             # Small body
        )
        
        if not is_shooting_star_shape:
            return {'pattern': 'NONE', 'strength': 0}
        
        prev_trend = self.determine_trend(df.iloc[-5:-1])
        
        pattern = 'NONE'
        strength = current['upper_wick_ratio']
        
        if prev_trend == 'UP' and current['is_bearish']:
            pattern = 'BEARISH_SHOOTING_STAR'
        elif prev_trend == 'DOWN' and current['is_bullish']:
            pattern = 'BULLISH_INVERTED_HAMMER'
        
        return {
            'pattern': pattern,
            'strength': strength,
            'wick_ratio': current['upper_wick_ratio'],
            'body_ratio': current['body_ratio']
        }
    
    def detect_doji_patterns(self, df: pd.DataFrame) -> Dict:
        """Detect various doji patterns"""
        if len(df) < 3:
            return {'pattern': 'NONE', 'strength': 0}
        
        current = df.iloc[-1]
        
        if not current['is_doji']:
            return {'pattern': 'NONE', 'strength': 0}
        
        # Classify doji type
        pattern = 'DOJI'
        strength = 1 - current['body_ratio']  # Smaller body = stronger doji
        
        # Dragonfly Doji (long lower wick, no upper wick)
        if current['lower_wick_ratio'] > 0.7 and current['upper_wick_ratio'] < 0.1:
            pattern = 'DRAGONFLY_DOJI'
            strength = current['lower_wick_ratio']
        
        # Gravestone Doji (long upper wick, no lower wick)
        elif current['upper_wick_ratio'] > 0.7 and current['lower_wick_ratio'] < 0.1:
            pattern = 'GRAVESTONE_DOJI'
            strength = current['upper_wick_ratio']
        
        # Long-legged Doji (both wicks long)
        elif current['upper_wick_ratio'] > 0.3 and current['lower_wick_ratio'] > 0.3:
            pattern = 'LONG_LEGGED_DOJI'
            strength = (current['upper_wick_ratio'] + current['lower_wick_ratio']) / 2
        
        return {
            'pattern': pattern,
            'strength': strength,
            'upper_wick_ratio': current['upper_wick_ratio'],
            'lower_wick_ratio': current['lower_wick_ratio']
        }
    
    def detect_engulfing_patterns(self, df: pd.DataFrame) -> Dict:
        """Detect bullish and bearish engulfing patterns"""
        if len(df) < 2:
            return {'pattern': 'NONE', 'strength': 0}
        
        current = df.iloc[-1]
        previous = df.iloc[-2]
        
        # Engulfing criteria
        current_body_larger = current['body_size'] > previous['body_size']
        
        pattern = 'NONE'
        strength = 0
        
        # Bullish Engulfing
        if (previous['is_bearish'] and current['is_bullish'] and 
            current['open'] < previous['close'] and 
            current['close'] > previous['open'] and
            current_body_larger):
            pattern = 'BULLISH_ENGULFING'
            strength = current['body_size'] / previous['body_size']
        
        # Bearish Engulfing
        elif (previous['is_bullish'] and current['is_bearish'] and 
              current['open'] > previous['close'] and 
              current['close'] < previous['open'] and
              current_body_larger):
            pattern = 'BEARISH_ENGULFING'
            strength = current['body_size'] / previous['body_size']
        
        return {
            'pattern': pattern,
            'strength': min(strength, 2.0),  # Cap at 2.0
            'size_ratio': current['body_size'] / previous['body_size'] if previous['body_size'] > 0 else 1
        }
    
    def determine_trend(self, df: pd.DataFrame) -> str:
        """Determine the prevailing trend"""
        if len(df) < 3:
            return 'NEUTRAL'
        
        closes = df['close']
        
        # Simple trend determination
        if closes.iloc[-1] > closes.iloc[0]:
            return 'UP'
        elif closes.iloc[-1] < closes.iloc[0]:
            return 'DOWN'
        else:
            return 'NEUTRAL'
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main trap candle analysis function
        Returns score between 0 (bearish trap) and 1 (bullish trap)
        """
        try:
            if len(data) < 5:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for trap candle analysis'
                }
            
            # Calculate candle metrics
            df = self.calculate_candle_metrics(data)
            
            # Detect various patterns
            hammer_pattern = self.detect_hammer_patterns(df)
            shooting_star_pattern = self.detect_shooting_star_patterns(df)
            doji_pattern = self.detect_doji_patterns(df)
            engulfing_pattern = self.detect_engulfing_patterns(df)
            
            # Scoring system
            score = 0.5  # Neutral starting point
            confidence = 0.0
            patterns_detected = []
            
            # Pattern scoring
            if hammer_pattern['pattern'] == 'BULLISH_HAMMER':
                score += 0.3 * hammer_pattern['strength']
                confidence += 0.2
                patterns_detected.append('BULLISH_HAMMER')
            elif hammer_pattern['pattern'] == 'BEARISH_HANGING_MAN':
                score -= 0.3 * hammer_pattern['strength']
                confidence += 0.2
                patterns_detected.append('BEARISH_HANGING_MAN')
            
            if shooting_star_pattern['pattern'] == 'BEARISH_SHOOTING_STAR':
                score -= 0.3 * shooting_star_pattern['strength']
                confidence += 0.2
                patterns_detected.append('BEARISH_SHOOTING_STAR')
            elif shooting_star_pattern['pattern'] == 'BULLISH_INVERTED_HAMMER':
                score += 0.3 * shooting_star_pattern['strength']
                confidence += 0.2
                patterns_detected.append('BULLISH_INVERTED_HAMMER')
            
            if engulfing_pattern['pattern'] == 'BULLISH_ENGULFING':
                score += 0.25 * min(engulfing_pattern['strength'], 1.0)
                confidence += 0.25
                patterns_detected.append('BULLISH_ENGULFING')
            elif engulfing_pattern['pattern'] == 'BEARISH_ENGULFING':
                score -= 0.25 * min(engulfing_pattern['strength'], 1.0)
                confidence += 0.25
                patterns_detected.append('BEARISH_ENGULFING')
            
            # Doji patterns (indecision)
            if doji_pattern['pattern'] != 'NONE':
                if doji_pattern['pattern'] == 'DRAGONFLY_DOJI':
                    score += 0.15
                elif doji_pattern['pattern'] == 'GRAVESTONE_DOJI':
                    score -= 0.15
                confidence += 0.1
                patterns_detected.append(doji_pattern['pattern'])
            
            # Ensure score is within bounds
            score = max(0, min(1, score))
            confidence = min(1, confidence)
            
            # Determine direction
            if score > 0.6:
                direction = 'CALL'
            elif score < 0.4:
                direction = 'PUT'
            else:
                direction = 'NEUTRAL'
            
            result = {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'patterns_detected': patterns_detected,
                'pattern_details': {
                    'hammer': hammer_pattern,
                    'shooting_star': shooting_star_pattern,
                    'doji': doji_pattern,
                    'engulfing': engulfing_pattern
                },
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.debug(f"Trap Candle analysis: Score={score:.3f}, Direction={direction}, Patterns={patterns_detected}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Trap candle analysis failed: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
