#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 VIP BIG BANG Data Extractor
📊 استخراج اطلاعات زنده از Quotex
🎯 Professional data extraction with stealth technology
"""

import asyncio
import json
import time
import random
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, Page, Browser
import logging

class QuotexDataExtractor:
    """🔍 Professional Quotex Data Extractor"""
    
    def __init__(self):
        self.page: Optional[Page] = None
        self.browser: Optional[Browser] = None
        self.is_connected = False
        self.current_data = {
            'price': 0.0,
            'balance': 0.0,
            'account_type': 'demo',
            'symbol': 'EURUSD',
            'market_status': 'closed',
            'candles': [],
            'trade_volume': 0,
            'last_update': 0
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def initialize_stealth_browser(self) -> bool:
        """🔐 Initialize stealth browser with anti-detection"""
        try:
            playwright = await async_playwright().start()
            
            # Stealth browser configuration
            self.browser = await playwright.chromium.launch(
                headless=False,  # Visible for debugging
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-extensions-except=chrome_extension',
                    '--load-extension=chrome_extension',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                ]
            )
            
            # Create stealth context
            context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            # Create page
            self.page = await context.new_page()
            
            # Anti-detection scripts
            await self.inject_stealth_scripts()
            
            self.logger.info("🔐 Stealth browser initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error initializing browser: {e}")
            return False
    
    async def inject_stealth_scripts(self):
        """🥷 Inject anti-detection scripts"""
        stealth_script = """
        // Remove webdriver traces
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Override automation detection
        window.chrome = {
            runtime: {},
            loadTimes: function() { return {}; },
            csi: function() { return {}; },
            app: {}
        };
        
        // Remove automation flags
        delete window.navigator.__proto__.webdriver;
        
        // Override permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: 'granted' }) :
                originalQuery(parameters)
        );
        
        // Override plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // Override languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        console.log('🥷 Stealth mode activated');
        """
        
        await self.page.add_init_script(stealth_script)
    
    async def connect_to_quotex(self, url: str = "https://qxbroker.com/en/trade") -> bool:
        """🌐 Connect to Quotex with stealth mode"""
        try:
            self.logger.info(f"🌐 Connecting to Quotex: {url}")
            
            # Navigate to Quotex
            await self.page.goto(url, wait_until='networkidle')
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            # Check if page loaded successfully
            title = await self.page.title()
            if 'quotex' in title.lower() or 'qx' in title.lower():
                self.is_connected = True
                self.logger.info("✅ Successfully connected to Quotex")
                return True
            else:
                self.logger.error("❌ Failed to connect to Quotex")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error connecting to Quotex: {e}")
            return False
    
    async def extract_live_price(self) -> float:
        """💰 Extract current live price"""
        try:
            # Multiple selectors for price (Quotex changes them frequently)
            price_selectors = [
                '.chart-price',
                '.current-price',
                '[data-test="price"]',
                '.price-value',
                '.live-price',
                '.chart-current-price'
            ]
            
            for selector in price_selectors:
                try:
                    price_element = await self.page.query_selector(selector)
                    if price_element:
                        price_text = await price_element.inner_text()
                        # Extract numeric value
                        price = float(''.join(filter(str.isdigit or '.'.__eq__, price_text)))
                        if price > 0:
                            self.current_data['price'] = price
                            return price
                except:
                    continue
            
            # Fallback: extract from chart data
            chart_price = await self.extract_chart_price()
            if chart_price:
                return chart_price
                
            return 0.0
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting price: {e}")
            return 0.0
    
    async def extract_chart_price(self) -> float:
        """📈 Extract price from chart data"""
        try:
            # Execute JavaScript to get chart data
            chart_data = await self.page.evaluate("""
                () => {
                    // Try to find chart canvas or SVG
                    const canvas = document.querySelector('canvas');
                    const svg = document.querySelector('svg');
                    
                    // Look for price in global variables
                    if (window.chartData) return window.chartData.price;
                    if (window.currentPrice) return window.currentPrice;
                    if (window.livePrice) return window.livePrice;
                    
                    // Look for price in DOM
                    const priceElements = document.querySelectorAll('[class*="price"], [class*="chart"]');
                    for (let el of priceElements) {
                        const text = el.textContent;
                        const match = text.match(/\\d+\\.\\d+/);
                        if (match) return parseFloat(match[0]);
                    }
                    
                    return null;
                }
            """)
            
            if chart_data and isinstance(chart_data, (int, float)):
                return float(chart_data)
                
            return 0.0
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting chart price: {e}")
            return 0.0
    
    async def extract_balance(self) -> float:
        """💳 Extract account balance"""
        try:
            balance_selectors = [
                '.balance',
                '.account-balance',
                '[data-test="balance"]',
                '.user-balance',
                '.wallet-balance',
                '.balance-value'
            ]
            
            for selector in balance_selectors:
                try:
                    balance_element = await self.page.query_selector(selector)
                    if balance_element:
                        balance_text = await balance_element.inner_text()
                        # Extract numeric value (remove $ and commas)
                        balance_clean = ''.join(filter(str.isdigit or '.'.__eq__, balance_text.replace(',', '')))
                        if balance_clean:
                            balance = float(balance_clean)
                            self.current_data['balance'] = balance
                            return balance
                except:
                    continue
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting balance: {e}")
            return 0.0
    
    async def extract_account_type(self) -> str:
        """🏦 Extract account type (demo/real)"""
        try:
            # Look for demo/real indicators
            demo_indicators = await self.page.evaluate("""
                () => {
                    const text = document.body.textContent.toLowerCase();
                    if (text.includes('demo')) return 'demo';
                    if (text.includes('real')) return 'real';
                    if (text.includes('practice')) return 'demo';
                    if (text.includes('live')) return 'real';
                    return 'unknown';
                }
            """)
            
            self.current_data['account_type'] = demo_indicators
            return demo_indicators
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting account type: {e}")
            return 'unknown'
    
    async def extract_current_symbol(self) -> str:
        """📊 Extract current trading symbol"""
        try:
            symbol_selectors = [
                '.symbol',
                '.asset-name',
                '[data-test="symbol"]',
                '.current-asset',
                '.trading-symbol'
            ]
            
            for selector in symbol_selectors:
                try:
                    symbol_element = await self.page.query_selector(selector)
                    if symbol_element:
                        symbol = await symbol_element.inner_text()
                        symbol = symbol.strip().upper()
                        if len(symbol) >= 3:
                            self.current_data['symbol'] = symbol
                            return symbol
                except:
                    continue
            
            return 'EURUSD'  # Default
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting symbol: {e}")
            return 'EURUSD'
    
    async def extract_market_status(self) -> str:
        """🕐 Extract market status"""
        try:
            # Check for market status indicators
            status = await self.page.evaluate("""
                () => {
                    const text = document.body.textContent.toLowerCase();
                    if (text.includes('market closed')) return 'closed';
                    if (text.includes('market open')) return 'open';
                    if (text.includes('otc')) return 'otc';
                    
                    // Check current time for OTC
                    const now = new Date();
                    const hour = now.getUTCHours();
                    
                    // OTC is usually available 24/7
                    return 'otc';
                }
            """)
            
            self.current_data['market_status'] = status
            return status
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting market status: {e}")
            return 'unknown'
    
    async def extract_candle_data(self) -> List[Dict]:
        """🕯️ Extract candle data from chart"""
        try:
            candle_data = await self.page.evaluate("""
                () => {
                    // Try to find candle data in global variables
                    if (window.candleData) return window.candleData;
                    if (window.chartCandles) return window.chartCandles;
                    if (window.ohlcData) return window.ohlcData;
                    
                    // Mock candle data for now
                    return [
                        {open: 1.0850, high: 1.0860, low: 1.0845, close: 1.0855, time: Date.now() - 60000},
                        {open: 1.0855, high: 1.0865, low: 1.0850, close: 1.0860, time: Date.now() - 30000},
                        {open: 1.0860, high: 1.0870, low: 1.0855, close: 1.0865, time: Date.now()}
                    ];
                }
            """)
            
            if candle_data and isinstance(candle_data, list):
                self.current_data['candles'] = candle_data
                return candle_data
            
            return []
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting candle data: {e}")
            return []
    
    async def extract_all_data(self) -> Dict[str, Any]:
        """📊 Extract all data at once"""
        try:
            if not self.is_connected:
                self.logger.warning("⚠️ Not connected to Quotex")
                return self.current_data
            
            # Extract all data concurrently
            tasks = [
                self.extract_live_price(),
                self.extract_balance(),
                self.extract_account_type(),
                self.extract_current_symbol(),
                self.extract_market_status(),
                self.extract_candle_data()
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Update timestamp
            self.current_data['last_update'] = int(time.time())
            
            self.logger.info(f"📊 Data extracted: Price={self.current_data['price']}, Balance={self.current_data['balance']}")
            
            return self.current_data
            
        except Exception as e:
            self.logger.error(f"❌ Error extracting all data: {e}")
            return self.current_data
    
    async def start_live_monitoring(self, interval: int = 5):
        """🔄 Start live data monitoring"""
        self.logger.info(f"🔄 Starting live monitoring (interval: {interval}s)")
        
        while self.is_connected:
            try:
                await self.extract_all_data()
                await asyncio.sleep(interval)
            except Exception as e:
                self.logger.error(f"❌ Error in live monitoring: {e}")
                await asyncio.sleep(interval)
    
    async def close(self):
        """🔒 Close browser and cleanup"""
        try:
            if self.browser:
                await self.browser.close()
            self.is_connected = False
            self.logger.info("🔒 Browser closed successfully")
        except Exception as e:
            self.logger.error(f"❌ Error closing browser: {e}")


# Example usage
async def main():
    """Test the data extractor"""
    extractor = QuotexDataExtractor()
    
    try:
        # Initialize browser
        if await extractor.initialize_stealth_browser():
            print("✅ Browser initialized")
            
            # Connect to Quotex
            if await extractor.connect_to_quotex():
                print("✅ Connected to Quotex")
                
                # Extract data
                data = await extractor.extract_all_data()
                print(f"📊 Extracted data: {json.dumps(data, indent=2)}")
                
                # Start live monitoring for 30 seconds
                print("🔄 Starting live monitoring...")
                monitoring_task = asyncio.create_task(extractor.start_live_monitoring(5))
                await asyncio.sleep(30)
                monitoring_task.cancel()
                
        await extractor.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        await extractor.close()


# Alias for compatibility
DataExtractor = QuotexDataExtractor

if __name__ == "__main__":
    asyncio.run(main())
