#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Real Data Server
📊 دریافت اطلاعات واقعی از Chrome Extension
⚡ WebSocket Server برای اتصال مستقیم
💎 100% اطلاعات واقعی
"""

import asyncio
import websockets
import json
import threading
import time
from datetime import datetime
import logging

class RealDataServer:
    """
    🚀 Real Data Server
    📊 دریافت اطلاعات واقعی از Chrome Extension
    ⚡ WebSocket Server
    💎 100% اطلاعات واقعی
    """

    def __init__(self, port=8765):
        self.port = port
        self.is_running = False
        self.connected_clients = set()
        self.latest_data = {}
        self.data_callbacks = []
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        print(f"🚀 Real Data Server initialized on port {port}")

    def add_data_callback(self, callback):
        """📝 Add callback for when new data arrives"""
        self.data_callbacks.append(callback)

    def remove_data_callback(self, callback):
        """🗑️ Remove data callback"""
        if callback in self.data_callbacks:
            self.data_callbacks.remove(callback)

    async def handle_client(self, websocket):
        """🔗 Handle WebSocket client connection"""
        try:
            self.connected_clients.add(websocket)
            client_ip = websocket.remote_address[0] if websocket.remote_address else "unknown"
            self.logger.info(f"✅ Client connected from {client_ip}")
            
            # Send welcome message
            await websocket.send(json.dumps({
                "type": "welcome",
                "message": "🚀 VIP BIG BANG Real Data Server",
                "timestamp": datetime.now().isoformat()
            }))

            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.process_message(data, websocket)
                except json.JSONDecodeError as e:
                    self.logger.error(f"❌ JSON decode error: {e}")
                except Exception as e:
                    self.logger.error(f"❌ Message processing error: {e}")

        except websockets.exceptions.ConnectionClosed:
            self.logger.info("🔌 Client disconnected")
        except Exception as e:
            self.logger.error(f"❌ Client handling error: {e}")
        finally:
            self.connected_clients.discard(websocket)

    async def process_message(self, data, websocket):
        """📊 Process incoming message from Chrome Extension"""
        try:
            message_type = data.get("type", "unknown")

            if message_type == "quotex_data":
                # Real Quotex data received
                quotex_data = data.get("data", {})
                self.latest_data = {
                    **quotex_data,
                    "timestamp": datetime.now().isoformat(),
                    "source": "REAL_CHROME_EXTENSION",
                    "server_received": time.time()
                }

                self.logger.info(f"📊 Real Quotex data received: {quotex_data.get('balance', 'N/A')}")

                # Notify all callbacks
                for callback in self.data_callbacks:
                    try:
                        callback(self.latest_data)
                    except Exception as e:
                        self.logger.error(f"❌ Callback error: {e}")

                # Save to shared file for UI access
                self.save_shared_data(self.latest_data)

                # Send confirmation
                await websocket.send(json.dumps({
                    "type": "data_received",
                    "status": "success",
                    "timestamp": datetime.now().isoformat()
                }))

            elif message_type == "complete_scan_data":
                # Complete scan data from Advanced Scanner
                scan_data = data.get("data", {})
                self.latest_data = {
                    **scan_data,
                    "timestamp": datetime.now().isoformat(),
                    "source": "ADVANCED_SCANNER",
                    "server_received": time.time()
                }

                # Extract key data for logging
                asset = scan_data.get('currentAsset', 'Unknown')
                price = scan_data.get('currentPrice', 'Unknown')
                balance = scan_data.get('balance', 'Unknown')
                scan_num = scan_data.get('scanNumber', 0)

                self.logger.info(f"🔍 Complete scan #{scan_num} received:")
                self.logger.info(f"  📊 Asset: {asset}")
                self.logger.info(f"  💰 Price: {price}")
                self.logger.info(f"  💵 Balance: {balance}")

                # Notify all callbacks
                for callback in self.data_callbacks:
                    try:
                        callback(self.latest_data)
                    except Exception as e:
                        self.logger.error(f"❌ Callback error: {e}")

                # Save to shared file for UI access
                self.save_shared_data(self.latest_data)

                # Send confirmation
                await websocket.send(json.dumps({
                    "type": "scan_received",
                    "status": "success",
                    "scan_number": scan_num,
                    "timestamp": datetime.now().isoformat()
                }))

            elif message_type == "no_real_data":
                # No real data found message
                no_data_msg = data.get("data", {})
                self.logger.warning(f"⚠️ No real data found: {no_data_msg.get('message', 'Unknown')}")

                # Send acknowledgment
                await websocket.send(json.dumps({
                    "type": "no_data_ack",
                    "status": "acknowledged",
                    "timestamp": datetime.now().isoformat()
                }))

            elif message_type == "browser_fingerprint":
                # Browser fingerprint data
                fingerprint_data = data.get("data", {})
                self.logger.info(f"🔍 Browser fingerprint received")
                
                # Store fingerprint
                self.latest_data["fingerprint"] = fingerprint_data
                
            elif message_type == "trade_executed":
                # Trade execution data
                trade_data = data.get("data", {})
                self.logger.info(f"💰 Trade executed: {trade_data}")
                
                # Store trade data
                self.latest_data["last_trade"] = trade_data
                
            elif message_type == "ping":
                # Heartbeat
                await websocket.send(json.dumps({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }))

            else:
                self.logger.warning(f"⚠️ Unknown message type: {message_type}")

        except Exception as e:
            self.logger.error(f"❌ Message processing error: {e}")

    def start_server(self):
        """🚀 Start WebSocket server"""
        try:
            self.is_running = True

            def run_server():
                try:
                    # Create new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # Create server coroutine
                    async def start_websocket_server():
                        self.logger.info(f"🚀 Real Data Server starting on ws://localhost:{self.port}")

                        server = await websockets.serve(
                            self.handle_client,
                            "localhost",
                            self.port,
                            ping_interval=30,
                            ping_timeout=10
                        )

                        self.logger.info(f"✅ WebSocket server running on port {self.port}")

                        # Keep server running
                        await server.wait_closed()

                    # Run the server
                    loop.run_until_complete(start_websocket_server())

                except Exception as e:
                    self.logger.error(f"❌ Server thread error: {e}")

            # Start server in separate thread
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            # Give server time to start
            time.sleep(1)

            self.logger.info(f"✅ Real Data Server started on port {self.port}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Server start error: {e}")
            return False

    def stop_server(self):
        """⏹️ Stop WebSocket server"""
        try:
            self.is_running = False
            self.logger.info("⏹️ Real Data Server stopped")
        except Exception as e:
            self.logger.error(f"❌ Server stop error: {e}")

    def get_latest_data(self):
        """📊 Get latest real data"""
        return self.latest_data.copy() if self.latest_data else None

    def is_connected(self):
        """🔗 Check if any clients are connected"""
        return len(self.connected_clients) > 0

    def get_connection_count(self):
        """📊 Get number of connected clients"""
        return len(self.connected_clients)

    def save_shared_data(self, data):
        """💾 Save data to shared file for UI access"""
        try:
            import json
            import os

            shared_file = "shared_quotex_data.json"

            # Prepare data for sharing
            shared_data = {
                "balance": data.get("balance", "N/A"),
                "currentAsset": data.get("currentAsset", "N/A"),
                "currentPrice": data.get("currentPrice", "N/A"),
                "timestamp": data.get("timestamp", datetime.now().isoformat()),
                "source": data.get("source", "REAL_DATA_SERVER"),
                "last_updated": datetime.now().isoformat(),
                "server_status": "connected",
                "connection_count": len(self.connected_clients)
            }

            # Add additional data if available
            if "scanNumber" in data:
                shared_data["scanNumber"] = data["scanNumber"]
            if "last_trade" in data:
                shared_data["last_trade"] = data["last_trade"]
            if "fingerprint" in data:
                shared_data["fingerprint"] = data["fingerprint"]

            # Write to shared file
            with open(shared_file, 'w') as f:
                json.dump(shared_data, f, indent=2)

            self.logger.debug(f"💾 Shared data saved: {shared_data.get('balance', 'N/A')}")

        except Exception as e:
            self.logger.error(f"❌ Error saving shared data: {e}")

    async def broadcast_message(self, message):
        """📢 Broadcast message to all connected clients"""
        if not self.connected_clients:
            return

        disconnected = set()
        for websocket in self.connected_clients:
            try:
                await websocket.send(json.dumps(message))
            except websockets.exceptions.ConnectionClosed:
                disconnected.add(websocket)
            except Exception as e:
                self.logger.error(f"❌ Broadcast error: {e}")
                disconnected.add(websocket)

        # Remove disconnected clients
        self.connected_clients -= disconnected

    def send_command_to_extension(self, command):
        """📤 Send command to Chrome Extension"""
        try:
            if not self.connected_clients:
                self.logger.warning("⚠️ No connected clients to send command")
                return False

            message = {
                "type": "command",
                "command": command,
                "timestamp": datetime.now().isoformat()
            }

            # Send to all connected clients
            asyncio.create_task(self.broadcast_message(message))
            return True

        except Exception as e:
            self.logger.error(f"❌ Command send error: {e}")
            return False

# Global server instance
real_data_server = None

def start_real_data_server(port=8765):
    """🚀 Start global real data server"""
    global real_data_server
    
    if real_data_server is None:
        real_data_server = RealDataServer(port)
        return real_data_server.start_server()
    
    return True

def get_real_data_server():
    """📊 Get global real data server instance"""
    return real_data_server

def get_latest_real_data():
    """📊 Get latest real data from server"""
    if real_data_server:
        return real_data_server.get_latest_data()
    return None

def is_real_data_available():
    """🔗 Check if real data is available"""
    if real_data_server:
        return real_data_server.is_connected()
    return False

# Test function
def test_real_data_server():
    """🧪 Test real data server"""
    print("🧪 Testing Real Data Server...")
    
    server = RealDataServer(8765)
    
    def data_callback(data):
        print(f"📊 Real data received: {data}")
    
    server.add_data_callback(data_callback)
    
    if server.start_server():
        print("✅ Server started successfully")
        print("🔗 Waiting for Chrome Extension connection...")
        print("📊 Open Quotex in Chrome with VIP BIG BANG extension")
        
        try:
            while True:
                time.sleep(1)
                if server.is_connected():
                    print(f"✅ {server.get_connection_count()} client(s) connected")
                    latest = server.get_latest_data()
                    if latest:
                        print(f"📊 Latest data: {latest.get('balance', 'N/A')}")
        except KeyboardInterrupt:
            print("⏹️ Stopping server...")
            server.stop_server()
    else:
        print("❌ Failed to start server")

if __name__ == "__main__":
    test_real_data_server()
