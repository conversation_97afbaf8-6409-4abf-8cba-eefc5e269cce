// VIP BIG BANG Popup Script

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 VIP BIG BANG Popup loaded');
    
    // Get DOM elements
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const dataDisplay = document.getElementById('dataDisplay');
    const priceValue = document.getElementById('priceValue');
    const balanceValue = document.getElementById('balanceValue');
    const activateBtn = document.getElementById('activateBtn');
    const openQuotexBtn = document.getElementById('openQuotexBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    
    // Update status
    function updateStatus() {
        chrome.runtime.sendMessage({ type: 'get-connection-status' }, (response) => {
            if (response && response.connected) {
                statusDot.className = 'status-dot active';
                statusText.textContent = `✅ Connected (${response.connections} tab${response.connections !== 1 ? 's' : ''})`;
                
                if (response.hasData) {
                    // Get latest data
                    chrome.runtime.sendMessage({ type: 'get-latest-data' }, (dataResponse) => {
                        if (dataResponse && dataResponse.data) {
                            updateDataDisplay(dataResponse.data);
                        }
                    });
                }
            } else {
                statusDot.className = 'status-dot inactive';
                statusText.textContent = '❌ Not connected to Quotex';
                dataDisplay.textContent = 'Please open Quotex.io and refresh this popup';
                priceValue.textContent = '--';
                balanceValue.textContent = '--';
            }
        });
    }
    
    // Update data display
    function updateDataDisplay(data) {
        if (data) {
            let displayText = '';
            
            if (data.price && data.price.price) {
                displayText += `💰 Price: ${data.price.price}\n`;
                priceValue.textContent = data.price.price.toFixed(5);
            }
            
            if (data.balance) {
                displayText += `💳 Balance: $${data.balance}\n`;
                balanceValue.textContent = `$${data.balance}`;
            }
            
            if (data.asset) {
                displayText += `📊 Asset: ${data.asset}\n`;
            }
            
            displayText += `🕐 Updated: ${new Date(data.timestamp).toLocaleTimeString()}`;
            
            dataDisplay.textContent = displayText || 'No trading data available';
        }
    }
    
    // Button event listeners
    activateBtn.addEventListener('click', function() {
        activateBtn.textContent = '🔄 Refreshing...';
        
        // Check current tab
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            const currentTab = tabs[0];
            
            if (currentTab.url.includes('quotex.io') || currentTab.url.includes('qxbroker.com')) {
                // Inject content script
                chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    files: ['content.js']
                }, () => {
                    setTimeout(() => {
                        updateStatus();
                        activateBtn.textContent = '🔄 Refresh Status';
                    }, 2000);
                });
            } else {
                statusText.textContent = '⚠️ Please open Quotex.io first';
                activateBtn.textContent = '🔄 Refresh Status';
            }
        });
    });
    
    openQuotexBtn.addEventListener('click', function() {
        chrome.tabs.create({ url: 'https://quotex.io' });
        window.close();
    });
    
    settingsBtn.addEventListener('click', function() {
        // Create settings tab
        chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
        window.close();
    });
    
    // Auto-refresh status every 3 seconds
    setInterval(updateStatus, 3000);
    
    // Initial status check
    updateStatus();
    
    console.log('✅ VIP BIG BANG Popup ready');
});
