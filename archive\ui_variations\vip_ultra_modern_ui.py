"""
🚀 VIP BIG BANG - Ultra Modern UI
رابط کاربری فوق مدرن و زیبا
"""

import sys
import random
from datetime import datetime
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class GlassCard(QFrame):
    """کارت شیشه‌ای مدرن"""
    
    def __init__(self, title="", value="", icon="", color="#4A90E2"):
        super().__init__()
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم UI"""
        self.setFixedSize(220, 140)
        
        # Glass effect style
        self.setStyleSheet(f"""
            QFrame {{
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                backdrop-filter: blur(10px);
            }}
            QFrame:hover {{
                background: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 20, 25, 20)
        layout.setSpacing(10)
        
        # Header with icon and title
        header_layout = QHBoxLayout()
        
        # Icon
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet(f"""
            font-size: 32px;
            color: {self.color};
        """)
        header_layout.addWidget(icon_label)
        
        header_layout.addStretch()
        
        # Title
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            text-transform: uppercase;
            letter-spacing: 1px;
        """)
        header_layout.addWidget(title_label)
        
        layout.addLayout(header_layout)
        
        # Value
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-size: 32px;
            font-weight: 700;
            color: {self.color};
            margin-top: 10px;
        """)
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.value_label)
        
        # Animated line
        self.line = QFrame()
        self.line.setFixedHeight(3)
        self.line.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent,
                    stop:0.5 {self.color},
                    stop:1 transparent);
                border: none;
                border-radius: 1px;
            }}
        """)
        layout.addWidget(self.line)
    
    def update_value(self, new_value):
        """به‌روزرسانی مقدار با انیمیشن"""
        self.value_label.setText(str(new_value))
        
        # Glow effect animation
        self.animate_glow()
    
    def animate_glow(self):
        """انیمیشن درخشش"""
        self.glow_animation = QPropertyAnimation(self.value_label, b"styleSheet")
        self.glow_animation.setDuration(500)
        
        start_style = f"""
            font-size: 32px;
            font-weight: 700;
            color: {self.color};
            margin-top: 10px;
        """
        
        glow_style = f"""
            font-size: 32px;
            font-weight: 700;
            color: {self.color};
            margin-top: 10px;
            text-shadow: 0 0 20px {self.color};
        """
        
        self.glow_animation.setStartValue(start_style)
        self.glow_animation.setEndValue(glow_style)
        self.glow_animation.finished.connect(lambda: self.value_label.setStyleSheet(start_style))
        self.glow_animation.start()

class NeonButton(QPushButton):
    """دکمه نئونی"""
    
    def __init__(self, text="", icon="", color="#4A90E2"):
        super().__init__(f"{icon} {text}")
        self.color = color
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم UI"""
        self.setFixedHeight(55)
        self.setMinimumWidth(150)
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid {self.color};
                border-radius: 27px;
                color: {self.color};
                font-size: 16px;
                font-weight: 600;
                padding: 0 30px;
            }}
            QPushButton:hover {{
                background: {self.color};
                color: white;
                box-shadow: 0 0 30px {self.color};
            }}
            QPushButton:pressed {{
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid white;
                color: white;
            }}
        """)

class LiveChart(QWidget):
    """چارت زنده"""
    
    def __init__(self):
        super().__init__()
        self.data_points = []
        self.setup_ui()
        self.setup_timer()
        
        # Generate initial data
        base_price = 1.0850
        for i in range(100):
            self.data_points.append(base_price + random.uniform(-0.02, 0.02))
    
    def setup_ui(self):
        """تنظیم UI"""
        self.setMinimumHeight(350)
        self.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 20px;
            }
        """)
    
    def setup_timer(self):
        """تنظیم تایمر"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(1500)  # Every 1.5 seconds
    
    def update_data(self):
        """به‌روزرسانی داده‌ها"""
        if self.data_points:
            last_price = self.data_points[-1]
            new_price = last_price + random.uniform(-0.008, 0.008)
            self.data_points.append(new_price)
            
            # Keep only last 100 points
            if len(self.data_points) > 100:
                self.data_points.pop(0)
        
        self.update()
    
    def paintEvent(self, event):
        """رسم چارت"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        if len(self.data_points) < 2:
            return
        
        rect = self.rect()
        margin = 30
        chart_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        # Calculate scaling
        min_price = min(self.data_points)
        max_price = max(self.data_points)
        price_range = max_price - min_price if max_price != min_price else 0.01
        
        # Draw grid
        painter.setPen(QPen(QColor(255, 255, 255, 30), 1))
        for i in range(6):
            y = chart_rect.top() + (chart_rect.height() * i / 5)
            painter.drawLine(chart_rect.left(), y, chart_rect.right(), y)
        
        for i in range(11):
            x = chart_rect.left() + (chart_rect.width() * i / 10)
            painter.drawLine(x, chart_rect.top(), x, chart_rect.bottom())
        
        # Draw price line with gradient
        gradient = QLinearGradient(0, chart_rect.top(), 0, chart_rect.bottom())
        gradient.setColorAt(0, QColor(74, 144, 226, 255))
        gradient.setColorAt(1, QColor(126, 211, 33, 255))
        
        painter.setPen(QPen(QBrush(gradient), 3))
        
        # Draw line
        points = []
        for i, price in enumerate(self.data_points):
            x = chart_rect.left() + (chart_rect.width() * i / (len(self.data_points) - 1))
            y = chart_rect.bottom() - ((price - min_price) / price_range * chart_rect.height())
            points.append(QPointF(x, y))
        
        if len(points) > 1:
            for i in range(len(points) - 1):
                painter.drawLine(points[i], points[i + 1])
        
        # Draw current price
        if points:
            current_point = points[-1]
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.setBrush(QBrush(QColor(74, 144, 226)))
            painter.drawEllipse(current_point, 6, 6)
            
            # Price label
            current_price = self.data_points[-1]
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            painter.drawText(current_point.x() + 10, current_point.y() - 10, f"{current_price:.4f}")

class VIPUltraModernUI(QMainWindow):
    """رابط کاربری فوق مدرن VIP BIG BANG"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 VIP BIG BANG - Ultra Modern Interface")
        self.setGeometry(100, 100, 1500, 950)
        
        # Data
        self.balance = 3250.89
        self.winrate = 94.7
        self.trades = 127
        self.profit = 2150.89
        
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Dark gradient background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23,
                    stop:0.3 #1a1a2e,
                    stop:0.7 #16213e,
                    stop:1 #0f3460);
            }
            QLabel {
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(30)
        
        # Header
        self.create_header(layout)
        
        # Main content
        main_layout = QHBoxLayout()
        main_layout.setSpacing(30)
        
        # Left panel - Stats
        self.create_stats_panel(main_layout)
        
        # Center panel - Chart
        self.create_chart_panel(main_layout)
        
        # Right panel - Controls
        self.create_controls_panel(main_layout)
        
        layout.addLayout(main_layout)
        
        # Footer
        self.create_footer(layout)
    
    def create_header(self, layout):
        """ایجاد هدر"""
        header = QWidget()
        header.setFixedHeight(100)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Logo and title
        logo_layout = QHBoxLayout()
        
        logo = QLabel("🚀")
        logo.setStyleSheet("font-size: 48px;")
        logo_layout.addWidget(logo)
        
        title_layout = QVBoxLayout()
        title_layout.setSpacing(0)
        
        title = QLabel("VIP BIG BANG")
        title.setStyleSheet("""
            font-size: 36px;
            font-weight: 700;
            color: #4A90E2;
            text-shadow: 0 0 20px #4A90E2;
        """)
        title_layout.addWidget(title)
        
        subtitle = QLabel("Ultra Modern Trading Interface")
        subtitle.setStyleSheet("""
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 5px;
        """)
        title_layout.addWidget(subtitle)
        
        logo_layout.addLayout(title_layout)
        header_layout.addLayout(logo_layout)
        
        header_layout.addStretch()
        
        # Status
        status_layout = QVBoxLayout()
        status_layout.setSpacing(5)
        
        status = QLabel("🟢 LIVE TRADING")
        status.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: #7ED321;
            text-shadow: 0 0 10px #7ED321;
        """)
        status_layout.addWidget(status)
        
        self.time_label = QLabel()
        self.time_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
        """)
        status_layout.addWidget(self.time_label)
        
        header_layout.addLayout(status_layout)
        
        layout.addWidget(header)
    
    def create_stats_panel(self, layout):
        """پنل آمار"""
        stats_widget = QWidget()
        stats_widget.setFixedWidth(280)
        
        stats_layout = QVBoxLayout(stats_widget)
        stats_layout.setSpacing(20)
        
        # Title
        title = QLabel("📊 TRADING STATS")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 15px;
            text-align: center;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        stats_layout.addWidget(title)
        
        # Stats cards
        self.balance_card = GlassCard("Balance", f"${self.balance:,.2f}", "💰", "#7ED321")
        stats_layout.addWidget(self.balance_card)
        
        self.winrate_card = GlassCard("Win Rate", f"{self.winrate}%", "🏆", "#4A90E2")
        stats_layout.addWidget(self.winrate_card)
        
        self.trades_card = GlassCard("Trades", str(self.trades), "📊", "#9013FE")
        stats_layout.addWidget(self.trades_card)
        
        self.profit_card = GlassCard("Profit", f"${self.profit:,.2f}", "💎", "#F5A623")
        stats_layout.addWidget(self.profit_card)
        
        stats_layout.addStretch()
        
        layout.addWidget(stats_widget)
    
    def create_chart_panel(self, layout):
        """پنل چارت"""
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)
        chart_layout.setSpacing(20)
        
        # Chart title
        chart_title = QLabel("📈 EUR/USD LIVE CHART")
        chart_title.setStyleSheet("""
            font-size: 20px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            margin-bottom: 10px;
        """)
        chart_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        chart_layout.addWidget(chart_title)
        
        # Chart
        self.chart = LiveChart()
        chart_layout.addWidget(self.chart)
        
        layout.addWidget(chart_widget, 2)
    
    def create_controls_panel(self, layout):
        """پنل کنترل‌ها"""
        controls_widget = QWidget()
        controls_widget.setFixedWidth(280)
        
        controls_layout = QVBoxLayout(controls_widget)
        controls_layout.setSpacing(20)
        
        # Title
        title = QLabel("🎮 CONTROLS")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 15px;
            text-align: center;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        controls_layout.addWidget(title)
        
        # Main controls
        start_btn = NeonButton("START", "🚀", "#7ED321")
        start_btn.clicked.connect(self.start_trading)
        controls_layout.addWidget(start_btn)
        
        stop_btn = NeonButton("STOP", "🛑", "#D0021B")
        stop_btn.clicked.connect(self.stop_trading)
        controls_layout.addWidget(stop_btn)
        
        settings_btn = NeonButton("SETTINGS", "⚙️", "#4A90E2")
        settings_btn.clicked.connect(self.open_settings)
        controls_layout.addWidget(settings_btn)
        
        # Spacer
        controls_layout.addSpacing(30)
        
        # Quick trade
        trade_title = QLabel("⚡ QUICK TRADE")
        trade_title.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
        """)
        trade_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        controls_layout.addWidget(trade_title)
        
        call_btn = NeonButton("CALL", "📈", "#7ED321")
        call_btn.clicked.connect(lambda: self.place_trade("CALL"))
        controls_layout.addWidget(call_btn)
        
        put_btn = NeonButton("PUT", "📉", "#D0021B")
        put_btn.clicked.connect(lambda: self.place_trade("PUT"))
        controls_layout.addWidget(put_btn)
        
        controls_layout.addStretch()
        
        layout.addWidget(controls_widget)
    
    def create_footer(self, layout):
        """ایجاد فوتر"""
        footer = QWidget()
        footer.setFixedHeight(60)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(0, 0, 0, 0)
        
        # Connection status
        status = QLabel("🔗 Connected to VIP BIG BANG Core")
        status.setStyleSheet("""
            color: #7ED321;
            font-weight: 600;
            font-size: 14px;
            text-shadow: 0 0 10px #7ED321;
        """)
        footer_layout.addWidget(status)
        
        footer_layout.addStretch()
        
        # Performance
        performance = QLabel("⚡ Performance: Excellent")
        performance.setStyleSheet("""
            color: #4A90E2;
            font-weight: 600;
            font-size: 14px;
        """)
        footer_layout.addWidget(performance)
        
        layout.addWidget(footer)
    
    def setup_timer(self):
        """تنظیم تایمر"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.start(3000)  # Every 3 seconds
        
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # Every second
    
    def update_data(self):
        """به‌روزرسانی داده‌ها"""
        # Update balance
        self.balance += random.uniform(-50, 100)
        self.balance_card.update_value(f"${self.balance:,.2f}")
        
        # Update winrate
        self.winrate += random.uniform(-0.3, 0.3)
        self.winrate = max(88, min(98, self.winrate))
        self.winrate_card.update_value(f"{self.winrate:.1f}%")
        
        # Update trades
        if random.random() < 0.4:  # 40% chance
            self.trades += 1
            self.trades_card.update_value(str(self.trades))
        
        # Update profit
        self.profit += random.uniform(-30, 80)
        self.profit_card.update_value(f"${self.profit:,.2f}")
    
    def update_time(self):
        """به‌روزرسانی زمان"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)
    
    def start_trading(self):
        """شروع تریدینگ"""
        QMessageBox.information(self, "🚀 Trading Started", 
            "VIP BIG BANG Ultra Modern Trading System Activated!\n\n"
            "✅ All systems online\n"
            "✅ Real-time analysis active\n"
            "✅ Risk management enabled")
    
    def stop_trading(self):
        """توقف تریدینگ"""
        QMessageBox.information(self, "🛑 Trading Stopped", 
            "VIP BIG BANG Trading System Deactivated!\n\n"
            "📊 Session summary will be generated\n"
            "💾 All data saved securely")
    
    def open_settings(self):
        """باز کردن تنظیمات"""
        QMessageBox.information(self, "⚙️ Settings", 
            "VIP BIG BANG Advanced Settings\n\n"
            "🎯 Risk Management\n"
            "📊 Analysis Parameters\n"
            "🎨 UI Customization\n"
            "🔔 Notifications")
    
    def place_trade(self, direction):
        """انجام معامله"""
        QMessageBox.information(self, f"📊 {direction} Trade", 
            f"✅ {direction} trade executed successfully!\n\n"
            f"💰 Amount: $50\n"
            f"⏱️ Duration: 60 seconds\n"
            f"📈 Expected return: 85%")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set modern style
    app.setStyle('Fusion')
    
    window = VIPUltraModernUI()
    window.show()
    
    sys.exit(app.exec())
