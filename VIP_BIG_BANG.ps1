# VIP BIG BANG - PowerShell Launcher
# Ultimate Trading Bot Interface

# Set console properties
$Host.UI.RawUI.WindowTitle = "VIP BIG BANG - Ultimate Trading Bot"
$Host.UI.RawUI.BackgroundColor = "DarkBlue"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

# Banner
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
Write-Host "║                                                              ║" -ForegroundColor Magenta
Write-Host "║                    🎮 VIP BIG BANG 🎮                        ║" -ForegroundColor Cyan
Write-Host "║                                                              ║" -ForegroundColor Magenta
Write-Host "║              Ultimate Trading Bot Interface                  ║" -ForegroundColor White
Write-Host "║                                                              ║" -ForegroundColor Magenta
Write-Host "║                     🚀 STARTING... 🚀                       ║" -ForegroundColor Yellow
Write-Host "║                                                              ║" -ForegroundColor Magenta
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check Python installation
Write-Host "🔍 Checking Python installation..." -ForegroundColor Yellow

if (Test-Command python) {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "📦 Please install Python from https://python.org" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check PySide6 installation
Write-Host "🔍 Checking PySide6..." -ForegroundColor Yellow

try {
    python -c "import PySide6" 2>$null
    Write-Host "✅ PySide6 is ready" -ForegroundColor Green
} catch {
    Write-Host "📦 Installing PySide6..." -ForegroundColor Yellow
    
    try {
        python -m pip install PySide6
        Write-Host "✅ PySide6 installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install PySide6" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""

# Check required files
Write-Host "📁 Checking required files..." -ForegroundColor Yellow

$requiredFiles = @("launch_vip.py", "vip_exact_ui.py", "vip_styles.py")

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file - Missing!" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""

# Launch VIP BIG BANG
Write-Host "🚀 Launching VIP BIG BANG..." -ForegroundColor Cyan
Write-Host "🎮 Get ready for the ultimate trading experience!" -ForegroundColor Yellow
Write-Host ""

try {
    python launch_vip.py
} catch {
    Write-Host ""
    Write-Host "❌ An error occurred while launching VIP BIG BANG" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# If we reach here, the application closed normally
Write-Host ""
Write-Host "👋 VIP BIG BANG closed. Thank you for trading!" -ForegroundColor Green
Read-Host "Press Enter to exit"
