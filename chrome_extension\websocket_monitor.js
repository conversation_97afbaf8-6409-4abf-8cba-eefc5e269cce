// 🔍 VIP BIG BANG - Advanced WebSocket Monitor
// Monitors and intercepts all WebSocket connections for real-time data

console.log('🔍 VIP BIG BANG WebSocket Monitor Loaded');

class AdvancedWebSocketMonitor {
    constructor() {
        this.originalWebSocket = window.WebSocket;
        this.monitoredSockets = new Map();
        this.quotexSockets = new Set();
        this.dataBuffer = [];
        this.maxBufferSize = 1000;
        this.vipWebSocket = null;
        this.init();
    }

    init() {
        this.interceptWebSocket();
        this.connectToVIPSystem();
        this.startDataProcessing();
        console.log('🚀 Advanced WebSocket Monitor initialized');
    }

    interceptWebSocket() {
        const self = this;

        // Store original WebSocket constructor
        const OriginalWebSocket = window.WebSocket;

        window.WebSocket = function(url, protocols) {
            console.log('🔗 WebSocket connection detected:', url);

            // Create socket with original constructor
            const socket = new OriginalWebSocket(url, protocols);
            const socketId = self.generateSocketId();

            // Handle Quotex WebSocket specifically
            if (url.includes('qxbroker.com') || url.includes('socket.io')) {
                console.log('📡 Quotex WebSocket intercepted:', url);

                // Override connection behavior for failed connections
                const originalOnError = socket.onerror;
                socket.onerror = function(event) {
                    console.log('⚠️ WebSocket error intercepted, attempting fallback');
                    self.handleQuotexWebSocketError(url, event);

                    if (originalOnError) {
                        originalOnError.call(this, event);
                    }
                };
            }
            
            // Store socket info
            self.monitoredSockets.set(socketId, {
                url: url,
                socket: socket,
                connected: false,
                messageCount: 0,
                lastMessage: null,
                isQuotex: self.isQuotexSocket(url)
            });

            // Monitor connection events
            const originalOnOpen = socket.onopen;
            socket.onopen = function(event) {
                console.log('✅ WebSocket connected:', url);
                const socketInfo = self.monitoredSockets.get(socketId);
                if (socketInfo) {
                    socketInfo.connected = true;
                    if (socketInfo.isQuotex) {
                        self.quotexSockets.add(socketId);
                        console.log('📡 Quotex WebSocket connected');
                    }
                }
                
                if (originalOnOpen) {
                    originalOnOpen.call(this, event);
                }
            };

            // Monitor messages
            const originalOnMessage = socket.onmessage;
            socket.onmessage = function(event) {
                const socketInfo = self.monitoredSockets.get(socketId);
                if (socketInfo) {
                    socketInfo.messageCount++;
                    socketInfo.lastMessage = Date.now();
                    
                    if (socketInfo.isQuotex) {
                        self.processQuotexMessage(event.data, url);
                    }
                }
                
                if (originalOnMessage) {
                    originalOnMessage.call(this, event);
                }
            };

            // Monitor close events
            const originalOnClose = socket.onclose;
            socket.onclose = function(event) {
                console.log('❌ WebSocket closed:', url);
                const socketInfo = self.monitoredSockets.get(socketId);
                if (socketInfo) {
                    socketInfo.connected = false;
                    if (socketInfo.isQuotex) {
                        self.quotexSockets.delete(socketId);
                    }
                }
                
                if (originalOnClose) {
                    originalOnClose.call(this, event);
                }
            };

            return socket;
        };

        // Preserve WebSocket prototype
        window.WebSocket.prototype = self.originalWebSocket.prototype;
        window.WebSocket.CONNECTING = self.originalWebSocket.CONNECTING;
        window.WebSocket.OPEN = self.originalWebSocket.OPEN;
        window.WebSocket.CLOSING = self.originalWebSocket.CLOSING;
        window.WebSocket.CLOSED = self.originalWebSocket.CLOSED;
    }

    generateSocketId() {
        return 'ws_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    isQuotexSocket(url) {
        const quotexPatterns = [
            'qxbroker.com',
            'quotex.io',
            'quotex.com',
            'socket.io',
            'ws2.qxbroker.com',
            'wss://ws'
        ];
        
        return quotexPatterns.some(pattern => url.includes(pattern));
    }

    processQuotexMessage(data, url) {
        try {
            let parsedData = null;
            
            // Try to parse as JSON
            if (typeof data === 'string') {
                if (data.startsWith('{') || data.startsWith('[')) {
                    parsedData = JSON.parse(data);
                } else if (data.includes('42[')) {
                    // Socket.IO format
                    const jsonStart = data.indexOf('[');
                    if (jsonStart !== -1) {
                        parsedData = JSON.parse(data.substring(jsonStart));
                    }
                }
            }

            const processedData = {
                timestamp: new Date().toISOString(),
                source: 'WEBSOCKET_MONITOR',
                url: url,
                rawData: data,
                parsedData: parsedData,
                dataType: typeof data,
                size: data.length || 0
            };

            // Add to buffer
            this.addToBuffer(processedData);
            
            // Send to VIP system immediately for real-time processing
            this.sendToVIPSystem(processedData);
            
            console.log('📊 Quotex data processed:', {
                url: url,
                size: processedData.size,
                type: processedData.dataType,
                hasJson: !!parsedData
            });

        } catch (error) {
            console.error('❌ Error processing Quotex message:', error);
        }
    }

    addToBuffer(data) {
        this.dataBuffer.push(data);
        
        // Maintain buffer size
        if (this.dataBuffer.length > this.maxBufferSize) {
            this.dataBuffer.shift();
        }
    }

    connectToVIPSystem() {
        try {
            this.vipWebSocket = new this.originalWebSocket('ws://localhost:8765');
            
            this.vipWebSocket.onopen = () => {
                console.log('✅ Connected to VIP BIG BANG System');
                this.sendToVIPSystem({
                    type: 'monitor_connected',
                    timestamp: new Date().toISOString(),
                    monitor: 'Advanced WebSocket Monitor'
                });
            };

            this.vipWebSocket.onclose = () => {
                console.log('❌ Disconnected from VIP BIG BANG System - reconnecting...');
                setTimeout(() => this.connectToVIPSystem(), 3000);
            };

            this.vipWebSocket.onerror = (error) => {
                console.error('❌ VIP WebSocket error:', error);
            };

        } catch (error) {
            console.error('❌ Failed to connect to VIP system:', error);
            setTimeout(() => this.connectToVIPSystem(), 5000);
        }
    }

    sendToVIPSystem(data) {
        if (this.vipWebSocket && this.vipWebSocket.readyState === this.originalWebSocket.OPEN) {
            try {
                this.vipWebSocket.send(JSON.stringify({
                    type: 'websocket_data',
                    data: data,
                    monitor: true
                }));
            } catch (error) {
                console.error('❌ Failed to send to VIP system:', error);
            }
        }
    }

    startDataProcessing() {
        // Process buffered data every 2 seconds
        setInterval(() => {
            if (this.dataBuffer.length > 0) {
                const summary = {
                    timestamp: new Date().toISOString(),
                    type: 'buffer_summary',
                    totalMessages: this.dataBuffer.length,
                    quotexSockets: this.quotexSockets.size,
                    monitoredSockets: this.monitoredSockets.size,
                    recentData: this.dataBuffer.slice(-5) // Last 5 messages
                };
                
                this.sendToVIPSystem(summary);
            }
        }, 2000);
    }

    handleQuotexWebSocketError(url, error) {
        console.log('🔧 Implementing WebSocket fallback for:', url);

        // Start DOM polling as fallback
        this.startDOMPolling();

        // Send error info to VIP system
        this.sendToVIPSystem({
            type: 'websocket_error',
            url: url,
            error: error.toString(),
            fallback: 'DOM_POLLING_ACTIVATED',
            timestamp: new Date().toISOString()
        });
    }

    startDOMPolling() {
        if (this.domPollingInterval) return;

        console.log('🔄 Starting DOM polling fallback...');

        this.domPollingInterval = setInterval(() => {
            try {
                const data = this.extractQuotexDataFromDOM();
                if (data && Object.keys(data).length > 0) {
                    this.sendToVIPSystem({
                        type: 'dom_polling_data',
                        data: data,
                        source: 'DOM_FALLBACK'
                    });
                }
            } catch (error) {
                console.error('❌ DOM polling error:', error);
            }
        }, 2000); // Poll every 2 seconds
    }

    extractQuotexDataFromDOM() {
        // Extract data using DOM selectors as fallback
        const selectors = {
            balance: [
                '[data-testid="balance"]',
                '.balance-value',
                '[class*="balance"]',
                '.header-balance'
            ],
            asset: [
                '[data-testid="asset-name"]',
                '.asset-name',
                '[class*="asset-name"]'
            ],
            price: [
                '[data-testid="current-price"]',
                '.current-price',
                '[class*="price"]'
            ]
        };

        const extractText = (selectorArray) => {
            for (const selector of selectorArray) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    return element.textContent.trim();
                }
            }
            return null;
        };

        return {
            balance: extractText(selectors.balance),
            currentAsset: extractText(selectors.asset),
            currentPrice: extractText(selectors.price),
            timestamp: new Date().toISOString(),
            source: 'DOM_FALLBACK'
        };
    }

    getStatus() {
        return {
            monitoredSockets: this.monitoredSockets.size,
            quotexSockets: this.quotexSockets.size,
            bufferSize: this.dataBuffer.length,
            vipConnected: this.vipWebSocket && this.vipWebSocket.readyState === this.originalWebSocket.OPEN,
            lastActivity: this.dataBuffer.length > 0 ? this.dataBuffer[this.dataBuffer.length - 1].timestamp : null,
            domPollingActive: !!this.domPollingInterval
        };
    }
}

// Initialize the monitor
const advancedWebSocketMonitor = new AdvancedWebSocketMonitor();

// Export for external access
window.vipWebSocketMonitor = advancedWebSocketMonitor;

console.log('✅ VIP BIG BANG Advanced WebSocket Monitor Ready');
