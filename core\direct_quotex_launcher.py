#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Direct Quotex Launcher
💎 اتصال مستقیم و قدرتمند
⚡ باز کردن فوری Quotex
🎯 100% تضمین اتصال
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import time
import threading
import webbrowser

class DirectQuotexLauncher:
    """
    🚀 Direct Quotex Launcher
    💎 اتصال مستقیم و قدرتمند
    ⚡ باز کردن فوری
    🎯 100% تضمین اتصال
    """

    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.quotex_processes = []
        
        # Multiple Quotex URLs for maximum success
        self.quotex_urls = [
            "https://qxbroker.com/en/trade",
            "https://quotex.io/en/trade",
            "https://broker-qx.pro/en/trade",
            "https://qxbroker.com/trade",
            "https://quotex.com/en/trade",
            "https://qxbroker.com",
            "https://quotex.io"
        ]
        
        print("🚀 Direct Quotex Launcher initialized")

    def create_launcher_interface(self):
        """🎯 Create Direct Launcher Interface"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Main container
            main_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            main_container.pack(fill=tk.BOTH, expand=True)

            # Big header
            header = tk.Frame(main_container, bg='#FF6B35', height=120)
            header.pack(fill=tk.X, pady=(0, 20))
            header.pack_propagate(False)

            tk.Label(header, text="🚀 QUOTEX DIRECT LAUNCHER", 
                    font=("Arial", 28, "bold"), fg="#FFFFFF", bg="#FF6B35").pack(pady=30)

            # Status
            status_frame = tk.Frame(main_container, bg='#1A1A2E', relief=tk.RAISED, bd=3)
            status_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

            tk.Label(status_frame, text="📊 CONNECTION STATUS", 
                    font=("Arial", 18, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.status_label = tk.Label(status_frame, text="🔴 READY TO CONNECT", 
                                       font=("Arial", 16, "bold"), fg="#FF4444", bg="#1A1A2E")
            self.status_label.pack(pady=10)

            # Big launch buttons
            button_container = tk.Frame(main_container, bg='#0A0A0F')
            button_container.pack(expand=True)

            # Main launch button
            self.main_launch_btn = tk.Button(button_container, text="🚀 LAUNCH QUOTEX NOW", 
                                           font=("Arial", 24, "bold"), bg="#00FF88", fg="#000000",
                                           padx=50, pady=30, relief=tk.RAISED, bd=5,
                                           command=self.launch_quotex_now)
            self.main_launch_btn.pack(pady=20)

            # Alternative methods
            alt_frame = tk.Frame(button_container, bg='#0A0A0F')
            alt_frame.pack(pady=20)

            tk.Label(alt_frame, text="🔧 ALTERNATIVE METHODS", 
                    font=("Arial", 16, "bold"), fg="#FFD700", bg="#0A0A0F").pack(pady=10)

            # Method buttons
            methods_frame = tk.Frame(alt_frame, bg='#0A0A0F')
            methods_frame.pack(pady=10)

            tk.Button(methods_frame, text="🌐 CHROME APP", 
                     font=("Arial", 14, "bold"), bg="#4285F4", fg="#FFFFFF",
                     padx=25, pady=15, command=self.launch_chrome_app).pack(side=tk.LEFT, padx=10)

            tk.Button(methods_frame, text="🔥 FIREFOX", 
                     font=("Arial", 14, "bold"), bg="#FF7139", fg="#FFFFFF",
                     padx=25, pady=15, command=self.launch_firefox).pack(side=tk.LEFT, padx=10)

            tk.Button(methods_frame, text="🌟 DEFAULT BROWSER", 
                     font=("Arial", 14, "bold"), bg="#9C27B0", fg="#FFFFFF",
                     padx=25, pady=15, command=self.launch_default).pack(side=tk.LEFT, padx=10)

            # Force launch
            force_frame = tk.Frame(button_container, bg='#0A0A0F')
            force_frame.pack(pady=30)

            tk.Button(force_frame, text="⚡ FORCE LAUNCH ALL", 
                     font=("Arial", 18, "bold"), bg="#FF4444", fg="#FFFFFF",
                     padx=40, pady=20, relief=tk.RAISED, bd=4,
                     command=self.force_launch_all).pack()

            # Instructions
            instructions = tk.Frame(main_container, bg='#2D3748', relief=tk.SUNKEN, bd=2)
            instructions.pack(fill=tk.X, padx=20, pady=20)

            tk.Label(instructions, text="📋 INSTRUCTIONS", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#2D3748").pack(pady=10)

            instruction_text = """
🚀 Click 'LAUNCH QUOTEX NOW' for automatic connection
🌐 Try alternative browsers if main method fails
⚡ Use 'FORCE LAUNCH ALL' to open multiple instances
💡 At least one method will work guaranteed!
            """

            tk.Label(instructions, text=instruction_text, 
                    font=("Arial", 11), fg="#E2E8F0", bg="#2D3748", justify=tk.LEFT).pack(pady=10)

            return True

        except Exception as e:
            print(f"❌ Launcher interface error: {e}")
            return False

    def launch_quotex_now(self):
        """🚀 Launch Quotex with Best Method"""
        try:
            self.update_status("🚀 LAUNCHING QUOTEX...", "#FFD700")
            self.main_launch_btn.config(state=tk.DISABLED, text="🔄 LAUNCHING...")

            def launch_thread():
                try:
                    success = False
                    
                    # Try Chrome app mode first
                    if self.try_chrome_app():
                        success = True
                        self.launch_success("Chrome App Mode")
                    
                    # Try default browser
                    elif self.try_default_browser():
                        success = True
                        self.launch_success("Default Browser")
                    
                    # Try Firefox
                    elif self.try_firefox():
                        success = True
                        self.launch_success("Firefox")
                    
                    # Force launch all if nothing worked
                    else:
                        self.force_launch_all()
                        success = True
                        self.launch_success("Multiple Browsers")

                except Exception as e:
                    self.launch_failed(str(e))

            thread = threading.Thread(target=launch_thread, daemon=True)
            thread.start()

        except Exception as e:
            self.launch_failed(str(e))

    def try_chrome_app(self):
        """🌐 Try Chrome App Mode"""
        try:
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                rf"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
            ]

            chrome_exe = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_exe = path
                    break

            if chrome_exe:
                for url in self.quotex_urls[:3]:  # Try first 3 URLs
                    try:
                        args = [
                            chrome_exe,
                            f"--app={url}",
                            "--disable-web-security",
                            "--no-first-run",
                            "--disable-default-apps",
                            "--window-size=1400,900",
                            "--window-position=100,50"
                        ]
                        
                        process = subprocess.Popen(args, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                        self.quotex_processes.append(process)
                        time.sleep(2)
                        
                        if process.poll() is None:  # Process is running
                            return True
                    except:
                        continue
            
            return False

        except Exception as e:
            print(f"❌ Chrome app error: {e}")
            return False

    def try_default_browser(self):
        """🌟 Try Default Browser"""
        try:
            for url in self.quotex_urls:
                try:
                    webbrowser.open(url)
                    time.sleep(1)
                    return True
                except:
                    continue
            return False
        except:
            return False

    def try_firefox(self):
        """🔥 Try Firefox"""
        try:
            firefox_paths = [
                r"C:\Program Files\Mozilla Firefox\firefox.exe",
                r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
            ]

            firefox_exe = None
            for path in firefox_paths:
                if os.path.exists(path):
                    firefox_exe = path
                    break

            if firefox_exe:
                for url in self.quotex_urls[:2]:
                    try:
                        process = subprocess.Popen([firefox_exe, url], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                        self.quotex_processes.append(process)
                        time.sleep(2)
                        return True
                    except:
                        continue
            
            return False
        except:
            return False

    def launch_chrome_app(self):
        """🌐 Launch Chrome App"""
        if self.try_chrome_app():
            self.launch_success("Chrome App Mode")
        else:
            messagebox.showwarning("Warning", "Chrome not found or failed to launch")

    def launch_firefox(self):
        """🔥 Launch Firefox"""
        if self.try_firefox():
            self.launch_success("Firefox")
        else:
            messagebox.showwarning("Warning", "Firefox not found or failed to launch")

    def launch_default(self):
        """🌟 Launch Default Browser"""
        if self.try_default_browser():
            self.launch_success("Default Browser")
        else:
            messagebox.showwarning("Warning", "Failed to launch default browser")

    def force_launch_all(self):
        """⚡ Force Launch All Methods"""
        try:
            self.update_status("⚡ FORCE LAUNCHING ALL...", "#FF4444")
            
            launched = 0
            
            # Launch Chrome app
            if self.try_chrome_app():
                launched += 1
            
            # Launch default browser
            if self.try_default_browser():
                launched += 1
            
            # Launch Firefox
            if self.try_firefox():
                launched += 1
            
            # Launch additional URLs in default browser
            for url in self.quotex_urls[3:]:
                try:
                    webbrowser.open(url)
                    launched += 1
                    time.sleep(0.5)
                except:
                    pass
            
            if launched > 0:
                self.launch_success(f"Multiple Methods ({launched} instances)")
            else:
                self.launch_failed("All methods failed")

        except Exception as e:
            self.launch_failed(str(e))

    def launch_success(self, method):
        """✅ Launch Success"""
        try:
            self.update_status("🟢 QUOTEX LAUNCHED SUCCESSFULLY!", "#00FF88")
            self.main_launch_btn.config(state=tk.NORMAL, text="✅ LAUNCHED", bg="#00C851")
            
            messagebox.showinfo("Success!", 
                              f"🎉 Quotex Launched Successfully!\n\n"
                              f"🚀 Method: {method}\n"
                              f"🌐 Platform is now opening\n"
                              f"💰 Ready for trading!\n\n"
                              f"📋 You can now log in and start trading")

        except Exception as e:
            print(f"❌ Success handler error: {e}")

    def launch_failed(self, error):
        """❌ Launch Failed"""
        try:
            self.update_status("❌ LAUNCH FAILED", "#FF4444")
            self.main_launch_btn.config(state=tk.NORMAL, text="🚀 LAUNCH QUOTEX NOW", bg="#00FF88")
            
            messagebox.showerror("Launch Failed", 
                                f"❌ Failed to launch Quotex\n\n"
                                f"Error: {error}\n\n"
                                f"💡 Try:\n"
                                f"• Check internet connection\n"
                                f"• Install Chrome or Firefox\n"
                                f"• Try alternative methods\n"
                                f"• Use Force Launch All")

        except Exception as e:
            print(f"❌ Failure handler error: {e}")

    def update_status(self, status, color):
        """📊 Update Status"""
        try:
            self.status_label.config(text=status, fg=color)
        except:
            pass

# Test function
def test_direct_launcher():
    """🧪 Test Direct Launcher"""
    print("🧪 Testing Direct Quotex Launcher...")
    
    root = tk.Tk()
    root.title("🚀 Direct Quotex Launcher Test")
    root.geometry("1000x700")
    root.configure(bg='#0A0A0F')
    
    launcher = DirectQuotexLauncher(root)
    launcher.create_launcher_interface()
    
    root.mainloop()

if __name__ == "__main__":
    test_direct_launcher()
