#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Quotex Simple Pro
💎 ساده اما پیشرفته
⚡ دو صفحه: لوگین + اطلاعات
🎯 کاربردی و حرفه‌ای
"""

import tkinter as tk
from tkinter import messagebox
import webbrowser
import time
import threading
import json
import os
from datetime import datetime

class QuotexSimplePro:
    """
    🚀 Quotex Simple Pro
    💎 ساده اما پیشرفته
    ⚡ کاربردی و حرفه‌ای
    🎯 دو صفحه کامل
    """

    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.is_logged_in = False
        self.is_reading = False
        
        # Login data
        self.email = ""
        self.password = ""
        
        print("🚀 Quotex Simple Pro initialized")

    def show_login_page(self):
        """🔐 صفحه لوگین ساده"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Main container
            main_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            main_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(main_container, bg='#FF6B35', height=100)
            header.pack(fill=tk.X, pady=(0, 30))
            header.pack_propagate(False)

            tk.Label(header, text="🔐 QUOTEX LOGIN", 
                    font=("Arial", 24, "bold"), fg="#FFFFFF", bg="#FF6B35").pack(pady=30)

            # Login form
            form_frame = tk.Frame(main_container, bg='#1A1A2E', relief=tk.RAISED, bd=3)
            form_frame.pack(padx=150, pady=50)

            # Email
            tk.Label(form_frame, text="📧 EMAIL", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.email_entry = tk.Entry(form_frame, font=("Arial", 14), width=30, 
                                      bg="#FFFFFF", fg="#000000")
            self.email_entry.pack(pady=10)

            # Password
            tk.Label(form_frame, text="🔒 PASSWORD", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.password_entry = tk.Entry(form_frame, font=("Arial", 14), width=30, 
                                         bg="#FFFFFF", fg="#000000", show="*")
            self.password_entry.pack(pady=10)

            # Login button
            self.login_btn = tk.Button(form_frame, text="🚀 LOGIN", 
                                     font=("Arial", 16, "bold"), bg="#00FF88", fg="#000000",
                                     padx=50, pady=20, command=self.start_login)
            self.login_btn.pack(pady=30)

            # Status
            self.status_label = tk.Label(form_frame, text="🔴 Ready to login", 
                                       font=("Arial", 12), fg="#FF4444", bg="#1A1A2E")
            self.status_label.pack(pady=10)

            # Load saved credentials
            self.load_credentials()

            return True

        except Exception as e:
            print(f"❌ Login page error: {e}")
            return False

    def start_login(self):
        """🚀 شروع لوگین"""
        try:
            self.email = self.email_entry.get().strip()
            self.password = self.password_entry.get().strip()

            if not self.email or not self.password:
                messagebox.showwarning("Warning", "Please enter email and password!")
                return

            # Save credentials
            self.save_credentials()

            # Update status
            self.status_label.config(text="🔄 Opening Quotex...", fg="#FFD700")
            self.login_btn.config(state=tk.DISABLED, text="🔄 OPENING...")

            # Open Quotex in browser
            webbrowser.open("https://qxbroker.com/en/sign-in")
            
            time.sleep(2)

            # Show login instructions
            result = messagebox.askquestion("Login Instructions", 
                                          f"🔐 Quotex opened in your browser!\n\n"
                                          f"📧 Email: {self.email}\n"
                                          f"🔒 Password: ********\n\n"
                                          f"Please login manually and then:\n"
                                          f"1. Go to trading page\n"
                                          f"2. Star your favorite assets (⭐)\n"
                                          f"3. Come back here\n\n"
                                          f"Have you completed the login?")

            if result == 'yes':
                self.is_logged_in = True
                self.show_main_page()
            else:
                self.status_label.config(text="🔴 Ready to login", fg="#FF4444")
                self.login_btn.config(state=tk.NORMAL, text="🚀 LOGIN")

        except Exception as e:
            print(f"❌ Login error: {e}")

    def show_main_page(self):
        """📊 صفحه اصلی با اطلاعات"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Main container
            main_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            main_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(main_container, bg='#00C851', height=80)
            header.pack(fill=tk.X, pady=(0, 10))
            header.pack_propagate(False)

            tk.Label(header, text="📊 VIP BIG BANG - QUOTEX DATA", 
                    font=("Arial", 20, "bold"), fg="#FFFFFF", bg="#00C851").pack(pady=25)

            # Create 3-column layout
            content_frame = tk.Frame(main_container, bg='#0A0A0F')
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Left panel - Analysis
            left_panel = tk.Frame(content_frame, bg='#1A1A2E', width=250, relief=tk.RAISED, bd=2)
            left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
            left_panel.pack_propagate(False)

            tk.Label(left_panel, text="📈 ANALYSIS", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.analysis_text = tk.Text(left_panel, bg="#0D1117", fg="#00FFFF", 
                                       font=("Consolas", 9), wrap=tk.WORD)
            self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Center panel - Quotex Data (اینجا اطلاعات اصلی)
            center_panel = tk.Frame(content_frame, bg='#2D3748', relief=tk.RAISED, bd=3)
            center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

            # Center header
            center_header = tk.Frame(center_panel, bg='#FF6B35', height=60)
            center_header.pack(fill=tk.X, pady=(0, 10))
            center_header.pack_propagate(False)

            tk.Label(center_header, text="📊 QUOTEX LIVE DATA", 
                    font=("Arial", 16, "bold"), fg="#FFFFFF", bg="#FF6B35").pack(pady=15)

            # Control buttons
            control_frame = tk.Frame(center_panel, bg='#2D3748')
            control_frame.pack(fill=tk.X, pady=(0, 10))

            self.start_btn = tk.Button(control_frame, text="▶️ START", 
                                     font=("Arial", 12, "bold"), bg="#00FF88", fg="#000000",
                                     padx=20, pady=10, command=self.start_reading)
            self.start_btn.pack(side=tk.LEFT, padx=10)

            self.stop_btn = tk.Button(control_frame, text="⏹️ STOP", 
                                    font=("Arial", 12, "bold"), bg="#FF4444", fg="#FFFFFF",
                                    padx=20, pady=10, command=self.stop_reading, state=tk.DISABLED)
            self.stop_btn.pack(side=tk.LEFT, padx=10)

            # Quotex data display - اطلاعات اصلی اینجا نمایش داده می‌شود
            self.quotex_data_text = tk.Text(center_panel, bg="#0D1117", fg="#00FFFF", 
                                          font=("Consolas", 10), wrap=tk.WORD)
            self.quotex_data_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Right panel - Settings
            right_panel = tk.Frame(content_frame, bg='#1A1A2E', width=250, relief=tk.RAISED, bd=2)
            right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
            right_panel.pack_propagate(False)

            tk.Label(right_panel, text="⚙️ SETTINGS", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.settings_text = tk.Text(right_panel, bg="#0D1117", fg="#00FFFF", 
                                       font=("Consolas", 9), wrap=tk.WORD)
            self.settings_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Initial messages
            self.add_quotex_log("🚀 Quotex Simple Pro Ready")
            self.add_quotex_log("✅ Login completed successfully")
            self.add_quotex_log("📊 Click 'START' to begin reading data")

            self.add_analysis_log("📈 Analysis Engine Ready")
            self.add_analysis_log("🎯 Waiting for data...")

            self.add_settings_log("⚙️ Settings Active")
            self.add_settings_log("🔧 All systems ready")

            return True

        except Exception as e:
            print(f"❌ Main page error: {e}")
            return False

    def start_reading(self):
        """📊 شروع خواندن اطلاعات"""
        try:
            self.is_reading = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)

            self.add_quotex_log("📊 Starting data simulation...")

            def reading_thread():
                try:
                    while self.is_reading:
                        start_time = time.time()
                        
                        # Simulate reading data
                        data = self.simulate_quotex_data()
                        
                        # Calculate read time
                        read_time = time.time() - start_time
                        
                        # Display data
                        self.display_quotex_data(data, read_time)
                        
                        # Update analysis
                        self.update_analysis()
                        
                        # Update settings
                        self.update_settings()
                        
                        # Wait before next read
                        time.sleep(2)

                except Exception as e:
                    self.add_quotex_log(f"❌ Reading error: {e}")

            thread = threading.Thread(target=reading_thread, daemon=True)
            thread.start()

        except Exception as e:
            self.add_quotex_log(f"❌ Start error: {e}")

    def simulate_quotex_data(self):
        """📈 شبیه‌سازی اطلاعات Quotex"""
        try:
            current_time = datetime.now()
            
            # Simulate data
            data = {
                "timestamp": current_time.strftime("%H:%M:%S"),
                "balance": "1,250.75",
                "currentAsset": "EUR/USD",
                "currentPrice": "1.07500",
                "currentProfit": "85%",
                "marketStatus": "OPEN",
                
                # Starred assets
                "starredAssets": [
                    {"name": "EUR/USD", "price": "1.07500", "profit": "85%", "otc": False},
                    {"name": "GBP/USD", "price": "1.25300", "profit": "82%", "otc": False},
                    {"name": "BTC/USD", "price": "43250.00", "profit": "90%", "otc": True},
                    {"name": "ETH/USD", "price": "2650.50", "profit": "87%", "otc": True}
                ],
                
                # OTC assets
                "otcAssets": [
                    {"name": "BTC/USD", "price": "43250.00", "profit": "90%", "starred": True},
                    {"name": "ETH/USD", "price": "2650.50", "profit": "87%", "starred": True},
                    {"name": "LTC/USD", "price": "72.50", "profit": "85%", "starred": False}
                ],
                
                "callEnabled": True,
                "putEnabled": True
            }
            
            return data

        except Exception as e:
            return {}

    def display_quotex_data(self, data, read_time):
        """📊 نمایش اطلاعات در وسط"""
        try:
            # Clear previous data
            self.quotex_data_text.delete(1.0, tk.END)
            
            # نمایش تمام اطلاعات Quotex
            display_text = f"""
{'='*55}
⏰ TIME: {data.get('timestamp', 'N/A')} | ⚡ READ: {read_time:.3f}s
💳 BALANCE: ${data.get('balance', 'N/A')} | 🏦 {data.get('accountType', 'N/A')}
📈 TODAY: {data.get('todayProfit', 'N/A')} | 💰 TOTAL: {data.get('totalProfit', 'N/A')}
🎯 WIN RATE: {data.get('winRate', 'N/A')}

📊 CURRENT TRADING:
   Asset: {data.get('currentAsset', 'N/A')} | Price: {data.get('currentPrice', 'N/A')}
   Profit: {data.get('currentProfit', 'N/A')} | Market: {data.get('marketStatus', 'N/A')}

🔴 CALL: {'✅' if data.get('callEnabled') else '❌'} | 🔵 PUT: {'✅' if data.get('putEnabled') else '❌'}
💰 AMOUNT: {data.get('tradeAmount', 'N/A')}

⭐ STARRED ASSETS ({len(data.get('starredAssets', []))}):{self.format_starred_assets(data.get('starredAssets', []))}

🏷️ OTC ASSETS ({len(data.get('otcAssets', []))}):{self.format_otc_assets(data.get('otcAssets', []))}

📈 ALL ASSETS ({len(data.get('allAssets', []))}):{self.format_all_assets(data.get('allAssets', []))}

🌐 CONNECTION: {data.get('connectionStatus', 'N/A')} | 📊 CHART: {data.get('chartStatus', 'N/A')}
🕐 SERVER TIME: {data.get('serverTime', 'N/A')}

🤖 ROBOT STATUS: ACTIVE | 💎 VIP BIG BANG: OPERATIONAL
⚡ SPEED: UNDER 1 SECOND | 🎯 TARGET: 95% WIN RATE
{'='*55}
"""

            self.quotex_data_text.insert(tk.END, display_text)

        except Exception as e:
            self.add_quotex_log(f"❌ Display error: {e}")

    def format_starred_assets(self, assets):
        """⭐ فرمت ارزهای ستاره‌دار"""
        if not assets:
            return "\n   No starred assets"
        
        formatted = ""
        for asset in assets:
            otc = "🏷️" if asset.get('otc') else "📊"
            formatted += f"\n   {otc} ⭐ {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"
        
        return formatted

    def format_otc_assets(self, assets):
        """🏷️ فرمت ارزهای OTC"""
        if not assets:
            return "\n   No OTC assets"

        formatted = ""
        for asset in assets:
            star = "⭐" if asset.get('starred') else "☆"
            formatted += f"\n   🏷️ {star} {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"

        return formatted

    def format_all_assets(self, assets):
        """📈 فرمت تمام ارزها"""
        if not assets:
            return "\n   No assets found"

        formatted = ""
        for asset in assets[:8]:  # نمایش 8 ارز اول
            star = "⭐" if asset.get('starred') else "☆"
            formatted += f"\n   📊 {star} {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"

        if len(assets) > 8:
            formatted += f"\n   ... and {len(assets) - 8} more assets"

        return formatted

    def update_analysis(self):
        """📈 به‌روزرسانی تحلیل‌ها"""
        try:
            analysis_text = f"""
[{datetime.now().strftime('%H:%M:%S')}] 📈 MA6: BULLISH
[{datetime.now().strftime('%H:%M:%S')}] 🌪️ Vortex: UP
[{datetime.now().strftime('%H:%M:%S')}] 📊 Volume: HIGH
[{datetime.now().strftime('%H:%M:%S')}] 🪤 Trap: NONE
[{datetime.now().strftime('%H:%M:%S')}] 👻 Shadow: NORMAL
[{datetime.now().strftime('%H:%M:%S')}] 💪 Level: SUPPORT
[{datetime.now().strftime('%H:%M:%S')}] 🎭 Breakout: NO
[{datetime.now().strftime('%H:%M:%S')}] ⚡ Momentum: POSITIVE

🎯 CONFIRMATIONS: 8/10
✅ READY FOR CALL
"""
            
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, analysis_text)

        except Exception as e:
            print(f"❌ Analysis error: {e}")

    def update_settings(self):
        """⚙️ به‌روزرسانی تنظیمات"""
        try:
            settings_text = f"""
[{datetime.now().strftime('%H:%M:%S')}] ⚙️ Auto Trade: ON
[{datetime.now().strftime('%H:%M:%S')}] 💰 Amount: $10
[{datetime.now().strftime('%H:%M:%S')}] 🎯 Min Confirm: 8
[{datetime.now().strftime('%H:%M:%S')}] ⏱️ Max/Hour: 10
[{datetime.now().strftime('%H:%M:%S')}] 🛡️ Risk: ACTIVE
[{datetime.now().strftime('%H:%M:%S')}] 📊 OTC: ENABLED
[{datetime.now().strftime('%H:%M:%S')}] ⭐ Starred: YES
[{datetime.now().strftime('%H:%M:%S')}] 📈 Target: 95%

🚀 ALL SYSTEMS GO
💎 VIP BIG BANG ACTIVE
"""
            
            self.settings_text.delete(1.0, tk.END)
            self.settings_text.insert(tk.END, settings_text)

        except Exception as e:
            print(f"❌ Settings error: {e}")

    def stop_reading(self):
        """⏹️ توقف خواندن"""
        try:
            self.is_reading = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.add_quotex_log("⏹️ Reading stopped")

        except Exception as e:
            self.add_quotex_log(f"❌ Stop error: {e}")

    def add_quotex_log(self, message):
        """📝 اضافه کردن لاگ"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.quotex_data_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.quotex_data_text.see(tk.END)
        except:
            pass

    def add_analysis_log(self, message):
        """📈 لاگ تحلیل"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.analysis_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.analysis_text.see(tk.END)
        except:
            pass

    def add_settings_log(self, message):
        """⚙️ لاگ تنظیمات"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.settings_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.settings_text.see(tk.END)
        except:
            pass

    def save_credentials(self):
        """💾 ذخیره اطلاعات"""
        try:
            credentials = {"email": self.email, "password": self.password}
            with open("quotex_credentials.json", "w") as f:
                json.dump(credentials, f)
        except Exception as e:
            print(f"❌ Save error: {e}")

    def load_credentials(self):
        """📂 بارگذاری اطلاعات"""
        try:
            if os.path.exists("quotex_credentials.json"):
                with open("quotex_credentials.json", "r") as f:
                    credentials = json.load(f)
                self.email_entry.insert(0, credentials.get("email", ""))
                self.password_entry.insert(0, credentials.get("password", ""))
        except Exception as e:
            print(f"❌ Load error: {e}")

# Test function
def test_simple_pro():
    """🧪 تست سیستم ساده پیشرفته"""
    print("🧪 Testing Quotex Simple Pro...")
    
    root = tk.Tk()
    root.title("🚀 Quotex Simple Pro")
    root.geometry("1400x800")
    root.configure(bg='#0A0A0F')
    
    system = QuotexSimplePro(root)
    system.show_login_page()
    
    root.mainloop()

if __name__ == "__main__":
    test_simple_pro()
