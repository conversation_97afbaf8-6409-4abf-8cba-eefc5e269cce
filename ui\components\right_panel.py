"""
Professional Right Panel Component for VIP BIG BANG
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from .base_component import BaseComponent, VIPPanel, VIPButton

class VIPRightPanel(BaseComponent):
    """Professional right panel component exactly matching the design"""
    
    # Signals for button clicks
    autotrade_clicked = Signal()
    confirm_mode_clicked = Signal()
    heatmap_clicked = Signal()
    economic_news_clicked = Signal()
    can_clicked = Signal()
    settings_clicked = Signal()
    secures_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def _setup_component(self):
        """Setup the right panel component"""
        self.apply_style("vip-panel")
        self.setFixedWidth(200)
        
        # Main vertical layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Create 4x2 grid of control buttons
        grid_layout = QGridLayout()
        grid_layout.setSpacing(10)
        
        # Define buttons exactly as in the image
        buttons_config = [
            # Row 0
            (0, 0, "🚀", "AutoTrade", self.autotrade_clicked),
            (0, 1, "✓", "Confirm Mode", self.confirm_mode_clicked),
            
            # Row 1  
            (1, 0, "🚀", "Confirm Mode", self.confirm_mode_clicked),  # Duplicate in image
            (1, 1, "🔥", "Heatmap", self.heatmap_clicked),
            
            # Row 2
            (2, 0, "📊", "Economic News", self.economic_news_clicked),
            (2, 1, "😊", "Can", self.can_clicked),
            
            # Row 3
            (3, 0, "⚙️", "Settings", self.settings_clicked),
            (3, 1, "🔒", "Secures", self.secures_clicked),
        ]
        
        # Create and add buttons
        self.control_buttons = {}
        for row, col, icon, text, signal in buttons_config:
            button = VIPButton(text, icon, "control")
            button.set_fixed_size_square(80)
            button.clicked.connect(signal.emit)
            
            grid_layout.addWidget(button, row, col)
            self.control_buttons[text.lower().replace(" ", "_")] = button
        
        layout.addLayout(grid_layout)
        layout.addStretch()
    
    def set_button_state(self, button_name: str, enabled: bool):
        """Set the enabled state of a specific button"""
        button_key = button_name.lower().replace(" ", "_")
        if button_key in self.control_buttons:
            self.control_buttons[button_key].setEnabled(enabled)
    
    def set_button_active(self, button_name: str, active: bool):
        """Set the active state of a specific button"""
        button_key = button_name.lower().replace(" ", "_")
        if button_key in self.control_buttons:
            button = self.control_buttons[button_key]
            if active:
                button.setProperty("class", "vip-btn-active")
            else:
                button.setProperty("class", "vip-btn-control")
            
            # Refresh button style
            button.style().unpolish(button)
            button.style().polish(button)
    
    def get_button(self, button_name: str) -> VIPButton:
        """Get a specific button by name"""
        button_key = button_name.lower().replace(" ", "_")
        return self.control_buttons.get(button_key)
    
    def enable_all_buttons(self):
        """Enable all control buttons"""
        for button in self.control_buttons.values():
            button.setEnabled(True)
    
    def disable_all_buttons(self):
        """Disable all control buttons"""
        for button in self.control_buttons.values():
            button.setEnabled(False)
    
    def set_autotrade_active(self, active: bool):
        """Set autotrade button active state"""
        self.set_button_active("AutoTrade", active)
    
    def set_confirm_mode_active(self, active: bool):
        """Set confirm mode button active state"""
        self.set_button_active("Confirm Mode", active)
    
    def set_heatmap_active(self, active: bool):
        """Set heatmap button active state"""
        self.set_button_active("Heatmap", active)
    
    def set_economic_news_active(self, active: bool):
        """Set economic news button active state"""
        self.set_button_active("Economic News", active)
    
    def set_settings_active(self, active: bool):
        """Set settings button active state"""
        self.set_button_active("Settings", active)
    
    def set_secures_active(self, active: bool):
        """Set secures button active state"""
        self.set_button_active("Secures", active)
