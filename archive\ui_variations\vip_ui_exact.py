import sys
from PySide6.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QGridLayout, QLabel, QPushButton,
                               QFrame, QProgressBar)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class VIPBigBangExactUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VIP BIG BANG Enterprise")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set exact purple gradient from image
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4A2C7A, stop:0.5 #3D1A6B, stop:1 #2D1B69);
                color: white;
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout with exact proportions
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # Create header
        self.create_header(main_layout)
        
        # Create main content area
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)
        
        # Create panels with exact proportions
        self.create_left_panel(content_layout)
        self.create_center_panel(content_layout)
        self.create_right_panel(content_layout)
        
        main_layout.addLayout(content_layout)
    
    def create_header(self, main_layout):
        """Create exact header from image"""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        # Left side - Currency pairs
        currency_layout = QHBoxLayout()
        
        # BUG/USD with green checkmark
        bug_btn = QPushButton("✓ BUG/USD")
        bug_btn.setStyleSheet("""
            QPushButton {
                background: rgba(76, 175, 80, 0.3);
                border: 1px solid #4CAF50;
                border-radius: 8px;
                padding: 8px 12px;
                color: #4CAF50;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        currency_layout.addWidget(bug_btn)
        
        # Other currency pairs
        pairs = ["GBP/USD", "EUR/JPY", "LIVE"]
        for pair in pairs:
            btn = QPushButton(pair)
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255,255,255,0.1);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 8px;
                    padding: 8px 12px;
                    color: white;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background: rgba(255,255,255,0.2);
                }
            """)
            currency_layout.addWidget(btn)
        
        header_layout.addLayout(currency_layout)
        header_layout.addStretch()
        
        # Center - VIP BIG BANG title
        title = QLabel("VIP BIG BANG")
        title.setStyleSheet("""
            font-size: 28px; 
            font-weight: bold; 
            color: white;
            letter-spacing: 2px;
        """)
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Right side - Mode buttons and BUY/SELL
        right_layout = QHBoxLayout()
        
        # Mode buttons
        modes = [("OTC", False), ("LIVE", True), ("DEMO", False)]
        for mode, active in modes:
            btn = QPushButton(mode)
            if active:
                btn.setStyleSheet("""
                    QPushButton {
                        background: rgba(76, 175, 80, 0.3);
                        border: 1px solid #4CAF50;
                        border-radius: 8px;
                        padding: 8px 15px;
                        color: #4CAF50;
                        font-weight: bold;
                        font-size: 12px;
                    }
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        background: rgba(255,255,255,0.1);
                        border: 1px solid rgba(255,255,255,0.3);
                        border-radius: 8px;
                        padding: 8px 15px;
                        color: white;
                        font-size: 12px;
                    }
                """)
            right_layout.addWidget(btn)
        
        # BUY label
        buy_label = QLabel("BUY")
        buy_label.setStyleSheet("color: white; font-size: 14px; margin: 0 10px;")
        right_layout.addWidget(buy_label)
        
        # BUY button
        buy_btn = QPushButton("BUY")
        buy_btn.setStyleSheet("""
            QPushButton {
                background: #4CAF50;
                border: none;
                border-radius: 20px;
                padding: 10px 25px;
                font-weight: bold;
                color: white;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: #45a049;
            }
        """)
        right_layout.addWidget(buy_btn)
        
        # SELL button
        sell_btn = QPushButton("SELL")
        sell_btn.setStyleSheet("""
            QPushButton {
                background: #F44336;
                border: none;
                border-radius: 20px;
                padding: 10px 25px;
                font-weight: bold;
                color: white;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: #da190b;
            }
        """)
        right_layout.addWidget(sell_btn)
        
        # Menu button
        menu_btn = QPushButton("≡")
        menu_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                padding: 8px 12px;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        right_layout.addWidget(menu_btn)
        
        header_layout.addLayout(right_layout)
        main_layout.addLayout(header_layout)
    
    def create_left_panel(self, content_layout):
        """Create exact left panel from image"""
        left_widget = QWidget()
        left_widget.setFixedWidth(200)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # Manual Trading section
        manual_frame = self.create_rounded_frame()
        manual_layout = QVBoxLayout(manual_frame)
        
        # Manual Trading header with icon
        manual_header = QHBoxLayout()
        manual_icon = QLabel("🖱️")
        manual_icon.setStyleSheet("font-size: 20px;")
        manual_header.addWidget(manual_icon)
        
        manual_title = QLabel("Manual Trading")
        manual_title.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        manual_header.addWidget(manual_title)
        manual_header.addStretch()
        
        manual_layout.addLayout(manual_header)
        
        # Toggle switch (green ON state)
        toggle_widget = QWidget()
        toggle_widget.setFixedHeight(40)
        toggle_widget.setStyleSheet("""
            QWidget {
                background: #4CAF50;
                border-radius: 20px;
                margin: 5px 20px;
            }
        """)
        manual_layout.addWidget(toggle_widget)
        
        left_layout.addWidget(manual_frame)
        
        # Account Summary section
        account_frame = self.create_rounded_frame()
        account_layout = QVBoxLayout(account_frame)
        
        account_title = QLabel("Account Summary")
        account_title.setStyleSheet("font-size: 12px; color: rgba(255,255,255,0.8);")
        account_layout.addWidget(account_title)
        
        balance_label = QLabel("$1251,76")
        balance_label.setStyleSheet("""
            font-size: 24px; 
            font-weight: bold; 
            color: #4CAF50;
            margin: 5px 0;
        """)
        account_layout.addWidget(balance_label)
        
        left_layout.addWidget(account_frame)
        
        # AutoTrade section
        autotrade_frame = self.create_rounded_frame()
        autotrade_layout = QVBoxLayout(autotrade_frame)
        
        autotrade_title = QLabel("AutoTrade ON")
        autotrade_title.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        autotrade_layout.addWidget(autotrade_title)
        
        trade_info = QLabel("Trade $    +5")
        trade_info.setStyleSheet("font-size: 12px; color: rgba(255,255,255,0.8);")
        autotrade_layout.addWidget(trade_info)
        
        profit_info = QLabel("Profit / Loss +10")
        profit_info.setStyleSheet("font-size: 12px; color: rgba(255,255,255,0.8);")
        autotrade_layout.addWidget(profit_info)
        
        left_layout.addWidget(autotrade_frame)
        
        # PulseBar section
        pulsebar_frame = self.create_rounded_frame()
        pulsebar_layout = QVBoxLayout(pulsebar_frame)
        
        pulsebar_title = QLabel("PulseBar")
        pulsebar_title.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        pulsebar_layout.addWidget(pulsebar_title)
        
        # Color bars exactly like in image
        colors = ["#FF4444", "#FF8844", "#FFDD44", "#88FF44", "#44FF88", "#44FFFF", "#4488FF"]
        for color in colors:
            bar = QWidget()
            bar.setFixedHeight(6)
            bar.setStyleSheet(f"""
                background: {color};
                border-radius: 3px;
                margin: 1px 0;
            """)
            pulsebar_layout.addWidget(bar)
        
        left_layout.addWidget(pulsebar_frame)
        
        left_layout.addStretch()
        content_layout.addWidget(left_widget)
    
    def create_center_panel(self, content_layout):
        """Create exact center panel with chart from image"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(10)

        # Main chart area
        chart_frame = self.create_rounded_frame()
        chart_frame.setMinimumHeight(350)
        chart_layout = QVBoxLayout(chart_frame)

        # Price and alert section
        price_layout = QHBoxLayout()

        # Alert bell (yellow)
        alert_bell = QLabel("🔔")
        alert_bell.setStyleSheet("font-size: 24px; color: #FFD700;")
        price_layout.addWidget(alert_bell)

        price_layout.addStretch()

        # Current price display
        price_display = QLabel("1.07329")
        price_display.setStyleSheet("""
            background: #4CAF50;
            border-radius: 12px;
            padding: 8px 20px;
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)
        price_layout.addWidget(price_display)

        # Price details
        price_details = QVBoxLayout()
        price_high = QLabel("1.07325")
        price_low = QLabel("1.07320")
        price_current = QLabel("1.07320")
        price_target = QLabel("1.07330")

        for label in [price_high, price_low, price_current, price_target]:
            label.setStyleSheet("font-size: 10px; color: rgba(255,255,255,0.7);")
            price_details.addWidget(label)

        price_layout.addLayout(price_details)
        chart_layout.addLayout(price_layout)

        # Chart area placeholder (will be replaced with actual chart)
        chart_area = QLabel()
        chart_area.setMinimumHeight(250)
        chart_area.setStyleSheet("""
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.1);
        """)

        # Add candlestick pattern simulation
        chart_content = QVBoxLayout(chart_area)
        chart_placeholder = QLabel("📊 CANDLESTICK CHART")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("color: rgba(255,255,255,0.5); font-size: 18px;")
        chart_content.addWidget(chart_placeholder)

        chart_layout.addWidget(chart_area)
        center_layout.addWidget(chart_frame)

        # Bottom indicators section
        indicators_layout = QHBoxLayout()
        indicators_layout.setSpacing(10)

        # VORTEX indicator
        vortex_frame = self.create_rounded_frame()
        vortex_frame.setFixedWidth(120)
        vortex_layout = QVBoxLayout(vortex_frame)

        vortex_title = QLabel("VORTEX")
        vortex_title.setStyleSheet("font-size: 12px; font-weight: bold; color: white;")
        vortex_layout.addWidget(vortex_title)

        # Vortex wave visualization
        vortex_wave = QLabel("〰️〰️〰️〰️〰️")
        vortex_wave.setStyleSheet("color: #4CAF50; font-size: 14px;")
        vortex_layout.addWidget(vortex_wave)

        vortex_value = QLabel("0.0436")
        vortex_value.setStyleSheet("font-size: 12px; color: rgba(255,255,255,0.8);")
        vortex_layout.addWidget(vortex_value)

        indicators_layout.addWidget(vortex_frame)

        # Economic News
        news_frame = self.create_rounded_frame()
        news_frame.setFixedWidth(100)
        news_layout = QVBoxLayout(news_frame)

        news_icon = QLabel("📊")
        news_icon.setAlignment(Qt.AlignCenter)
        news_icon.setStyleSheet("font-size: 24px;")
        news_layout.addWidget(news_icon)

        news_title = QLabel("Economic\nNews")
        news_title.setAlignment(Qt.AlignCenter)
        news_title.setStyleSheet("font-size: 10px; color: white; font-weight: bold;")
        news_layout.addWidget(news_title)

        indicators_layout.addWidget(news_frame)

        # Live Signals
        signals_frame = self.create_rounded_frame()
        signals_frame.setFixedWidth(150)
        signals_layout = QVBoxLayout(signals_frame)

        signals_title = QLabel("LIVE SIGNALS")
        signals_title.setStyleSheet("font-size: 12px; font-weight: bold; color: white;")
        signals_layout.addWidget(signals_title)

        buy_signal = QLabel("BUY    71%")
        buy_signal.setStyleSheet("font-size: 14px; color: #4CAF50; font-weight: bold;")
        signals_layout.addWidget(buy_signal)

        signal_percent = QLabel("71%    29%")
        signal_percent.setStyleSheet("font-size: 12px; color: rgba(255,255,255,0.8);")
        signals_layout.addWidget(signal_percent)

        indicators_layout.addWidget(signals_frame)

        # Buyer/Seller Power
        power_frame = self.create_rounded_frame()
        power_layout = QVBoxLayout(power_frame)

        power_title = QLabel("Buyer/Seller Power")
        power_title.setStyleSheet("font-size: 12px; font-weight: bold; color: white;")
        power_layout.addWidget(power_title)

        # Power bar (green section)
        power_bar_container = QWidget()
        power_bar_container.setFixedHeight(20)
        power_bar_container.setStyleSheet("""
            background: #666;
            border-radius: 10px;
        """)

        # Green portion (34%)
        power_green = QWidget(power_bar_container)
        power_green.setGeometry(0, 0, int(power_bar_container.width() * 0.34), 20)
        power_green.setStyleSheet("""
            background: #4CAF50;
            border-radius: 10px;
        """)

        power_layout.addWidget(power_bar_container)

        power_values = QLabel("34%        66%")
        power_values.setStyleSheet("font-size: 12px; color: rgba(255,255,255,0.8);")
        power_layout.addWidget(power_values)

        indicators_layout.addWidget(power_frame)

        center_layout.addLayout(indicators_layout)
        content_layout.addWidget(center_widget, 2)  # Takes 2/4 of space

    def create_right_panel(self, content_layout):
        """Create exact right panel with 4x2 grid from image"""
        right_widget = QWidget()
        right_widget.setFixedWidth(200)
        right_layout = QGridLayout(right_widget)
        right_layout.setSpacing(10)

        # Control buttons exactly like in image
        buttons_data = [
            (0, 0, "🚀", "AutoTrade", "#4CAF50"),
            (0, 1, "✓", "Confirm Mode", "#9C27B0"),
            (1, 0, "🚀", "Confirm Mode", "#FF5722"),
            (1, 1, "🔥", "Heatmap", "#FF9800"),
            (2, 0, "📊", "Economic News", "#2196F3"),
            (2, 1, "😊", "Can", "#FFC107"),
            (3, 0, "⚙️", "Settings", "#9E9E9E"),
            (3, 1, "🔒", "Secures", "#795548")
        ]

        for row, col, icon, text, color in buttons_data:
            btn = QPushButton()
            btn.setFixedSize(85, 85)

            # Create button content
            btn_layout = QVBoxLayout()

            icon_label = QLabel(icon)
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("font-size: 24px; margin-bottom: 5px;")

            text_label = QLabel(text)
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setStyleSheet("font-size: 10px; color: white; font-weight: bold;")
            text_label.setWordWrap(True)

            # Set button style with specific color
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: rgba(255,255,255,0.1);
                    border: 1px solid rgba(255,255,255,0.2);
                    border-radius: 15px;
                    color: white;
                }}
                QPushButton:hover {{
                    background: {color};
                    border: 1px solid {color};
                }}
            """)

            # Add icon and text to button
            btn.setText(f"{icon}\n{text}")
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: rgba(255,255,255,0.1);
                    border: 1px solid rgba(255,255,255,0.2);
                    border-radius: 15px;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background: {color};
                    border: 1px solid {color};
                }}
            """)

            right_layout.addWidget(btn, row, col)

        content_layout.addWidget(right_widget)

    def create_rounded_frame(self):
        """Create rounded frame like in image"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 15px;
                padding: 15px;
            }
        """)
        return frame

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = VIPBigBangExactUI()
    window.show()
    sys.exit(app.exec())
