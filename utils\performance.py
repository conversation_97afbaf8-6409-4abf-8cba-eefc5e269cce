"""
VIP BIG BANG Enterprise - Performance Monitor
Advanced performance monitoring and optimization
"""

import time
import threading
import psutil
import gc
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import deque, defaultdict
import logging
from contextlib import contextmanager
import functools

class PerformanceMonitor:
    """
    Enterprise-level performance monitoring system
    Tracks CPU, memory, execution times, and system health
    """
    
    def __init__(self):
        self.logger = logging.getLogger("PerformanceMonitor")
        
        # Performance metrics storage
        self.metrics = {
            'cpu_usage': deque(maxlen=1000),
            'memory_usage': deque(maxlen=1000),
            'execution_times': defaultdict(lambda: deque(maxlen=100)),
            'function_calls': defaultdict(int),
            'error_counts': defaultdict(int),
            'system_health': deque(maxlen=100)
        }
        
        # Performance thresholds
        self.thresholds = {
            'cpu_warning': 80.0,      # CPU usage %
            'cpu_critical': 95.0,
            'memory_warning': 80.0,   # Memory usage %
            'memory_critical': 95.0,
            'execution_warning': 5.0, # Execution time seconds
            'execution_critical': 10.0
        }
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread = None
        self.start_time = datetime.now()
        
        # Performance alerts
        self.alerts = deque(maxlen=100)
        
        # Thread safety
        self.lock = threading.Lock()
        
        self.logger.info("Performance Monitor initialized")
    
    def start_monitoring(self, interval: float = 1.0):
        """Start continuous performance monitoring"""
        if self.monitoring_active:
            self.logger.warning("Performance monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True,
            name="PerformanceMonitor"
        )
        self.monitor_thread.start()
        
        self.logger.info(f"Performance monitoring started (interval: {interval}s)")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Check thresholds and generate alerts
                self._check_performance_thresholds()
                
                # Perform garbage collection if needed
                self._manage_memory()
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Process-specific metrics
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            process_cpu = process.cpu_percent()
            
            timestamp = datetime.now()
            
            with self.lock:
                self.metrics['cpu_usage'].append({
                    'timestamp': timestamp,
                    'system_cpu': cpu_percent,
                    'process_cpu': process_cpu
                })
                
                self.metrics['memory_usage'].append({
                    'timestamp': timestamp,
                    'system_memory': memory_percent,
                    'process_memory_mb': process_memory,
                    'available_memory_gb': memory.available / 1024 / 1024 / 1024
                })
                
                # System health score (0-100)
                health_score = self._calculate_health_score(cpu_percent, memory_percent)
                self.metrics['system_health'].append({
                    'timestamp': timestamp,
                    'score': health_score
                })
            
        except Exception as e:
            self.logger.error(f"Metrics collection error: {e}")
    
    def _calculate_health_score(self, cpu_percent: float, memory_percent: float) -> float:
        """Calculate overall system health score"""
        # Simple health calculation (can be made more sophisticated)
        cpu_score = max(0, 100 - cpu_percent)
        memory_score = max(0, 100 - memory_percent)
        
        # Weighted average
        health_score = (cpu_score * 0.6 + memory_score * 0.4)
        
        return round(health_score, 2)
    
    def _check_performance_thresholds(self):
        """Check performance thresholds and generate alerts"""
        if not self.metrics['cpu_usage'] or not self.metrics['memory_usage']:
            return
        
        latest_cpu = self.metrics['cpu_usage'][-1]['system_cpu']
        latest_memory = self.metrics['memory_usage'][-1]['system_memory']
        
        # CPU alerts
        if latest_cpu >= self.thresholds['cpu_critical']:
            self._create_alert('CPU_CRITICAL', f'Critical CPU usage: {latest_cpu:.1f}%')
        elif latest_cpu >= self.thresholds['cpu_warning']:
            self._create_alert('CPU_WARNING', f'High CPU usage: {latest_cpu:.1f}%')
        
        # Memory alerts
        if latest_memory >= self.thresholds['memory_critical']:
            self._create_alert('MEMORY_CRITICAL', f'Critical memory usage: {latest_memory:.1f}%')
        elif latest_memory >= self.thresholds['memory_warning']:
            self._create_alert('MEMORY_WARNING', f'High memory usage: {latest_memory:.1f}%')
    
    def _create_alert(self, alert_type: str, message: str):
        """Create performance alert"""
        alert = {
            'type': alert_type,
            'message': message,
            'timestamp': datetime.now(),
            'severity': 'CRITICAL' if 'CRITICAL' in alert_type else 'WARNING'
        }
        
        with self.lock:
            self.alerts.append(alert)
        
        self.logger.warning(f"Performance Alert: {message}")
    
    def _manage_memory(self):
        """Manage memory usage and garbage collection"""
        try:
            # Get current memory usage
            if self.metrics['memory_usage']:
                latest_memory = self.metrics['memory_usage'][-1]['system_memory']
                
                # Force garbage collection if memory usage is high
                if latest_memory > 85.0:
                    collected = gc.collect()
                    self.logger.debug(f"Garbage collection: {collected} objects collected")
        
        except Exception as e:
            self.logger.error(f"Memory management error: {e}")
    
    @contextmanager
    def measure(self, operation_name: str):
        """Context manager to measure execution time"""
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            yield
        finally:
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            execution_time = end_time - start_time
            memory_delta = end_memory - start_memory
            
            # Record metrics
            with self.lock:
                self.metrics['execution_times'][operation_name].append({
                    'timestamp': datetime.now(),
                    'execution_time': execution_time,
                    'memory_delta': memory_delta
                })
                
                self.metrics['function_calls'][operation_name] += 1
            
            # Check execution time thresholds
            if execution_time >= self.thresholds['execution_critical']:
                self._create_alert(
                    'EXECUTION_CRITICAL',
                    f'{operation_name} took {execution_time:.2f}s (critical)'
                )
            elif execution_time >= self.thresholds['execution_warning']:
                self._create_alert(
                    'EXECUTION_WARNING',
                    f'{operation_name} took {execution_time:.2f}s (warning)'
                )
            
            self.logger.debug(f"Performance: {operation_name} - {execution_time:.4f}s, {memory_delta:.2f}MB")
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0
    
    def profile_function(self, func):
        """Decorator to profile function performance"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with self.measure(func.__name__):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    with self.lock:
                        self.metrics['error_counts'][func.__name__] += 1
                    raise
        
        return wrapper
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        with self.lock:
            report = {
                'uptime': (datetime.now() - self.start_time).total_seconds(),
                'monitoring_active': self.monitoring_active,
                'current_metrics': self._get_current_metrics(),
                'averages': self._calculate_averages(),
                'function_performance': self._get_function_performance(),
                'recent_alerts': list(self.alerts)[-10:],  # Last 10 alerts
                'system_health': self._get_health_summary(),
                'recommendations': self._get_performance_recommendations()
            }
        
        return report
    
    def _get_current_metrics(self) -> Dict[str, Any]:
        """Get current system metrics"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            process = psutil.Process()
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'available_memory_gb': memory.available / 1024 / 1024 / 1024,
                'process_memory_mb': process.memory_info().rss / 1024 / 1024,
                'process_cpu': process.cpu_percent(),
                'thread_count': threading.active_count()
            }
        except Exception as e:
            self.logger.error(f"Current metrics error: {e}")
            return {}
    
    def _calculate_averages(self) -> Dict[str, float]:
        """Calculate average performance metrics"""
        averages = {}
        
        # CPU averages
        if self.metrics['cpu_usage']:
            cpu_values = [m['system_cpu'] for m in self.metrics['cpu_usage']]
            averages['avg_cpu'] = sum(cpu_values) / len(cpu_values)
            averages['max_cpu'] = max(cpu_values)
        
        # Memory averages
        if self.metrics['memory_usage']:
            memory_values = [m['system_memory'] for m in self.metrics['memory_usage']]
            averages['avg_memory'] = sum(memory_values) / len(memory_values)
            averages['max_memory'] = max(memory_values)
        
        # Health averages
        if self.metrics['system_health']:
            health_values = [h['score'] for h in self.metrics['system_health']]
            averages['avg_health'] = sum(health_values) / len(health_values)
            averages['min_health'] = min(health_values)
        
        return averages
    
    def _get_function_performance(self) -> Dict[str, Any]:
        """Get function performance statistics"""
        function_stats = {}
        
        for func_name, times in self.metrics['execution_times'].items():
            if times:
                execution_times = [t['execution_time'] for t in times]
                function_stats[func_name] = {
                    'call_count': self.metrics['function_calls'][func_name],
                    'avg_time': sum(execution_times) / len(execution_times),
                    'max_time': max(execution_times),
                    'min_time': min(execution_times),
                    'error_count': self.metrics['error_counts'][func_name]
                }
        
        return function_stats
    
    def _get_health_summary(self) -> Dict[str, Any]:
        """Get system health summary"""
        if not self.metrics['system_health']:
            return {'status': 'UNKNOWN'}
        
        recent_health = [h['score'] for h in list(self.metrics['system_health'])[-10:]]
        avg_health = sum(recent_health) / len(recent_health)
        
        if avg_health >= 80:
            status = 'EXCELLENT'
        elif avg_health >= 60:
            status = 'GOOD'
        elif avg_health >= 40:
            status = 'FAIR'
        elif avg_health >= 20:
            status = 'POOR'
        else:
            status = 'CRITICAL'
        
        return {
            'status': status,
            'score': avg_health,
            'trend': self._calculate_health_trend()
        }
    
    def _calculate_health_trend(self) -> str:
        """Calculate health trend (improving/declining/stable)"""
        if len(self.metrics['system_health']) < 5:
            return 'INSUFFICIENT_DATA'
        
        recent_scores = [h['score'] for h in list(self.metrics['system_health'])[-5:]]
        
        # Simple trend calculation
        first_half = sum(recent_scores[:2]) / 2
        second_half = sum(recent_scores[-2:]) / 2
        
        if second_half > first_half + 5:
            return 'IMPROVING'
        elif second_half < first_half - 5:
            return 'DECLINING'
        else:
            return 'STABLE'
    
    def _get_performance_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        # Check recent metrics
        if self.metrics['cpu_usage']:
            recent_cpu = [m['system_cpu'] for m in list(self.metrics['cpu_usage'])[-10:]]
            avg_cpu = sum(recent_cpu) / len(recent_cpu)
            
            if avg_cpu > 80:
                recommendations.append("High CPU usage detected. Consider optimizing algorithms or reducing analysis frequency.")
        
        if self.metrics['memory_usage']:
            recent_memory = [m['system_memory'] for m in list(self.metrics['memory_usage'])[-10:]]
            avg_memory = sum(recent_memory) / len(recent_memory)
            
            if avg_memory > 80:
                recommendations.append("High memory usage detected. Consider implementing data cleanup or reducing cache sizes.")
        
        # Check function performance
        for func_name, times in self.metrics['execution_times'].items():
            if times:
                recent_times = [t['execution_time'] for t in list(times)[-5:]]
                avg_time = sum(recent_times) / len(recent_times)
                
                if avg_time > 2.0:
                    recommendations.append(f"Function '{func_name}' is slow (avg: {avg_time:.2f}s). Consider optimization.")
        
        if not recommendations:
            recommendations.append("System performance is optimal. No recommendations at this time.")
        
        return recommendations
    
    def optimize_performance(self):
        """Perform automatic performance optimizations"""
        try:
            # Force garbage collection
            collected = gc.collect()
            self.logger.info(f"Performance optimization: {collected} objects collected")
            
            # Clear old metrics to free memory
            cutoff_time = datetime.now() - timedelta(hours=1)
            
            with self.lock:
                # Keep only recent CPU metrics
                self.metrics['cpu_usage'] = deque(
                    [m for m in self.metrics['cpu_usage'] if m['timestamp'] > cutoff_time],
                    maxlen=1000
                )
                
                # Keep only recent memory metrics
                self.metrics['memory_usage'] = deque(
                    [m for m in self.metrics['memory_usage'] if m['timestamp'] > cutoff_time],
                    maxlen=1000
                )
            
            self.logger.info("Performance optimization completed")
            
        except Exception as e:
            self.logger.error(f"Performance optimization error: {e}")
    
    def export_metrics(self, filepath: str):
        """Export performance metrics to file"""
        try:
            import json
            
            with self.lock:
                export_data = {
                    'export_time': datetime.now().isoformat(),
                    'uptime': (datetime.now() - self.start_time).total_seconds(),
                    'performance_report': self.get_performance_report()
                }
            
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            self.logger.info(f"Performance metrics exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Metrics export error: {e}")

# Example usage
if __name__ == "__main__":
    # Test the performance monitor
    monitor = PerformanceMonitor()
    monitor.start_monitoring(interval=0.5)
    
    # Test function profiling
    @monitor.profile_function
    def test_function():
        import time
        time.sleep(0.1)
        return "Test completed"
    
    # Test measurement context
    with monitor.measure("test_operation"):
        time.sleep(0.05)
    
    # Run test function
    result = test_function()
    
    # Wait a bit for metrics collection
    time.sleep(2)
    
    # Get performance report
    report = monitor.get_performance_report()
    print("Performance Report:")
    print(f"Uptime: {report['uptime']:.2f}s")
    print(f"System Health: {report['system_health']}")
    print(f"Current CPU: {report['current_metrics'].get('cpu_usage', 'N/A')}%")
    print(f"Current Memory: {report['current_metrics'].get('memory_usage', 'N/A')}%")
    
    # Stop monitoring
    monitor.stop_monitoring()
