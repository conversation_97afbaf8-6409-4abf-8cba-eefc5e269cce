<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP BIG BANG Enterprise</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 350px;
            min-height: 400px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #4a8a4a;
        }
        
        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #4a8a4a;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 12px;
            color: #888888;
        }
        
        .status-section {
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            border-left: 3px solid #666666;
        }
        
        .status-item.connected {
            border-left-color: #4a8a4a;
        }
        
        .status-item.disconnected {
            border-left-color: #8a4a4a;
        }
        
        .status-label {
            font-size: 13px;
            font-weight: 500;
        }
        
        .status-value {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .status-value.online {
            background: rgba(74, 138, 74, 0.3);
            color: #4a8a4a;
        }
        
        .status-value.offline {
            background: rgba(138, 74, 74, 0.3);
            color: #8a4a4a;
        }
        
        .controls-section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4a8a4a;
        }
        
        .button {
            width: 100%;
            padding: 10px;
            margin-bottom: 8px;
            background: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 6px;
            color: #ffffff;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #5a5a5a;
            border-color: #888888;
        }
        
        .button:active {
            background: #3a3a3a;
        }
        
        .button.primary {
            background: #2d5a2d;
            border-color: #4a8a4a;
        }
        
        .button.primary:hover {
            background: #3d6a3d;
        }
        
        .button.danger {
            background: #5a2d2d;
            border-color: #8a4a4a;
        }
        
        .button.danger:hover {
            background: #6a3d3d;
        }
        
        .button:disabled {
            background: #333333;
            border-color: #444444;
            color: #666666;
            cursor: not-allowed;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #4a8a4a;
        }
        
        .stat-label {
            font-size: 11px;
            color: #888888;
            margin-top: 2px;
        }
        
        .footer {
            text-align: center;
            font-size: 10px;
            color: #666666;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #333333;
        }
        
        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #333333;
            border-radius: 50%;
            border-top-color: #4a8a4a;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🚀 VIP BIG BANG</div>
        <div class="subtitle">Enterprise Trading Robot</div>
    </div>
    
    <div class="status-section">
        <div class="status-item" id="desktop-status">
            <span class="status-label">Desktop App</span>
            <span class="status-value offline" id="desktop-value">Offline</span>
        </div>
        
        <div class="status-item" id="quotex-status">
            <span class="status-label">Quotex Connection</span>
            <span class="status-value offline" id="quotex-value">Offline</span>
        </div>
        
        <div class="status-item" id="extension-status">
            <span class="status-label">Extension</span>
            <span class="status-value online" id="extension-value">Active</span>
        </div>
    </div>
    
    <div class="controls-section">
        <div class="section-title">📊 Quick Stats</div>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="balance-value">$0.00</div>
                <div class="stat-label">Balance</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="trades-value">0</div>
                <div class="stat-label">Trades Today</div>
            </div>
        </div>
    </div>
    
    <div class="controls-section">
        <div class="section-title">🎮 Controls</div>
        
        <button class="button primary" id="connect-btn">
            🔌 Connect to Desktop
        </button>
        
        <button class="button" id="refresh-btn">
            🔄 Refresh Status
        </button>
        
        <button class="button" id="open-quotex-btn">
            📈 Open Quotex
        </button>
        
        <button class="button danger hidden" id="emergency-stop-btn">
            🛑 Emergency Stop
        </button>
    </div>
    
    <div class="footer">
        VIP BIG BANG Enterprise v1.0.0<br>
        <span id="last-update">Last update: Never</span>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
