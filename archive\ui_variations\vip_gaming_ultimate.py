"""
🎮 VIP BIG BANG Gaming Ultimate UI
بهترین UI Gaming/Robotic/Cartoonish با تکنیک‌های حرفه‌ای
"""

import sys
import math
import random
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Import our cyberpunk components
from gaming_robotic_ui import CyberpunkButton, CyberpunkPanel, AnimatedStatsWidget, CyberpunkColors

class HolographicDisplay(QWidget):
    """نمایشگر هولوگرافیک برای نمودار"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(300)
        self.data_points = []
        self.generate_data()
        
        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_data)
        self.animation_timer.start(100)  # Update every 100ms
        
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 0, 0, 0.9),
                    stop:0.5 rgba(0, 255, 255, 0.1),
                    stop:1 rgba(0, 0, 0, 0.9));
                border: 2px solid {CyberpunkColors.NEON_CYAN};
                border-radius: 15px;
            }}
        """)
    
    def generate_data(self):
        """Generate holographic data points"""
        self.data_points = []
        for i in range(50):
            x = i * 10
            y = 150 + 50 * math.sin(i * 0.2) + random.uniform(-20, 20)
            self.data_points.append((x, y))
    
    def update_data(self):
        """Update holographic data with animation"""
        if self.data_points:
            # Shift data points
            self.data_points.pop(0)
            last_x = self.data_points[-1][0] if self.data_points else 0
            new_y = 150 + 50 * math.sin(len(self.data_points) * 0.2) + random.uniform(-20, 20)
            self.data_points.append((last_x + 10, new_y))
        
        self.update()
    
    def paintEvent(self, event):
        """Paint holographic chart"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw grid
        painter.setPen(QPen(QColor(0, 255, 255, 50), 1))
        width, height = self.width(), self.height()
        
        # Vertical grid lines
        for i in range(0, width, 50):
            painter.drawLine(i, 0, i, height)
        
        # Horizontal grid lines
        for i in range(0, height, 30):
            painter.drawLine(0, i, width, i)
        
        # Draw data line with glow effect
        if len(self.data_points) > 1:
            # Glow effect
            painter.setPen(QPen(QColor(0, 255, 255, 100), 8))
            for i in range(len(self.data_points) - 1):
                x1, y1 = self.data_points[i]
                x2, y2 = self.data_points[i + 1]
                painter.drawLine(int(x1), int(y1), int(x2), int(y2))
            
            # Main line
            painter.setPen(QPen(QColor(0, 255, 255, 255), 2))
            for i in range(len(self.data_points) - 1):
                x1, y1 = self.data_points[i]
                x2, y2 = self.data_points[i + 1]
                painter.drawLine(int(x1), int(y1), int(x2), int(y2))
        
        # Draw price display
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Orbitron", 16, QFont.Weight.Bold))
        painter.drawText(20, 30, "1.07329")
        
        # Draw status indicators
        painter.setPen(QPen(QColor(0, 255, 65), 2))
        painter.setFont(QFont("Orbitron", 12))
        painter.drawText(20, height - 20, "🟢 LIVE TRADING ACTIVE")

class RoboticControlPanel(CyberpunkPanel):
    """پنل کنترل رباتیک"""
    
    def __init__(self):
        super().__init__("ROBOTIC CONTROL", "primary")
        self.setup_controls()
    
    def setup_controls(self):
        """Setup robotic controls"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Title
        title = QLabel("🤖 ROBOTIC CONTROL SYSTEM")
        title.setStyleSheet(f"""
            font-family: 'Orbitron', 'Consolas', monospace;
            font-size: 18px;
            font-weight: 800;
            color: {CyberpunkColors.NEON_CYAN};
            letter-spacing: 2px;
            margin-bottom: 10px;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Control buttons
        controls = [
            ("🚀 ACTIVATE", "primary"),
            ("⚡ BOOST MODE", "success"),
            ("🛡️ SHIELD ON", "default"),
            ("💥 TERMINATE", "danger")
        ]
        
        for text, btn_type in controls:
            btn = CyberpunkButton(text.split()[1], text.split()[0], btn_type, (200, 50))
            layout.addWidget(btn)
        
        # Status indicators
        status_layout = QHBoxLayout()
        
        status_items = [
            ("🟢", "ONLINE"),
            ("🔵", "SCANNING"),
            ("🟡", "LEARNING"),
            ("🔴", "STANDBY")
        ]
        
        for icon, status in status_items:
            status_widget = QWidget()
            status_widget.setFixedSize(80, 40)
            
            status_layout_inner = QVBoxLayout(status_widget)
            status_layout_inner.setContentsMargins(5, 5, 5, 5)
            
            icon_label = QLabel(icon)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("font-size: 16px;")
            
            text_label = QLabel(status)
            text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            text_label.setStyleSheet(f"""
                font-size: 8px;
                color: {CyberpunkColors.NEON_CYAN};
                font-weight: 600;
            """)
            
            status_layout_inner.addWidget(icon_label)
            status_layout_inner.addWidget(text_label)
            
            status_widget.setStyleSheet(f"""
                QWidget {{
                    background: rgba(0, 0, 0, 0.5);
                    border: 1px solid {CyberpunkColors.NEON_CYAN};
                    border-radius: 8px;
                }}
            """)
            
            status_layout.addWidget(status_widget)
        
        layout.addLayout(status_layout)

class VIPGamingUltimateUI(QMainWindow):
    """VIP BIG BANG Gaming Ultimate Interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎮 VIP BIG BANG Gaming Ultimate")
        self.setGeometry(100, 100, 1400, 900)
        
        # Gaming background
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {CyberpunkColors.DARK_PRIMARY},
                    stop:0.2 {CyberpunkColors.DARK_SECONDARY},
                    stop:0.5 {CyberpunkColors.DARK_TERTIARY},
                    stop:0.8 {CyberpunkColors.DARK_ACCENT},
                    stop:1 {CyberpunkColors.DARK_PRIMARY});
                color: white;
            }}
            QLabel {{
                color: white;
                font-family: 'Orbitron', 'Consolas', monospace;
            }}
        """)
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Setup gaming UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Gaming header
        self.create_gaming_header(main_layout)
        
        # Main content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        self.create_left_gaming_panel(content_layout)
        self.create_center_gaming_panel(content_layout)
        self.create_right_gaming_panel(content_layout)
        
        main_layout.addLayout(content_layout)
    
    def create_gaming_header(self, main_layout):
        """Create gaming header"""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)
        
        # Left - Gaming logo
        logo_layout = QHBoxLayout()
        
        logo_icon = QLabel("🎮")
        logo_icon.setStyleSheet("font-size: 32px;")
        logo_layout.addWidget(logo_icon)
        
        logo_text = QLabel("VIP BIG BANG")
        logo_text.setStyleSheet(f"""
            font-family: 'Orbitron', 'Consolas', monospace;
            font-size: 24px;
            font-weight: 800;
            color: {CyberpunkColors.NEON_CYAN};
            letter-spacing: 3px;
        """)
        logo_layout.addWidget(logo_text)
        
        header_layout.addLayout(logo_layout)
        header_layout.addStretch()
        
        # Center - Gaming title
        title = QLabel("GAMING ULTIMATE EDITION")
        title.setStyleSheet(f"""
            font-family: 'Orbitron', 'Consolas', monospace;
            font-size: 20px;
            font-weight: 700;
            color: {CyberpunkColors.NEON_MAGENTA};
            letter-spacing: 2px;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Right - Gaming controls
        controls_layout = QHBoxLayout()
        
        power_btn = CyberpunkButton("POWER", "⚡", "success", (100, 40))
        settings_btn = CyberpunkButton("CONFIG", "⚙️", "default", (100, 40))
        exit_btn = CyberpunkButton("EXIT", "🚪", "danger", (100, 40))
        
        controls_layout.addWidget(power_btn)
        controls_layout.addWidget(settings_btn)
        controls_layout.addWidget(exit_btn)
        
        header_layout.addLayout(controls_layout)
        main_layout.addLayout(header_layout)
    
    def create_left_gaming_panel(self, content_layout):
        """Create left gaming panel"""
        left_widget = QWidget()
        left_widget.setFixedWidth(280)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(20)
        
        # Robotic control panel
        control_panel = RoboticControlPanel()
        left_layout.addWidget(control_panel)
        
        # Gaming stats
        stats_panel = CyberpunkPanel("GAMING STATS", "success")
        stats_layout = QVBoxLayout(stats_panel)
        
        stats_title = QLabel("📊 PERFORMANCE METRICS")
        stats_title.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 700;
            color: {CyberpunkColors.NEON_GREEN};
            letter-spacing: 1px;
            margin-bottom: 10px;
        """)
        stats_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        stats_layout.addWidget(stats_title)
        
        # Individual stats
        balance_stats = AnimatedStatsWidget("BALANCE", "$1,251.76", "💰", CyberpunkColors.NEON_GREEN)
        power_stats = AnimatedStatsWidget("POWER", "87%", "⚡", CyberpunkColors.NEON_CYAN)
        level_stats = AnimatedStatsWidget("LEVEL", "42", "🎯", CyberpunkColors.NEON_MAGENTA)
        
        stats_layout.addWidget(balance_stats)
        stats_layout.addWidget(power_stats)
        stats_layout.addWidget(level_stats)
        
        left_layout.addWidget(stats_panel)
        left_layout.addStretch()
        content_layout.addWidget(left_widget)
    
    def create_center_gaming_panel(self, content_layout):
        """Create center gaming panel"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(15)
        
        # Holographic display
        holo_display = HolographicDisplay()
        center_layout.addWidget(holo_display)
        
        # Gaming indicators
        indicators_layout = QHBoxLayout()
        indicators_layout.setSpacing(15)
        
        # Create gaming indicators
        indicators = [
            ("🎯 TARGET", "LOCKED", CyberpunkColors.NEON_GREEN),
            ("⚡ ENERGY", "98%", CyberpunkColors.NEON_CYAN),
            ("🛡️ SHIELD", "ACTIVE", CyberpunkColors.NEON_BLUE),
            ("🔥 BOOST", "READY", CyberpunkColors.NEON_ORANGE)
        ]
        
        for title, value, color in indicators:
            indicator = self.create_gaming_indicator(title, value, color)
            indicators_layout.addWidget(indicator)
        
        center_layout.addLayout(indicators_layout)
        content_layout.addWidget(center_widget, 2)
    
    def create_right_gaming_panel(self, content_layout):
        """Create right gaming panel"""
        right_widget = QWidget()
        right_widget.setFixedWidth(280)
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(20)
        
        # Gaming actions panel
        actions_panel = CyberpunkPanel("GAMING ACTIONS", "danger")
        actions_layout = QGridLayout(actions_panel)
        actions_layout.setSpacing(15)
        
        # Gaming action buttons
        gaming_actions = [
            (0, 0, "🚀", "LAUNCH"),
            (0, 1, "🎯", "TARGET"),
            (1, 0, "⚡", "BOOST"),
            (1, 1, "🛡️", "SHIELD"),
            (2, 0, "🔥", "FIRE"),
            (2, 1, "💥", "EXPLODE"),
            (3, 0, "🌟", "SPECIAL"),
            (3, 1, "🏆", "VICTORY")
        ]
        
        for row, col, icon, text in gaming_actions:
            btn = CyberpunkButton(text, icon, "primary", (120, 60))
            actions_layout.addWidget(btn, row, col)
        
        right_layout.addWidget(actions_panel)
        right_layout.addStretch()
        content_layout.addWidget(right_widget)
    
    def create_gaming_indicator(self, title, value, color):
        """Create gaming indicator widget"""
        indicator = QWidget()
        indicator.setFixedSize(150, 80)
        
        layout = QVBoxLayout(indicator)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 10px;
            color: rgba(255,255,255,0.8);
            font-weight: 600;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 800;
            color: {color};
            letter-spacing: 1px;
        """)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)
        
        indicator.setStyleSheet(f"""
            QWidget {{
                background: rgba(0, 0, 0, 0.7);
                border: 1px solid {color};
                border-radius: 10px;
            }}
        """)
        
        return indicator
    
    def setup_animations(self):
        """Setup UI animations"""
        # Window fade-in
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set gaming application style
    app.setStyle('Fusion')
    
    window = VIPGamingUltimateUI()
    window.show()
    sys.exit(app.exec())
