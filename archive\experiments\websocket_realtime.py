"""
🌐 VIP BIG BANG - WebSocket Real-time Data System
سیستم داده‌های زنده با WebSocket برای به‌روزرسانی فوری
"""

import asyncio
import json
import websockets
import threading
import time
from datetime import datetime
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWebSockets import QWebSocket, QWebSocketServer
from PySide6.QtNetwork import QHostAddress

class VIPWebSocketServer(QObject):
    """سرور WebSocket برای ارسال داده‌های زنده"""
    
    # Signals
    client_connected = Signal(str)
    client_disconnected = Signal(str)
    data_sent = Signal(dict)
    
    def __init__(self, port=8765):
        super().__init__()
        self.port = port
        self.server = None
        self.clients = []
        self.running = False
        
        # Data update timer
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.send_market_data)
        
        # Demo market data
        self.current_price = 1.07329
        self.volume = 1250000
        self.trend = "UP"
        
    def start_server(self):
        """شروع سرور WebSocket"""
        try:
            self.server = QWebSocketServer("VIP BIG BANG Server", 
                                         QWebSocketServer.NonSecureMode)
            
            if self.server.listen(QHostAddress.Any, self.port):
                print(f"🌐 WebSocket Server started on port {self.port}")
                self.server.newConnection.connect(self.on_new_connection)
                self.running = True
                
                # Start data updates
                self.data_timer.start(1000)  # Every second
                return True
            else:
                print(f"❌ Failed to start WebSocket server on port {self.port}")
                return False
                
        except Exception as e:
            print(f"❌ WebSocket server error: {e}")
            return False
    
    def stop_server(self):
        """توقف سرور WebSocket"""
        self.running = False
        if self.data_timer:
            self.data_timer.stop()
        
        if self.server:
            self.server.close()
            print("🛑 WebSocket Server stopped")
    
    def on_new_connection(self):
        """اتصال کلاینت جدید"""
        client = self.server.nextPendingConnection()
        client.textMessageReceived.connect(self.on_message_received)
        client.disconnected.connect(lambda: self.on_client_disconnected(client))
        
        self.clients.append(client)
        client_id = f"Client_{len(self.clients)}"
        
        print(f"🔗 New client connected: {client_id}")
        self.client_connected.emit(client_id)
        
        # Send welcome message
        welcome_data = {
            "type": "welcome",
            "message": "Connected to VIP BIG BANG WebSocket",
            "timestamp": datetime.now().isoformat(),
            "client_id": client_id
        }
        self.send_to_client(client, welcome_data)
    
    def on_client_disconnected(self, client):
        """قطع اتصال کلاینت"""
        if client in self.clients:
            self.clients.remove(client)
            print(f"🔌 Client disconnected")
            self.client_disconnected.emit("Client disconnected")
    
    def on_message_received(self, message):
        """دریافت پیام از کلاینت"""
        try:
            data = json.loads(message)
            print(f"📨 Received: {data}")
            
            # Handle different message types
            if data.get("type") == "subscribe":
                self.handle_subscription(data)
            elif data.get("type") == "trade_request":
                self.handle_trade_request(data)
                
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON received: {message}")
    
    def handle_subscription(self, data):
        """مدیریت درخواست اشتراک"""
        symbol = data.get("symbol", "EUR/USD")
        response = {
            "type": "subscription_confirmed",
            "symbol": symbol,
            "timestamp": datetime.now().isoformat()
        }
        self.broadcast_to_all(response)
    
    def handle_trade_request(self, data):
        """مدیریت درخواست معامله"""
        direction = data.get("direction", "CALL")
        amount = data.get("amount", 10)
        
        # Simulate trade execution
        trade_result = {
            "type": "trade_executed",
            "direction": direction,
            "amount": amount,
            "entry_price": self.current_price,
            "timestamp": datetime.now().isoformat(),
            "trade_id": f"T_{int(time.time())}"
        }
        self.broadcast_to_all(trade_result)
    
    def send_market_data(self):
        """ارسال داده‌های بازار"""
        if not self.clients:
            return
        
        # Update demo data
        import random
        price_change = random.uniform(-0.0005, 0.0005)
        self.current_price += price_change
        self.volume += random.randint(-10000, 10000)
        
        # Determine trend
        if price_change > 0.0002:
            self.trend = "STRONG_UP"
        elif price_change > 0:
            self.trend = "UP"
        elif price_change < -0.0002:
            self.trend = "STRONG_DOWN"
        elif price_change < 0:
            self.trend = "DOWN"
        else:
            self.trend = "SIDEWAYS"
        
        market_data = {
            "type": "market_data",
            "symbol": "EUR/USD",
            "price": round(self.current_price, 5),
            "volume": self.volume,
            "trend": self.trend,
            "timestamp": datetime.now().isoformat(),
            "change": round(price_change, 5),
            "change_percent": round((price_change / self.current_price) * 100, 3)
        }
        
        self.broadcast_to_all(market_data)
        self.data_sent.emit(market_data)
    
    def send_signal_data(self, signal_type, confidence, direction):
        """ارسال داده‌های سیگنال"""
        signal_data = {
            "type": "trading_signal",
            "signal_type": signal_type,
            "direction": direction,
            "confidence": confidence,
            "timestamp": datetime.now().isoformat(),
            "price": round(self.current_price, 5)
        }
        
        self.broadcast_to_all(signal_data)
        print(f"📡 Signal sent: {signal_type} - {direction} ({confidence}%)")
    
    def send_to_client(self, client, data):
        """ارسال داده به کلاینت خاص"""
        try:
            message = json.dumps(data, ensure_ascii=False)
            client.sendTextMessage(message)
        except Exception as e:
            print(f"❌ Failed to send to client: {e}")
    
    def broadcast_to_all(self, data):
        """ارسال داده به همه کلاینت‌ها"""
        if not self.clients:
            return
        
        message = json.dumps(data, ensure_ascii=False)
        for client in self.clients[:]:  # Copy list to avoid modification during iteration
            try:
                client.sendTextMessage(message)
            except Exception as e:
                print(f"❌ Failed to send to client: {e}")
                if client in self.clients:
                    self.clients.remove(client)

class VIPWebSocketClient(QObject):
    """کلاینت WebSocket برای دریافت داده‌های زنده"""
    
    # Signals
    connected = Signal()
    disconnected = Signal()
    market_data_received = Signal(dict)
    signal_received = Signal(dict)
    trade_executed = Signal(dict)
    
    def __init__(self, url="ws://localhost:8765"):
        super().__init__()
        self.url = url
        self.websocket = None
        self.connected_flag = False
    
    def connect_to_server(self):
        """اتصال به سرور"""
        try:
            self.websocket = QWebSocket()
            self.websocket.connected.connect(self.on_connected)
            self.websocket.disconnected.connect(self.on_disconnected)
            self.websocket.textMessageReceived.connect(self.on_message_received)
            
            print(f"🔗 Connecting to WebSocket server: {self.url}")
            self.websocket.open(self.url)
            
        except Exception as e:
            print(f"❌ Failed to connect to WebSocket: {e}")
    
    def disconnect_from_server(self):
        """قطع اتصال از سرور"""
        if self.websocket:
            self.websocket.close()
    
    def on_connected(self):
        """اتصال برقرار شد"""
        self.connected_flag = True
        print("✅ Connected to WebSocket server")
        self.connected.emit()
        
        # Subscribe to market data
        self.subscribe_to_symbol("EUR/USD")
    
    def on_disconnected(self):
        """اتصال قطع شد"""
        self.connected_flag = False
        print("🔌 Disconnected from WebSocket server")
        self.disconnected.emit()
    
    def on_message_received(self, message):
        """دریافت پیام"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "market_data":
                self.market_data_received.emit(data)
            elif message_type == "trading_signal":
                self.signal_received.emit(data)
            elif message_type == "trade_executed":
                self.trade_executed.emit(data)
            elif message_type == "welcome":
                print(f"🎉 {data.get('message')}")
            
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON received: {message}")
    
    def subscribe_to_symbol(self, symbol):
        """اشتراک در نماد"""
        if not self.connected_flag:
            return
        
        subscription_data = {
            "type": "subscribe",
            "symbol": symbol,
            "timestamp": datetime.now().isoformat()
        }
        
        self.send_message(subscription_data)
        print(f"📡 Subscribed to {symbol}")
    
    def send_trade_request(self, direction, amount=10):
        """ارسال درخواست معامله"""
        if not self.connected_flag:
            return
        
        trade_data = {
            "type": "trade_request",
            "direction": direction,
            "amount": amount,
            "timestamp": datetime.now().isoformat()
        }
        
        self.send_message(trade_data)
        print(f"📊 Trade request sent: {direction} ${amount}")
    
    def send_message(self, data):
        """ارسال پیام"""
        if self.websocket and self.connected_flag:
            message = json.dumps(data, ensure_ascii=False)
            self.websocket.sendTextMessage(message)

# Demo WebSocket integration
class VIPWebSocketDemo:
    """نمایش عملکرد WebSocket"""
    
    def __init__(self):
        self.server = VIPWebSocketServer()
        self.client = VIPWebSocketClient()
        
        # Connect signals
        self.server.client_connected.connect(self.on_client_connected)
        self.server.data_sent.connect(self.on_data_sent)
        
        self.client.market_data_received.connect(self.on_market_data)
        self.client.signal_received.connect(self.on_signal_received)
        self.client.connected.connect(self.on_client_ready)
    
    def start_demo(self):
        """شروع نمایش"""
        print("🌐 Starting WebSocket Demo...")
        
        # Start server
        if self.server.start_server():
            # Wait a moment then connect client
            QTimer.singleShot(1000, self.client.connect_to_server)
            return True
        return False
    
    def stop_demo(self):
        """توقف نمایش"""
        self.client.disconnect_from_server()
        self.server.stop_server()
        print("🛑 WebSocket Demo stopped")
    
    def on_client_connected(self, client_id):
        print(f"🎉 Client connected: {client_id}")
    
    def on_data_sent(self, data):
        if data.get("type") == "market_data":
            price = data.get("price")
            trend = data.get("trend")
            print(f"📈 Market Data: {price} ({trend})")
    
    def on_market_data(self, data):
        price = data.get("price")
        change = data.get("change")
        print(f"📊 Received: EUR/USD {price} ({change:+.5f})")
    
    def on_signal_received(self, data):
        signal_type = data.get("signal_type")
        direction = data.get("direction")
        confidence = data.get("confidence")
        print(f"🚨 Signal: {signal_type} - {direction} ({confidence}%)")
    
    def on_client_ready(self):
        print("✅ Client ready - sending demo signals...")
        
        # Send some demo signals
        QTimer.singleShot(3000, lambda: self.server.send_signal_data("MA6_CROSS", 85, "CALL"))
        QTimer.singleShot(6000, lambda: self.server.send_signal_data("VORTEX_STRONG", 92, "PUT"))
        QTimer.singleShot(9000, lambda: self.client.send_trade_request("CALL", 25))

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    demo = VIPWebSocketDemo()
    if demo.start_demo():
        print("🎮 WebSocket Demo running... Press Ctrl+C to stop")
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n🛑 Demo interrupted")
        finally:
            demo.stop_demo()
    else:
        print("❌ Failed to start WebSocket demo")
    
    sys.exit(0)
