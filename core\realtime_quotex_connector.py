"""
🔗 VIP BIG BANG - Real-time Quotex Connector
🚀 Professional real-time connection system with multiple fallback methods
💎 Enterprise-level reliability and performance
"""

import asyncio
import websockets
import json
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class ConnectionMethod(Enum):
    """Connection methods priority order"""
    WEBSOCKET_DIRECT = "websocket_direct"
    CHROME_EXTENSION = "chrome_extension"
    SELENIUM_BRIDGE = "selenium_bridge"
    API_FALLBACK = "api_fallback"

@dataclass
class MarketData:
    """Market data structure"""
    asset: str
    price: float
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[float] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None

@dataclass
class TradeOrder:
    """Trade order structure"""
    asset: str
    direction: str  # "CALL" or "PUT"
    amount: float
    duration: int  # seconds
    timestamp: datetime
    order_id: Optional[str] = None

@dataclass
class TradeResult:
    """Trade result structure"""
    order_id: str
    success: bool
    profit: Optional[float] = None
    result: Optional[str] = None  # "WIN", "LOSS", "PENDING"
    close_price: Optional[float] = None
    timestamp: Optional[datetime] = None

class RealtimeQuotexConnector:
    """
    🔗 Professional Real-time Quotex Connector
    
    Features:
    - Multiple connection methods with automatic fallback
    - Real-time price streaming
    - Trade execution with confirmation
    - Connection health monitoring
    - Automatic reconnection
    - Performance optimization
    """
    
    def __init__(self, settings=None):
        self.logger = logging.getLogger("RealtimeQuotexConnector")
        self.settings = settings or {}
        
        # Connection state
        self.is_connected = False
        self.current_method = None
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        
        # WebSocket connections
        self.websocket_server = None
        self.websocket_clients = set()
        self.extension_websocket = None
        
        # Selenium driver
        self.chrome_driver = None
        
        # Data streams
        self.market_data_callbacks: List[Callable[[MarketData], None]] = []
        self.trade_result_callbacks: List[Callable[[TradeResult], None]] = []
        self.connection_status_callbacks: List[Callable[[bool], None]] = []
        
        # Current market data
        self.current_prices: Dict[str, MarketData] = {}
        self.current_balance = 0.0
        self.is_demo_mode = True
        
        # Trade management
        self.pending_trades: Dict[str, TradeOrder] = {}
        self.trade_history: List[TradeResult] = []
        
        # Performance monitoring
        self.connection_start_time = None
        self.last_price_update = None
        self.update_count = 0
        self.error_count = 0
        
        # Threading
        self.connection_thread = None
        self.monitoring_thread = None
        self.running = False
        
        self.logger.info("🔗 Real-time Quotex Connector initialized")
    
    async def connect(self, email: str = None, password: str = None) -> bool:
        """
        🚀 Connect to Quotex using best available method
        
        Priority order:
        1. WebSocket Direct (fastest)
        2. Chrome Extension (most reliable)
        3. Selenium Bridge (fallback)
        4. API Fallback (last resort)
        """
        try:
            self.logger.info("🚀 Starting connection to Quotex...")
            self.connection_start_time = datetime.now()
            self.running = True
            
            # Try connection methods in priority order
            connection_methods = [
                (ConnectionMethod.WEBSOCKET_DIRECT, self._connect_websocket_direct),
                (ConnectionMethod.CHROME_EXTENSION, self._connect_chrome_extension),
                (ConnectionMethod.SELENIUM_BRIDGE, self._connect_selenium_bridge),
                (ConnectionMethod.API_FALLBACK, self._connect_api_fallback)
            ]
            
            for method, connect_func in connection_methods:
                try:
                    self.logger.info(f"🔄 Trying {method.value}...")
                    
                    if await connect_func(email, password):
                        self.current_method = method
                        self.is_connected = True
                        self.connection_attempts = 0
                        
                        # Start monitoring
                        await self._start_monitoring()
                        
                        # Notify callbacks
                        self._notify_connection_status(True)
                        
                        self.logger.info(f"✅ Connected via {method.value}")
                        return True
                        
                except Exception as e:
                    self.logger.warning(f"❌ {method.value} failed: {e}")
                    continue
            
            self.logger.error("❌ All connection methods failed")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Connection error: {e}")
            return False
    
    async def _connect_websocket_direct(self, email: str = None, password: str = None) -> bool:
        """🔌 Direct WebSocket connection to Quotex"""
        try:
            # Try to connect to Quotex WebSocket endpoints
            websocket_urls = [
                "wss://ws.quotex.io/socket.io/?EIO=4&transport=websocket",
                "wss://quotex.io/socket.io/?EIO=4&transport=websocket",
                "wss://api.quotex.io/ws"
            ]
            
            for ws_url in websocket_urls:
                try:
                    self.extension_websocket = await websockets.connect(
                        ws_url,
                        extra_headers={
                            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                            "Origin": "https://quotex.io"
                        },
                        timeout=10
                    )
                    
                    # Start listening for messages
                    asyncio.create_task(self._websocket_listener())
                    
                    self.logger.info(f"✅ WebSocket connected: {ws_url}")
                    return True
                    
                except Exception as e:
                    self.logger.debug(f"WebSocket {ws_url} failed: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"WebSocket direct connection failed: {e}")
            return False
    
    async def _connect_chrome_extension(self, email: str = None, password: str = None) -> bool:
        """🌐 Chrome Extension bridge connection"""
        try:
            # Start WebSocket server for extension communication
            self.websocket_server = await websockets.serve(
                self._handle_extension_connection,
                "localhost",
                8765
            )
            
            self.logger.info("🌐 WebSocket server started for Chrome Extension")
            
            # Wait for extension to connect (timeout after 30 seconds)
            timeout = 30
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if self.websocket_clients:
                    # Send test message to verify connection
                    test_message = {
                        "type": "CONNECTION_TEST",
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    await self._broadcast_to_extensions(test_message)
                    
                    # Wait for response
                    await asyncio.sleep(2)
                    
                    if self.is_connected:
                        return True
                
                await asyncio.sleep(1)
            
            self.logger.warning("⏰ Chrome Extension connection timeout")
            return False
            
        except Exception as e:
            self.logger.error(f"Chrome Extension connection failed: {e}")
            return False
    
    async def _connect_selenium_bridge(self, email: str = None, password: str = None) -> bool:
        """🤖 Selenium bridge connection"""
        try:
            # Setup Chrome with stealth options
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Add VIP BIG BANG extension
            extension_path = "chrome_extension"
            chrome_options.add_argument(f"--load-extension={extension_path}")
            
            self.chrome_driver = webdriver.Chrome(options=chrome_options)
            
            # Execute stealth script
            self.chrome_driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # Navigate to Quotex
            self.chrome_driver.get("https://quotex.io")
            
            # Wait for page load
            await asyncio.sleep(5)
            
            # Check if login is needed
            if email and password:
                login_success = await self._perform_selenium_login(email, password)
                if not login_success:
                    self.logger.warning("⚠️ Login failed, trying demo mode")
            
            # Verify connection by checking for trading interface
            try:
                WebDriverWait(self.chrome_driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".trading-chart, .chart-container, .trade-panel"))
                )
                
                # Start data extraction
                asyncio.create_task(self._selenium_data_extractor())
                
                return True
                
            except Exception as e:
                self.logger.error(f"Trading interface not found: {e}")
                return False
            
        except Exception as e:
            self.logger.error(f"Selenium bridge connection failed: {e}")
            return False
    
    async def _connect_api_fallback(self, email: str = None, password: str = None) -> bool:
        """🔄 API fallback connection (simulation for demo)"""
        try:
            self.logger.info("🔄 Using API fallback (demo mode)")
            
            # Simulate API connection
            await asyncio.sleep(2)
            
            # Start demo data simulation
            asyncio.create_task(self._simulate_market_data())
            
            return True
            
        except Exception as e:
            self.logger.error(f"API fallback failed: {e}")
            return False
    
    async def _handle_extension_connection(self, websocket, path):
        """Handle Chrome Extension WebSocket connections"""
        try:
            self.websocket_clients.add(websocket)
            self.logger.info(f"🌐 Extension connected: {websocket.remote_address}")
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self._process_extension_message(data, websocket)
                except json.JSONDecodeError:
                    self.logger.error(f"Invalid JSON from extension: {message}")
                except Exception as e:
                    self.logger.error(f"Extension message error: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.info("🌐 Extension disconnected")
        except Exception as e:
            self.logger.error(f"Extension connection error: {e}")
        finally:
            self.websocket_clients.discard(websocket)
    
    async def _process_extension_message(self, data: Dict, websocket):
        """Process messages from Chrome Extension"""
        try:
            msg_type = data.get('type')
            payload = data.get('data', {})
            
            if msg_type == 'PRICE_UPDATE':
                await self._handle_price_update(payload)
            
            elif msg_type == 'BALANCE_UPDATE':
                await self._handle_balance_update(payload)
            
            elif msg_type == 'TRADE_RESULT':
                await self._handle_trade_result(payload)
            
            elif msg_type == 'CONNECTION_STATUS':
                self.is_connected = payload.get('connected', False)
                self._notify_connection_status(self.is_connected)
            
            elif msg_type == 'CONNECTION_TEST_RESPONSE':
                self.is_connected = True
                self.logger.info("✅ Extension connection verified")
            
            elif msg_type == 'ERROR':
                self.logger.error(f"Extension error: {payload}")
                self.error_count += 1
            
        except Exception as e:
            self.logger.error(f"Extension message processing error: {e}")
    
    async def _handle_price_update(self, data: Dict):
        """Handle price update from any source"""
        try:
            asset = data.get('asset', 'EUR/USD')
            price = float(data.get('price', 0))
            
            if price > 0:
                market_data = MarketData(
                    asset=asset,
                    price=price,
                    timestamp=datetime.now(),
                    bid=data.get('bid'),
                    ask=data.get('ask'),
                    volume=data.get('volume'),
                    change=data.get('change'),
                    change_percent=data.get('change_percent')
                )
                
                self.current_prices[asset] = market_data
                self.last_price_update = datetime.now()
                self.update_count += 1
                
                # Notify callbacks
                for callback in self.market_data_callbacks:
                    try:
                        callback(market_data)
                    except Exception as e:
                        self.logger.error(f"Market data callback error: {e}")
                        
        except Exception as e:
            self.logger.error(f"Price update error: {e}")
    
    async def _handle_balance_update(self, data: Dict):
        """Handle balance update"""
        try:
            new_balance = float(data.get('balance', 0))
            if new_balance != self.current_balance:
                self.current_balance = new_balance
                self.logger.debug(f"💰 Balance updated: ${new_balance:.2f}")
                
        except Exception as e:
            self.logger.error(f"Balance update error: {e}")
    
    async def _handle_trade_result(self, data: Dict):
        """Handle trade result"""
        try:
            result = TradeResult(
                order_id=data.get('order_id', ''),
                success=data.get('success', False),
                profit=data.get('profit'),
                result=data.get('result'),
                close_price=data.get('close_price'),
                timestamp=datetime.now()
            )
            
            self.trade_history.append(result)
            
            # Remove from pending trades
            if result.order_id in self.pending_trades:
                del self.pending_trades[result.order_id]
            
            # Notify callbacks
            for callback in self.trade_result_callbacks:
                try:
                    callback(result)
                except Exception as e:
                    self.logger.error(f"Trade result callback error: {e}")
                    
        except Exception as e:
            self.logger.error(f"Trade result error: {e}")
    
    def add_market_data_callback(self, callback: Callable[[MarketData], None]):
        """Add market data callback"""
        self.market_data_callbacks.append(callback)
    
    def add_trade_result_callback(self, callback: Callable[[TradeResult], None]):
        """Add trade result callback"""
        self.trade_result_callbacks.append(callback)
    
    def add_connection_status_callback(self, callback: Callable[[bool], None]):
        """Add connection status callback"""
        self.connection_status_callbacks.append(callback)
    
    def _notify_connection_status(self, connected: bool):
        """Notify connection status callbacks"""
        for callback in self.connection_status_callbacks:
            try:
                callback(connected)
            except Exception as e:
                self.logger.error(f"Connection status callback error: {e}")
    
    async def execute_trade(self, asset: str, direction: str, amount: float, duration: int) -> Dict:
        """Execute trade order"""
        try:
            order = TradeOrder(
                asset=asset,
                direction=direction.upper(),
                amount=amount,
                duration=duration,
                timestamp=datetime.now(),
                order_id=f"trade_{int(time.time() * 1000)}"
            )
            
            self.pending_trades[order.order_id] = order
            
            if self.current_method == ConnectionMethod.CHROME_EXTENSION:
                return await self._execute_trade_via_extension(order)
            elif self.current_method == ConnectionMethod.SELENIUM_BRIDGE:
                return await self._execute_trade_via_selenium(order)
            else:
                return await self._execute_trade_simulation(order)
                
        except Exception as e:
            self.logger.error(f"Trade execution error: {e}")
            return {"success": False, "error": str(e)}
    
    async def _execute_trade_via_extension(self, order: TradeOrder) -> Dict:
        """Execute trade via Chrome Extension"""
        try:
            message = {
                "type": "EXECUTE_TRADE",
                "data": {
                    "asset": order.asset,
                    "direction": order.direction,
                    "amount": order.amount,
                    "duration": order.duration,
                    "order_id": order.order_id
                }
            }
            
            await self._broadcast_to_extensions(message)
            
            return {
                "success": True,
                "order_id": order.order_id,
                "message": "Trade sent to extension"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _broadcast_to_extensions(self, message: Dict):
        """Broadcast message to all connected extensions"""
        if self.websocket_clients:
            message_str = json.dumps(message)
            disconnected_clients = set()
            
            for client in self.websocket_clients:
                try:
                    await client.send(message_str)
                except websockets.exceptions.ConnectionClosed:
                    disconnected_clients.add(client)
                except Exception as e:
                    self.logger.error(f"Broadcast error: {e}")
                    disconnected_clients.add(client)
            
            # Remove disconnected clients
            self.websocket_clients -= disconnected_clients
    
    def get_current_price(self, asset: str) -> Optional[float]:
        """Get current price for asset"""
        market_data = self.current_prices.get(asset)
        return market_data.price if market_data else None
    
    def get_current_balance(self) -> float:
        """Get current balance"""
        return self.current_balance
    
    def is_connection_healthy(self) -> bool:
        """Check if connection is healthy"""
        if not self.is_connected:
            return False
        
        # Check if we received price updates recently
        if self.last_price_update:
            time_since_update = (datetime.now() - self.last_price_update).total_seconds()
            return time_since_update < 30  # 30 seconds threshold
        
        return False
    
    def get_connection_stats(self) -> Dict:
        """Get connection statistics"""
        uptime = None
        if self.connection_start_time:
            uptime = (datetime.now() - self.connection_start_time).total_seconds()
        
        return {
            "connected": self.is_connected,
            "method": self.current_method.value if self.current_method else None,
            "uptime_seconds": uptime,
            "update_count": self.update_count,
            "error_count": self.error_count,
            "last_update": self.last_price_update.isoformat() if self.last_price_update else None,
            "pending_trades": len(self.pending_trades),
            "total_trades": len(self.trade_history)
        }
    
    async def disconnect(self):
        """Disconnect from Quotex"""
        try:
            self.logger.info("🔌 Disconnecting from Quotex...")
            self.running = False
            self.is_connected = False
            
            # Close WebSocket connections
            if self.extension_websocket:
                await self.extension_websocket.close()
            
            if self.websocket_server:
                self.websocket_server.close()
                await self.websocket_server.wait_closed()
            
            # Close Selenium driver
            if self.chrome_driver:
                self.chrome_driver.quit()
            
            # Notify callbacks
            self._notify_connection_status(False)
            
            self.logger.info("✅ Disconnected from Quotex")
            
        except Exception as e:
            self.logger.error(f"Disconnect error: {e}")


# Test the connector
if __name__ == "__main__":
    import asyncio
    
    async def test_connector():
        connector = RealtimeQuotexConnector()
        
        # Add callbacks
        def on_price_update(data):
            print(f"💰 Price: {data.asset} = {data.price}")
        
        def on_connection_status(connected):
            print(f"🔗 Connection: {'✅ Connected' if connected else '❌ Disconnected'}")
        
        connector.add_market_data_callback(on_price_update)
        connector.add_connection_status_callback(on_connection_status)
        
        # Connect
        success = await connector.connect()
        if success:
            print("🚀 Connector test successful!")
            
            # Wait for some data
            await asyncio.sleep(10)
            
            # Show stats
            stats = connector.get_connection_stats()
            print(f"📊 Stats: {stats}")
        else:
            print("❌ Connector test failed!")
        
        await connector.disconnect()
    
    asyncio.run(test_connector())

    async def _start_monitoring(self):
        """Start connection monitoring and health checks"""
        try:
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_worker,
                daemon=True,
                name="QuotexMonitoring"
            )
            self.monitoring_thread.start()

            self.logger.info("📊 Connection monitoring started")

        except Exception as e:
            self.logger.error(f"Monitoring start error: {e}")

    def _monitoring_worker(self):
        """Monitoring worker thread"""
        asyncio.run(self._monitoring_loop())

    async def _monitoring_loop(self):
        """Connection monitoring loop"""
        try:
            while self.running and self.is_connected:
                try:
                    # Check connection health
                    if not self.is_connection_healthy():
                        self.logger.warning("⚠️ Connection unhealthy, attempting reconnection...")
                        await self._attempt_reconnection()

                    # Send heartbeat if using WebSocket
                    if self.current_method == ConnectionMethod.CHROME_EXTENSION:
                        await self._send_heartbeat()

                    # Log statistics periodically
                    if self.update_count % 100 == 0 and self.update_count > 0:
                        stats = self.get_connection_stats()
                        self.logger.info(f"📊 Connection stats: {stats}")

                    await asyncio.sleep(10)  # Check every 10 seconds

                except Exception as e:
                    self.logger.error(f"Monitoring loop error: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            self.logger.error(f"Monitoring loop failed: {e}")

    async def _send_heartbeat(self):
        """Send heartbeat to maintain connection"""
        try:
            heartbeat_message = {
                "type": "HEARTBEAT",
                "timestamp": datetime.now().isoformat()
            }

            await self._broadcast_to_extensions(heartbeat_message)

        except Exception as e:
            self.logger.debug(f"Heartbeat error: {e}")

    async def _attempt_reconnection(self):
        """Attempt to reconnect"""
        try:
            self.logger.info("🔄 Attempting reconnection...")
            self.connection_attempts += 1

            if self.connection_attempts > self.max_connection_attempts:
                self.logger.error("❌ Max reconnection attempts reached")
                self.is_connected = False
                self._notify_connection_status(False)
                return

            # Try to reconnect using current method
            if self.current_method == ConnectionMethod.CHROME_EXTENSION:
                success = await self._reconnect_extension()
            elif self.current_method == ConnectionMethod.SELENIUM_BRIDGE:
                success = await self._reconnect_selenium()
            else:
                success = await self._reconnect_websocket()

            if success:
                self.connection_attempts = 0
                self.logger.info("✅ Reconnection successful")
            else:
                self.logger.warning(f"❌ Reconnection failed (attempt {self.connection_attempts})")

        except Exception as e:
            self.logger.error(f"Reconnection error: {e}")

    async def _reconnect_extension(self) -> bool:
        """Reconnect Chrome Extension"""
        try:
            # Check if extension is still connected
            if self.websocket_clients:
                test_message = {
                    "type": "CONNECTION_TEST",
                    "timestamp": datetime.now().isoformat()
                }
                await self._broadcast_to_extensions(test_message)

                # Wait for response
                await asyncio.sleep(2)
                return self.is_connected

            return False

        except Exception as e:
            self.logger.error(f"Extension reconnection error: {e}")
            return False

    async def _reconnect_selenium(self) -> bool:
        """Reconnect Selenium"""
        try:
            if self.chrome_driver:
                # Check if driver is still alive
                try:
                    self.chrome_driver.current_url
                    return True
                except:
                    # Driver is dead, need to restart
                    self.chrome_driver.quit()
                    return await self._connect_selenium_bridge()

            return False

        except Exception as e:
            self.logger.error(f"Selenium reconnection error: {e}")
            return False

    async def _reconnect_websocket(self) -> bool:
        """Reconnect WebSocket"""
        try:
            if self.extension_websocket:
                if self.extension_websocket.closed:
                    return await self._connect_websocket_direct()
                return True

            return False

        except Exception as e:
            self.logger.error(f"WebSocket reconnection error: {e}")
            return False

    async def _websocket_listener(self):
        """Listen for WebSocket messages"""
        try:
            async for message in self.extension_websocket:
                try:
                    data = json.loads(message)
                    await self._process_websocket_message(data)
                except json.JSONDecodeError:
                    self.logger.error(f"Invalid WebSocket JSON: {message}")
                except Exception as e:
                    self.logger.error(f"WebSocket message error: {e}")

        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("🔌 WebSocket connection closed")
            self.is_connected = False
        except Exception as e:
            self.logger.error(f"WebSocket listener error: {e}")

    async def _process_websocket_message(self, data: Dict):
        """Process WebSocket messages"""
        try:
            msg_type = data.get('type', '')

            if 'price' in msg_type.lower():
                await self._handle_price_update(data)
            elif 'balance' in msg_type.lower():
                await self._handle_balance_update(data)
            elif 'trade' in msg_type.lower():
                await self._handle_trade_result(data)
            else:
                self.logger.debug(f"Unknown WebSocket message: {msg_type}")

        except Exception as e:
            self.logger.error(f"WebSocket message processing error: {e}")

    async def _perform_selenium_login(self, email: str, password: str) -> bool:
        """Perform login using Selenium"""
        try:
            # Look for login button/link
            login_selectors = [
                "a[href*='login']", ".login-btn", "#login", ".auth-login",
                "button[data-testid='login']", ".header-login"
            ]

            login_element = None
            for selector in login_selectors:
                try:
                    login_element = WebDriverWait(self.chrome_driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if not login_element:
                self.logger.warning("⚠️ Login button not found")
                return False

            # Click login
            login_element.click()
            await asyncio.sleep(2)

            # Find email field
            email_selectors = [
                "input[type='email']", "input[name='email']", "#email",
                "input[placeholder*='email']", ".email-input"
            ]

            email_element = None
            for selector in email_selectors:
                try:
                    email_element = WebDriverWait(self.chrome_driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if not email_element:
                self.logger.warning("⚠️ Email field not found")
                return False

            # Enter email
            email_element.clear()
            email_element.send_keys(email)
            await asyncio.sleep(1)

            # Find password field
            password_selectors = [
                "input[type='password']", "input[name='password']", "#password",
                "input[placeholder*='password']", ".password-input"
            ]

            password_element = None
            for selector in password_selectors:
                try:
                    password_element = WebDriverWait(self.chrome_driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if not password_element:
                self.logger.warning("⚠️ Password field not found")
                return False

            # Enter password
            password_element.clear()
            password_element.send_keys(password)
            await asyncio.sleep(1)

            # Find and click submit button
            submit_selectors = [
                "button[type='submit']", ".login-submit", "#login-submit",
                "button[data-testid='login-submit']", ".auth-submit"
            ]

            submit_element = None
            for selector in submit_selectors:
                try:
                    submit_element = WebDriverWait(self.chrome_driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if not submit_element:
                self.logger.warning("⚠️ Submit button not found")
                return False

            # Submit login
            submit_element.click()

            # Wait for login to complete
            await asyncio.sleep(5)

            # Check if login was successful
            current_url = self.chrome_driver.current_url
            if 'login' not in current_url.lower():
                self.logger.info("✅ Login successful")
                return True
            else:
                self.logger.warning("⚠️ Login may have failed")
                return False

        except Exception as e:
            self.logger.error(f"Selenium login error: {e}")
            return False

    async def _selenium_data_extractor(self):
        """Extract data using Selenium"""
        try:
            while self.running and self.chrome_driver:
                try:
                    # Extract current price
                    price_selectors = [
                        ".chart-price", ".current-rate", ".asset-price",
                        ".price-display", ".rate-value", ".live-price"
                    ]

                    for selector in price_selectors:
                        try:
                            price_element = self.chrome_driver.find_element(By.CSS_SELECTOR, selector)
                            price_text = price_element.text.strip()

                            # Extract numeric value
                            import re
                            price_match = re.search(r'(\d+\.\d+)', price_text)
                            if price_match:
                                price = float(price_match.group(1))

                                await self._handle_price_update({
                                    'asset': 'EUR/USD',  # Default asset
                                    'price': price
                                })
                                break
                        except:
                            continue

                    # Extract balance
                    balance_selectors = [
                        ".balance__value", ".user-balance", ".account-balance",
                        ".wallet-balance", ".current-balance"
                    ]

                    for selector in balance_selectors:
                        try:
                            balance_element = self.chrome_driver.find_element(By.CSS_SELECTOR, selector)
                            balance_text = balance_element.text.strip()

                            # Extract numeric value
                            import re
                            balance_match = re.search(r'(\d+(?:\.\d+)?)', balance_text.replace(',', ''))
                            if balance_match:
                                balance = float(balance_match.group(1))

                                await self._handle_balance_update({
                                    'balance': balance
                                })
                                break
                        except:
                            continue

                    await asyncio.sleep(1)  # Update every second

                except Exception as e:
                    self.logger.error(f"Selenium extraction error: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            self.logger.error(f"Selenium data extractor failed: {e}")

    async def _execute_trade_via_selenium(self, order: TradeOrder) -> Dict:
        """Execute trade via Selenium"""
        try:
            if not self.chrome_driver:
                return {"success": False, "error": "Selenium driver not available"}

            # Set trade amount
            amount_selectors = [
                ".amount-input", ".trade-amount", "input[type='number']",
                ".investment-amount", ".bet-amount"
            ]

            amount_element = None
            for selector in amount_selectors:
                try:
                    amount_element = self.chrome_driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue

            if amount_element:
                amount_element.clear()
                amount_element.send_keys(str(order.amount))
                await asyncio.sleep(0.5)

            # Click trade button
            if order.direction == "CALL":
                button_selectors = [
                    ".call-btn", ".higher-btn", ".up-btn",
                    "[data-direction='call']", ".trade-call"
                ]
            else:
                button_selectors = [
                    ".put-btn", ".lower-btn", ".down-btn",
                    "[data-direction='put']", ".trade-put"
                ]

            trade_button = None
            for selector in button_selectors:
                try:
                    trade_button = WebDriverWait(self.chrome_driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue

            if trade_button:
                trade_button.click()

                return {
                    "success": True,
                    "order_id": order.order_id,
                    "message": "Trade executed via Selenium"
                }
            else:
                return {"success": False, "error": "Trade button not found"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _execute_trade_simulation(self, order: TradeOrder) -> Dict:
        """Execute trade simulation for demo"""
        try:
            self.logger.info(f"🎮 Simulating trade: {order.direction} {order.asset} ${order.amount}")

            # Simulate trade execution delay
            await asyncio.sleep(1)

            # Simulate trade result after duration
            asyncio.create_task(self._simulate_trade_result(order))

            return {
                "success": True,
                "order_id": order.order_id,
                "message": "Trade simulated successfully"
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _simulate_trade_result(self, order: TradeOrder):
        """Simulate trade result"""
        try:
            # Wait for trade duration
            await asyncio.sleep(order.duration)

            # Simulate random result
            import random
            is_win = random.choice([True, False])
            profit = order.amount * 0.8 if is_win else -order.amount

            result = TradeResult(
                order_id=order.order_id,
                success=True,
                profit=profit,
                result="WIN" if is_win else "LOSS",
                close_price=random.uniform(1.0700, 1.0750),
                timestamp=datetime.now()
            )

            await self._handle_trade_result(result.__dict__)

        except Exception as e:
            self.logger.error(f"Trade simulation error: {e}")

    async def _simulate_market_data(self):
        """Simulate market data for demo"""
        try:
            base_prices = {
                "EUR/USD": 1.07320,
                "GBP/USD": 1.26450,
                "USD/JPY": 149.85,
                "AUD/USD": 0.65230
            }

            while self.running:
                try:
                    for asset, base_price in base_prices.items():
                        # Generate realistic price movement
                        import random
                        change = random.uniform(-0.0005, 0.0005)
                        new_price = base_price + change
                        base_prices[asset] = new_price

                        await self._handle_price_update({
                            'asset': asset,
                            'price': new_price,
                            'change': change,
                            'change_percent': (change / base_price) * 100
                        })

                    await asyncio.sleep(1)  # Update every second

                except Exception as e:
                    self.logger.error(f"Market simulation error: {e}")
                    await asyncio.sleep(5)

        except Exception as e:
            self.logger.error(f"Market simulation failed: {e}")
