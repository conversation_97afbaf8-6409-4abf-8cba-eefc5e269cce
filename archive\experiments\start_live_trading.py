#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG Live Trading Launcher
راه‌انداز سیستم ترید زنده کامل
"""

import sys
import os
import asyncio
from datetime import datetime

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

# Add project root to path
from pathlib import Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_banner():
    """نمایش بنر سیستم"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🚀 VIP BIG BANG                           ║
║                 LIVE TRADING SYSTEM                          ║
║                                                              ║
║  💎 Enterprise Trading Robot                                ║
║  ⚡ Ultra-fast 15s Analysis | 5s Trades                    ║
║  🎯 Target: 95% Win Rate | $1000/day                       ║
║  🛡️ Advanced Risk Management                               ║
║                                                              ║
║  📊 10 Core Analyzers + 10 Complementary Systems           ║
║  🌐 Real Quotex Connection                                  ║
║  🤖 Full Automation                                         ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_requirements():
    """بررسی پیش‌نیازها"""
    print("🔍 Checking system requirements...")
    
    required_modules = [
        'PySide6',
        'pandas',
        'numpy',
        'playwright',
        'cryptography',
        'keyring'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"  ❌ {module} - MISSING")
    
    if missing_modules:
        print(f"\n❌ Missing modules: {', '.join(missing_modules)}")
        print("Please install missing modules:")
        for module in missing_modules:
            print(f"  pip install {module}")
        return False
    
    print("✅ All requirements satisfied")
    return True

def check_core_systems():
    """بررسی سیستم‌های اصلی"""
    print("\n🔍 Checking core systems...")
    
    try:
        from core.analysis_engine import AnalysisEngine
        from core.signal_manager import SignalManager
        from core.settings import Settings
        from trading.autotrade import AutoTrader
        from trading.quotex_client import QuotexClient
        print("  ✅ Core trading systems")
    except Exception as e:
        print(f"  ❌ Core systems error: {e}")
        return False
    
    try:
        from core.data_extractor import DataExtractor
        from core.quotex_login import QuotexLogin
        from core.realtime_quotex_connector import RealtimeQuotexConnector
        print("  ✅ Connection systems")
    except Exception as e:
        print(f"  ❌ Connection systems error: {e}")
        return False
    
    try:
        from ui.vip_ui import VIPBigBangUI
        print("  ✅ UI systems")
    except Exception as e:
        print(f"  ❌ UI systems error: {e}")
        return False
    
    print("✅ All core systems ready")
    return True

def show_trading_menu():
    """نمایش منوی ترید"""
    print("\n🎮 VIP BIG BANG Trading Options:")
    print("=" * 50)
    print("1. 🚀 Start Live Trading System (Full UI)")
    print("2. ⚡ Quick Demo Trading")
    print("3. 💰 Live Account Trading")
    print("4. 📊 Analysis Only Mode")
    print("5. 🔧 System Configuration")
    print("6. 📈 Trading Statistics")
    print("7. 🧪 Test All Systems")
    print("8. ❌ Exit")
    print("=" * 50)
    
    while True:
        try:
            choice = input("Select option (1-8): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6', '7', '8']:
                return int(choice)
            else:
                print("❌ Invalid choice. Please select 1-8.")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)

def start_live_trading_ui():
    """راه‌اندازی UI ترید زنده"""
    try:
        print("🚀 Starting Live Trading UI...")
        from vip_live_trading_system import main as live_trading_main
        live_trading_main()
    except Exception as e:
        print(f"❌ Error starting live trading UI: {e}")

def start_demo_trading():
    """شروع ترید دمو"""
    try:
        print("⚡ Starting Demo Trading...")
        from vip_live_trading_system import VIPLiveTradingSystem
        
        async def demo_trading():
            system = VIPLiveTradingSystem()
            system.switch_account_type('demo')
            
            print("🌐 Connecting to Quotex (Demo)...")
            if await system.connect_to_quotex():
                print("✅ Connected successfully")
                print("🚀 Starting demo trading...")
                system.start_trading()
                
                # Run for 5 minutes
                print("⏰ Demo trading will run for 5 minutes...")
                await asyncio.sleep(300)
                
                system.stop_trading()
                await system.disconnect()
                print("✅ Demo trading completed")
            else:
                print("❌ Failed to connect to Quotex")
        
        asyncio.run(demo_trading())
        
    except Exception as e:
        print(f"❌ Error in demo trading: {e}")

def start_live_trading():
    """شروع ترید زنده"""
    try:
        print("💰 Starting Live Account Trading...")
        print("⚠️  WARNING: This will use real money!")
        
        confirm = input("Are you sure you want to continue? (yes/no): ").strip().lower()
        if confirm != 'yes':
            print("❌ Live trading cancelled")
            return
        
        # Get credentials
        email = input("Enter Quotex email: ").strip()
        password = input("Enter Quotex password: ").strip()
        
        if not email or not password:
            print("❌ Email and password required")
            return
        
        from vip_live_trading_system import VIPLiveTradingSystem
        
        async def live_trading():
            system = VIPLiveTradingSystem()
            system.switch_account_type('live')
            
            print("🌐 Connecting to Quotex (Live Account)...")
            if await system.connect_to_quotex(email, password):
                print("✅ Connected successfully")
                
                # Get account info
                account_info = await system.get_account_info()
                print(f"💳 Account Balance: ${account_info.get('balance', 0):.2f}")
                
                confirm_trade = input("Start live trading? (yes/no): ").strip().lower()
                if confirm_trade == 'yes':
                    print("🚀 Starting live trading...")
                    system.start_trading()
                    
                    # Run until stopped
                    print("🔄 Live trading active. Press Ctrl+C to stop...")
                    try:
                        while True:
                            await asyncio.sleep(1)
                    except KeyboardInterrupt:
                        print("\n⏹️ Stopping live trading...")
                        system.stop_trading()
                        await system.disconnect()
                        print("✅ Live trading stopped")
                else:
                    await system.disconnect()
                    print("❌ Live trading cancelled")
            else:
                print("❌ Failed to connect to Quotex")
        
        asyncio.run(live_trading())
        
    except Exception as e:
        print(f"❌ Error in live trading: {e}")

def analysis_only_mode():
    """حالت فقط تحلیل"""
    try:
        print("📊 Starting Analysis Only Mode...")
        from core.analysis_engine import AnalysisEngine
        from core.settings import Settings
        
        settings = Settings()
        engine = AnalysisEngine(settings)
        
        print("🔄 Running continuous analysis...")
        print("Press Ctrl+C to stop...")
        
        try:
            while True:
                # Simulate market data
                import random
                market_data = {
                    'price': 1.07500 + random.uniform(-0.001, 0.001),
                    'volume': random.randint(1000, 5000),
                    'high': 1.07600 + random.uniform(-0.001, 0.001),
                    'low': 1.07400 + random.uniform(-0.001, 0.001),
                    'open': 1.07450 + random.uniform(-0.001, 0.001),
                    'close': 1.07500 + random.uniform(-0.001, 0.001)
                }
                
                engine.update_market_data(market_data)
                result = engine.analyze()
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] Direction: {result.get('direction', 'N/A')}, "
                      f"Confidence: {result.get('confidence', 0):.3f}, "
                      f"Score: {result.get('overall_score', 0):.3f}")
                
                import time
                time.sleep(15)  # 15-second intervals
                
        except KeyboardInterrupt:
            print("\n✅ Analysis stopped")
            
    except Exception as e:
        print(f"❌ Error in analysis mode: {e}")

def system_configuration():
    """تنظیمات سیستم"""
    print("🔧 System Configuration")
    print("This feature will be implemented in future updates")

def trading_statistics():
    """آمار ترید"""
    print("📈 Trading Statistics")
    print("This feature will be implemented in future updates")

def test_all_systems():
    """تست همه سیستم‌ها"""
    try:
        print("🧪 Testing All Systems...")
        from test_performance import main as test_main
        test_main()
    except Exception as e:
        print(f"❌ Error testing systems: {e}")

def main():
    """تابع اصلی"""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Please install missing requirements and try again")
        sys.exit(1)
    
    # Check core systems
    if not check_core_systems():
        print("\n❌ Core systems not ready. Please check installation")
        sys.exit(1)
    
    print(f"\n🕐 System Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("✅ VIP BIG BANG Trading System Ready!")
    
    # Main menu loop
    while True:
        try:
            choice = show_trading_menu()
            
            if choice == 1:
                start_live_trading_ui()
            elif choice == 2:
                start_demo_trading()
            elif choice == 3:
                start_live_trading()
            elif choice == 4:
                analysis_only_mode()
            elif choice == 5:
                system_configuration()
            elif choice == 6:
                trading_statistics()
            elif choice == 7:
                test_all_systems()
            elif choice == 8:
                print("👋 Thank you for using VIP BIG BANG!")
                break
            
            print("\n" + "="*50)
            input("Press Enter to continue...")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
