"""
🧩 Component-Based Design Demo - طراحی ماژولار ChatGPT
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import Qt
from PySide6.QtGui import QGraphicsDropShadowEffect, QColor

# 🧩 Base Component Class
class BaseComponent(QWidget):
    """Base component with common functionality"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_base_style()
    
    def setup_base_style(self):
        """Setup base styling for all components"""
        self.setStyleSheet("""
            QWidget {
                font-family: 'Segoe UI', system-ui, sans-serif;
                color: white;
            }
        """)
    
    def add_shadow(self, blur=15, offset_y=4, alpha=30):
        """Add consistent shadow to component"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur)
        shadow.setXOffset(0)
        shadow.setYOffset(offset_y)
        shadow.setColor(QColor(0, 0, 0, alpha))
        self.setGraphicsEffect(shadow)

# 🎨 Modern Card Component
class ModernCard(BaseComponent):
    """Reusable card component"""
    
    def __init__(self, title="", content="", card_type="default", parent=None):
        super().__init__(parent)
        self.title = title
        self.content = content
        self.card_type = card_type
        self.setup_card()
    
    def setup_card(self):
        """Setup card layout and styling"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)
        
        # Card styling based on type
        if self.card_type == "success":
            background = "rgba(76, 175, 80, 0.1)"
            border = "rgba(76, 175, 80, 0.3)"
        elif self.card_type == "warning":
            background = "rgba(255, 152, 0, 0.1)"
            border = "rgba(255, 152, 0, 0.3)"
        elif self.card_type == "error":
            background = "rgba(244, 67, 54, 0.1)"
            border = "rgba(244, 67, 54, 0.3)"
        else:
            background = "rgba(255,255,255,0.08)"
            border = "rgba(255,255,255,0.2)"
        
        self.setStyleSheet(f"""
            ModernCard {{
                background: {background};
                border: 1px solid {border};
                border-radius: 16px;
            }}
        """)
        
        # Title
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("""
                font-size: 16px;
                font-weight: 600;
                color: white;
                margin-bottom: 8px;
            """)
            layout.addWidget(title_label)
        
        # Content
        if self.content:
            content_label = QLabel(self.content)
            content_label.setStyleSheet("""
                font-size: 13px;
                color: rgba(255,255,255,0.8);
                line-height: 1.5;
            """)
            content_label.setWordWrap(True)
            layout.addWidget(content_label)
        
        # Add shadow
        self.add_shadow()

# 🔘 Modern Button Component
class ModernButton(BaseComponent):
    """Reusable button component"""
    
    def __init__(self, text="", icon="", button_type="default", size="medium", parent=None):
        super().__init__(parent)
        self.button = QPushButton()
        self.text = text
        self.icon = icon
        self.button_type = button_type
        self.size = size
        self.setup_button()
    
    def setup_button(self):
        """Setup button layout and styling"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.button)
        
        # Size configurations
        sizes = {
            "small": {"width": 80, "height": 32, "font": 12, "padding": "6px 12px"},
            "medium": {"width": 120, "height": 40, "font": 14, "padding": "8px 16px"},
            "large": {"width": 160, "height": 48, "font": 16, "padding": "12px 24px"}
        }
        
        size_config = sizes.get(self.size, sizes["medium"])
        
        # Button text
        button_text = f"{self.icon} {self.text}".strip()
        self.button.setText(button_text)
        self.button.setFixedSize(size_config["width"], size_config["height"])
        
        # Button styling based on type
        if self.button_type == "primary":
            background = "#4CAF50"
            hover_bg = "#45a049"
        elif self.button_type == "danger":
            background = "#F44336"
            hover_bg = "#da190b"
        elif self.button_type == "warning":
            background = "#FF9800"
            hover_bg = "#e68900"
        else:
            background = "rgba(255,255,255,0.1)"
            hover_bg = "rgba(255,255,255,0.2)"
        
        self.button.setStyleSheet(f"""
            QPushButton {{
                background: {background};
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 8px;
                color: white;
                font-size: {size_config["font"]}px;
                font-weight: 600;
                padding: {size_config["padding"]};
            }}
            QPushButton:hover {{
                background: {hover_bg};
            }}
            QPushButton:pressed {{
                background: rgba(0,0,0,0.2);
            }}
        """)
        
        # Add shadow
        self.add_shadow(blur=10, offset_y=2, alpha=25)

# 📊 Stats Component
class StatsCard(BaseComponent):
    """Reusable stats display component"""
    
    def __init__(self, title="", value="", change="", icon="", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.change = change
        self.icon = icon
        self.setup_stats()
    
    def setup_stats(self):
        """Setup stats card layout"""
        self.setFixedSize(200, 120)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)
        
        # Header with icon and title
        header_layout = QHBoxLayout()
        
        if self.icon:
            icon_label = QLabel(self.icon)
            icon_label.setStyleSheet("font-size: 20px;")
            header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            font-size: 12px;
            color: rgba(255,255,255,0.7);
            font-weight: 500;
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Value
        value_label = QLabel(self.value)
        value_label.setStyleSheet("""
            font-size: 24px;
            font-weight: 700;
            color: white;
            margin: 4px 0;
        """)
        layout.addWidget(value_label)
        
        # Change indicator
        if self.change:
            change_color = "#4CAF50" if self.change.startswith("+") else "#F44336"
            change_label = QLabel(self.change)
            change_label.setStyleSheet(f"""
                font-size: 12px;
                font-weight: 600;
                color: {change_color};
            """)
            layout.addWidget(change_label)
        
        # Card styling
        self.setStyleSheet("""
            StatsCard {
                background: rgba(255,255,255,0.08);
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 12px;
            }
        """)
        
        # Add shadow
        self.add_shadow()

# 🎛️ Control Panel Component
class ControlPanel(BaseComponent):
    """Reusable control panel component"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.controls = []
        self.setup_panel()
    
    def setup_panel(self):
        """Setup control panel layout"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(16)
        
        # Title
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("""
                font-size: 18px;
                font-weight: 600;
                color: white;
                margin-bottom: 8px;
            """)
            self.main_layout.addWidget(title_label)
        
        # Controls container
        self.controls_layout = QVBoxLayout()
        self.controls_layout.setSpacing(12)
        self.main_layout.addLayout(self.controls_layout)
        
        # Panel styling
        self.setStyleSheet("""
            ControlPanel {
                background: rgba(255,255,255,0.06);
                border: 1px solid rgba(255,255,255,0.15);
                border-radius: 16px;
            }
        """)
        
        # Add shadow
        self.add_shadow(blur=20, offset_y=6, alpha=35)
    
    def add_control(self, widget):
        """Add a control widget to the panel"""
        self.controls.append(widget)
        self.controls_layout.addWidget(widget)

# 🎨 Main Demo Window
class ComponentDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧩 Component-Based Design Demo")
        self.setGeometry(100, 100, 1200, 800)
        
        # ChatGPT background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:1 #1a1a2e);
                color: white;
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(32)
        
        # Title
        title = QLabel("🧩 Component-Based Design System")
        title.setStyleSheet("""
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 16px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Components showcase
        showcase_layout = QGridLayout()
        showcase_layout.setSpacing(24)
        
        # Cards showcase
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(16)
        
        default_card = ModernCard("Default Card", "This is a default card component with standard styling.")
        success_card = ModernCard("Success Card", "This is a success card for positive feedback.", "success")
        warning_card = ModernCard("Warning Card", "This is a warning card for important notices.", "warning")
        
        cards_layout.addWidget(default_card)
        cards_layout.addWidget(success_card)
        cards_layout.addWidget(warning_card)
        
        showcase_layout.addLayout(cards_layout, 0, 0, 1, 2)
        
        # Buttons showcase
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(16)
        
        default_btn = ModernButton("Default", "🔘", "default", "medium")
        primary_btn = ModernButton("Primary", "✓", "primary", "medium")
        danger_btn = ModernButton("Danger", "✗", "danger", "medium")
        
        buttons_layout.addWidget(default_btn)
        buttons_layout.addWidget(primary_btn)
        buttons_layout.addWidget(danger_btn)
        buttons_layout.addStretch()
        
        showcase_layout.addLayout(buttons_layout, 1, 0)
        
        # Stats showcase
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(16)
        
        balance_stats = StatsCard("Balance", "$1,251.76", "+25.17%", "💰")
        trades_stats = StatsCard("Trades", "15", "+3", "📊")
        winrate_stats = StatsCard("Win Rate", "87%", "+5%", "🎯")
        
        stats_layout.addWidget(balance_stats)
        stats_layout.addWidget(trades_stats)
        stats_layout.addWidget(winrate_stats)
        stats_layout.addStretch()
        
        showcase_layout.addLayout(stats_layout, 1, 1)
        
        # Control Panel showcase
        control_panel = ControlPanel("Trading Controls")
        control_panel.add_control(ModernButton("Start AutoTrade", "🚀", "primary", "large"))
        control_panel.add_control(ModernButton("Stop Trading", "⏹️", "danger", "large"))
        control_panel.add_control(ModernButton("Settings", "⚙️", "default", "large"))
        
        showcase_layout.addWidget(control_panel, 2, 0, 1, 2)
        
        layout.addLayout(showcase_layout)
        
        # Component benefits
        benefits_card = ModernCard(
            "🧩 Component Benefits",
            """
• Reusability: Write once, use everywhere
• Consistency: Same look and behavior
• Maintainability: Easy to update and fix
• Scalability: Add new features easily
• Testing: Test components individually
• Collaboration: Team can work on different components
            """
        )
        layout.addWidget(benefits_card)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ComponentDemo()
    window.show()
    sys.exit(app.exec())
