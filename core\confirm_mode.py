"""
VIP BIG BANG Enterprise - Confirm Mode
Semi-automatic confirmation system before executing real trades
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional
import logging
from datetime import datetime, timedelta
import json

class ConfirmMode:
    """
    Confirm Mode - VIP BIG BANG complementary analysis
    Requires manual or semi-automatic confirmation before executing trades
    Analyzes confidence levels and provides confirmation recommendations
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("ConfirmMode")
        
        # Confirmation parameters
        self.auto_confirm_threshold = 0.90    # Auto-confirm above 90% confidence
        self.manual_confirm_threshold = 0.70  # Manual confirm between 70-90%
        self.reject_threshold = 0.50          # Auto-reject below 50%
        
        # Confirmation timeout
        self.confirmation_timeout = 30  # 30 seconds to confirm
        
        # Risk assessment parameters
        self.high_risk_indicators = [
            'low_confidence', 'conflicting_signals', 'high_volatility',
            'news_events', 'otc_mode', 'low_volume'
        ]
        
        # Pending confirmations storage
        self.pending_confirmations = {}
        
        self.logger.debug("Confirm Mode initialized")
    
    def assess_trade_confidence(self, analysis_results: Dict, signal_data: Dict) -> Dict:
        """Assess overall confidence in trade signal"""
        confidence_factors = []
        risk_factors = []
        
        # Collect confidence scores from all analyses
        for analysis_name, result in analysis_results.items():
            if isinstance(result, dict) and 'confidence' in result:
                confidence_factors.append(result['confidence'])
        
        # Calculate average confidence
        avg_confidence = np.mean(confidence_factors) if confidence_factors else 0.0
        
        # Check for risk factors
        if avg_confidence < 0.7:
            risk_factors.append('low_confidence')
        
        # Check signal alignment
        if signal_data.get('alignment_data', {}).get('alignment_percentage', 0) < 0.7:
            risk_factors.append('conflicting_signals')
        
        # Check for news events
        if analysis_results.get('economic_news_filter', {}).get('risk_level') == 'HIGH':
            risk_factors.append('news_events')
        
        # Check for OTC mode
        if analysis_results.get('otc_mode_detector', {}).get('is_otc_mode', False):
            risk_factors.append('otc_mode')
        
        # Check volume conditions
        volume_analysis = analysis_results.get('volume_per_candle', {})
        if volume_analysis.get('volume_metrics', {}).get('volume_ratio', 1.0) < 0.5:
            risk_factors.append('low_volume')
        
        # Calculate risk score
        risk_score = len(risk_factors) / len(self.high_risk_indicators)
        
        # Adjust confidence based on risk
        adjusted_confidence = avg_confidence * (1 - risk_score * 0.3)
        
        return {
            'raw_confidence': avg_confidence,
            'adjusted_confidence': adjusted_confidence,
            'risk_factors': risk_factors,
            'risk_score': risk_score,
            'confidence_factors_count': len(confidence_factors)
        }
    
    def determine_confirmation_requirement(self, confidence_assessment: Dict, signal_data: Dict) -> Dict:
        """Determine what type of confirmation is required"""
        adjusted_confidence = confidence_assessment['adjusted_confidence']
        risk_factors = confidence_assessment['risk_factors']
        
        # Determine confirmation type
        if adjusted_confidence >= self.auto_confirm_threshold and len(risk_factors) == 0:
            confirmation_type = 'AUTO_CONFIRM'
            recommendation = 'EXECUTE'
            reason = f"High confidence ({adjusted_confidence:.1%}) with no risk factors"
        elif adjusted_confidence >= self.manual_confirm_threshold:
            confirmation_type = 'MANUAL_CONFIRM'
            recommendation = 'REVIEW_REQUIRED'
            reason = f"Medium confidence ({adjusted_confidence:.1%}) - manual review recommended"
        elif adjusted_confidence >= self.reject_threshold:
            confirmation_type = 'MANUAL_CONFIRM'
            recommendation = 'CAUTION_REQUIRED'
            reason = f"Low confidence ({adjusted_confidence:.1%}) - high caution required"
        else:
            confirmation_type = 'AUTO_REJECT'
            recommendation = 'REJECT'
            reason = f"Very low confidence ({adjusted_confidence:.1%}) - auto-reject"
        
        # Override for high risk situations
        if len(risk_factors) >= 3:
            confirmation_type = 'MANUAL_CONFIRM'
            recommendation = 'HIGH_RISK_REVIEW'
            reason = f"Multiple risk factors detected: {', '.join(risk_factors)}"
        
        return {
            'confirmation_type': confirmation_type,
            'recommendation': recommendation,
            'reason': reason,
            'requires_manual': confirmation_type in ['MANUAL_CONFIRM'],
            'auto_execute': confirmation_type == 'AUTO_CONFIRM',
            'auto_reject': confirmation_type == 'AUTO_REJECT'
        }
    
    def create_confirmation_request(self, trade_data: Dict, confidence_assessment: Dict, 
                                  confirmation_requirement: Dict) -> Dict:
        """Create a confirmation request for manual review"""
        confirmation_id = f"confirm_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        confirmation_request = {
            'id': confirmation_id,
            'timestamp': datetime.now(),
            'expires_at': datetime.now() + timedelta(seconds=self.confirmation_timeout),
            'trade_data': trade_data,
            'confidence_assessment': confidence_assessment,
            'confirmation_requirement': confirmation_requirement,
            'status': 'PENDING',
            'user_decision': None,
            'decision_timestamp': None
        }
        
        # Store pending confirmation
        self.pending_confirmations[confirmation_id] = confirmation_request
        
        return confirmation_request
    
    def process_user_confirmation(self, confirmation_id: str, user_decision: str, 
                                user_notes: str = "") -> Dict:
        """Process user confirmation decision"""
        if confirmation_id not in self.pending_confirmations:
            return {
                'success': False,
                'error': 'Confirmation request not found or expired'
            }
        
        confirmation_request = self.pending_confirmations[confirmation_id]
        
        # Check if expired
        if datetime.now() > confirmation_request['expires_at']:
            del self.pending_confirmations[confirmation_id]
            return {
                'success': False,
                'error': 'Confirmation request has expired'
            }
        
        # Update confirmation with user decision
        confirmation_request['status'] = 'COMPLETED'
        confirmation_request['user_decision'] = user_decision
        confirmation_request['user_notes'] = user_notes
        confirmation_request['decision_timestamp'] = datetime.now()
        
        # Remove from pending
        del self.pending_confirmations[confirmation_id]
        
        return {
            'success': True,
            'confirmation_request': confirmation_request,
            'final_decision': user_decision
        }
    
    def cleanup_expired_confirmations(self):
        """Clean up expired confirmation requests"""
        current_time = datetime.now()
        expired_ids = []
        
        for conf_id, conf_request in self.pending_confirmations.items():
            if current_time > conf_request['expires_at']:
                expired_ids.append(conf_id)
        
        for conf_id in expired_ids:
            del self.pending_confirmations[conf_id]
        
        if expired_ids:
            self.logger.info(f"Cleaned up {len(expired_ids)} expired confirmation requests")
    
    def get_confirmation_summary(self) -> Dict:
        """Get summary of current confirmation status"""
        self.cleanup_expired_confirmations()
        
        return {
            'pending_count': len(self.pending_confirmations),
            'pending_confirmations': list(self.pending_confirmations.keys()),
            'auto_confirm_threshold': self.auto_confirm_threshold,
            'manual_confirm_threshold': self.manual_confirm_threshold,
            'reject_threshold': self.reject_threshold
        }
    
    def analyze(self, analysis_results: Dict, signal_data: Dict, trade_data: Dict = None) -> Dict:
        """
        Main confirm mode analysis
        Returns confirmation requirements and recommendations
        """
        try:
            # Clean up expired confirmations
            self.cleanup_expired_confirmations()
            
            # Assess trade confidence
            confidence_assessment = self.assess_trade_confidence(analysis_results, signal_data)
            
            # Determine confirmation requirement
            confirmation_requirement = self.determine_confirmation_requirement(
                confidence_assessment, signal_data
            )
            
            # Create confirmation request if manual confirmation needed
            confirmation_request = None
            if confirmation_requirement['requires_manual'] and trade_data:
                confirmation_request = self.create_confirmation_request(
                    trade_data, confidence_assessment, confirmation_requirement
                )
            
            # Calculate score based on confirmation type
            if confirmation_requirement['auto_execute']:
                score = 1.0
            elif confirmation_requirement['requires_manual']:
                score = 0.7
            else:  # auto_reject
                score = 0.0
            
            return {
                'score': score,
                'direction': 'NEUTRAL',  # Confirm mode doesn't provide direction
                'confidence': confidence_assessment['adjusted_confidence'],
                'confidence_assessment': confidence_assessment,
                'confirmation_requirement': confirmation_requirement,
                'confirmation_request': confirmation_request,
                'allow_trading': not confirmation_requirement['auto_reject'],
                'requires_confirmation': confirmation_requirement['requires_manual'],
                'confirmation_summary': self.get_confirmation_summary(),
                'details': f"Confirm: {confirmation_requirement['recommendation']} - {confirmation_requirement['reason']}"
            }
            
        except Exception as e:
            self.logger.error(f"Confirm mode error: {e}")
            return {
                'score': 0.0,  # Conservative approach on error
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'allow_trading': False,
                'requires_confirmation': True,
                'details': f'Confirm mode failed: {str(e)}'
            }
