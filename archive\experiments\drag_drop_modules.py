"""
🎯 VIP BIG BANG - Drag & Drop Module Manager
سیستم مدیریت ماژول‌ها با قابلیت کشیدن و رها کردن
"""

import sys
import json
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class DraggableAnalysisWidget(QFrame):
    """ویجت تحلیل قابل کشیدن"""
    
    # Signals
    analysis_activated = Signal(str, bool)
    analysis_configured = Signal(str, dict)
    
    def __init__(self, analysis_name, analysis_data):
        super().__init__()
        self.analysis_name = analysis_name
        self.analysis_data = analysis_data
        self.is_active = analysis_data.get("active", False)
        
        self.setup_widget()
        self.setAcceptDrops(True)
    
    def setup_widget(self):
        """تنظیم ویجت"""
        self.setFixedSize(200, 120)
        self.setFrameStyle(QFrame.Shape.Box)
        
        # Layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Header
        header_layout = QHBoxLayout()
        
        # Icon
        icon_label = QLabel(self.analysis_data.get("icon", "📊"))
        icon_label.setStyleSheet("font-size: 24px;")
        header_layout.addWidget(icon_label)
        
        # Name
        name_label = QLabel(self.analysis_name)
        name_label.setStyleSheet("""
            font-size: 12px;
            font-weight: bold;
            color: white;
        """)
        name_label.setWordWrap(True)
        header_layout.addWidget(name_label)
        
        # Toggle button
        self.toggle_btn = QPushButton("●" if self.is_active else "○")
        self.toggle_btn.setFixedSize(30, 30)
        self.toggle_btn.clicked.connect(self.toggle_analysis)
        header_layout.addWidget(self.toggle_btn)
        
        layout.addLayout(header_layout)
        
        # Description
        desc_label = QLabel(self.analysis_data.get("description", ""))
        desc_label.setStyleSheet("""
            font-size: 10px;
            color: rgba(255,255,255,0.7);
        """)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Parameters
        params_label = QLabel(f"پارامترها: {len(self.analysis_data.get('parameters', {}))}")
        params_label.setStyleSheet("""
            font-size: 9px;
            color: rgba(255,255,255,0.5);
        """)
        layout.addWidget(params_label)
        
        # Update styling
        self.update_styling()
    
    def update_styling(self):
        """به‌روزرسانی استایل"""
        if self.is_active:
            color = self.analysis_data.get("color", "#4A90E2")
            self.setStyleSheet(f"""
                QFrame {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}, stop:1 rgba(0,0,0,0.3));
                    border: 2px solid {color};
                    border-radius: 10px;
                }}
            """)
            self.toggle_btn.setStyleSheet(f"""
                QPushButton {{
                    background: {color};
                    border: 1px solid white;
                    border-radius: 15px;
                    color: white;
                    font-weight: bold;
                }}
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    background: rgba(100,100,100,0.3);
                    border: 2px solid rgba(255,255,255,0.3);
                    border-radius: 10px;
                }
            """)
            self.toggle_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(100,100,100,0.5);
                    border: 1px solid rgba(255,255,255,0.5);
                    border-radius: 15px;
                    color: rgba(255,255,255,0.7);
                }
            """)
    
    def toggle_analysis(self):
        """تغییر وضعیت تحلیل"""
        self.is_active = not self.is_active
        self.toggle_btn.setText("●" if self.is_active else "○")
        self.update_styling()
        self.analysis_activated.emit(self.analysis_name, self.is_active)
    
    def mousePressEvent(self, event):
        """شروع کشیدن"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.pos()
    
    def mouseMoveEvent(self, event):
        """کشیدن ویجت"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return
        
        if ((event.pos() - self.drag_start_position).manhattanLength() < 
            QApplication.startDragDistance()):
            return
        
        # Start drag operation
        drag = QDrag(self)
        mimeData = QMimeData()
        
        # Set drag data
        drag_data = {
            "type": "analysis_widget",
            "name": self.analysis_name,
            "data": self.analysis_data,
            "active": self.is_active
        }
        mimeData.setText(json.dumps(drag_data))
        drag.setMimeData(mimeData)
        
        # Create drag pixmap
        pixmap = self.grab()
        drag.setPixmap(pixmap)
        drag.setHotSpot(event.pos())
        
        # Execute drag
        dropAction = drag.exec(Qt.DropAction.MoveAction)

class DropZoneWidget(QFrame):
    """منطقه رها کردن ماژول‌ها"""
    
    # Signals
    module_dropped = Signal(str, dict)
    module_removed = Signal(str)
    layout_changed = Signal(list)
    
    def __init__(self, zone_name, zone_type="analysis"):
        super().__init__()
        self.zone_name = zone_name
        self.zone_type = zone_type
        self.modules = []
        
        self.setup_zone()
        self.setAcceptDrops(True)
    
    def setup_zone(self):
        """تنظیم منطقه رها کردن"""
        self.setMinimumSize(300, 200)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setLineWidth(2)
        
        # Layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(15, 15, 15, 15)
        self.layout.setSpacing(10)
        
        # Header
        header = QLabel(f"📋 {self.zone_name}")
        header.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: white;
            padding: 5px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
        """)
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(header)
        
        # Modules area
        self.modules_area = QScrollArea()
        self.modules_area.setWidgetResizable(True)
        self.modules_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: 1px dashed rgba(255,255,255,0.3);
                border-radius: 5px;
            }
        """)
        
        self.modules_widget = QWidget()
        self.modules_layout = QVBoxLayout(self.modules_widget)
        self.modules_layout.setSpacing(5)
        
        self.modules_area.setWidget(self.modules_widget)
        self.layout.addWidget(self.modules_area)
        
        # Update styling
        self.update_styling()
    
    def update_styling(self):
        """به‌روزرسانی استایل"""
        if self.zone_type == "active":
            color = "#7ED321"
        elif self.zone_type == "inactive":
            color = "#D0021B"
        else:
            color = "#4A90E2"
        
        self.setStyleSheet(f"""
            QFrame {{
                background: rgba(0,0,0,0.2);
                border: 2px solid {color};
                border-radius: 10px;
            }}
        """)
    
    def dragEnterEvent(self, event):
        """ورود کشیدن"""
        if event.mimeData().hasText():
            try:
                data = json.loads(event.mimeData().text())
                if data.get("type") == "analysis_widget":
                    event.acceptProposedAction()
                    self.setStyleSheet(self.styleSheet() + """
                        QFrame { border: 3px solid yellow; }
                    """)
            except:
                pass
    
    def dragLeaveEvent(self, event):
        """خروج کشیدن"""
        self.update_styling()
    
    def dropEvent(self, event):
        """رها کردن"""
        try:
            data = json.loads(event.mimeData().text())
            if data.get("type") == "analysis_widget":
                module_name = data.get("name")
                module_data = data.get("data")
                
                # Add to this zone
                self.add_module(module_name, module_data)
                
                event.acceptProposedAction()
                self.module_dropped.emit(module_name, module_data)
                
        except Exception as e:
            print(f"❌ Drop error: {e}")
        
        self.update_styling()
    
    def add_module(self, name, data):
        """افزودن ماژول"""
        if name not in [m["name"] for m in self.modules]:
            # Create module item
            module_item = QFrame()
            module_item.setFixedHeight(40)
            module_item.setStyleSheet(f"""
                QFrame {{
                    background: {data.get('color', '#4A90E2')};
                    border: 1px solid white;
                    border-radius: 5px;
                    padding: 5px;
                }}
            """)
            
            layout = QHBoxLayout(module_item)
            layout.setContentsMargins(5, 5, 5, 5)
            
            # Icon and name
            icon_label = QLabel(data.get("icon", "📊"))
            name_label = QLabel(name)
            name_label.setStyleSheet("color: white; font-weight: bold; font-size: 11px;")
            
            # Remove button
            remove_btn = QPushButton("✕")
            remove_btn.setFixedSize(20, 20)
            remove_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255,0,0,0.7);
                    border: none;
                    border-radius: 10px;
                    color: white;
                    font-weight: bold;
                }
            """)
            remove_btn.clicked.connect(lambda: self.remove_module(name))
            
            layout.addWidget(icon_label)
            layout.addWidget(name_label)
            layout.addStretch()
            layout.addWidget(remove_btn)
            
            self.modules_layout.addWidget(module_item)
            
            # Store module data
            self.modules.append({
                "name": name,
                "data": data,
                "widget": module_item
            })
            
            self.layout_changed.emit([m["name"] for m in self.modules])
    
    def remove_module(self, name):
        """حذف ماژول"""
        for i, module in enumerate(self.modules):
            if module["name"] == name:
                # Remove widget
                module["widget"].setParent(None)
                module["widget"].deleteLater()
                
                # Remove from list
                self.modules.pop(i)
                
                self.module_removed.emit(name)
                self.layout_changed.emit([m["name"] for m in self.modules])
                break
    
    def get_modules(self):
        """دریافت لیست ماژول‌ها"""
        return [m["name"] for m in self.modules]

class VIPModuleManager(QMainWindow):
    """مدیر ماژول‌های VIP BIG BANG"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎯 VIP BIG BANG - Module Manager")
        self.setGeometry(100, 100, 1200, 800)
        
        # Analysis modules data
        self.analysis_modules = {
            "MA6": {
                "icon": "📈",
                "description": "میانگین متحرک 6 دوره‌ای",
                "color": "#4A90E2",
                "active": True,
                "parameters": {"period": 6, "type": "simple"}
            },
            "Vortex": {
                "icon": "🌪️",
                "description": "اندیکاتور ورتکس",
                "color": "#7ED321",
                "active": True,
                "parameters": {"period": 5}
            },
            "Volume": {
                "icon": "📊",
                "description": "تحلیل حجم معاملات",
                "color": "#F5A623",
                "active": True,
                "parameters": {"threshold": 1000000}
            },
            "Trap Candle": {
                "icon": "🪤",
                "description": "شناسایی کندل تله",
                "color": "#D0021B",
                "active": True,
                "parameters": {"sensitivity": 0.7}
            },
            "Shadow Candle": {
                "icon": "👻",
                "description": "تحلیل سایه کندل",
                "color": "#9013FE",
                "active": True,
                "parameters": {"ratio": 2.0}
            },
            "Strong Level": {
                "icon": "💪",
                "description": "سطوح قوی حمایت/مقاومت",
                "color": "#FF6B9D",
                "active": True,
                "parameters": {"strength": 3}
            },
            "Fake Breakout": {
                "icon": "🎭",
                "description": "شناسایی شکست کاذب",
                "color": "#00BCD4",
                "active": False,
                "parameters": {"confirmation": 5}
            },
            "Momentum": {
                "icon": "🚀",
                "description": "تحلیل مومنتوم",
                "color": "#FF8000",
                "active": False,
                "parameters": {"period": 14}
            },
            "Trend Analyzer": {
                "icon": "📊",
                "description": "تحلیلگر روند",
                "color": "#8000FF",
                "active": False,
                "parameters": {"timeframe": "1m"}
            },
            "Buyer/Seller Power": {
                "icon": "⚖️",
                "description": "قدرت خریداران/فروشندگان",
                "color": "#FF0080",
                "active": False,
                "parameters": {"calculation": "volume_based"}
            }
        }
        
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1A1A2E, stop:1 #0F3460);
                color: white;
            }
            QLabel { color: white; }
        """)
        
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Left panel - Available modules
        left_panel = QFrame()
        left_panel.setFixedWidth(250)
        left_panel.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.3);
                border: 2px solid #4A90E2;
                border-radius: 10px;
            }
        """)
        
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title = QLabel("📦 ماژول‌های موجود")
        title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #4A90E2;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(title)
        
        # Modules scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { background: transparent; border: none; }")
        
        modules_widget = QWidget()
        modules_layout = QVBoxLayout(modules_widget)
        modules_layout.setSpacing(10)
        
        # Create draggable widgets
        for name, data in self.analysis_modules.items():
            widget = DraggableAnalysisWidget(name, data)
            widget.analysis_activated.connect(self.on_analysis_toggled)
            modules_layout.addWidget(widget)
        
        modules_layout.addStretch()
        scroll_area.setWidget(modules_widget)
        left_layout.addWidget(scroll_area)
        
        layout.addWidget(left_panel)
        
        # Right panel - Drop zones
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setSpacing(15)
        
        # Active zone
        self.active_zone = DropZoneWidget("ماژول‌های فعال", "active")
        self.active_zone.module_dropped.connect(self.on_module_dropped)
        self.active_zone.layout_changed.connect(self.on_layout_changed)
        right_layout.addWidget(self.active_zone)
        
        # Inactive zone
        self.inactive_zone = DropZoneWidget("ماژول‌های غیرفعال", "inactive")
        self.inactive_zone.module_dropped.connect(self.on_module_dropped)
        self.inactive_zone.layout_changed.connect(self.on_layout_changed)
        right_layout.addWidget(self.inactive_zone)
        
        layout.addWidget(right_panel, 2)
        
        # Initialize with current active modules
        self.initialize_zones()
    
    def initialize_zones(self):
        """راه‌اندازی اولیه مناطق"""
        for name, data in self.analysis_modules.items():
            if data.get("active", False):
                self.active_zone.add_module(name, data)
            else:
                self.inactive_zone.add_module(name, data)
    
    def on_analysis_toggled(self, name, active):
        """تغییر وضعیت تحلیل"""
        self.analysis_modules[name]["active"] = active
        print(f"🔄 {name} {'activated' if active else 'deactivated'}")
    
    def on_module_dropped(self, name, data):
        """رها کردن ماژول"""
        print(f"📦 Module dropped: {name}")
    
    def on_layout_changed(self, modules):
        """تغییر چیدمان"""
        print(f"🔄 Layout changed: {modules}")
    
    def save_layout(self):
        """ذخیره چیدمان"""
        layout_data = {
            "active_modules": self.active_zone.get_modules(),
            "inactive_modules": self.inactive_zone.get_modules(),
            "timestamp": QDateTime.currentDateTime().toString()
        }
        
        # Save to file
        config_file = Path("module_layout.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(layout_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Layout saved to {config_file}")
    
    def load_layout(self):
        """بارگذاری چیدمان"""
        config_file = Path("module_layout.json")
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    layout_data = json.load(f)
                
                # Clear current zones
                for module in self.active_zone.modules[:]:
                    self.active_zone.remove_module(module["name"])
                for module in self.inactive_zone.modules[:]:
                    self.inactive_zone.remove_module(module["name"])
                
                # Restore layout
                for name in layout_data.get("active_modules", []):
                    if name in self.analysis_modules:
                        self.active_zone.add_module(name, self.analysis_modules[name])
                
                for name in layout_data.get("inactive_modules", []):
                    if name in self.analysis_modules:
                        self.inactive_zone.add_module(name, self.analysis_modules[name])
                
                print(f"📂 Layout loaded from {config_file}")
                
            except Exception as e:
                print(f"❌ Failed to load layout: {e}")
    
    def closeEvent(self, event):
        """بستن برنامه"""
        self.save_layout()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    window = VIPModuleManager()
    window.show()
    
    sys.exit(app.exec())
