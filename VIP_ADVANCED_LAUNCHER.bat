@echo off
chcp 65001 >nul
title 🚀 VIP BIG BANG - Advanced Professional Launcher

REM ===================================================================
REM 🚀 VIP BIG BANG - Advanced Professional Launcher
REM پیشرفته‌ترین سیستم راه‌اندازی VIP BIG BANG
REM ===================================================================

color 0A
cls

echo.
echo ████████████████████████████████████████████████████████████████████
echo ██                                                                ██
echo ██    🚀 VIP BIG BANG - ADVANCED PROFESSIONAL LAUNCHER 🚀        ██
echo ██                                                                ██
echo ██    ⚡ Quantum Trading System ⚡                                ██
echo ██    🎯 Real Data Extraction 🎯                                 ██
echo ██    🤖 AI-Powered Analysis 🤖                                  ██
echo ██                                                                ██
echo ████████████████████████████████████████████████████████████████████
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Administrator privileges detected
) else (
    echo ⚠️  Running without administrator privileges
    echo 💡 Some features may be limited
)

echo.
echo 🔍 System Diagnostics Starting...
echo ═══════════════════════════════════════

REM Check Python installation
echo 🐍 Checking Python installation...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Python is installed
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo    📋 Version: %%i
) else (
    echo ❌ Python is not installed or not in PATH
    echo 💡 Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Check virtual environment
echo 🔧 Checking virtual environment...
if exist "venv\Scripts\activate.bat" (
    echo ✅ Virtual environment found
) else (
    echo ⚠️  Virtual environment not found
    echo 🔧 Creating virtual environment...
    python -m venv venv
    if %errorLevel% == 0 (
        echo ✅ Virtual environment created
    ) else (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo 🚀 Activating virtual environment...
call venv\Scripts\activate.bat
if %errorLevel% == 0 (
    echo ✅ Virtual environment activated
) else (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

REM Check and install dependencies
echo 📦 Checking dependencies...
pip show playwright >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Playwright is installed
) else (
    echo 🔧 Installing Playwright...
    pip install playwright
    echo 🌐 Installing Playwright browsers...
    python -m playwright install
)

pip show pandas >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Pandas is installed
) else (
    echo 🔧 Installing Pandas...
    pip install pandas
)

pip show cryptography >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Cryptography is installed
) else (
    echo 🔧 Installing Cryptography...
    pip install cryptography
)

echo 📋 Installing all requirements...
pip install -r requirements.txt --quiet

echo.
echo 🎯 Advanced Launch Options
echo ═══════════════════════════════════════
echo.
echo [1] 🚀 Quick Launch (Recommended)
echo [2] 🔧 Full System Test
echo [3] 🌐 Chrome Extension Setup
echo [4] 🎭 Playwright Test
echo [5] 📊 Real Data Test
echo [6] ⚙️  Advanced Configuration
echo [7] 🔄 System Cleanup
echo [8] 📋 View System Status
echo [9] 🆘 Emergency Recovery
echo [0] ❌ Exit
echo.

set /p choice="🎯 Select option (1-9, 0 to exit): "

if "%choice%"=="1" goto quick_launch
if "%choice%"=="2" goto full_test
if "%choice%"=="3" goto chrome_setup
if "%choice%"=="4" goto playwright_test
if "%choice%"=="5" goto real_data_test
if "%choice%"=="6" goto advanced_config
if "%choice%"=="7" goto system_cleanup
if "%choice%"=="8" goto system_status
if "%choice%"=="9" goto emergency_recovery
if "%choice%"=="0" goto exit
goto invalid_choice

:quick_launch
echo.
echo 🚀 Quick Launch Mode
echo ═══════════════════════════════════════
echo ⚡ Starting VIP BIG BANG Quantum System...
echo.

REM Start the main system
python main.py
if %errorLevel% == 0 (
    echo ✅ VIP BIG BANG launched successfully
) else (
    echo ❌ Launch failed, trying alternative method...
    python vip_real_quotex_main.py
)
goto end

:full_test
echo.
echo 🔧 Full System Test
echo ═══════════════════════════════════════
echo 🧪 Running comprehensive system tests...
echo.

python test_real_data_system.py
if %errorLevel% == 0 (
    echo ✅ All tests passed!
    echo 🚀 System is ready for launch
    set /p launch="Launch VIP BIG BANG now? (y/n): "
    if /i "%launch%"=="y" goto quick_launch
) else (
    echo ❌ Some tests failed
    echo 🔧 Please check the issues above
)
goto menu

:chrome_setup
echo.
echo 🌐 Chrome Extension Setup
echo ═══════════════════════════════════════
echo 📋 Chrome Extension Installation Guide:
echo.
echo 1. Open Chrome browser
echo 2. Go to chrome://extensions/
echo 3. Enable "Developer mode" (top right)
echo 4. Click "Load unpacked"
echo 5. Select the "chrome_extension" folder
echo 6. Extension will be installed
echo.
echo 📂 Opening chrome_extension folder...
start explorer chrome_extension
echo.
echo 🌐 Opening Chrome Extensions page...
start chrome chrome://extensions/
echo.
echo ✅ Setup instructions displayed
pause
goto menu

:playwright_test
echo.
echo 🎭 Playwright Test
echo ═══════════════════════════════════════
echo 🧪 Testing Playwright installation...
echo.

python -c "from playwright.sync_api import sync_playwright; p = sync_playwright().start(); browser = p.chromium.launch(); page = browser.new_page(); page.goto('https://www.google.com'); print('✅ Playwright working - Title:', page.title()); browser.close(); p.stop()"
if %errorLevel% == 0 (
    echo ✅ Playwright test successful
) else (
    echo ❌ Playwright test failed
    echo 🔧 Reinstalling Playwright...
    pip install --upgrade playwright
    python -m playwright install
)
pause
goto menu

:real_data_test
echo.
echo 📊 Real Data Test
echo ═══════════════════════════════════════
echo 🧪 Testing real data extraction system...
echo.

python -c "from core.professional_quotex_real_extractor import ProfessionalQuotexRealExtractor; extractor = ProfessionalQuotexRealExtractor(); print('✅ Professional Quotex Extractor loaded successfully')"
if %errorLevel% == 0 (
    echo ✅ Real data system test successful
) else (
    echo ❌ Real data system test failed
    echo 🔧 Please check dependencies
)
pause
goto menu

:advanced_config
echo.
echo ⚙️  Advanced Configuration
echo ═══════════════════════════════════════
echo.
echo [1] 🔧 Update Dependencies
echo [2] 🌐 Configure Network Settings
echo [3] 🎯 Set Trading Parameters
echo [4] 🔒 Security Settings
echo [5] 📊 Performance Tuning
echo [6] 🔙 Back to Main Menu
echo.

set /p config_choice="Select configuration option: "

if "%config_choice%"=="1" (
    echo 🔧 Updating all dependencies...
    pip install --upgrade -r requirements.txt
    python -m playwright install
    echo ✅ Dependencies updated
)
if "%config_choice%"=="2" (
    echo 🌐 Network configuration...
    echo Current network settings:
    ipconfig | findstr "IPv4"
    echo ✅ Network info displayed
)
if "%config_choice%"=="3" (
    echo 🎯 Trading parameters...
    echo Opening configuration file...
    if exist config.json (
        start notepad config.json
    ) else (
        echo Creating default config...
        echo {"trading_enabled": true, "risk_level": "medium"} > config.json
        start notepad config.json
    )
)
if "%config_choice%"=="4" (
    echo 🔒 Security settings...
    echo Checking security status...
    echo ✅ Security check completed
)
if "%config_choice%"=="5" (
    echo 📊 Performance tuning...
    echo Optimizing system performance...
    echo ✅ Performance optimization completed
)
if "%config_choice%"=="6" goto menu

pause
goto menu

:system_cleanup
echo.
echo 🔄 System Cleanup
echo ═══════════════════════════════════════
echo 🧹 Cleaning temporary files...

if exist __pycache__ (
    rmdir /s /q __pycache__
    echo ✅ Python cache cleared
)

if exist logs\*.log (
    del /q logs\*.log
    echo ✅ Log files cleared
)

if exist temp_extension (
    rmdir /s /q temp_extension
    echo ✅ Temporary extension files cleared
)

echo ✅ System cleanup completed
pause
goto menu

:system_status
echo.
echo 📋 System Status
echo ═══════════════════════════════════════
echo.

echo 🐍 Python Status:
python --version

echo.
echo 📦 Key Dependencies:
pip show playwright pandas cryptography | findstr "Name Version"

echo.
echo 📁 Project Structure:
if exist main.py echo ✅ main.py
if exist vip_real_quotex_main.py echo ✅ vip_real_quotex_main.py
if exist chrome_extension echo ✅ chrome_extension folder
if exist core echo ✅ core folder
if exist requirements.txt echo ✅ requirements.txt

echo.
echo 🌐 Network Status:
ping -n 1 google.com >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Internet connection active
) else (
    echo ❌ No internet connection
)

pause
goto menu

:emergency_recovery
echo.
echo 🆘 Emergency Recovery
echo ═══════════════════════════════════════
echo 🔧 Attempting system recovery...
echo.

echo 1. Recreating virtual environment...
if exist venv rmdir /s /q venv
python -m venv venv
call venv\Scripts\activate.bat

echo 2. Reinstalling core dependencies...
pip install playwright pandas cryptography

echo 3. Installing Playwright browsers...
python -m playwright install

echo 4. Installing all requirements...
pip install -r requirements.txt

echo ✅ Emergency recovery completed
echo 🚀 Try launching the system now
pause
goto menu

:invalid_choice
echo.
echo ❌ Invalid choice. Please select a valid option.
pause
goto menu

:menu
cls
goto :eof

:end
echo.
echo 🎉 VIP BIG BANG session completed
echo 💡 Thank you for using VIP BIG BANG Professional System
pause

:exit
echo.
echo 👋 Goodbye! VIP BIG BANG Professional Launcher closing...
timeout /t 2 >nul
exit /b 0
