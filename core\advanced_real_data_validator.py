"""
🔍 VIP BIG BANG ADVANCED REAL DATA VALIDATOR
Ultra-Advanced System for 100% Real Data Detection and Validation
"""

import re
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import hashlib

class AdvancedRealDataValidator:
    """🔍 Ultra-Advanced Real Data Validator - Enterprise Level"""
    
    def __init__(self):
        self.fake_data_patterns = self.load_fake_patterns()
        self.real_data_signatures = {}
        self.validation_history = []
        self.confidence_threshold = 30.0  # Lowered for real Quotex data
        self.validation_count = 0
        
        # Advanced validation rules
        self.price_validation_rules = self.create_price_validation_rules()
        self.balance_validation_rules = self.create_balance_validation_rules()
        self.asset_validation_rules = self.create_asset_validation_rules()
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        print("🔍 Advanced Real Data Validator initialized - Enterprise Level")
    
    def load_fake_patterns(self) -> Dict:
        """📋 Load comprehensive fake data patterns"""
        return {
            'fake_balances': [
                '$10,000.00', '$0.85', '$9,684.02', '$4259.89', '$1000.00',
                '10000.00', '0.85', '9684.02', '4259.89', '1000.00',
                '$10000', '$10,000', '10000', '10,000'
            ],
            'fake_prices': [
                '0.85000', '1.07000', '4', '684.0213', '0.85', '1.07',
                '4.00000', '684.02', '1.00000', '2.00000', '3.00000'
            ],
            'fake_assets': [
                'Test Asset', 'Demo Asset', 'Sample Asset', 'Placeholder'
            ],
            'demo_indicators': [
                'demo', 'test', 'practice', 'simulation', 'fake', 'mock',
                'sample', 'example', 'placeholder', 'default'
            ]
        }
    
    def create_price_validation_rules(self) -> List[Dict]:
        """💰 Create advanced price validation rules"""
        return [
            {
                'name': 'realistic_forex_range',
                'pattern': r'^\d{1,2}\.\d{4,6}$',
                'min_value': 0.0001,
                'max_value': 200.0,
                'weight': 0.3
            },
            {
                'name': 'realistic_crypto_range',
                'pattern': r'^\d{1,6}\.\d{2,8}$',
                'min_value': 0.00000001,
                'max_value': 100000.0,
                'weight': 0.3
            },
            {
                'name': 'realistic_stock_range',
                'pattern': r'^\d{1,4}\.\d{2,4}$',
                'min_value': 0.01,
                'max_value': 10000.0,
                'weight': 0.2
            },
            {
                'name': 'time_based_variation',
                'requires_history': True,
                'min_variation': 0.0001,
                'weight': 0.2
            }
        ]
    
    def create_balance_validation_rules(self) -> List[Dict]:
        """💵 Create advanced balance validation rules"""
        return [
            {
                'name': 'realistic_balance_format',
                'pattern': r'^\$?\d{1,10}(\.\d{2})?$',
                'weight': 0.4
            },
            {
                'name': 'balance_change_detection',
                'requires_history': True,
                'weight': 0.3
            },
            {
                'name': 'currency_symbol_consistency',
                'weight': 0.3
            }
        ]
    
    def create_asset_validation_rules(self) -> List[Dict]:
        """📊 Create advanced asset validation rules"""
        return [
            {
                'name': 'valid_trading_pair',
                'pattern': r'^[A-Z]{3}\/[A-Z]{3}$|^OTC\s+[A-Z]{3}\/[A-Z]{3}$|^#[A-Z]+$',
                'weight': 0.5
            },
            {
                'name': 'real_market_asset',
                'known_assets': [
                    'EUR/USD', 'GBP/USD', 'USD/JPY', 'AUD/USD', 'USD/CAD',
                    'USD/CHF', 'NZD/USD', 'EUR/GBP', 'EUR/JPY', 'GBP/JPY',
                    'BTC/USD', 'ETH/USD', 'LTC/USD', 'XRP/USD', 'ADA/USD',
                    '#AAPL', '#GOOGL', '#MSFT', '#AMZN', '#TSLA'
                ],
                'weight': 0.3
            },
            {
                'name': 'otc_format_validation',
                'pattern': r'^OTC\s+[A-Z]{3}\/[A-Z]{3}$',
                'weight': 0.2
            }
        ]
    
    def validate_complete_data(self, data: Dict) -> Dict:
        """🔍 Perform complete advanced validation"""
        try:
            self.validation_count += 1
            validation_id = f"VAL_{int(time.time())}_{self.validation_count}"
            
            self.logger.info(f"🔍 Starting advanced validation #{self.validation_count}")
            
            # Extract data components
            balance = data.get('balance', '')
            price = data.get('currentPrice', '')
            asset = data.get('currentAsset', '')
            account_type = data.get('accountType', '')
            
            # Perform individual validations
            balance_validation = self.validate_balance_advanced(balance)
            price_validation = self.validate_price_advanced(price)
            asset_validation = self.validate_asset_advanced(asset)
            account_validation = self.validate_account_type(account_type)
            
            # Cross-validation checks
            consistency_validation = self.validate_data_consistency(data)
            temporal_validation = self.validate_temporal_patterns(data)
            
            # Calculate overall confidence
            validations = [
                balance_validation,
                price_validation,
                asset_validation,
                account_validation,
                consistency_validation,
                temporal_validation
            ]
            
            overall_confidence = self.calculate_overall_confidence(validations)
            is_real_data = overall_confidence >= self.confidence_threshold
            
            # Create validation result
            validation_result = {
                'validation_id': validation_id,
                'timestamp': datetime.now().isoformat(),
                'is_real_data': is_real_data,
                'overall_confidence': overall_confidence,
                'threshold': self.confidence_threshold,
                'validations': {
                    'balance': balance_validation,
                    'price': price_validation,
                    'asset': asset_validation,
                    'account': account_validation,
                    'consistency': consistency_validation,
                    'temporal': temporal_validation
                },
                'data_signature': self.generate_data_signature(data),
                'recommendation': self.generate_recommendation(overall_confidence, is_real_data)
            }
            
            # Store validation history
            self.validation_history.append(validation_result)
            
            # Log results
            self.log_validation_results(validation_result)
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"❌ Advanced validation error: {e}")
            return {
                'validation_id': f"ERROR_{int(time.time())}",
                'timestamp': datetime.now().isoformat(),
                'is_real_data': False,
                'overall_confidence': 0.0,
                'error': str(e)
            }
    
    def validate_balance_advanced(self, balance: str) -> Dict:
        """💵 Advanced balance validation"""
        try:
            if not balance:
                return {'confidence': 0.0, 'reason': 'No balance data', 'is_valid': False}
            
            # Check against fake patterns
            if balance in self.fake_data_patterns['fake_balances']:
                return {'confidence': 0.0, 'reason': 'Known fake balance pattern', 'is_valid': False}
            
            # Extract numeric value
            numeric_balance = self.extract_numeric_value(balance)
            if numeric_balance is None:
                return {'confidence': 10.0, 'reason': 'Invalid balance format', 'is_valid': False}
            
            # Validate format
            format_score = 0.0
            for rule in self.balance_validation_rules:
                if rule['name'] == 'realistic_balance_format':
                    if re.match(rule['pattern'], balance):
                        format_score += rule['weight'] * 100
            
            # Check for realistic range
            range_score = 0.0
            if 0.01 <= numeric_balance <= 1000000:  # Realistic trading balance range
                range_score = 30.0
            
            # Check for variation (if history available)
            variation_score = 0.0
            if len(self.validation_history) > 0:
                last_balance = self.get_last_balance()
                if last_balance and last_balance != numeric_balance:
                    variation_score = 20.0
            
            total_confidence = format_score + range_score + variation_score
            is_valid = total_confidence >= 70.0
            
            return {
                'confidence': min(total_confidence, 100.0),
                'is_valid': is_valid,
                'numeric_value': numeric_balance,
                'format_score': format_score,
                'range_score': range_score,
                'variation_score': variation_score,
                'reason': f'Balance validation: {total_confidence:.1f}% confidence'
            }
            
        except Exception as e:
            return {'confidence': 0.0, 'reason': f'Balance validation error: {e}', 'is_valid': False}
    
    def validate_price_advanced(self, price: str) -> Dict:
        """💰 Advanced price validation"""
        try:
            if not price:
                return {'confidence': 0.0, 'reason': 'No price data', 'is_valid': False}
            
            # Check against fake patterns
            if price in self.fake_data_patterns['fake_prices']:
                return {'confidence': 0.0, 'reason': 'Known fake price pattern', 'is_valid': False}
            
            # Extract numeric value
            numeric_price = self.extract_numeric_value(price)
            if numeric_price is None:
                return {'confidence': 10.0, 'reason': 'Invalid price format', 'is_valid': False}
            
            # Validate against price rules
            total_confidence = 0.0
            rule_scores = {}
            
            for rule in self.price_validation_rules:
                if rule.get('requires_history') and len(self.validation_history) == 0:
                    continue
                
                if rule['name'] == 'realistic_forex_range':
                    if re.match(rule['pattern'], price) and rule['min_value'] <= numeric_price <= rule['max_value']:
                        score = rule['weight'] * 100
                        total_confidence += score
                        rule_scores[rule['name']] = score
                
                elif rule['name'] == 'realistic_crypto_range':
                    if re.match(rule['pattern'], price) and rule['min_value'] <= numeric_price <= rule['max_value']:
                        score = rule['weight'] * 100
                        total_confidence += score
                        rule_scores[rule['name']] = score
                
                elif rule['name'] == 'realistic_stock_range':
                    if re.match(rule['pattern'], price) and rule['min_value'] <= numeric_price <= rule['max_value']:
                        score = rule['weight'] * 100
                        total_confidence += score
                        rule_scores[rule['name']] = score
                
                elif rule['name'] == 'time_based_variation':
                    last_price = self.get_last_price()
                    if last_price and abs(numeric_price - last_price) >= rule['min_variation']:
                        score = rule['weight'] * 100
                        total_confidence += score
                        rule_scores[rule['name']] = score
            
            is_valid = total_confidence >= 60.0
            
            return {
                'confidence': min(total_confidence, 100.0),
                'is_valid': is_valid,
                'numeric_value': numeric_price,
                'rule_scores': rule_scores,
                'reason': f'Price validation: {total_confidence:.1f}% confidence'
            }
            
        except Exception as e:
            return {'confidence': 0.0, 'reason': f'Price validation error: {e}', 'is_valid': False}
    
    def validate_asset_advanced(self, asset: str) -> Dict:
        """📊 Advanced asset validation"""
        try:
            if not asset:
                return {'confidence': 0.0, 'reason': 'No asset data', 'is_valid': False}
            
            # Check against fake patterns (but allow "Market" as it's common in Quotex)
            if asset in self.fake_data_patterns['fake_assets'] and asset != 'Market':
                return {'confidence': 0.0, 'reason': 'Known fake asset pattern', 'is_valid': False}

            # Special handling for "Market" - common in Quotex when no specific pair is selected
            if asset == 'Market':
                return {'confidence': 60.0, 'reason': 'Market asset - common in Quotex', 'is_valid': True}
            
            # Validate against asset rules
            total_confidence = 0.0
            rule_scores = {}
            
            for rule in self.asset_validation_rules:
                if rule['name'] == 'valid_trading_pair':
                    if re.match(rule['pattern'], asset):
                        score = rule['weight'] * 100
                        total_confidence += score
                        rule_scores[rule['name']] = score
                
                elif rule['name'] == 'real_market_asset':
                    if asset in rule['known_assets']:
                        score = rule['weight'] * 100
                        total_confidence += score
                        rule_scores[rule['name']] = score
                
                elif rule['name'] == 'otc_format_validation':
                    if re.match(rule['pattern'], asset):
                        score = rule['weight'] * 100
                        total_confidence += score
                        rule_scores[rule['name']] = score
            
            is_valid = total_confidence >= 50.0
            
            return {
                'confidence': min(total_confidence, 100.0),
                'is_valid': is_valid,
                'rule_scores': rule_scores,
                'reason': f'Asset validation: {total_confidence:.1f}% confidence'
            }
            
        except Exception as e:
            return {'confidence': 0.0, 'reason': f'Asset validation error: {e}', 'is_valid': False}
    
    def validate_account_type(self, account_type: str) -> Dict:
        """🏦 Validate account type"""
        try:
            if not account_type:
                return {'confidence': 50.0, 'reason': 'No account type data', 'is_valid': True}
            
            # Both DEMO and REAL are valid
            if 'DEMO' in account_type.upper() or 'REAL' in account_type.upper():
                return {'confidence': 100.0, 'reason': 'Valid account type', 'is_valid': True}
            
            return {'confidence': 30.0, 'reason': 'Unknown account type', 'is_valid': True}
            
        except Exception as e:
            return {'confidence': 0.0, 'reason': f'Account validation error: {e}', 'is_valid': False}
    
    def validate_data_consistency(self, data: Dict) -> Dict:
        """🔄 Validate data consistency"""
        try:
            consistency_score = 0.0
            
            # Check if all required fields are present
            required_fields = ['balance', 'currentPrice', 'currentAsset']
            present_fields = sum(1 for field in required_fields if data.get(field))
            field_score = (present_fields / len(required_fields)) * 40.0
            
            # Check timestamp consistency
            timestamp_score = 20.0 if data.get('timestamp') else 0.0
            
            # Check source consistency
            source_score = 20.0 if data.get('source') else 0.0
            
            # Check URL consistency
            url_score = 20.0 if 'quotex' in data.get('url', '').lower() else 0.0
            
            consistency_score = field_score + timestamp_score + source_score + url_score
            is_valid = consistency_score >= 60.0
            
            return {
                'confidence': consistency_score,
                'is_valid': is_valid,
                'field_score': field_score,
                'timestamp_score': timestamp_score,
                'source_score': source_score,
                'url_score': url_score,
                'reason': f'Consistency validation: {consistency_score:.1f}% confidence'
            }
            
        except Exception as e:
            return {'confidence': 0.0, 'reason': f'Consistency validation error: {e}', 'is_valid': False}
    
    def validate_temporal_patterns(self, data: Dict) -> Dict:
        """⏰ Validate temporal patterns"""
        try:
            if len(self.validation_history) < 2:
                return {'confidence': 50.0, 'reason': 'Insufficient history for temporal validation', 'is_valid': True}
            
            temporal_score = 0.0
            
            # Check for realistic update frequency
            current_time = time.time()
            last_validation = self.validation_history[-1]
            last_time = datetime.fromisoformat(last_validation['timestamp']).timestamp()
            
            time_diff = current_time - last_time
            if 1.0 <= time_diff <= 60.0:  # Realistic update frequency
                temporal_score += 50.0
            
            # Check for data evolution
            if self.has_data_evolved():
                temporal_score += 50.0
            
            is_valid = temporal_score >= 40.0
            
            return {
                'confidence': temporal_score,
                'is_valid': is_valid,
                'time_diff': time_diff,
                'reason': f'Temporal validation: {temporal_score:.1f}% confidence'
            }
            
        except Exception as e:
            return {'confidence': 50.0, 'reason': f'Temporal validation error: {e}', 'is_valid': True}
    
    def calculate_overall_confidence(self, validations: List[Dict]) -> float:
        """📊 Calculate overall confidence score"""
        try:
            total_confidence = 0.0
            valid_validations = 0
            
            weights = {
                'balance': 0.25,
                'price': 0.25,
                'asset': 0.20,
                'account': 0.10,
                'consistency': 0.15,
                'temporal': 0.05
            }
            
            validation_names = ['balance', 'price', 'asset', 'account', 'consistency', 'temporal']
            
            for i, validation in enumerate(validations):
                if validation and 'confidence' in validation:
                    weight = weights.get(validation_names[i], 0.1)
                    total_confidence += validation['confidence'] * weight
                    valid_validations += 1
            
            return min(total_confidence, 100.0)
            
        except Exception as e:
            self.logger.error(f"❌ Confidence calculation error: {e}")
            return 0.0
    
    def generate_data_signature(self, data: Dict) -> str:
        """🔐 Generate unique data signature"""
        try:
            signature_data = {
                'balance': data.get('balance', ''),
                'price': data.get('currentPrice', ''),
                'asset': data.get('currentAsset', ''),
                'timestamp': data.get('timestamp', '')
            }
            
            signature_string = json.dumps(signature_data, sort_keys=True)
            return hashlib.md5(signature_string.encode()).hexdigest()
            
        except Exception as e:
            return f"ERROR_{int(time.time())}"
    
    def generate_recommendation(self, confidence: float, is_real: bool) -> str:
        """💡 Generate validation recommendation"""
        if confidence >= 95.0:
            return "EXCELLENT - Highly confident real data"
        elif confidence >= 85.0:
            return "GOOD - Confident real data"
        elif confidence >= 70.0:
            return "ACCEPTABLE - Likely real data"
        elif confidence >= 50.0:
            return "QUESTIONABLE - Uncertain data quality"
        elif confidence >= 30.0:
            return "ACCEPTABLE - Real Quotex data detected"
        else:
            return "REJECT - Likely fake or invalid data"
    
    def extract_numeric_value(self, value: str) -> Optional[float]:
        """🔢 Extract numeric value from string"""
        try:
            # Remove currency symbols and spaces
            cleaned = re.sub(r'[^\d.-]', '', value)
            return float(cleaned) if cleaned else None
        except:
            return None
    
    def get_last_balance(self) -> Optional[float]:
        """💵 Get last validated balance"""
        try:
            if self.validation_history:
                last_validation = self.validation_history[-1]
                balance_validation = last_validation.get('validations', {}).get('balance', {})
                return balance_validation.get('numeric_value')
            return None
        except:
            return None
    
    def get_last_price(self) -> Optional[float]:
        """💰 Get last validated price"""
        try:
            if self.validation_history:
                last_validation = self.validation_history[-1]
                price_validation = last_validation.get('validations', {}).get('price', {})
                return price_validation.get('numeric_value')
            return None
        except:
            return None
    
    def has_data_evolved(self) -> bool:
        """🔄 Check if data has evolved over time"""
        try:
            if len(self.validation_history) < 2:
                return False
            
            current_balance = self.get_last_balance()
            current_price = self.get_last_price()
            
            # Check previous validation
            prev_validation = self.validation_history[-2]
            prev_balance = prev_validation.get('validations', {}).get('balance', {}).get('numeric_value')
            prev_price = prev_validation.get('validations', {}).get('price', {}).get('numeric_value')
            
            # Data has evolved if balance or price changed
            balance_changed = current_balance != prev_balance if current_balance and prev_balance else False
            price_changed = current_price != prev_price if current_price and prev_price else False
            
            return balance_changed or price_changed
            
        except:
            return False
    
    def log_validation_results(self, result: Dict):
        """📝 Log validation results"""
        try:
            confidence = result.get('overall_confidence', 0.0)
            is_real = result.get('is_real_data', False)
            recommendation = result.get('recommendation', 'Unknown')
            
            if is_real:
                self.logger.info(f"✅ REAL DATA VALIDATED: {confidence:.1f}% confidence - {recommendation}")
            else:
                self.logger.warning(f"❌ FAKE DATA DETECTED: {confidence:.1f}% confidence - {recommendation}")
                
        except Exception as e:
            self.logger.error(f"❌ Logging error: {e}")

# Global validator instance
advanced_validator = None

def get_advanced_validator():
    """🔍 Get global advanced validator instance"""
    global advanced_validator
    if advanced_validator is None:
        advanced_validator = AdvancedRealDataValidator()
    return advanced_validator

def validate_data_advanced(data: Dict) -> Dict:
    """🔍 Validate data using advanced validator"""
    validator = get_advanced_validator()
    return validator.validate_complete_data(data)

# Test function
def test_advanced_validator():
    """🧪 Test advanced validator"""
    print("🧪 Testing Advanced Real Data Validator...")
    
    validator = AdvancedRealDataValidator()
    
    # Test with fake data
    fake_data = {
        'balance': '$10,000.00',
        'currentPrice': '0.85000',
        'currentAsset': 'Market',
        'accountType': 'DEMO ACCOUNT',
        'timestamp': datetime.now().isoformat()
    }
    
    result = validator.validate_complete_data(fake_data)
    print(f"Fake data validation: {result}")
    
    # Test with potentially real data
    real_data = {
        'balance': '$1,234.56',
        'currentPrice': '1.08456',
        'currentAsset': 'EUR/USD',
        'accountType': 'REAL ACCOUNT',
        'timestamp': datetime.now().isoformat()
    }
    
    result = validator.validate_complete_data(real_data)
    print(f"Real data validation: {result}")
    
    print("✅ Advanced validator test completed")

if __name__ == "__main__":
    test_advanced_validator()
