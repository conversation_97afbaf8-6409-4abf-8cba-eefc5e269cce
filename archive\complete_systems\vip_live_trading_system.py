#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG Live Trading System
سیستم ترید زنده کامل با اتصال واقعی به Quotex
"""

import sys
import asyncio
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

# Add project root to path
from pathlib import Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, Q<PERSON><PERSON><PERSON>, QPushButton, QTextEdit, QGroupBox
from PySide6.QtCore import QTimer, Signal, QThread
from PySide6.QtGui import QFont

# Import VIP BIG BANG core systems
from core.analysis_engine import AnalysisEngine
from core.signal_manager import SignalManager
from core.settings import Settings
from core.data_extractor import DataExtractor
from core.quotex_login import QuotexLogin
from core.realtime_quotex_connector import RealtimeQuotexConnector
from trading.autotrade import AutoTrader
from utils.logger import setup_logger

class LiveTradingWorker(QThread):
    """
    🔄 Live Trading Worker Thread
    اجرای ترید زنده در thread جداگانه
    """
    
    signal_update = Signal(dict)
    trade_executed = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, trading_system):
        super().__init__()
        self.trading_system = trading_system
        self.running = False
        
    def run(self):
        """اجرای حلقه ترید زنده"""
        self.running = True
        asyncio.run(self.trading_loop())
    
    async def trading_loop(self):
        """حلقه اصلی ترید"""
        while self.running:
            try:
                # دریافت داده‌های زنده
                market_data = await self.trading_system.get_live_data()
                
                # تحلیل بازار
                analysis_result = self.trading_system.analyze_market(market_data)
                
                # ارسال به‌روزرسانی
                self.signal_update.emit({
                    'market_data': market_data,
                    'analysis': analysis_result,
                    'timestamp': datetime.now().isoformat()
                })
                
                # بررسی شرایط ترید
                if self.trading_system.should_trade(analysis_result):
                    trade_result = await self.trading_system.execute_trade(analysis_result)
                    self.trade_executed.emit(trade_result)
                
                # انتظار برای تحلیل بعدی (15 ثانیه)
                await asyncio.sleep(15)
                
            except Exception as e:
                self.error_occurred.emit(str(e))
                await asyncio.sleep(5)
    
    def stop(self):
        """توقف ترید"""
        self.running = False

class VIPLiveTradingSystem:
    """
    🎯 VIP BIG BANG Live Trading System
    سیستم ترید زنده کامل
    """
    
    def __init__(self):
        self.logger = setup_logger("LiveTrading")
        self.settings = Settings()
        
        # Core components
        self.analysis_engine = AnalysisEngine(self.settings)
        self.signal_manager = SignalManager(self.settings)

        # Initialize quotex client first
        from trading.quotex_client import QuotexClient
        self.quotex_client = QuotexClient(self.settings)

        # Initialize auto trader with required parameters
        self.auto_trader = AutoTrader(self.quotex_client, self.signal_manager)
        
        # Connection components
        self.data_extractor = None
        self.quotex_login = None
        self.realtime_connector = None
        
        # Trading state
        self.is_connected = False
        self.is_trading = False
        self.account_type = "demo"  # demo or live
        self.current_balance = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        
        # Market data
        self.current_market_data = {}
        self.last_analysis = {}
        
        self.logger.info("🚀 VIP Live Trading System initialized")
    
    async def connect_to_quotex(self, email: str = None, password: str = None) -> bool:
        """🌐 اتصال به Quotex"""
        try:
            self.logger.info("🌐 Connecting to Quotex...")
            
            # Initialize data extractor
            self.data_extractor = DataExtractor()
            
            # Initialize browser
            if await self.data_extractor.initialize_stealth_browser():
                self.logger.info("✅ Browser initialized")
                
                # Connect to Quotex
                if await self.data_extractor.connect_to_quotex():
                    self.logger.info("✅ Connected to Quotex")
                    
                    # Initialize login manager
                    self.quotex_login = QuotexLogin(self.data_extractor.page)
                    
                    # Auto login or manual login
                    if email and password:
                        login_success = await self.quotex_login.login_with_email(email, password)
                    else:
                        login_success = await self.quotex_login.auto_login()
                    
                    if login_success:
                        self.logger.info("✅ Login successful")
                        self.is_connected = True
                        
                        # Initialize realtime connector
                        self.realtime_connector = RealtimeQuotexConnector()
                        await self.realtime_connector.connect()
                        
                        return True
                    else:
                        self.logger.error("❌ Login failed")
                        return False
                else:
                    self.logger.error("❌ Failed to connect to Quotex")
                    return False
            else:
                self.logger.error("❌ Failed to initialize browser")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Connection error: {e}")
            return False
    
    async def get_live_data(self) -> Dict:
        """📊 دریافت داده‌های زنده"""
        try:
            if not self.is_connected or not self.data_extractor:
                return {}
            
            # Extract live data
            live_data = await self.data_extractor.extract_all_data()
            
            # Get additional data from realtime connector
            if self.realtime_connector:
                realtime_data = await self.realtime_connector.get_current_data()
                live_data.update(realtime_data)
            
            self.current_market_data = live_data
            return live_data
            
        except Exception as e:
            self.logger.error(f"❌ Error getting live data: {e}")
            return {}
    
    def analyze_market(self, market_data: Dict) -> Dict:
        """🧠 تحلیل بازار"""
        try:
            if not market_data:
                return {}
            
            # Update analysis engine with live data
            self.analysis_engine.update_market_data(market_data)
            
            # Perform analysis
            analysis_result = self.analysis_engine.analyze()
            
            # Generate signals
            signals = self.signal_manager.generate_signals(analysis_result)
            
            # Combine results
            complete_analysis = {
                **analysis_result,
                'signals': signals,
                'market_data': market_data,
                'timestamp': datetime.now().isoformat()
            }
            
            self.last_analysis = complete_analysis
            return complete_analysis
            
        except Exception as e:
            self.logger.error(f"❌ Analysis error: {e}")
            return {}
    
    def should_trade(self, analysis: Dict) -> bool:
        """🎯 بررسی شرایط ترید"""
        try:
            if not analysis or not self.is_connected:
                return False
            
            # Check minimum confidence
            confidence = analysis.get('confidence', 0)
            if confidence < 0.8:  # حداقل 80% اعتماد
                return False
            
            # Check signal strength
            overall_score = analysis.get('overall_score', 0)
            if overall_score < 0.7:  # حداقل امتیاز 0.7
                return False
            
            # Check direction
            direction = analysis.get('direction', 'NEUTRAL')
            if direction == 'NEUTRAL':
                return False
            
            # Check signal confirmations (8 confirmations required)
            signals = analysis.get('signals', {})
            confirmations = sum(1 for signal in signals.values() if signal.get('strength', 0) > 0.6)
            
            if confirmations < 8:  # حداقل 8 تأیید
                return False
            
            # Check account balance
            if self.current_balance < 1.0:  # حداقل 1 دلار
                return False
            
            self.logger.info(f"🎯 Trade conditions met: Direction={direction}, Confidence={confidence:.3f}, Score={overall_score:.3f}, Confirmations={confirmations}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error checking trade conditions: {e}")
            return False
    
    async def execute_trade(self, analysis: Dict) -> Dict:
        """💰 اجرای ترید"""
        try:
            direction = analysis.get('direction', 'NEUTRAL')
            confidence = analysis.get('confidence', 0)
            
            # Calculate trade amount (2% of balance)
            trade_amount = max(1.0, self.current_balance * 0.02)
            
            # Trade parameters
            trade_params = {
                'direction': direction,
                'amount': trade_amount,
                'duration': 5,  # 5 seconds
                'asset': self.current_market_data.get('symbol', 'EURUSD'),
                'account_type': self.account_type
            }
            
            self.logger.info(f"💰 Executing trade: {trade_params}")
            
            # Execute through auto trader
            trade_result = await self.auto_trader.execute_trade(trade_params)
            
            # Update statistics
            self.total_trades += 1
            if trade_result.get('success', False):
                if trade_result.get('result') == 'win':
                    self.winning_trades += 1
            
            # Calculate win rate
            win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
            
            trade_result.update({
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'win_rate': win_rate,
                'timestamp': datetime.now().isoformat()
            })
            
            self.logger.info(f"📊 Trade executed: Success={trade_result.get('success')}, Win Rate={win_rate:.1f}%")
            
            return trade_result
            
        except Exception as e:
            self.logger.error(f"❌ Trade execution error: {e}")
            return {'success': False, 'error': str(e)}
    
    def switch_account_type(self, account_type: str):
        """🔄 تغییر نوع حساب"""
        if account_type in ['demo', 'live']:
            self.account_type = account_type
            self.logger.info(f"🔄 Switched to {account_type} account")
        else:
            self.logger.error(f"❌ Invalid account type: {account_type}")
    
    async def get_account_info(self) -> Dict:
        """💳 دریافت اطلاعات حساب"""
        try:
            if not self.is_connected or not self.data_extractor:
                return {}
            
            balance = await self.data_extractor.extract_balance()
            account_type = await self.data_extractor.extract_account_type()
            
            self.current_balance = balance
            self.account_type = account_type
            
            return {
                'balance': balance,
                'account_type': account_type,
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'win_rate': (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting account info: {e}")
            return {}
    
    def start_trading(self):
        """🚀 شروع ترید زنده"""
        if self.is_connected and not self.is_trading:
            self.is_trading = True
            self.logger.info("🚀 Live trading started")
        else:
            self.logger.warning("⚠️ Cannot start trading - not connected or already trading")
    
    def stop_trading(self):
        """⏹️ توقف ترید"""
        self.is_trading = False
        self.logger.info("⏹️ Live trading stopped")
    
    async def disconnect(self):
        """🔌 قطع اتصال"""
        try:
            self.stop_trading()
            
            if self.realtime_connector:
                await self.realtime_connector.disconnect()
            
            if self.data_extractor:
                await self.data_extractor.close()
            
            self.is_connected = False
            self.logger.info("🔌 Disconnected from Quotex")
            
        except Exception as e:
            self.logger.error(f"❌ Disconnect error: {e}")

class VIPLiveTradingUI(QMainWindow):
    """
    🎮 VIP Live Trading UI
    رابط کاربری ترید زنده
    """
    
    def __init__(self):
        super().__init__()
        self.trading_system = VIPLiveTradingSystem()
        self.trading_worker = None
        
        self.setWindowTitle("🚀 VIP BIG BANG Live Trading System")
        self.setGeometry(100, 100, 1400, 900)
        
        self.setup_ui()
        self.setup_timers()
    
    def setup_ui(self):
        """راه‌اندازی رابط کاربری"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = QHBoxLayout()
        title = QLabel("🚀 VIP BIG BANG Live Trading System")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        header_layout.addWidget(title)
        
        # Connection controls
        self.connect_btn = QPushButton("🌐 Connect to Quotex")
        self.connect_btn.clicked.connect(self.connect_to_quotex)
        header_layout.addWidget(self.connect_btn)
        
        self.start_trading_btn = QPushButton("🚀 Start Trading")
        self.start_trading_btn.clicked.connect(self.start_trading)
        self.start_trading_btn.setEnabled(False)
        header_layout.addWidget(self.start_trading_btn)
        
        self.stop_trading_btn = QPushButton("⏹️ Stop Trading")
        self.stop_trading_btn.clicked.connect(self.stop_trading)
        self.stop_trading_btn.setEnabled(False)
        header_layout.addWidget(self.stop_trading_btn)
        
        main_layout.addLayout(header_layout)
        
        # Content area
        content_layout = QHBoxLayout()
        
        # Left panel - Market data and analysis
        left_panel = QGroupBox("📊 Market Analysis")
        left_layout = QVBoxLayout(left_panel)
        
        self.market_data_text = QTextEdit()
        self.market_data_text.setMaximumHeight(200)
        left_layout.addWidget(QLabel("Market Data:"))
        left_layout.addWidget(self.market_data_text)
        
        self.analysis_text = QTextEdit()
        self.analysis_text.setMaximumHeight(200)
        left_layout.addWidget(QLabel("Analysis Results:"))
        left_layout.addWidget(self.analysis_text)
        
        content_layout.addWidget(left_panel)
        
        # Right panel - Trading info
        right_panel = QGroupBox("💰 Trading Information")
        right_layout = QVBoxLayout(right_panel)
        
        self.account_info_text = QTextEdit()
        self.account_info_text.setMaximumHeight(150)
        right_layout.addWidget(QLabel("Account Info:"))
        right_layout.addWidget(self.account_info_text)
        
        self.trading_log_text = QTextEdit()
        right_layout.addWidget(QLabel("Trading Log:"))
        right_layout.addWidget(self.trading_log_text)
        
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
        
        # Status bar
        self.status_label = QLabel("Status: Disconnected")
        main_layout.addWidget(self.status_label)
    
    def setup_timers(self):
        """راه‌اندازی تایمرها"""
        # Update timer for UI
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(1000)  # Update every second
    
    def connect_to_quotex(self):
        """اتصال به Quotex"""
        asyncio.run(self._connect_async())
    
    async def _connect_async(self):
        """اتصال async"""
        self.connect_btn.setEnabled(False)
        self.status_label.setText("Status: Connecting...")
        
        success = await self.trading_system.connect_to_quotex()
        
        if success:
            self.status_label.setText("Status: Connected")
            self.start_trading_btn.setEnabled(True)
            self.connect_btn.setText("✅ Connected")
        else:
            self.status_label.setText("Status: Connection Failed")
            self.connect_btn.setEnabled(True)
    
    def start_trading(self):
        """شروع ترید"""
        self.trading_system.start_trading()
        
        # Start worker thread
        self.trading_worker = LiveTradingWorker(self.trading_system)
        self.trading_worker.signal_update.connect(self.on_trading_update)
        self.trading_worker.trade_executed.connect(self.on_trade_executed)
        self.trading_worker.error_occurred.connect(self.on_error)
        self.trading_worker.start()
        
        self.start_trading_btn.setEnabled(False)
        self.stop_trading_btn.setEnabled(True)
        self.status_label.setText("Status: Trading Active")
    
    def stop_trading(self):
        """توقف ترید"""
        self.trading_system.stop_trading()
        
        if self.trading_worker:
            self.trading_worker.stop()
            self.trading_worker.wait()
        
        self.start_trading_btn.setEnabled(True)
        self.stop_trading_btn.setEnabled(False)
        self.status_label.setText("Status: Trading Stopped")
    
    def on_trading_update(self, data):
        """به‌روزرسانی داده‌های ترید"""
        market_data = data.get('market_data', {})
        analysis = data.get('analysis', {})
        
        # Update market data display
        market_text = f"""
Price: {market_data.get('price', 0):.5f}
Balance: ${market_data.get('balance', 0):.2f}
Symbol: {market_data.get('symbol', 'N/A')}
Account: {market_data.get('account_type', 'N/A')}
Time: {data.get('timestamp', 'N/A')}
"""
        self.market_data_text.setText(market_text)
        
        # Update analysis display
        analysis_text = f"""
Direction: {analysis.get('direction', 'N/A')}
Confidence: {analysis.get('confidence', 0):.3f}
Overall Score: {analysis.get('overall_score', 0):.3f}
Signals: {len(analysis.get('signals', {}))}
"""
        self.analysis_text.setText(analysis_text)
    
    def on_trade_executed(self, trade_result):
        """اجرای ترید"""
        log_text = f"""
[{datetime.now().strftime('%H:%M:%S')}] Trade Executed:
Direction: {trade_result.get('direction', 'N/A')}
Amount: ${trade_result.get('amount', 0):.2f}
Result: {trade_result.get('result', 'N/A')}
Win Rate: {trade_result.get('win_rate', 0):.1f}%
Total Trades: {trade_result.get('total_trades', 0)}

"""
        self.trading_log_text.append(log_text)
    
    def on_error(self, error_message):
        """خطا"""
        self.trading_log_text.append(f"[ERROR] {error_message}\n")
    
    def update_ui(self):
        """به‌روزرسانی UI"""
        if self.trading_system.is_connected:
            asyncio.run(self._update_account_info())
    
    async def _update_account_info(self):
        """به‌روزرسانی اطلاعات حساب"""
        account_info = await self.trading_system.get_account_info()
        
        if account_info:
            info_text = f"""
Balance: ${account_info.get('balance', 0):.2f}
Account Type: {account_info.get('account_type', 'N/A')}
Total Trades: {account_info.get('total_trades', 0)}
Winning Trades: {account_info.get('winning_trades', 0)}
Win Rate: {account_info.get('win_rate', 0):.1f}%
"""
            self.account_info_text.setText(info_text)

def main():
    """تابع اصلی"""
    print("🚀 VIP BIG BANG Live Trading System")
    print("Starting complete live trading system...")
    print("-" * 50)
    
    app = QApplication(sys.argv)
    
    # Create and show UI
    ui = VIPLiveTradingUI()
    ui.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
