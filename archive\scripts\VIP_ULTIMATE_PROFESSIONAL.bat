@echo off
title VIP BIG BANG - Ultimate Professional UI
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║              🎮 VIP BIG BANG ULTIMATE 🎮                    ║
echo ║                                                              ║
echo ║            Professional Trading Bot Interface               ║
echo ║                                                              ║
echo ║                 🚀 ULTIMATE EDITION 🚀                     ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Initializing Ultimate Professional System...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    echo 📦 Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)
echo ✅ Python ready

REM Check PySide6
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing PySide6 Ultimate...
    python -m pip install PySide6 --upgrade
    if errorlevel 1 (
        echo ❌ Failed to install PySide6
        pause
        exit /b 1
    )
)
echo ✅ PySide6 Ultimate ready

REM Check Ultimate UI file
if not exist "vip_ultimate_professional_ui.py" (
    echo ❌ Ultimate Professional UI file not found!
    echo 📁 Please ensure vip_ultimate_professional_ui.py exists
    pause
    exit /b 1
)
echo ✅ Ultimate Professional UI ready

echo.
echo 🚀 Launching VIP BIG BANG Ultimate Professional...
echo 🎯 Prepare for the ultimate trading experience!
echo.

python vip_ultimate_professional_ui.py

if errorlevel 1 (
    echo.
    echo ❌ Error occurred in Ultimate Professional UI
    echo 🔧 Please check the error messages above
    pause
) else (
    echo.
    echo 👋 VIP BIG BANG Ultimate Professional closed successfully
    echo 🎮 Thank you for using the ultimate trading experience!
)

pause
