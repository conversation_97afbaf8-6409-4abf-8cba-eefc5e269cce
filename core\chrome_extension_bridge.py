"""
🌉 VIP BIG BANG CHROME EXTENSION BRIDGE
🚀 SEAMLESS CONNECTION BETWEEN PYTHON APP AND CHROME EXTENSION
🕵️‍♂️ STEALTH COMMUNICATION PROTOCOL
"""

import asyncio
import json
import logging
import time
import websockets
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import threading
import subprocess
import os
import psutil

class ChromeExtensionBridge:
    """
    🌉 Bridge between VIP BIG BANG Python app and Chrome Extension
    🚀 Handles all communication with Quotex through Chrome Extension
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("ChromeBridge")
        
        # WebSocket server for extension communication
        self.websocket_server = None
        self.connected_clients = set()
        self.server_port = 8765
        
        # Extension status
        self.extension_connected = False
        self.quotex_page_active = False
        
        # Market data
        self.current_prices = {}
        self.current_balance = 0.0
        
        # Trade management
        self.pending_trades = {}
        self.trade_results = {}
        
        # Callbacks
        self.price_update_callback = None
        self.trade_result_callback = None
        
        self.logger.info("🌉 Chrome Extension Bridge initialized")
    
    async def start_bridge(self) -> bool:
        """🚀 Start the bridge server"""
        try:
            self.logger.info("🚀 Starting Chrome Extension Bridge...")
            
            # Start WebSocket server
            await self._start_websocket_server()
            
            # Check for Chrome and extension
            chrome_status = await self._check_chrome_setup()
            
            if chrome_status:
                self.logger.info("✅ Chrome Extension Bridge ready")
                return True
            else:
                self.logger.warning("⚠️ Chrome setup incomplete, running in fallback mode")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to start bridge: {e}")
            return False
    
    async def _start_websocket_server(self):
        """🔌 Start WebSocket server for extension communication"""
        try:
            self.websocket_server = await websockets.serve(
                self._handle_websocket_connection,
                "localhost",
                self.server_port
            )
            
            self.logger.info(f"🔌 WebSocket server started on port {self.server_port}")
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket server failed: {e}")
            raise
    
    async def _handle_websocket_connection(self, websocket, path):
        """📡 Handle WebSocket connections from extension"""
        self.logger.info("📡 Extension connected via WebSocket")
        self.connected_clients.add(websocket)
        self.extension_connected = True
        
        try:
            async for message in websocket:
                await self._process_extension_message(json.loads(message), websocket)
                
        except websockets.exceptions.ConnectionClosed:
            self.logger.info("📡 Extension disconnected")
        except Exception as e:
            self.logger.error(f"❌ WebSocket error: {e}")
        finally:
            self.connected_clients.discard(websocket)
            if not self.connected_clients:
                self.extension_connected = False
    
    async def _process_extension_message(self, message: Dict, websocket):
        """📨 Process messages from Chrome Extension"""
        try:
            msg_type = message.get('type')
            data = message.get('data', {})
            
            self.logger.debug(f"📨 Extension message: {msg_type}")
            
            if msg_type == 'PRICE_DATA':
                await self._handle_price_update(data)
            
            elif msg_type == 'TRADE_EXECUTED':
                await self._handle_trade_result(data)
            
            elif msg_type == 'BALANCE_UPDATE':
                await self._handle_balance_update(data)
            
            elif msg_type == 'QUOTEX_STATUS':
                await self._handle_quotex_status(data)
            
            elif msg_type == 'ERROR':
                self.logger.error(f"❌ Extension error: {data}")
            
        except Exception as e:
            self.logger.error(f"❌ Message processing error: {e}")
    
    async def _handle_price_update(self, data: Dict):
        """📈 Handle price updates from extension"""
        self.current_prices.update(data.get('prices', {}))
        
        if self.price_update_callback:
            try:
                await self.price_update_callback(self.current_prices)
            except Exception as e:
                self.logger.error(f"❌ Price callback error: {e}")
    
    async def _handle_trade_result(self, data: Dict):
        """💰 Handle trade execution results"""
        trade_id = data.get('tradeId')
        if trade_id:
            self.trade_results[trade_id] = data
            
            if self.trade_result_callback:
                try:
                    await self.trade_result_callback(data)
                except Exception as e:
                    self.logger.error(f"❌ Trade callback error: {e}")
    
    async def _handle_balance_update(self, data: Dict):
        """💳 Handle balance updates"""
        self.current_balance = data.get('balance', 0.0)
        self.logger.info(f"💳 Balance updated: ${self.current_balance}")
    
    async def _handle_quotex_status(self, data: Dict):
        """📊 Handle Quotex page status"""
        self.quotex_page_active = data.get('active', False)
        self.logger.info(f"📊 Quotex status: {'Active' if self.quotex_page_active else 'Inactive'}")
    
    async def execute_trade(self, asset: str, direction: str, amount: float, duration: int) -> Dict:
        """🚀 Execute trade through Chrome Extension"""
        try:
            if not self.extension_connected:
                return {"success": False, "error": "Extension not connected"}
            
            trade_data = {
                "asset": asset,
                "direction": direction,
                "amount": amount,
                "duration": duration,
                "timestamp": time.time()
            }
            
            message = {
                "type": "EXECUTE_TRADE",
                "data": trade_data
            }
            
            # Send to all connected clients
            if self.connected_clients:
                await self._broadcast_message(message)
                
                # Wait for trade result (with timeout)
                result = await self._wait_for_trade_result(trade_data, timeout=10)
                return result
            else:
                return {"success": False, "error": "No extension clients connected"}
                
        except Exception as e:
            self.logger.error(f"❌ Trade execution error: {e}")
            return {"success": False, "error": str(e)}
    
    async def _broadcast_message(self, message: Dict):
        """📡 Broadcast message to all connected extensions"""
        if self.connected_clients:
            message_str = json.dumps(message)
            await asyncio.gather(
                *[client.send(message_str) for client in self.connected_clients],
                return_exceptions=True
            )
    
    async def _wait_for_trade_result(self, trade_data: Dict, timeout: int = 10) -> Dict:
        """⏱️ Wait for trade execution result"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check if we have a result
            for trade_id, result in self.trade_results.items():
                if (result.get('direction') == trade_data['direction'] and
                    result.get('amount') == trade_data['amount'] and
                    abs(result.get('timestamp', 0) - trade_data['timestamp']) < 5):
                    
                    return {"success": True, "data": result}
            
            await asyncio.sleep(0.1)
        
        return {"success": False, "error": "Trade execution timeout"}
    
    async def get_market_data(self) -> Dict:
        """📊 Get current market data"""
        try:
            if not self.extension_connected:
                return {"success": False, "error": "Extension not connected"}
            
            message = {
                "type": "GET_MARKET_DATA",
                "data": {}
            }
            
            await self._broadcast_message(message)
            
            return {
                "success": True,
                "data": {
                    "prices": self.current_prices,
                    "balance": self.current_balance,
                    "timestamp": time.time()
                }
            }
            
        except Exception as e:
            self.logger.error(f"❌ Market data error: {e}")
            return {"success": False, "error": str(e)}
    
    async def _check_chrome_setup(self) -> bool:
        """🔍 Check Chrome and extension setup"""
        try:
            # Check if Chrome is running
            chrome_running = self._is_chrome_running()
            
            if not chrome_running:
                self.logger.warning("⚠️ Chrome not detected")
                return False
            
            # Check if extension is installed (we'll assume it is for now)
            self.logger.info("✅ Chrome detected")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Chrome setup check failed: {e}")
            return False
    
    def _is_chrome_running(self) -> bool:
        """🔍 Check if Chrome is running"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if 'chrome' in proc.info['name'].lower():
                    return True
            return False
        except Exception:
            return False
    
    async def open_quotex_page(self) -> bool:
        """🌐 Open Quotex page in Chrome"""
        try:
            self.logger.info("🌐 Opening Quotex page...")
            
            # Try to open Quotex in Chrome
            chrome_path = self._find_chrome_path()
            if chrome_path:
                subprocess.Popen([
                    chrome_path,
                    "--new-tab",
                    "https://quotex.io/en/trade"
                ])
                
                # Wait a bit for page to load
                await asyncio.sleep(3)
                
                return True
            else:
                self.logger.error("❌ Chrome not found")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to open Quotex: {e}")
            return False
    
    def _find_chrome_path(self) -> Optional[str]:
        """🔍 Find Chrome executable path"""
        possible_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/usr/bin/google-chrome",
            "/usr/bin/chromium-browser"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def set_price_update_callback(self, callback: Callable):
        """📈 Set callback for price updates"""
        self.price_update_callback = callback
    
    def set_trade_result_callback(self, callback: Callable):
        """💰 Set callback for trade results"""
        self.trade_result_callback = callback
    
    def is_connected(self) -> bool:
        """✅ Check if extension is connected"""
        return self.extension_connected
    
    def get_connection_status(self) -> Dict:
        """📊 Get detailed connection status"""
        return {
            "extension_connected": self.extension_connected,
            "quotex_page_active": self.quotex_page_active,
            "connected_clients": len(self.connected_clients),
            "websocket_port": self.server_port,
            "current_balance": self.current_balance,
            "tracked_assets": list(self.current_prices.keys())
        }
    
    async def stop_bridge(self):
        """🛑 Stop the bridge server"""
        try:
            if self.websocket_server:
                self.websocket_server.close()
                await self.websocket_server.wait_closed()
            
            self.logger.info("🛑 Chrome Extension Bridge stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Bridge stop error: {e}")
    
    async def send_stealth_command(self, command: str, data: Dict = None) -> Dict:
        """🕵️‍♂️ Send stealth command to extension"""
        try:
            message = {
                "type": f"STEALTH_{command.upper()}",
                "data": data or {},
                "timestamp": time.time()
            }
            
            await self._broadcast_message(message)
            
            return {"success": True}
            
        except Exception as e:
            self.logger.error(f"❌ Stealth command error: {e}")
            return {"success": False, "error": str(e)}
