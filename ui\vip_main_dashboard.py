"""
🚀 VIP BIG BANG - Professional Main Dashboard
🎮 Gaming-style UI with Real-time Trading Analysis
💎 Enterprise-level Design with Responsive Layout
🔗 Real Quotex Connection with Quantum Stealth Technology
"""

import sys
import asyncio
import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWebEngineWidgets import QWebEngineView

# Import VIP BIG BANG core systems
try:
    from core.realtime_quotex_connector import RealtimeQuotexConnector
    from core.stealth_quotex_connector import StealthQuotexConnector
    from core.quantum_stealth_chrome_connector import QuantumStealthChromeConnector
    from core.dynamic_timeframe_manager import DynamicTimeframeManager
    from core.analysis_engine import AnalysisEngine
    from core.signal_manager import SignalManager
    from trading.autotrade import AutoTrader
    from core.settings import Settings
    CORE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Core systems not available: {e}")
    CORE_AVAILABLE = False

# Import custom components
try:
    from ui.components.chart_widget import VIPChartWidget
    from ui.components.analysis_box import AnalysisBox
    from ui.components.control_panel import ControlPanel
    from ui.components.extension_data_widget import ExtensionDataWidget
    from ui.extension_data_manager import ExtensionDataConnector
    from ui.styles.vip_theme import VIPTheme
except ImportError:
    # Fallback imports for development
    VIPChartWidget = QWidget
    AnalysisBox = QFrame
    ControlPanel = QFrame
    ExtensionDataWidget = QFrame
    ExtensionDataConnector = None

    class VIPTheme:
        def get_stylesheet(self):
            return ""

class VIPMainDashboard(QMainWindow):
    """
    🚀 VIP BIG BANG Professional Main Dashboard

    Features:
    - Real-time Quotex connection with quantum stealth
    - Live chart with 5s/1m candles and indicators
    - 8 analysis modules in gaming-style boxes
    - Dynamic timeframe adjustment (15s/5s default)
    - Multi-OTC analysis (5 pairs simultaneously)
    - Professional control panel with auto-trading
    - Responsive design for 4K displays
    - Persian/English language support
    - Quantum-level anti-detection technology
    """

    # Signals for real-time updates
    price_updated = Signal(str, dict)  # asset, price_data
    analysis_updated = Signal(dict)    # analysis_results
    signal_generated = Signal(dict)    # trading_signal
    connection_status_changed = Signal(bool)  # connected
    trade_executed = Signal(dict)      # trade_result
    timeframe_changed = Signal(int, int)  # analysis_interval, trade_duration
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("VIPMainDashboard")

        # Initialize theme
        self.theme = VIPTheme()

        # Core VIP BIG BANG Systems
        self.settings = Settings() if CORE_AVAILABLE else None
        self.quotex_connector = None
        self.stealth_connector = None
        self.quantum_connector = None
        self.timeframe_manager = None
        self.analysis_engine = None
        self.signal_manager = None
        self.auto_trader = None

        # Dashboard state
        self.current_asset = "EUR/USD OTC"
        self.current_timeframe = "15s"
        self.current_analysis_interval = 15  # Default 15 seconds
        self.current_trade_duration = 5      # Default 5 seconds
        self.is_connected = False
        self.is_demo_mode = True
        self.balance = 1000.0

        # Multi-OTC analysis
        self.otc_pairs = [
            "EUR/USD OTC", "GBP/USD OTC", "USD/JPY OTC",
            "AUD/USD OTC", "USD/CAD OTC"
        ]
        self.otc_analysis_data = {}

        # Analysis modules data
        self.analysis_data = {}
        self.trading_signals = []
        self.trade_history = []

        # Initialize core systems
        self._initialize_core_systems()

        # Setup window
        self._setup_window()

        # Setup UI
        self._setup_ui()

        # Apply theme
        self._apply_theme()

        # Setup Extension Data Connection
        self._setup_extension_connection()

        # Setup Real Data Server Connection
        self._setup_real_data_server_connection()

        # Setup real-time updates
        self._setup_realtime_updates()

        # Start real-time systems
        self._start_realtime_systems()

        self.logger.info("🚀 VIP BIG BANG Dashboard initialized with quantum stealth technology")

    def _initialize_core_systems(self):
        """🚀 Initialize VIP BIG BANG core systems"""
        try:
            if not CORE_AVAILABLE:
                self.logger.warning("⚠️ Core systems not available, running in demo mode")
                return

            self.logger.info("🔧 Initializing VIP BIG BANG core systems...")

            # Initialize Dynamic Timeframe Manager
            self.timeframe_manager = DynamicTimeframeManager(self.settings)

            # Initialize Analysis Engine
            self.analysis_engine = AnalysisEngine(self.settings)

            # Initialize Signal Manager
            self.signal_manager = SignalManager(self.settings)

            # Initialize Auto Trader
            self.auto_trader = AutoTrader(self.settings)

            # Initialize Quotex Connectors
            self.quotex_connector = RealtimeQuotexConnector()
            self.stealth_connector = StealthQuotexConnector()
            self.quantum_connector = QuantumStealthChromeConnector()

            # Set default timeframe (15s analysis, 5s trades)
            asyncio.create_task(self.timeframe_manager.set_timeframe_and_duration(15, 5))

            self.logger.info("✅ VIP BIG BANG core systems initialized successfully")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize core systems: {e}")

    def _setup_extension_connection(self):
        """🔌 Setup Chrome Extension data connection"""
        try:
            if ExtensionDataConnector:
                self.extension_connector = ExtensionDataConnector(self)

                # Connect extension data signals to UI updates
                self.extension_connector.data_manager.data_received.connect(self._on_extension_data_received)
                self.extension_connector.data_manager.connection_status_changed.connect(self._on_extension_connection_changed)

                # Start extension data listener
                self.extension_connector.data_manager.start_listening()

                self.logger.info("🔌 Extension data connector initialized and listening")
            else:
                self.extension_connector = None
                self.logger.warning("⚠️ Extension data connector not available")
        except Exception as e:
            self.logger.error(f"❌ Failed to setup extension connection: {e}")
            self.extension_connector = None

    def _on_extension_data_received(self, data):
        """🔌 Handle extension data received"""
        try:
            self.logger.info(f"📊 Extension data received: {data}")

            # Update UI with real data
            if hasattr(self, 'extension_data_widget') and self.extension_data_widget:
                self.extension_data_widget.update_data(data)

            # Update balance if available
            if 'balance' in data:
                self.balance = float(data['balance'].replace('$', '').replace(',', '')) if isinstance(data['balance'], str) else data['balance']
                self._update_balance_display()

            # Update current asset if available
            if 'currentAsset' in data and data['currentAsset']:
                self.current_asset = data['currentAsset']
                self._update_asset_display()

            # Update connection status
            self.is_connected = True
            self._update_connection_status()

        except Exception as e:
            self.logger.error(f"❌ Error handling extension data: {e}")

    def _on_extension_connection_changed(self, connected):
        """🔌 Handle extension connection status change"""
        try:
            self.is_connected = connected
            self._update_connection_status()

            if connected:
                self.logger.info("✅ Extension connected")
            else:
                self.logger.warning("⚠️ Extension disconnected")

        except Exception as e:
            self.logger.error(f"❌ Error handling connection change: {e}")

    def _update_balance_display(self):
        """💰 Update balance display"""
        try:
            if hasattr(self, 'balance_label') and self.balance_label:
                self.balance_label.setText(f"💰 ${self.balance:.2f}")
        except Exception as e:
            self.logger.error(f"❌ Error updating balance display: {e}")

    def _update_asset_display(self):
        """📊 Update asset display"""
        try:
            if hasattr(self, 'asset_label') and self.asset_label:
                self.asset_label.setText(f"📊 {self.current_asset}")
        except Exception as e:
            self.logger.error(f"❌ Error updating asset display: {e}")

    def _update_connection_status(self):
        """🔌 Update connection status display"""
        try:
            if hasattr(self, 'connection_status_label') and self.connection_status_label:
                if self.is_connected:
                    self.connection_status_label.setText("🟢 Connected")
                    self.connection_status_label.setStyleSheet("color: #10B981; font-weight: bold;")
                else:
                    self.connection_status_label.setText("🔴 Disconnected")
                    self.connection_status_label.setStyleSheet("color: #EF4444; font-weight: bold;")
        except Exception as e:
            self.logger.error(f"❌ Error updating connection status: {e}")

    def _setup_real_data_server_connection(self):
        """🚀 Setup Real Data Server connection"""
        try:
            from core.real_data_server import get_real_data_server, start_real_data_server

            # Try to get existing server first
            self.real_data_server = get_real_data_server()

            if self.real_data_server is None:
                # Start new server if none exists
                self.logger.info("🚀 Starting Real Data Server...")
                if start_real_data_server(8765):
                    self.real_data_server = get_real_data_server()
                    self.logger.info("✅ Real Data Server started")
                else:
                    self.logger.error("❌ Failed to start Real Data Server")
                    return
            else:
                self.logger.info("✅ Connected to existing Real Data Server")

            # Add callback to receive data
            if self.real_data_server:
                self.real_data_server.add_data_callback(self._on_real_data_server_data)
                self.logger.info("🔌 Real Data Server callback registered")

        except Exception as e:
            self.logger.error(f"❌ Failed to setup Real Data Server connection: {e}")
            self.real_data_server = None

    def _on_real_data_server_data(self, data):
        """📊 Handle data from Real Data Server"""
        try:
            self.logger.info(f"📊 Real Data Server data received: {data}")

            # Update UI with real data from server
            if 'balance' in data:
                balance_str = str(data['balance'])
                # Extract numeric value from balance string
                import re
                balance_match = re.search(r'[\d.]+', balance_str)
                if balance_match:
                    self.balance = float(balance_match.group())
                    self._update_balance_display()

            # Update current asset if available
            if 'currentAsset' in data and data['currentAsset']:
                self.current_asset = data['currentAsset']
                self._update_asset_display()

            # Update connection status
            self.is_connected = True
            self._update_connection_status()

            # Update extension data widget if available
            if hasattr(self, 'extension_data_widget') and self.extension_data_widget:
                self.extension_data_widget.update_data(data)

        except Exception as e:
            self.logger.error(f"❌ Error handling Real Data Server data: {e}")

    def _start_realtime_systems(self):
        """🚀 Start real-time systems"""
        try:
            if not CORE_AVAILABLE:
                return

            self.logger.info("🚀 Starting real-time systems...")

            # Start Quotex connection in background
            threading.Thread(target=self._connect_to_quotex, daemon=True).start()

            # Start analysis engine
            threading.Thread(target=self._start_analysis_loop, daemon=True).start()

            # Start multi-OTC monitoring
            threading.Thread(target=self._start_multi_otc_analysis, daemon=True).start()

            self.logger.info("✅ Real-time systems started")

        except Exception as e:
            self.logger.error(f"❌ Failed to start real-time systems: {e}")

    def _connect_to_quotex(self):
        """🔗 Connect to Quotex with quantum stealth"""
        try:
            self.logger.info("🔗 Connecting to Quotex with quantum stealth technology...")

            # Try quantum stealth connection first
            if self.quantum_connector.start_connection():
                self.is_connected = True
                self.logger.info("✅ Connected via Quantum Stealth")
                self.connection_status_changed.emit(True)
                return

            # Fallback to stealth connector
            asyncio.run(self.stealth_connector.connect_to_quotex())
            if self.stealth_connector.connection_active:
                self.is_connected = True
                self.logger.info("✅ Connected via Stealth Connector")
                self.connection_status_changed.emit(True)
                return

            # Fallback to realtime connector
            asyncio.run(self.quotex_connector.connect())
            if self.quotex_connector.is_connected:
                self.is_connected = True
                self.logger.info("✅ Connected via Realtime Connector")
                self.connection_status_changed.emit(True)
                return

            self.logger.warning("⚠️ All connection methods failed, running in demo mode")

        except Exception as e:
            self.logger.error(f"❌ Quotex connection failed: {e}")

    def _start_analysis_loop(self):
        """🧠 Start analysis loop"""
        try:
            while True:
                if self.analysis_engine and self.is_connected:
                    # Perform analysis for current asset
                    analysis_result = self.analysis_engine.analyze_asset(self.current_asset)

                    # Update UI with results
                    self.analysis_updated.emit(analysis_result)

                    # Generate signals if conditions are met
                    if self.signal_manager:
                        signal = self.signal_manager.generate_signal(analysis_result)
                        if signal:
                            self.signal_generated.emit(signal)

                # Wait for next analysis interval
                time.sleep(self.current_analysis_interval)

        except Exception as e:
            self.logger.error(f"❌ Analysis loop error: {e}")

    def _start_multi_otc_analysis(self):
        """📊 Start multi-OTC analysis"""
        try:
            while True:
                for pair in self.otc_pairs:
                    if self.analysis_engine and self.is_connected:
                        # Analyze each OTC pair
                        analysis_result = self.analysis_engine.analyze_asset(pair)
                        self.otc_analysis_data[pair] = analysis_result

                        # Check for trading signals
                        if self.signal_manager:
                            signal = self.signal_manager.generate_signal(analysis_result)
                            if signal and self.auto_trader and self.auto_trader.is_enabled:
                                # Execute auto-trade
                                self.auto_trader.execute_trade(signal)

                # Update every 5 seconds for multi-OTC
                time.sleep(5)

        except Exception as e:
            self.logger.error(f"❌ Multi-OTC analysis error: {e}")

    def _setup_window(self):
        """Setup main window properties"""
        self.setWindowTitle("🚀 VIP BIG BANG - Professional Trading Dashboard")
        
        # Auto-detect screen size and set optimal dimensions
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        # Calculate optimal size (90% of screen)
        width = int(screen_geometry.width() * 0.9)
        height = int(screen_geometry.height() * 0.9)
        
        # Minimum size constraints
        width = max(width, 1366)
        height = max(height, 768)
        
        self.setGeometry(
            (screen_geometry.width() - width) // 2,
            (screen_geometry.height() - height) // 2,
            width,
            height
        )
        
        # Set minimum size
        self.setMinimumSize(1366, 768)
        
        # Window icon
        self.setWindowIcon(QIcon("ui/resources/vip_icon.png"))
        
        self.logger.info(f"Window setup: {width}x{height}")
    
    def _setup_ui(self):
        """Setup main UI layout"""
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(12)
        
        # Header
        self.header = self._create_header()
        main_layout.addWidget(self.header)
        
        # Main content area
        content_layout = QHBoxLayout()
        content_layout.setSpacing(12)
        
        # Left panel - Analysis modules
        self.left_panel = self._create_left_analysis_panel()
        content_layout.addWidget(self.left_panel)
        
        # Center panel - Chart and indicators
        self.center_panel = self._create_center_chart_panel()
        content_layout.addWidget(self.center_panel)
        
        # Right panel - Controls and status
        self.right_panel = self._create_right_control_panel()
        content_layout.addWidget(self.right_panel)

        main_layout.addLayout(content_layout)

        # Dynamic timeframe control panel
        self.timeframe_control_panel = self._create_timeframe_control_panel()
        main_layout.addWidget(self.timeframe_control_panel)

        # Footer
        self.footer = self._create_footer()
        main_layout.addWidget(self.footer)
    
    def _create_header(self) -> QWidget:
        """Create professional header with navigation and status"""
        header = QFrame()
        header.setObjectName("header")
        header.setFixedHeight(70)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(20)
        
        # Left section - Logo and title
        left_section = QHBoxLayout()
        
        # VIP BIG BANG logo
        logo_label = QLabel("🚀")
        logo_label.setObjectName("logo")
        logo_label.setFixedSize(50, 50)
        left_section.addWidget(logo_label)
        
        # Title and subtitle
        title_layout = QVBoxLayout()
        title_label = QLabel("VIP BIG BANG")
        title_label.setObjectName("title")
        subtitle_label = QLabel("Professional Trading Dashboard")
        subtitle_label.setObjectName("subtitle")
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        left_section.addLayout(title_layout)
        
        layout.addLayout(left_section)
        layout.addStretch()
        
        # Center section - Asset and timeframe
        center_section = QHBoxLayout()
        
        # Asset selector
        self.asset_combo = QComboBox()
        self.asset_combo.setObjectName("assetCombo")
        self.asset_combo.addItems([
            "EUR/USD", "GBP/USD", "USD/JPY", "AUD/USD", 
            "USD/CAD", "EUR/GBP", "EUR/JPY", "GBP/JPY"
        ])
        self.asset_combo.setCurrentText(self.current_asset)
        center_section.addWidget(self.asset_combo)
        
        # Timeframe selector
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.setObjectName("timeframeCombo")
        self.timeframe_combo.addItems(["5s", "15s", "30s", "1m", "5m"])
        self.timeframe_combo.setCurrentText(self.current_timeframe)
        center_section.addWidget(self.timeframe_combo)
        
        layout.addLayout(center_section)
        layout.addStretch()
        
        # Right section - Status and controls
        right_section = self._create_header_right_section()
        layout.addLayout(right_section)
        
        return header
    
    def _create_header_right_section(self) -> QHBoxLayout:
        """Create header right section with status indicators"""
        right_section = QHBoxLayout()
        
        # Connection status
        self.connection_status_label = QLabel("🔴 Disconnected")
        self.connection_status_label.setObjectName("connectionStatus")
        self.connection_status_label.setStyleSheet("color: #EF4444; font-weight: bold;")
        right_section.addWidget(self.connection_status_label)
        
        # Current Asset
        self.asset_label = QLabel(f"📊 {self.current_asset}")
        self.asset_label.setObjectName("assetLabel")
        right_section.addWidget(self.asset_label)

        # Demo/Live mode
        self.mode_label = QLabel("📊 DEMO")
        self.mode_label.setObjectName("modeLabel")
        right_section.addWidget(self.mode_label)
        
        # Balance
        self.balance_label = QLabel(f"💰 ${self.balance:.2f}")
        self.balance_label.setObjectName("balanceLabel")
        right_section.addWidget(self.balance_label)
        
        # Settings button
        settings_btn = QPushButton("⚙️")
        settings_btn.setObjectName("settingsBtn")
        settings_btn.setFixedSize(40, 40)
        settings_btn.clicked.connect(self._show_settings)
        right_section.addWidget(settings_btn)
        
        return right_section
    
    def _create_left_analysis_panel(self) -> QWidget:
        """Create left panel with analysis modules"""
        panel = QFrame()
        panel.setObjectName("leftPanel")
        panel.setFixedWidth(220)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)
        
        # Analysis modules grid (2x4)
        grid_layout = QGridLayout()
        grid_layout.setSpacing(8)
        
        # Row 1
        self.momentum_box = AnalysisBox("⚡", "Momentum", "85%", "#8B5CF6")
        grid_layout.addWidget(self.momentum_box, 0, 0)
        
        self.heatmap_box = AnalysisBox("🔥", "Heatmap", "Strong", "#EC4899")
        grid_layout.addWidget(self.heatmap_box, 0, 1)
        
        # Row 2
        self.buyer_seller_box = AnalysisBox("⚖️", "Buyer/Seller", "67%", "#60A5FA")
        grid_layout.addWidget(self.buyer_seller_box, 1, 0)
        
        self.live_signals_box = AnalysisBox("📡", "Live Signals", "BUY", "#10B981")
        grid_layout.addWidget(self.live_signals_box, 1, 1)
        
        # Row 3
        self.brothers_can_box = AnalysisBox("🤝", "Brothers Can", "Active", "#F59E0B")
        grid_layout.addWidget(self.brothers_can_box, 2, 0)
        
        self.strong_level_box = AnalysisBox("🎯", "Strong Level", "1.0732", "#EF4444")
        grid_layout.addWidget(self.strong_level_box, 2, 1)
        
        # Row 4
        self.confirm_mode_box = AnalysisBox("✅", "Confirm Mode", "ON", "#8B5CF6")
        grid_layout.addWidget(self.confirm_mode_box, 3, 0)
        
        self.economic_news_box = AnalysisBox("📰", "Economic News", "High", "#6366F1")
        grid_layout.addWidget(self.economic_news_box, 3, 1)
        
        layout.addLayout(grid_layout)
        layout.addStretch()
        
        return panel

    def _create_center_chart_panel(self) -> QWidget:
        """Create center panel with chart and indicators"""
        panel = QFrame()
        panel.setObjectName("centerPanel")

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Chart widget
        self.chart_widget = VIPChartWidget()
        self.chart_widget.setMinimumHeight(400)
        layout.addWidget(self.chart_widget)

        # Indicators panel
        indicators_panel = self._create_indicators_panel()
        layout.addWidget(indicators_panel)

        return panel

    def _create_indicators_panel(self) -> QWidget:
        """Create indicators panel below chart"""
        panel = QFrame()
        panel.setObjectName("indicatorsPanel")
        panel.setFixedHeight(120)

        layout = QHBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # MA6 Indicator
        ma6_frame = QFrame()
        ma6_frame.setObjectName("indicatorFrame")
        ma6_layout = QVBoxLayout(ma6_frame)

        ma6_title = QLabel("MA6")
        ma6_title.setObjectName("indicatorTitle")
        ma6_layout.addWidget(ma6_title)

        self.ma6_value = QLabel("1.07325")
        self.ma6_value.setObjectName("indicatorValue")
        ma6_layout.addWidget(self.ma6_value)

        layout.addWidget(ma6_frame)

        # Vortex Indicator
        vortex_frame = QFrame()
        vortex_frame.setObjectName("indicatorFrame")
        vortex_layout = QVBoxLayout(vortex_frame)

        vortex_title = QLabel("Vortex")
        vortex_title.setObjectName("indicatorTitle")
        vortex_layout.addWidget(vortex_title)

        # Vortex visualization
        vortex_visual = QFrame()
        vortex_visual.setObjectName("vortexVisual")
        vortex_visual.setFixedHeight(40)
        vortex_layout.addWidget(vortex_visual)

        self.vortex_value = QLabel("VI+: 1.02 | VI-: 0.98")
        self.vortex_value.setObjectName("indicatorValue")
        vortex_layout.addWidget(self.vortex_value)

        layout.addWidget(vortex_frame)

        # Volume/PulseBar
        volume_frame = QFrame()
        volume_frame.setObjectName("indicatorFrame")
        volume_layout = QVBoxLayout(volume_frame)

        volume_title = QLabel("Volume PulseBar")
        volume_title.setObjectName("indicatorTitle")
        volume_layout.addWidget(volume_title)

        # PulseBar visualization
        pulsebar_widget = self._create_pulsebar_widget()
        volume_layout.addWidget(pulsebar_widget)

        layout.addWidget(volume_frame)

        return panel

    def _create_pulsebar_widget(self) -> QWidget:
        """Create PulseBar visualization widget"""
        widget = QFrame()
        widget.setObjectName("pulsebarWidget")
        widget.setFixedHeight(50)

        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)

        # Create color bars
        colors = ["#EF4444", "#F97316", "#EAB308", "#22C55E", "#10B981"]
        intensities = [0.3, 0.6, 0.8, 0.9, 0.7]  # Sample data

        for i, (color, intensity) in enumerate(zip(colors, intensities)):
            bar = QFrame()
            bar.setObjectName("pulseBar")
            bar.setStyleSheet(f"""
                QFrame#pulseBar {{
                    background-color: {color};
                    border-radius: 3px;
                    opacity: {intensity};
                }}
            """)
            bar.setFixedWidth(20)
            layout.addWidget(bar)

        return widget

    def _create_right_control_panel(self) -> QWidget:
        """Create right panel with controls and status"""
        panel = QFrame()
        panel.setObjectName("rightPanel")
        panel.setFixedWidth(200)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # Extension Data Widget
        self.extension_data_widget = ExtensionDataWidget()
        layout.addWidget(self.extension_data_widget)

        # Register extension widget with connector
        if hasattr(self, 'extension_connector') and self.extension_connector:
            self.extension_connector.register_ui_component(self.extension_data_widget)

        # Trading controls
        trading_controls = self._create_trading_controls()
        layout.addWidget(trading_controls)

        # AutoTrade status
        autotrade_status = self._create_autotrade_status()
        layout.addWidget(autotrade_status)

        # Account info
        account_info = self._create_account_info()
        layout.addWidget(account_info)

        layout.addStretch()

        return panel

    def _create_trading_controls(self) -> QWidget:
        """Create trading control buttons"""
        frame = QFrame()
        frame.setObjectName("tradingControls")

        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title
        title = QLabel("Trading Controls")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Volume control
        volume_layout = QHBoxLayout()
        volume_label = QLabel("Volume:")
        volume_label.setObjectName("controlLabel")
        volume_layout.addWidget(volume_label)

        self.volume_spinbox = QSpinBox()
        self.volume_spinbox.setObjectName("volumeSpinbox")
        self.volume_spinbox.setRange(1, 1000)
        self.volume_spinbox.setValue(10)
        self.volume_spinbox.setSuffix(" $")
        volume_layout.addWidget(self.volume_spinbox)

        layout.addLayout(volume_layout)

        # Trade buttons
        buttons_layout = QHBoxLayout()

        self.buy_btn = QPushButton("📈 BUY")
        self.buy_btn.setObjectName("buyBtn")
        self.buy_btn.clicked.connect(self._place_buy_order)
        buttons_layout.addWidget(self.buy_btn)

        self.sell_btn = QPushButton("📉 SELL")
        self.sell_btn.setObjectName("sellBtn")
        self.sell_btn.clicked.connect(self._place_sell_order)
        buttons_layout.addWidget(self.sell_btn)

        layout.addLayout(buttons_layout)

        # Emergency stop
        self.emergency_btn = QPushButton("🚨 EMERGENCY STOP")
        self.emergency_btn.setObjectName("emergencyBtn")
        self.emergency_btn.clicked.connect(self._emergency_stop)
        layout.addWidget(self.emergency_btn)

        return frame

    def _create_autotrade_status(self) -> QWidget:
        """Create AutoTrade status panel"""
        frame = QFrame()
        frame.setObjectName("autotradeStatus")

        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # Title
        title = QLabel("AutoTrade Status")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Status indicator
        self.autotrade_indicator = QLabel("🔴 OFF")
        self.autotrade_indicator.setObjectName("statusIndicator")
        layout.addWidget(self.autotrade_indicator)

        # Toggle button
        self.autotrade_toggle = QPushButton("Enable AutoTrade")
        self.autotrade_toggle.setObjectName("toggleBtn")
        self.autotrade_toggle.clicked.connect(self._toggle_autotrade)
        layout.addWidget(self.autotrade_toggle)

        # Stats
        stats_layout = QGridLayout()

        stats_layout.addWidget(QLabel("Trades:"), 0, 0)
        self.trades_count = QLabel("0")
        self.trades_count.setObjectName("statValue")
        stats_layout.addWidget(self.trades_count, 0, 1)

        stats_layout.addWidget(QLabel("Win Rate:"), 1, 0)
        self.win_rate = QLabel("0%")
        self.win_rate.setObjectName("statValue")
        stats_layout.addWidget(self.win_rate, 1, 1)

        stats_layout.addWidget(QLabel("P&L:"), 2, 0)
        self.pnl = QLabel("$0.00")
        self.pnl.setObjectName("statValue")
        stats_layout.addWidget(self.pnl, 2, 1)

        layout.addLayout(stats_layout)

        return frame

    def _create_account_info(self) -> QWidget:
        """Create account information panel"""
        frame = QFrame()
        frame.setObjectName("accountInfo")

        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # Title
        title = QLabel("Account Info")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Account details
        details_layout = QGridLayout()

        details_layout.addWidget(QLabel("Mode:"), 0, 0)
        self.account_mode = QLabel("DEMO")
        self.account_mode.setObjectName("accountMode")
        details_layout.addWidget(self.account_mode, 0, 1)

        details_layout.addWidget(QLabel("Balance:"), 1, 0)
        self.account_balance = QLabel(f"${self.balance:.2f}")
        self.account_balance.setObjectName("accountBalance")
        details_layout.addWidget(self.account_balance, 1, 1)

        details_layout.addWidget(QLabel("Equity:"), 2, 0)
        self.account_equity = QLabel(f"${self.balance:.2f}")
        self.account_equity.setObjectName("accountEquity")
        details_layout.addWidget(self.account_equity, 2, 1)

        layout.addLayout(details_layout)

        return frame

    def _create_alert_center(self) -> QWidget:
        """Create alert center panel"""
        frame = QFrame()
        frame.setObjectName("alertCenter")

        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # Title
        title = QLabel("Alert Center")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Alerts list
        self.alerts_list = QListWidget()
        self.alerts_list.setObjectName("alertsList")
        self.alerts_list.setMaximumHeight(100)
        layout.addWidget(self.alerts_list)

        # Add sample alerts
        self.alerts_list.addItem("🟢 Strong BUY signal detected")
        self.alerts_list.addItem("⚠️ High volatility warning")
        self.alerts_list.addItem("📊 Economic news impact")

        return frame

    def _create_timeframe_control_panel(self) -> QWidget:
        """🎯 Create dynamic timeframe control panel"""
        panel = QFrame()
        panel.setObjectName("timeframeControlPanel")
        panel.setFixedHeight(80)

        layout = QHBoxLayout(panel)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(20)

        # Title
        title = QLabel("🎯 Dynamic Timeframe Control")
        title.setObjectName("panelTitle")
        layout.addWidget(title)

        layout.addStretch()

        # Analysis interval control
        analysis_layout = QVBoxLayout()
        analysis_label = QLabel("Analysis Interval:")
        analysis_label.setObjectName("controlLabel")
        analysis_layout.addWidget(analysis_label)

        self.analysis_interval_combo = QComboBox()
        self.analysis_interval_combo.setObjectName("timeframeCombo")
        self.analysis_interval_combo.addItems(["5s", "15s", "30s", "1m", "5m"])
        self.analysis_interval_combo.setCurrentText("15s")
        self.analysis_interval_combo.currentTextChanged.connect(self._on_timeframe_changed)
        analysis_layout.addWidget(self.analysis_interval_combo)

        layout.addLayout(analysis_layout)

        # Trade duration control
        trade_layout = QVBoxLayout()
        trade_label = QLabel("Trade Duration:")
        trade_label.setObjectName("controlLabel")
        trade_layout.addWidget(trade_label)

        self.trade_duration_combo = QComboBox()
        self.trade_duration_combo.setObjectName("timeframeCombo")
        self.trade_duration_combo.addItems(["5s", "15s", "30s", "1m", "5m"])
        self.trade_duration_combo.setCurrentText("5s")
        self.trade_duration_combo.currentTextChanged.connect(self._on_timeframe_changed)
        trade_layout.addWidget(self.trade_duration_combo)

        layout.addLayout(trade_layout)

        # Quick preset buttons
        presets_layout = QVBoxLayout()
        presets_label = QLabel("Quick Presets:")
        presets_label.setObjectName("controlLabel")
        presets_layout.addWidget(presets_label)

        presets_buttons_layout = QHBoxLayout()

        # Ultra Fast preset (5s/5s)
        ultra_fast_btn = QPushButton("⚡ Ultra Fast")
        ultra_fast_btn.setObjectName("presetBtn")
        ultra_fast_btn.clicked.connect(lambda: self._apply_preset(5, 5))
        presets_buttons_layout.addWidget(ultra_fast_btn)

        # VIP Default preset (15s/5s)
        vip_default_btn = QPushButton("🚀 VIP Default")
        vip_default_btn.setObjectName("presetBtn")
        vip_default_btn.clicked.connect(lambda: self._apply_preset(15, 5))
        presets_buttons_layout.addWidget(vip_default_btn)

        # Balanced preset (60s/60s)
        balanced_btn = QPushButton("⚖️ Balanced")
        balanced_btn.setObjectName("presetBtn")
        balanced_btn.clicked.connect(lambda: self._apply_preset(60, 60))
        presets_buttons_layout.addWidget(balanced_btn)

        presets_layout.addLayout(presets_buttons_layout)
        layout.addLayout(presets_layout)

        # Multi-OTC toggle
        multi_otc_layout = QVBoxLayout()
        multi_otc_label = QLabel("Multi-OTC Analysis:")
        multi_otc_label.setObjectName("controlLabel")
        multi_otc_layout.addWidget(multi_otc_label)

        self.multi_otc_toggle = QPushButton("🔄 Enable 5-Pair Analysis")
        self.multi_otc_toggle.setObjectName("toggleBtn")
        self.multi_otc_toggle.setCheckable(True)
        self.multi_otc_toggle.clicked.connect(self._toggle_multi_otc)
        multi_otc_layout.addWidget(self.multi_otc_toggle)

        layout.addLayout(multi_otc_layout)

        layout.addStretch()

        # Status indicator
        self.timeframe_status = QLabel("📊 Ready")
        self.timeframe_status.setObjectName("statusLabel")
        layout.addWidget(self.timeframe_status)

        return panel

    def _create_footer(self) -> QWidget:
        """Create footer with system status and performance metrics"""
        footer = QFrame()
        footer.setObjectName("footer")
        footer.setFixedHeight(40)

        layout = QHBoxLayout(footer)
        layout.setContentsMargins(20, 5, 20, 5)
        layout.setSpacing(20)

        # System status
        self.system_status = QLabel("🟢 System: Online")
        self.system_status.setObjectName("systemStatus")
        layout.addWidget(self.system_status)

        layout.addStretch()

        # Performance metrics
        self.performance_label = QLabel("⚡ Analysis: 0.2s | 🎯 Accuracy: 87%")
        self.performance_label.setObjectName("performanceLabel")
        layout.addWidget(self.performance_label)

        layout.addStretch()

        # Current time
        self.time_label = QLabel()
        self.time_label.setObjectName("timeLabel")
        layout.addWidget(self.time_label)

        # Update time every second
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self._update_time)
        self.time_timer.start(1000)
        self._update_time()

        return footer

    def _apply_theme(self):
        """Apply VIP theme to the dashboard"""
        self.setStyleSheet(self.theme.get_stylesheet())

    def _setup_realtime_updates(self):
        """Setup real-time update timers"""
        # Price update timer (every 500ms)
        self.price_timer = QTimer()
        self.price_timer.timeout.connect(self._update_price_data)
        self.price_timer.start(500)

        # Analysis update timer (every 5 seconds)
        self.analysis_timer = QTimer()
        self.analysis_timer.timeout.connect(self._update_analysis_data)
        self.analysis_timer.start(5000)

        # Chart update timer (every 1 second)
        self.chart_timer = QTimer()
        self.chart_timer.timeout.connect(self._update_chart_data)
        self.chart_timer.start(1000)

    # Event Handlers and Control Methods
    def _show_settings(self):
        """Show settings dialog"""
        from ui.dialogs.settings_dialog import SettingsDialog
        dialog = SettingsDialog(self)
        dialog.exec()

    def _place_buy_order(self):
        """Place BUY order"""
        volume = self.volume_spinbox.value()
        self.logger.info(f"Placing BUY order: {self.current_asset}, Volume: ${volume}")

        # Emit signal for order placement
        order_data = {
            "type": "BUY",
            "asset": self.current_asset,
            "volume": volume,
            "timestamp": datetime.now()
        }
        self.signal_generated.emit(order_data)

        # Update UI
        self._add_alert(f"🟢 BUY order placed: {self.current_asset} ${volume}")

    def _place_sell_order(self):
        """Place SELL order"""
        volume = self.volume_spinbox.value()
        self.logger.info(f"Placing SELL order: {self.current_asset}, Volume: ${volume}")

        # Emit signal for order placement
        order_data = {
            "type": "SELL",
            "asset": self.current_asset,
            "volume": volume,
            "timestamp": datetime.now()
        }
        self.signal_generated.emit(order_data)

        # Update UI
        self._add_alert(f"🔴 SELL order placed: {self.current_asset} ${volume}")

    def _emergency_stop(self):
        """Emergency stop all trading"""
        reply = QMessageBox.question(
            self,
            "Emergency Stop",
            "Are you sure you want to stop all trading activities?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.logger.warning("🚨 EMERGENCY STOP activated")
            self._add_alert("🚨 EMERGENCY STOP - All trading halted")

            # Disable trading buttons
            self.buy_btn.setEnabled(False)
            self.sell_btn.setEnabled(False)
            self.autotrade_toggle.setEnabled(False)

    def _toggle_autotrade(self):
        """Toggle AutoTrade on/off"""
        if self.autotrade_indicator.text() == "🔴 OFF":
            self.autotrade_indicator.setText("🟢 ON")
            self.autotrade_toggle.setText("Disable AutoTrade")
            self._add_alert("🤖 AutoTrade enabled")
        else:
            self.autotrade_indicator.setText("🔴 OFF")
            self.autotrade_toggle.setText("Enable AutoTrade")
            self._add_alert("⏹️ AutoTrade disabled")

    def _add_alert(self, message: str):
        """Add alert to alert center"""
        self.alerts_list.insertItem(0, message)

        # Keep only last 10 alerts
        while self.alerts_list.count() > 10:
            self.alerts_list.takeItem(self.alerts_list.count() - 1)

    def _update_time(self):
        """Update current time display"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"🕐 {current_time}")

    def _update_price_data(self):
        """Update price data (simulated)"""
        import random

        # Simulate price movement
        base_price = 1.07320
        price_change = random.uniform(-0.00010, 0.00010)
        new_price = base_price + price_change

        # Update price displays
        price_data = {
            "price": new_price,
            "change": price_change,
            "timestamp": datetime.now()
        }

        # Update chart
        if hasattr(self, 'chart_widget'):
            self.chart_widget.update_price(new_price)

        # Update MA6
        self.ma6_value.setText(f"{new_price:.5f}")

        # Emit price update signal
        self.price_updated.emit(self.current_asset, price_data)

    def _update_analysis_data(self):
        """Update analysis data (simulated)"""
        import random

        # Simulate analysis results
        analysis_results = {
            "momentum": random.randint(70, 95),
            "heatmap": random.choice(["Weak", "Moderate", "Strong", "Very Strong"]),
            "buyer_seller": random.randint(30, 80),
            "live_signals": random.choice(["BUY", "SELL", "HOLD"]),
            "brothers_can": random.choice(["Active", "Inactive", "Pending"]),
            "strong_level": round(random.uniform(1.0700, 1.0750), 5),
            "confirm_mode": random.choice(["ON", "OFF"]),
            "economic_news": random.choice(["Low", "Medium", "High", "Critical"])
        }

        # Update analysis boxes
        self.momentum_box.update_value(f"{analysis_results['momentum']}%")
        self.heatmap_box.update_value(analysis_results['heatmap'])
        self.buyer_seller_box.update_value(f"{analysis_results['buyer_seller']}%")
        self.live_signals_box.update_value(analysis_results['live_signals'])
        self.brothers_can_box.update_value(analysis_results['brothers_can'])
        self.strong_level_box.update_value(str(analysis_results['strong_level']))
        self.confirm_mode_box.update_value(analysis_results['confirm_mode'])
        self.economic_news_box.update_value(analysis_results['economic_news'])

        # Emit analysis update signal
        self.analysis_updated.emit(analysis_results)

    def _update_chart_data(self):
        """Update chart data"""
        if hasattr(self, 'chart_widget'):
            self.chart_widget.update_chart()

    def update_connection_status(self, connected: bool):
        """Update connection status"""
        self.is_connected = connected
        if connected:
            self.connection_status.setText("🟢 Connected")
            self.system_status.setText("🟢 System: Online")
        else:
            self.connection_status.setText("🔴 Disconnected")
            self.system_status.setText("🔴 System: Offline")

        self.connection_status_changed.emit(connected)

    def update_balance(self, new_balance: float):
        """Update account balance"""
        self.balance = new_balance
        self.balance_label.setText(f"💰 ${new_balance:.2f}")
        self.account_balance.setText(f"${new_balance:.2f}")
        self.account_equity.setText(f"${new_balance:.2f}")

    def set_demo_mode(self, demo: bool):
        """Set demo/live mode"""
        self.is_demo_mode = demo
        if demo:
            self.mode_label.setText("📊 DEMO")
            self.account_mode.setText("DEMO")
        else:
            self.mode_label.setText("💰 LIVE")
            self.account_mode.setText("LIVE")

    def _on_timeframe_changed(self):
        """🎯 Handle timeframe changes"""
        try:
            # Get current values
            analysis_text = self.analysis_interval_combo.currentText()
            trade_text = self.trade_duration_combo.currentText()

            # Convert to seconds
            analysis_seconds = self._timeframe_to_seconds(analysis_text)
            trade_seconds = self._timeframe_to_seconds(trade_text)

            # Update current settings
            self.current_analysis_interval = analysis_seconds
            self.current_trade_duration = trade_seconds

            # Apply changes to timeframe manager
            if self.timeframe_manager:
                asyncio.create_task(
                    self.timeframe_manager.set_timeframe_and_duration(
                        analysis_seconds, trade_seconds
                    )
                )

            # Update status
            self.timeframe_status.setText(f"📊 {analysis_text}/{trade_text}")

            # Emit signal
            self.timeframe_changed.emit(analysis_seconds, trade_seconds)

            self.logger.info(f"🎯 Timeframe changed: {analysis_text} analysis, {trade_text} trades")

        except Exception as e:
            self.logger.error(f"❌ Timeframe change error: {e}")

    def _apply_preset(self, analysis_seconds: int, trade_seconds: int):
        """🚀 Apply timeframe preset"""
        try:
            # Convert seconds to text
            analysis_text = self._seconds_to_timeframe(analysis_seconds)
            trade_text = self._seconds_to_timeframe(trade_seconds)

            # Update combos
            self.analysis_interval_combo.setCurrentText(analysis_text)
            self.trade_duration_combo.setCurrentText(trade_text)

            # This will trigger _on_timeframe_changed

        except Exception as e:
            self.logger.error(f"❌ Preset application error: {e}")

    def _toggle_multi_otc(self):
        """🔄 Toggle multi-OTC analysis"""
        try:
            is_enabled = self.multi_otc_toggle.isChecked()

            if is_enabled:
                self.multi_otc_toggle.setText("🔄 Disable 5-Pair Analysis")
                self.logger.info("✅ Multi-OTC analysis enabled")
                # Start multi-OTC analysis if not already running
            else:
                self.multi_otc_toggle.setText("🔄 Enable 5-Pair Analysis")
                self.logger.info("⏹️ Multi-OTC analysis disabled")

        except Exception as e:
            self.logger.error(f"❌ Multi-OTC toggle error: {e}")

    def _timeframe_to_seconds(self, timeframe_text: str) -> int:
        """Convert timeframe text to seconds"""
        timeframe_map = {
            "5s": 5,
            "15s": 15,
            "30s": 30,
            "1m": 60,
            "5m": 300
        }
        return timeframe_map.get(timeframe_text, 15)

    def _seconds_to_timeframe(self, seconds: int) -> str:
        """Convert seconds to timeframe text"""
        seconds_map = {
            5: "5s",
            15: "15s",
            30: "30s",
            60: "1m",
            300: "5m"
        }
        return seconds_map.get(seconds, "15s")


# Test the dashboard
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("VIP BIG BANG")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("VIP Trading Systems")

    # Create and show dashboard
    dashboard = VIPMainDashboard()
    dashboard.show()

    sys.exit(app.exec())
