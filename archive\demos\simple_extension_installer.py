#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 VIP BIG BANG - Simple Extension Installer
نصب ساده و مطمئن اکستنشن
"""

import os
import sys
import subprocess
import time
import shutil
from pathlib import Path

def find_chrome():
    """پیدا کردن Chrome"""
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            return path
    return None

def kill_chrome():
    """بستن Chrome"""
    try:
        subprocess.run(["taskkill", "/f", "/im", "chrome.exe"], 
                     capture_output=True, check=False)
        time.sleep(2)
        print("✅ Chrome closed")
    except:
        pass

def check_extension_files():
    """بررسی فایل‌های اکستنشن"""
    extension_dir = Path("chrome_extension")
    
    if not extension_dir.exists():
        print("❌ Extension directory not found!")
        return False
    
    required_files = ["manifest.json", "background.js", "content.js"]
    
    for file in required_files:
        if not (extension_dir / file).exists():
            print(f"❌ Missing file: {file}")
            return False
    
    print("✅ Extension files found")
    return True

def create_simple_batch():
    """ایجاد فایل batch ساده"""
    chrome_exe = find_chrome()
    if not chrome_exe:
        return False
    
    extension_path = os.path.abspath("chrome_extension")
    
    batch_content = f'''@echo off
title VIP BIG BANG Extension Installer
color 0A

echo.
echo ========================================
echo    VIP BIG BANG Extension Installer
echo ========================================
echo.

echo Closing Chrome...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul

echo Starting Chrome with extension...
echo.
echo Chrome Path: {chrome_exe}
echo Extension Path: {extension_path}
echo.

start "" "{chrome_exe}" --load-extension="{extension_path}" --disable-extensions-file-access-check --no-first-run --no-default-browser-check "https://qxbroker.com/en/trade"

echo.
echo ✅ Chrome started with VIP BIG BANG extension!
echo.
echo 📋 Next steps:
echo 1. Go to chrome://extensions/
echo 2. Enable "Developer mode" (top right)
echo 3. Make sure VIP BIG BANG is enabled
echo 4. Test on Quotex page
echo.
echo Press any key to exit...
pause >nul
'''
    
    with open("install_extension_simple.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("✅ Simple installer created: install_extension_simple.bat")
    return True

def install_extension():
    """نصب اکستنشن"""
    print("="*50)
    print("🔧 VIP BIG BANG Simple Extension Installer")
    print("="*50)
    
    # Check extension files
    if not check_extension_files():
        return False
    
    # Find Chrome
    chrome_exe = find_chrome()
    if not chrome_exe:
        print("❌ Chrome not found!")
        print("\n📋 Please install Google Chrome first")
        return False
    
    print(f"✅ Chrome found: {chrome_exe}")
    
    # Create batch installer
    create_simple_batch()
    
    # Kill Chrome
    print("🔄 Closing Chrome...")
    kill_chrome()
    
    # Launch Chrome with extension
    extension_path = os.path.abspath("chrome_extension")
    
    chrome_args = [
        chrome_exe,
        f"--load-extension={extension_path}",
        "--disable-extensions-file-access-check",
        "--no-first-run",
        "--no-default-browser-check",
        "https://qxbroker.com/en/trade"
    ]
    
    try:
        print("🚀 Starting Chrome with extension...")
        subprocess.Popen(chrome_args, shell=False)
        time.sleep(3)
        
        print("✅ Chrome started successfully!")
        print("\n📋 Verification steps:")
        print("1. Go to chrome://extensions/")
        print("2. Enable 'Developer mode' (top right)")
        print("3. Check if VIP BIG BANG is enabled")
        print("4. Test on Quotex page")
        
        print("\n🔧 If extension is not working:")
        print("1. Run: install_extension_simple.bat")
        print("2. Enable Developer mode manually")
        print("3. Refresh Quotex page")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        print("\n🔧 Manual steps:")
        print("1. Run: install_extension_simple.bat")
        print("2. Or install manually via chrome://extensions/")
        return False

def main():
    """اجرای نصب"""
    success = install_extension()
    
    if success:
        print("\n" + "="*50)
        print("✅ Installation completed!")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("❌ Installation failed!")
        print("Please try manual installation")
        print("="*50)

if __name__ == "__main__":
    main()
