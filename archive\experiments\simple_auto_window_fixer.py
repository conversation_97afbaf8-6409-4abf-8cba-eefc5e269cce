"""
📐 SIMPLE AUTO WINDOW FIXER
🎯 AUTOMATICALLY FIXES WINDOW SIZE FOR ANY SCREEN
✅ GUARANTEED TO WORK
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QFont

class SimpleAutoWindowFixer:
    """
    📐 SIMPLE AUTO WINDOW FIXER
    🎯 Automatically detects screen and fixes window size
    """
    
    def __init__(self):
        # Get application
        self.app = QApplication.instance()
        if not self.app:
            print("❌ No QApplication found")
            return
        
        # Get primary screen
        self.screen = self.app.primaryScreen()
        self.screen_rect = self.screen.geometry()
        self.available_rect = self.screen.availableGeometry()
        
        # Screen dimensions
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()
        self.available_width = self.available_rect.width()
        self.available_height = self.available_rect.height()
        
        # Calculate perfect window size
        self.perfect_width = int(self.available_width * 0.8)  # 80% of available width
        self.perfect_height = int(self.available_height * 0.8)  # 80% of available height
        
        # Ensure reasonable limits
        self.perfect_width = max(1000, min(self.perfect_width, 1600))
        self.perfect_height = max(700, min(self.perfect_height, 1200))
        
        print(f"📺 Screen: {self.screen_width}x{self.screen_height}")
        print(f"📐 Available: {self.available_width}x{self.available_height}")
        print(f"🎯 Perfect Size: {self.perfect_width}x{self.perfect_height}")
        
        # List of windows to manage
        self.managed_windows = []
        
        # Auto-fix timer
        self.auto_timer = QTimer()
        self.auto_timer.timeout.connect(self.auto_fix_all_windows)
        self.auto_timer.start(3000)  # Check every 3 seconds
        
        print("✅ Simple Auto Window Fixer initialized")
    
    def add_window(self, window):
        """📝 Add window to auto-management"""
        if window not in self.managed_windows:
            self.managed_windows.append(window)
            print(f"📝 Window added to auto-management")
            
            # Immediately fix the window
            self.fix_window(window)
    
    def fix_window(self, window):
        """📐 Fix window size and position"""
        try:
            if not window:
                return False
            
            # Set perfect size
            window.resize(self.perfect_width, self.perfect_height)
            
            # Calculate center position
            x = self.available_rect.x() + (self.available_width - self.perfect_width) // 2
            y = self.available_rect.y() + (self.available_height - self.perfect_height) // 2
            
            # Move to center
            window.move(x, y)
            
            print(f"✅ Window fixed: {self.perfect_width}x{self.perfect_height} at ({x}, {y})")
            return True
            
        except Exception as e:
            print(f"❌ Fix window error: {e}")
            return False
    
    def auto_fix_all_windows(self):
        """🔄 Auto-fix all managed windows"""
        try:
            for window in self.managed_windows[:]:  # Copy list to avoid modification during iteration
                if window and hasattr(window, 'isVisible') and window.isVisible():
                    # Check if window needs fixing
                    current_size = window.size()
                    if (abs(current_size.width() - self.perfect_width) > 50 or 
                        abs(current_size.height() - self.perfect_height) > 50):
                        self.fix_window(window)
                else:
                    # Remove invalid windows
                    if window in self.managed_windows:
                        self.managed_windows.remove(window)
                        
        except Exception as e:
            print(f"❌ Auto-fix error: {e}")
    
    def get_screen_info(self):
        """📊 Get screen information"""
        return {
            "screen_size": f"{self.screen_width}x{self.screen_height}",
            "available_size": f"{self.available_width}x{self.available_height}",
            "perfect_size": f"{self.perfect_width}x{self.perfect_height}"
        }

# Global instance
auto_fixer = None

def initialize_auto_fixer():
    """🚀 Initialize global auto fixer"""
    global auto_fixer
    try:
        if auto_fixer is None:
            auto_fixer = SimpleAutoWindowFixer()
            print("🚀 Global Auto Fixer initialized")
        return auto_fixer
    except Exception as e:
        print(f"❌ Initialize auto fixer error: {e}")
        return None

def add_window_to_auto_fixer(window):
    """📝 Add window to global auto fixer"""
    global auto_fixer
    try:
        if auto_fixer is None:
            auto_fixer = initialize_auto_fixer()
        
        if auto_fixer:
            auto_fixer.add_window(window)
            return True
        return False
    except Exception as e:
        print(f"❌ Add window to auto fixer error: {e}")
        return False

def get_auto_fixer():
    """🔍 Get global auto fixer"""
    global auto_fixer
    return auto_fixer

class TestWindow(QMainWindow):
    """🧪 Test window for auto fixer"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📐 Simple Auto Window Fixer Test")
        
        # Setup UI
        self.setup_ui()
        
        # Add to auto fixer
        add_window_to_auto_fixer(self)
    
    def setup_ui(self):
        """🎨 Setup UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("📐 Simple Auto Window Fixer")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #2196F3; margin: 20px;")
        layout.addWidget(title)
        
        # Info
        fixer = get_auto_fixer()
        if fixer:
            info = fixer.get_screen_info()
            
            screen_label = QLabel(f"📺 Screen: {info['screen_size']}")
            screen_label.setFont(QFont("Arial", 14))
            screen_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(screen_label)
            
            available_label = QLabel(f"📐 Available: {info['available_size']}")
            available_label.setFont(QFont("Arial", 14))
            available_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(available_label)
            
            perfect_label = QLabel(f"🎯 Perfect Size: {info['perfect_size']}")
            perfect_label.setFont(QFont("Arial", 14, QFont.Bold))
            perfect_label.setStyleSheet("color: #4CAF50;")
            perfect_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(perfect_label)
        
        # Status
        status_label = QLabel("✅ Auto-fixing every 3 seconds!")
        status_label.setFont(QFont("Arial", 12, QFont.Bold))
        status_label.setStyleSheet("color: #4CAF50; margin: 20px;")
        status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(status_label)
        
        # Test button
        test_btn = QPushButton("🧪 Test Manual Fix")
        test_btn.clicked.connect(self.test_manual_fix)
        test_btn.setFont(QFont("Arial", 12, QFont.Bold))
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(test_btn)
        
        # Instructions
        instructions = QLabel("""
📋 How it works:
✅ Window automatically resizes every 3 seconds
✅ Perfect size calculated for your screen
✅ Always centered on screen
✅ Works with any screen resolution

🎯 Just resize this window and watch it auto-fix!
        """)
        instructions.setFont(QFont("Arial", 10))
        instructions.setStyleSheet("color: #666; margin: 20px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
    
    def test_manual_fix(self):
        """🧪 Test manual fix"""
        fixer = get_auto_fixer()
        if fixer:
            fixer.fix_window(self)
            print("🧪 Manual fix applied")

def main():
    """🚀 Main function"""
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    # Initialize auto fixer
    initialize_auto_fixer()
    
    # Create test window
    window = TestWindow()
    window.show()
    
    print("🚀 Simple Auto Window Fixer Test launched!")
    print("📐 Window will auto-fix every 3 seconds")
    
    return app.exec()

if __name__ == "__main__":
    main()
