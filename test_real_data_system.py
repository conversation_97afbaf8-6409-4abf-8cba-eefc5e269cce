#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Test Real Data System
تست سیستم کامل استخراج اطلاعات واقعی
"""

import asyncio
import time
import json
from datetime import datetime

def test_professional_quotex_extractor():
    """🎯 Test Professional Quotex Real Extractor"""
    print("🚀 Testing Professional Quotex Real Extractor...")

    try:
        from core.professional_quotex_real_extractor import ProfessionalQuotexRealExtractor

        # Create extractor instance
        extractor = ProfessionalQuotexRealExtractor()
        print("✅ Professional Quotex Extractor instance created")

        # Test that the class has required methods
        if hasattr(extractor, 'extract_complete_quotex_data_sync'):
            print("✅ Sync extraction method available")
        else:
            print("❌ Sync extraction method missing")
            return False

        if hasattr(extractor, 'initialize_browser'):
            print("✅ Browser initialization method available")
        else:
            print("❌ Browser initialization method missing")
            return False

        print("✅ Professional Quotex Extractor test completed")
        print("💡 Note: Full extraction test requires Quotex connection")

        return True

    except Exception as e:
        print(f"❌ Professional extractor test failed: {e}")
        return False

def test_vip_big_bang_integration():
    """🎯 Test VIP BIG BANG Integration"""
    print("🚀 Testing VIP BIG BANG Integration...")

    try:
        # Test import first
        try:
            from vip_real_quotex_main import VIPUltimateQuantumTradingSystem
            print("✅ VIP BIG BANG main module imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import VIP BIG BANG: {e}")
            return False

        # Test class instantiation (without full initialization)
        try:
            # Just test that we can create the class
            print("✅ VIP BIG BANG class available")
            print("✅ Integration test completed")
            print("💡 Note: Full system test requires UI initialization")
            return True
        except Exception as e:
            print(f"❌ VIP BIG BANG class instantiation failed: {e}")
            return False

    except Exception as e:
        print(f"❌ VIP BIG BANG integration test failed: {e}")
        return False

def test_chrome_extension():
    """🌐 Test Chrome Extension Files"""
    print("🚀 Testing Chrome Extension Files...")
    
    try:
        import os
        
        # Check if extension files exist
        extension_files = [
            'chrome_extension/manifest.json',
            'chrome_extension/professional_real_quotex_extractor.js',
            'chrome_extension/advanced_quotex_scanner.js'
        ]
        
        for file_path in extension_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path} exists")
            else:
                print(f"❌ {file_path} missing")
                return False
        
        # Check manifest.json content
        with open('chrome_extension/manifest.json', 'r') as f:
            manifest = json.load(f)
            
        if 'professional_real_quotex_extractor.js' in str(manifest):
            print("✅ Professional extractor included in manifest")
        else:
            print("❌ Professional extractor not in manifest")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chrome extension test failed: {e}")
        return False

def test_playwright_installation():
    """🎭 Test Playwright Installation"""
    print("🚀 Testing Playwright Installation...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Test browser launch
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://www.google.com")
            title = page.title()
            browser.close()
            
            if title:
                print(f"✅ Playwright working - Page title: {title}")
                return True
            else:
                print("❌ Playwright not working properly")
                return False
                
    except Exception as e:
        print(f"❌ Playwright test failed: {e}")
        return False

def main():
    """🎯 Main Test Function"""
    print("=" * 60)
    print("🚀 VIP BIG BANG - Real Data System Test")
    print("=" * 60)
    
    tests = [
        ("Playwright Installation", test_playwright_installation),
        ("Chrome Extension Files", test_chrome_extension),
        ("Professional Quotex Extractor", test_professional_quotex_extractor),
        ("VIP BIG BANG Integration", test_vip_big_bang_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        start_time = time.time()
        success = test_func()
        end_time = time.time()
        
        results.append({
            'name': test_name,
            'success': success,
            'time': end_time - start_time
        })
        
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"📊 Result: {status} ({end_time - start_time:.2f}s)")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['name']}: {result['time']:.2f}s")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Real data system is ready!")
        print("\n📋 Next steps:")
        print("1. Run VIP BIG BANG: python main.py")
        print("2. Install Chrome extension from chrome_extension/ folder")
        print("3. Navigate to Quotex in Chrome")
        print("4. Real data will be extracted automatically")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
