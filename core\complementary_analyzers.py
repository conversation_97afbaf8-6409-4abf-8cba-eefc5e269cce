"""
VIP BIG BANG Enterprise - Complementary Analyzers
Remaining complementary analysis components
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import logging
from datetime import datetime

class ActiveAnalysesPanel:
    """
    Active Analyses Panel - VIP BIG BANG complementary analysis
    Monitors how many analyses are currently giving entry signals
    Example: 7 out of 10 analyses are active
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("ActiveAnalysesPanel")
        
        # Panel parameters
        self.min_active_threshold = 0.6  # 60% minimum active for strong signal
        self.strong_active_threshold = 0.8  # 80% for very strong signal
        
        self.logger.debug("Active Analyses Panel initialized")
    
    def analyze(self, analysis_results: Dict) -> Dict:
        """Analyze how many analyses are currently active"""
        try:
            total_analyses = 0
            active_analyses = 0
            bullish_analyses = 0
            bearish_analyses = 0
            
            # Count active analyses
            for analysis_name, result in analysis_results.items():
                if isinstance(result, dict) and 'direction' in result and 'confidence' in result:
                    total_analyses += 1
                    
                    # Consider analysis active if confidence > 0.5 and direction is not neutral
                    if result['confidence'] > 0.5 and result['direction'] != 'NEUTRAL':
                        active_analyses += 1
                        
                        if result['direction'] == 'UP':
                            bullish_analyses += 1
                        elif result['direction'] == 'DOWN':
                            bearish_analyses += 1
            
            # Calculate activity percentage
            activity_percentage = active_analyses / total_analyses if total_analyses > 0 else 0
            
            # Determine dominant direction
            if bullish_analyses > bearish_analyses:
                dominant_direction = 'UP'
                direction_strength = bullish_analyses / active_analyses if active_analyses > 0 else 0
            elif bearish_analyses > bullish_analyses:
                dominant_direction = 'DOWN'
                direction_strength = bearish_analyses / active_analyses if active_analyses > 0 else 0
            else:
                dominant_direction = 'NEUTRAL'
                direction_strength = 0
            
            # Calculate score
            score = 0.5 + (activity_percentage - 0.5) * direction_strength
            score = max(0, min(1, score))
            
            return {
                'score': score,
                'direction': dominant_direction,
                'confidence': activity_percentage,
                'total_analyses': total_analyses,
                'active_analyses': active_analyses,
                'activity_percentage': activity_percentage,
                'bullish_analyses': bullish_analyses,
                'bearish_analyses': bearish_analyses,
                'details': f"Active: {active_analyses}/{total_analyses} ({activity_percentage:.1%})"
            }
            
        except Exception as e:
            self.logger.error(f"Active analyses panel error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'details': f'Active analyses panel failed: {str(e)}'
            }


class AutoTradeConditionsCheck:
    """
    AutoTrade Conditions Check - VIP BIG BANG complementary analysis
    Checks AutoTrade settings and allows entry only in optimal conditions
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("AutoTradeConditionsCheck")
        
        # AutoTrade parameters
        self.min_balance_threshold = 10.0  # Minimum balance for auto trading
        self.max_daily_trades = 50         # Maximum trades per day
        self.max_consecutive_losses = 3    # Maximum consecutive losses
        self.min_win_rate = 0.6           # Minimum win rate to continue
        
        self.logger.debug("AutoTrade Conditions Check initialized")
    
    def analyze(self, account_data: Dict = None, performance_data: Dict = None) -> Dict:
        """Check AutoTrade conditions"""
        try:
            # Default values if no data provided
            if account_data is None:
                account_data = {'balance': 100.0, 'daily_trades': 0}
            if performance_data is None:
                performance_data = {'consecutive_losses': 0, 'win_rate': 0.7}
            
            conditions_met = []
            conditions_failed = []
            
            # Check balance
            balance = account_data.get('balance', 0)
            if balance >= self.min_balance_threshold:
                conditions_met.append('sufficient_balance')
            else:
                conditions_failed.append('insufficient_balance')
            
            # Check daily trades limit
            daily_trades = account_data.get('daily_trades', 0)
            if daily_trades < self.max_daily_trades:
                conditions_met.append('daily_limit_ok')
            else:
                conditions_failed.append('daily_limit_exceeded')
            
            # Check consecutive losses
            consecutive_losses = performance_data.get('consecutive_losses', 0)
            if consecutive_losses < self.max_consecutive_losses:
                conditions_met.append('loss_limit_ok')
            else:
                conditions_failed.append('too_many_losses')
            
            # Check win rate
            win_rate = performance_data.get('win_rate', 0)
            if win_rate >= self.min_win_rate:
                conditions_met.append('win_rate_ok')
            else:
                conditions_failed.append('low_win_rate')
            
            # Calculate overall score
            total_conditions = len(conditions_met) + len(conditions_failed)
            score = len(conditions_met) / total_conditions if total_conditions > 0 else 0
            
            # Allow trading only if all conditions are met
            allow_autotrade = len(conditions_failed) == 0
            
            return {
                'score': score,
                'direction': 'NEUTRAL',
                'confidence': score,
                'allow_autotrade': allow_autotrade,
                'conditions_met': conditions_met,
                'conditions_failed': conditions_failed,
                'details': f"AutoTrade: {'ALLOWED' if allow_autotrade else 'BLOCKED'} ({len(conditions_met)}/{total_conditions})"
            }
            
        except Exception as e:
            self.logger.error(f"AutoTrade conditions check error: {e}")
            return {
                'score': 0.0,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'allow_autotrade': False,
                'details': f'AutoTrade check failed: {str(e)}'
            }


class AccountSafety:
    """
    Account Summary & Safety - VIP BIG BANG complementary analysis
    Monitors account balance, profit, loss, daily limits to prevent OverTrade
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("AccountSafety")
        
        # Safety parameters
        self.max_daily_loss_percent = 0.05  # 5% max daily loss
        self.max_drawdown_percent = 0.15    # 15% max drawdown
        self.min_balance_percent = 0.1      # 10% minimum balance
        
        self.logger.debug("Account Safety initialized")
    
    def analyze(self, account_data: Dict = None) -> Dict:
        """Analyze account safety metrics"""
        try:
            # Default account data
            if account_data is None:
                account_data = {
                    'balance': 100.0,
                    'initial_balance': 100.0,
                    'daily_pnl': 0.0,
                    'total_pnl': 0.0,
                    'max_balance': 100.0
                }
            
            balance = account_data.get('balance', 100.0)
            initial_balance = account_data.get('initial_balance', 100.0)
            daily_pnl = account_data.get('daily_pnl', 0.0)
            max_balance = account_data.get('max_balance', initial_balance)
            
            safety_checks = []
            warnings = []
            
            # Check daily loss limit
            daily_loss_percent = abs(daily_pnl) / initial_balance if daily_pnl < 0 else 0
            if daily_loss_percent < self.max_daily_loss_percent:
                safety_checks.append('daily_loss_ok')
            else:
                warnings.append('daily_loss_exceeded')
            
            # Check drawdown
            drawdown_percent = (max_balance - balance) / max_balance if max_balance > 0 else 0
            if drawdown_percent < self.max_drawdown_percent:
                safety_checks.append('drawdown_ok')
            else:
                warnings.append('high_drawdown')
            
            # Check minimum balance
            balance_percent = balance / initial_balance if initial_balance > 0 else 0
            if balance_percent > self.min_balance_percent:
                safety_checks.append('balance_ok')
            else:
                warnings.append('low_balance')
            
            # Calculate safety score
            total_checks = len(safety_checks) + len(warnings)
            safety_score = len(safety_checks) / total_checks if total_checks > 0 else 1.0
            
            # Determine safety level
            if safety_score >= 0.8:
                safety_level = 'SAFE'
            elif safety_score >= 0.6:
                safety_level = 'CAUTION'
            else:
                safety_level = 'DANGER'
            
            # Allow trading based on safety
            allow_trading = safety_level != 'DANGER'
            
            return {
                'score': safety_score,
                'direction': 'NEUTRAL',
                'confidence': safety_score,
                'safety_level': safety_level,
                'allow_trading': allow_trading,
                'safety_checks': safety_checks,
                'warnings': warnings,
                'balance': balance,
                'daily_pnl': daily_pnl,
                'drawdown_percent': drawdown_percent * 100,
                'details': f"Safety: {safety_level} - Balance: ${balance:.2f}"
            }
            
        except Exception as e:
            self.logger.error(f"Account safety error: {e}")
            return {
                'score': 0.0,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'allow_trading': False,
                'details': f'Account safety check failed: {str(e)}'
            }


class ManualConfirm:
    """
    Manual Confirm - VIP BIG BANG complementary analysis
    Requires manual trader confirmation for final trade execution
    Suitable for dangerous conditions or news events
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("ManualConfirm")
        
        # Manual confirm parameters
        self.auto_confirm_confidence = 0.95  # Auto-confirm above 95%
        self.force_manual_conditions = [
            'high_volatility', 'news_events', 'otc_mode', 'low_confidence'
        ]
        
        self.logger.debug("Manual Confirm initialized")
    
    def analyze(self, analysis_results: Dict, market_conditions: Dict = None) -> Dict:
        """Determine if manual confirmation is required"""
        try:
            # Calculate average confidence
            confidences = []
            for result in analysis_results.values():
                if isinstance(result, dict) and 'confidence' in result:
                    confidences.append(result['confidence'])
            
            avg_confidence = np.mean(confidences) if confidences else 0.0
            
            # Check for force manual conditions
            force_manual = False
            manual_reasons = []
            
            if market_conditions:
                # Check for high volatility
                if market_conditions.get('volatility', 'NORMAL') == 'HIGH':
                    force_manual = True
                    manual_reasons.append('high_volatility')
                
                # Check for news events
                if market_conditions.get('news_risk', 'LOW') == 'HIGH':
                    force_manual = True
                    manual_reasons.append('news_events')
                
                # Check for OTC mode
                if market_conditions.get('otc_mode', False):
                    force_manual = True
                    manual_reasons.append('otc_mode')
            
            # Check confidence level
            if avg_confidence < 0.7:
                force_manual = True
                manual_reasons.append('low_confidence')
            
            # Determine confirmation requirement
            if force_manual:
                requires_manual = True
                confirmation_type = 'MANUAL_REQUIRED'
                score = 0.3  # Low score to indicate manual needed
            elif avg_confidence >= self.auto_confirm_confidence:
                requires_manual = False
                confirmation_type = 'AUTO_CONFIRM'
                score = 1.0  # High score for auto-confirm
            else:
                requires_manual = True
                confirmation_type = 'MANUAL_RECOMMENDED'
                score = 0.6  # Medium score for manual recommended
            
            return {
                'score': score,
                'direction': 'NEUTRAL',
                'confidence': avg_confidence,
                'requires_manual': requires_manual,
                'confirmation_type': confirmation_type,
                'manual_reasons': manual_reasons,
                'avg_confidence': avg_confidence,
                'details': f"Manual: {confirmation_type} - Confidence: {avg_confidence:.1%}"
            }
            
        except Exception as e:
            self.logger.error(f"Manual confirm error: {e}")
            return {
                'score': 0.0,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'requires_manual': True,
                'details': f'Manual confirm failed: {str(e)}'
            }
