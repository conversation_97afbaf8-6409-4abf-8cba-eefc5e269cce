"""
VIP BIG BANG Enterprise - Trading Demo
نمایش عملکرد تحلیل 15 ثانیه و ترید 5 ثانیه
"""

import pandas as pd  # type: ignore
import time
import threading
from datetime import datetime, timedelta
import logging
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

class TradingSimulator:
    """شبیه‌ساز ترید برای نمایش عملکرد سیستم"""
    
    def __init__(self):
        self.logger = logging.getLogger("TradingSimulator")
        self.is_running = False
        self.analysis_thread = None
        self.trade_thread = None
        
        # Load VIP BIG BANG components
        from core.analysis_engine import AnalysisEngine
        from core.complementary_engine import ComplementaryEngine
        from core.settings import Settings
        
        self.settings = Settings()
        self.analysis_engine = AnalysisEngine(self.settings)
        self.complementary_engine = ComplementaryEngine(self.settings)
        
        # Trading state
        self.current_signal = None
        self.last_analysis_time = None
        self.active_trades = []
        self.trade_history = []
        
        self.logger.info("🚀 VIP BIG BANG Trading Simulator initialized")
        self.logger.info(f"📊 Analysis Interval: {self.settings.trading.analysis_interval} seconds")
        self.logger.info(f"⚡ Trade Duration: {self.settings.trading.trade_duration} seconds")
    
    def generate_market_data(self):
        """تولید داده‌های بازار شبیه‌سازی شده"""
        base_price = 1.2000 + random.uniform(-0.01, 0.01)
        
        return {
            'timestamp': datetime.now(),
            'open': base_price,
            'high': base_price + random.uniform(0, 0.002),
            'low': base_price - random.uniform(0, 0.002),
            'close': base_price + random.uniform(-0.001, 0.001),
            'volume': random.randint(100, 1000),
            'price': base_price
        }
    
    def analysis_loop(self):
        """حلقه تحلیل هر 15 ثانیه"""
        self.logger.info("🔍 Analysis loop started - Running every 15 seconds")
        
        while self.is_running:
            try:
                start_time = time.time()
                
                # Generate and feed market data
                market_data = self.generate_market_data()
                self.analysis_engine.update_market_data(market_data)
                
                # Run primary analysis
                primary_results = self.analysis_engine.analyze()
                
                if 'error' not in primary_results:
                    # Run complementary analysis
                    account_data = {
                        'balance': 1000.0,
                        'daily_trades': len(self.trade_history),
                        'daily_pnl': sum(t.get('profit', 0) for t in self.trade_history),
                        'performance': {'consecutive_losses': 0, 'win_rate': 0.75}
                    }
                    
                    comp_results = self.complementary_engine.run_all_complementary_analyses(
                        pd.DataFrame([market_data]), primary_results, account_data, None
                    )
                    
                    # Calculate final decision
                    final_decision = self.complementary_engine.calculate_final_trading_decision(
                        primary_results, comp_results
                    )
                    
                    # Update current signal
                    self.current_signal = {
                        'timestamp': datetime.now(),
                        'direction': final_decision['direction'],
                        'confidence': final_decision['confidence'],
                        'score': final_decision['final_score'],
                        'allow_trading': final_decision['allow_trading'],
                        'decision': final_decision['final_decision'],
                        'primary_results': primary_results,
                        'market_data': market_data
                    }
                    
                    processing_time = time.time() - start_time
                    self.last_analysis_time = datetime.now()
                    
                    self.logger.info(f"📈 Analysis completed in {processing_time:.3f}s")
                    self.logger.info(f"   Direction: {final_decision['direction']}")
                    self.logger.info(f"   Score: {final_decision['final_score']:.3f}")
                    self.logger.info(f"   Decision: {final_decision['final_decision']}")
                    self.logger.info(f"   Allow Trading: {final_decision['allow_trading']}")
                
                # Wait for next analysis cycle (15 seconds)
                time.sleep(self.settings.trading.analysis_interval)
                
            except Exception as e:
                self.logger.error(f"Analysis error: {e}")
                time.sleep(5)
    
    def trading_loop(self):
        """حلقه ترید هر 5 ثانیه"""
        self.logger.info("⚡ Trading loop started - Checking every 5 seconds")
        
        while self.is_running:
            try:
                if self.current_signal and self.current_signal['allow_trading']:
                    signal = self.current_signal
                    
                    # Check if signal is fresh (less than 30 seconds old)
                    signal_age = (datetime.now() - signal['timestamp']).total_seconds()
                    
                    if signal_age < 30 and signal['direction'] in ['CALL', 'PUT']:
                        # Execute trade
                        trade = self.execute_trade(signal)
                        if trade:
                            self.active_trades.append(trade)
                            self.logger.info(f"🎯 Trade executed: {trade['direction']} at {trade['entry_price']:.5f}")
                            self.logger.info(f"   Trade ID: {trade['id']}")
                            self.logger.info(f"   Confidence: {trade['confidence']:.2f}")
                            self.logger.info(f"   Expiry: {trade['expiry_time']}s")
                
                # Check for expired trades
                self.check_expired_trades()
                
                # Wait for next trading cycle (5 seconds)
                time.sleep(self.settings.trading.trade_duration)
                
            except Exception as e:
                self.logger.error(f"Trading error: {e}")
                time.sleep(2)
    
    def execute_trade(self, signal):
        """اجرای ترید"""
        if not signal['allow_trading'] or signal['direction'] not in ['CALL', 'PUT']:
            return None
        
        trade_id = f"VIP_{int(time.time())}"
        
        trade = {
            'id': trade_id,
            'direction': signal['direction'],
            'entry_price': signal['market_data']['price'],
            'entry_time': datetime.now(),
            'expiry_time': self.settings.trading.trade_duration * 60,  # 5 minutes
            'confidence': signal['confidence'],
            'score': signal['score'],
            'amount': 10.0,  # $10 trade
            'status': 'ACTIVE'
        }
        
        return trade
    
    def check_expired_trades(self):
        """بررسی ترید‌های منقضی شده"""
        current_time = datetime.now()
        
        for trade in self.active_trades[:]:  # Copy list to avoid modification during iteration
            trade_duration = (current_time - trade['entry_time']).total_seconds()
            
            if trade_duration >= trade['expiry_time']:
                # Simulate trade result
                win_probability = min(trade['confidence'], 0.85)  # Max 85% win rate
                is_winner = random.random() < win_probability
                
                profit = trade['amount'] * 0.8 if is_winner else -trade['amount']
                
                trade.update({
                    'status': 'CLOSED',
                    'result': 'WIN' if is_winner else 'LOSS',
                    'profit': profit,
                    'close_time': current_time
                })
                
                self.trade_history.append(trade)
                self.active_trades.remove(trade)
                
                self.logger.info(f"💰 Trade closed: {trade['id']} - {trade['result']}")
                self.logger.info(f"   Profit: ${profit:.2f}")
                self.logger.info(f"   Duration: {trade_duration:.0f}s")
    
    def start_trading(self):
        """شروع ترید"""
        if self.is_running:
            self.logger.warning("Trading is already running!")
            return
        
        self.is_running = True
        
        # Start analysis thread (15-second intervals)
        self.analysis_thread = threading.Thread(target=self.analysis_loop, daemon=True)
        self.analysis_thread.start()
        
        # Start trading thread (5-second intervals)
        self.trade_thread = threading.Thread(target=self.trading_loop, daemon=True)
        self.trade_thread.start()
        
        self.logger.info("🚀 VIP BIG BANG Trading started!")
        self.logger.info("📊 Analysis: Every 15 seconds")
        self.logger.info("⚡ Trading: Every 5 seconds")
    
    def stop_trading(self):
        """توقف ترید"""
        self.is_running = False
        self.logger.info("🛑 Trading stopped")
    
    def get_statistics(self):
        """آمار ترید"""
        total_trades = len(self.trade_history)
        if total_trades == 0:
            return "No trades executed yet"
        
        wins = sum(1 for t in self.trade_history if t['result'] == 'WIN')
        losses = total_trades - wins
        win_rate = (wins / total_trades) * 100
        total_profit = sum(t['profit'] for t in self.trade_history)
        
        stats = f"""
📊 VIP BIG BANG Trading Statistics:
   Total Trades: {total_trades}
   Wins: {wins} | Losses: {losses}
   Win Rate: {win_rate:.1f}%
   Total Profit: ${total_profit:.2f}
   Active Trades: {len(self.active_trades)}
   Last Analysis: {self.last_analysis_time.strftime('%H:%M:%S') if self.last_analysis_time else 'None'}
        """
        return stats

def main():
    """تست عملکرد سیستم"""
    print("🚀 VIP BIG BANG Enterprise - Trading Demo")
    print("=" * 60)
    
    simulator = TradingSimulator()
    
    try:
        # Start trading
        simulator.start_trading()
        
        # Run for 2 minutes to demonstrate
        print("⏰ Running demo for 2 minutes...")
        print("📊 Analysis every 15 seconds, Trading every 5 seconds")
        print("-" * 60)
        
        for i in range(24):  # 24 * 5 seconds = 2 minutes
            time.sleep(5)
            if i % 3 == 0:  # Every 15 seconds
                print(f"⏱️  {datetime.now().strftime('%H:%M:%S')} - Demo running...")
        
        # Show final statistics
        print("\n" + "=" * 60)
        print(simulator.get_statistics())
        
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    finally:
        simulator.stop_trading()
        print("✅ Demo completed!")

if __name__ == "__main__":
    main()
