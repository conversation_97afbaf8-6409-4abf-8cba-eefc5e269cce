@echo off
title VIP BIG BANG Extension Installer
color 0A

echo.
echo ========================================
echo    VIP BIG BANG Extension Installer
echo ========================================
echo.

echo Closing Chrome...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul

echo Starting Chrome with extension...
echo.
echo Chrome Path: C:\Program Files\Google\Chrome\Application\chrome.exe
echo Extension Path: C:\Users\<USER>\VIP_BIG_BANG\chrome_extension
echo.

start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" --load-extension="C:\Users\<USER>\VIP_BIG_BANG\chrome_extension" --disable-extensions-file-access-check --no-first-run --no-default-browser-check "https://qxbroker.com/en/trade"

echo.
echo ✅ Chrome started with VIP BIG BANG extension!
echo.
echo 📋 Next steps:
echo 1. Go to chrome://extensions/
echo 2. Enable "Developer mode" (top right)
echo 3. Make sure VIP BIG BANG is enabled
echo 4. Test on Quotex page
echo.
echo Press any key to exit...
pause >nul
