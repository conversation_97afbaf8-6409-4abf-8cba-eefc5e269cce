"""
🔍 VIP BIG BANG - Complete Status Monitor
Real-time monitoring of all system components
"""

import sys
import time
import json
import asyncio
import websocket
import threading
from datetime import datetime
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class VIPCompleteStatusMonitor(QMainWindow):
    """
    🔍 Complete system status monitor
    Real-time monitoring of all VIP BIG BANG components
    """
    
    def __init__(self):
        super().__init__()
        
        # Window setup
        self.setWindowTitle("🔍 VIP BIG BANG - Complete Status Monitor")
        self.setGeometry(100, 100, 1000, 700)
        
        # Status tracking
        self.desktop_robot_status = "🔴 Offline"
        self.dashboard_status = "🔴 Offline"
        self.extension_status = "🔴 Offline"
        self.quotex_status = "🔴 Offline"
        self.websocket_status = "🔴 Offline"
        
        # Data counters
        self.data_received_count = 0
        self.last_balance = "N/A"
        self.last_asset = "N/A"
        self.connection_count = 0
        
        # Setup UI
        self._setup_ui()
        self._apply_styles()
        
        # Start monitoring
        self._start_monitoring()
        
    def _setup_ui(self):
        """Setup monitoring UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Status panels
        status_layout = QHBoxLayout()
        status_layout.setSpacing(15)
        
        # System status panel
        system_panel = self._create_system_status_panel()
        status_layout.addWidget(system_panel, 1)
        
        # Data flow panel
        data_panel = self._create_data_flow_panel()
        status_layout.addWidget(data_panel, 1)
        
        layout.addLayout(status_layout)
        
        # Live data display
        data_display = self._create_data_display()
        layout.addWidget(data_display)
        
        # Control buttons
        controls = self._create_controls()
        layout.addWidget(controls)
        
    def _create_header(self):
        """Create header with title and overall status"""
        header = QFrame()
        header.setFixedHeight(80)
        header.setObjectName("header")
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Title
        title_layout = QVBoxLayout()
        
        title = QLabel("🔍 VIP BIG BANG - Complete Status Monitor")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #3B82F6;")
        title_layout.addWidget(title)
        
        subtitle = QLabel("Real-time monitoring of all system components")
        subtitle.setStyleSheet("font-size: 14px; color: #9CA3AF;")
        title_layout.addWidget(subtitle)
        
        layout.addLayout(title_layout)
        layout.addStretch()
        
        # Overall status
        self.overall_status = QLabel("🔴 System Offline")
        self.overall_status.setStyleSheet("font-size: 18px; font-weight: bold; color: #EF4444;")
        layout.addWidget(self.overall_status)
        
        return header
        
    def _create_system_status_panel(self):
        """Create system status panel"""
        panel = QGroupBox("🖥️ System Components")
        panel.setFixedWidth(450)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(10)
        
        # Status items
        self.desktop_robot_label = QLabel("🤖 Desktop Robot: 🔴 Offline")
        self.desktop_robot_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.desktop_robot_label)
        
        self.dashboard_label = QLabel("🎮 Dashboard UI: 🔴 Offline")
        self.dashboard_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.dashboard_label)
        
        self.websocket_label = QLabel("🔌 WebSocket Server: 🔴 Offline")
        self.websocket_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.websocket_label)
        
        self.extension_label = QLabel("🔌 Chrome Extension: 🔴 Offline")
        self.extension_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.extension_label)
        
        self.quotex_label = QLabel("🌐 Quotex Platform: 🔴 Offline")
        self.quotex_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.quotex_label)
        
        return panel
        
    def _create_data_flow_panel(self):
        """Create data flow panel"""
        panel = QGroupBox("📊 Data Flow")
        panel.setFixedWidth(450)
        
        layout = QVBoxLayout(panel)
        layout.setSpacing(10)
        
        # Data metrics
        self.data_count_label = QLabel("📈 Data Received: 0")
        self.data_count_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.data_count_label)
        
        self.balance_label = QLabel("💰 Last Balance: N/A")
        self.balance_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.balance_label)
        
        self.asset_label = QLabel("📊 Last Asset: N/A")
        self.asset_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.asset_label)
        
        self.connection_label = QLabel("🔗 Connections: 0")
        self.connection_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.connection_label)
        
        self.last_update_label = QLabel("⏰ Last Update: Never")
        self.last_update_label.setStyleSheet("font-size: 14px; padding: 8px;")
        layout.addWidget(self.last_update_label)
        
        return panel
        
    def _create_data_display(self):
        """Create live data display"""
        group = QGroupBox("📡 Live Data Stream")
        
        layout = QVBoxLayout(group)
        
        self.data_display = QTextEdit()
        self.data_display.setMaximumHeight(300)
        self.data_display.setStyleSheet("""
            QTextEdit {
                background: #1F2937;
                color: #F3F4F6;
                border: 1px solid #374151;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.data_display)
        
        # Add initial message
        self._log("🚀 VIP BIG BANG Status Monitor Started")
        self._log("📡 Monitoring WebSocket: ws://localhost:8765")
        self._log("⏳ Waiting for system components...")
        
        return group
        
    def _create_controls(self):
        """Create control buttons"""
        controls = QFrame()
        controls.setFixedHeight(60)
        
        layout = QHBoxLayout(controls)
        layout.setContentsMargins(0, 10, 0, 10)
        
        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh Status")
        refresh_btn.clicked.connect(self._refresh_status)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: #3B82F6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2563EB;
            }
        """)
        layout.addWidget(refresh_btn)
        
        # Clear log button
        clear_btn = QPushButton("🗑️ Clear Log")
        clear_btn.clicked.connect(self._clear_log)
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #4B5563;
            }
        """)
        layout.addWidget(clear_btn)
        
        layout.addStretch()
        
        # Status indicator
        self.live_indicator = QLabel("🔴 Monitoring")
        self.live_indicator.setStyleSheet("font-size: 16px; font-weight: bold; color: #EF4444;")
        layout.addWidget(self.live_indicator)
        
        return controls
        
    def _start_monitoring(self):
        """Start WebSocket monitoring"""
        def monitor():
            try:
                def on_message(ws, message):
                    try:
                        data = json.loads(message)
                        self._handle_websocket_data(data)
                    except Exception as e:
                        self._log(f"❌ Error parsing message: {e}")
                
                def on_error(ws, error):
                    self._log(f"❌ WebSocket Error: {error}")
                    self._update_websocket_status(False)
                
                def on_close(ws, close_status_code, close_msg):
                    self._log("🔌 WebSocket Connection Closed")
                    self._update_websocket_status(False)
                
                def on_open(ws):
                    self._log("✅ WebSocket Connected to Desktop Robot")
                    self._update_websocket_status(True)
                
                # Connect to WebSocket
                ws = websocket.WebSocketApp('ws://localhost:8765',
                                          on_open=on_open,
                                          on_message=on_message,
                                          on_error=on_error,
                                          on_close=on_close)
                ws.run_forever()
                
            except Exception as e:
                self._log(f"❌ Monitor Error: {e}")
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        
        # Start status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(2000)  # Update every 2 seconds
        
    def _handle_websocket_data(self, data):
        """Handle received WebSocket data"""
        self.data_received_count += 1
        
        # Update data metrics
        if 'balance' in data:
            self.last_balance = data['balance']
        if 'currentAsset' in data:
            self.last_asset = data['currentAsset'] or "N/A"
        
        # Log data
        timestamp = datetime.now().strftime("%H:%M:%S")
        source = data.get('source', 'Unknown')
        balance = data.get('balance', 'N/A')
        
        self._log(f"[{timestamp}] 📊 Data from {source}: Balance={balance}")
        
        # Update UI
        QTimer.singleShot(0, self._update_data_display)
        
    def _update_websocket_status(self, connected):
        """Update WebSocket status"""
        if connected:
            self.websocket_status = "🟢 Online"
            self.desktop_robot_status = "🟢 Online"
            self.live_indicator.setText("🟢 Live Monitoring")
            self.live_indicator.setStyleSheet("font-size: 16px; font-weight: bold; color: #10B981;")
        else:
            self.websocket_status = "🔴 Offline"
            self.live_indicator.setText("🔴 Monitoring")
            self.live_indicator.setStyleSheet("font-size: 16px; font-weight: bold; color: #EF4444;")
            
    def _update_status(self):
        """Update all status displays"""
        # Update system status labels
        self.desktop_robot_label.setText(f"🤖 Desktop Robot: {self.desktop_robot_status}")
        self.dashboard_label.setText(f"🎮 Dashboard UI: {self.dashboard_status}")
        self.websocket_label.setText(f"🔌 WebSocket Server: {self.websocket_status}")
        self.extension_label.setText(f"🔌 Chrome Extension: {self.extension_status}")
        self.quotex_label.setText(f"🌐 Quotex Platform: {self.quotex_status}")
        
        # Update overall status
        online_count = sum([
            "🟢" in self.desktop_robot_status,
            "🟢" in self.dashboard_status,
            "🟢" in self.websocket_status,
            "🟢" in self.extension_status,
            "🟢" in self.quotex_status
        ])
        
        if online_count >= 3:
            self.overall_status.setText("🟢 System Online")
            self.overall_status.setStyleSheet("font-size: 18px; font-weight: bold; color: #10B981;")
        elif online_count >= 1:
            self.overall_status.setText("🟡 System Partial")
            self.overall_status.setStyleSheet("font-size: 18px; font-weight: bold; color: #F59E0B;")
        else:
            self.overall_status.setText("🔴 System Offline")
            self.overall_status.setStyleSheet("font-size: 18px; font-weight: bold; color: #EF4444;")
            
    def _update_data_display(self):
        """Update data flow display"""
        self.data_count_label.setText(f"📈 Data Received: {self.data_received_count}")
        self.balance_label.setText(f"💰 Last Balance: {self.last_balance}")
        self.asset_label.setText(f"📊 Last Asset: {self.last_asset}")
        self.connection_label.setText(f"🔗 Connections: {self.connection_count}")
        
        current_time = datetime.now().strftime("%H:%M:%S")
        self.last_update_label.setText(f"⏰ Last Update: {current_time}")
        
    def _refresh_status(self):
        """Refresh system status"""
        self._log("🔄 Refreshing system status...")
        # Reset counters
        self.data_received_count = 0
        self._update_data_display()
        
    def _clear_log(self):
        """Clear data log"""
        self.data_display.clear()
        self._log("🗑️ Log cleared")
        
    def _log(self, message):
        """Log message to display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.data_display.append(log_message)
        print(log_message)
        
    def _apply_styles(self):
        """Apply global styles"""
        self.setStyleSheet("""
            QMainWindow {
                background: #0F172A;
                color: white;
            }
            QFrame#header {
                background: rgba(30, 41, 59, 0.9);
                border: 1px solid #475569;
                border-radius: 12px;
            }
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 2px solid #475569;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background: rgba(30, 41, 59, 0.5);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #3B82F6;
                font-size: 16px;
            }
            QLabel {
                color: white;
            }
        """)


def main():
    """Main function"""
    print("🔍 VIP BIG BANG - Complete Status Monitor")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show monitor
    monitor = VIPCompleteStatusMonitor()
    monitor.show()
    
    print("✅ Status Monitor launched")
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
