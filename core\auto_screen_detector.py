"""
🖥️ AUTO SCREEN DETECTOR
📐 AUTOMATICALLY DETECTS AND ADJUSTS TO ANY SCREEN SIZE
🎯 NO MANUAL CLICKS NEEDED
"""

import logging
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QObject, Signal
from PySide6.QtGui import QScreen

class AutoScreenDetector(QObject):
    """
    🖥️ AUTO SCREEN DETECTOR
    📐 Automatically detects screen and adjusts window size
    """
    
    # Signals
    screen_detected = Signal(dict)
    window_adjusted = Signal(int, int)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("AutoScreenDetector")
        
        # Get application and screen info
        self.app = QApplication.instance()
        if not self.app:
            self.logger.error("❌ No QApplication instance found")
            return
        
        # Screen information
        self.primary_screen = self.app.primaryScreen()
        self.all_screens = self.app.screens()
        
        # Screen geometry
        self.screen_geometry = self.primary_screen.geometry()
        self.available_geometry = self.primary_screen.availableGeometry()
        
        # Screen properties
        self.screen_width = self.screen_geometry.width()
        self.screen_height = self.screen_geometry.height()
        self.available_width = self.available_geometry.width()
        self.available_height = self.available_geometry.height()
        
        # DPI information
        self.logical_dpi = self.primary_screen.logicalDotsPerInch()
        self.device_pixel_ratio = self.primary_screen.devicePixelRatio()
        
        # Calculate optimal window size automatically
        self.calculate_optimal_size()
        
        # Auto-detection timer
        self.detection_timer = QTimer()
        self.detection_timer.timeout.connect(self.check_screen_changes)
        self.detection_timer.start(2000)  # Check every 2 seconds
        
        self.logger.info("🖥️ Auto Screen Detector initialized")
        self.logger.info(f"📺 Screen: {self.screen_width}x{self.screen_height}")
        self.logger.info(f"📐 Available: {self.available_width}x{self.available_height}")
        self.logger.info(f"🎯 Optimal: {self.optimal_width}x{self.optimal_height}")
    
    def calculate_optimal_size(self):
        """📐 Calculate optimal window size based on screen"""
        # Use 85% of available space for optimal viewing
        self.optimal_width = int(self.available_width * 0.85)
        self.optimal_height = int(self.available_height * 0.85)
        
        # Ensure minimum size for usability
        self.optimal_width = max(self.optimal_width, 1000)
        self.optimal_height = max(self.optimal_height, 700)
        
        # Ensure maximum size for very large screens
        self.optimal_width = min(self.optimal_width, 1800)
        self.optimal_height = min(self.optimal_height, 1200)
        
        # Adjust for high DPI screens
        if self.device_pixel_ratio > 1.5:
            self.optimal_width = int(self.optimal_width * 1.1)
            self.optimal_height = int(self.optimal_height * 1.1)
        
        self.logger.info(f"📐 Calculated optimal size: {self.optimal_width}x{self.optimal_height}")
    
    def detect_screen_type(self):
        """📺 Detect screen type automatically"""
        width = self.screen_width
        height = self.screen_height
        dpi = self.logical_dpi
        
        # Detect screen type
        if width >= 3840 and height >= 2160:  # 4K
            screen_type = "4K"
            scale_factor = 1.4
        elif width >= 2560 and height >= 1440:  # 2K/QHD
            screen_type = "2K"
            scale_factor = 1.2
        elif width >= 1920 and height >= 1080:  # Full HD
            screen_type = "Full HD"
            scale_factor = 1.0
        elif width >= 1366 and height >= 768:   # HD
            screen_type = "HD"
            scale_factor = 0.9
        else:  # Lower resolution
            screen_type = "Low Res"
            scale_factor = 0.8
        
        # Adjust for high DPI
        if dpi > 120:
            screen_type += " (High DPI)"
            scale_factor *= 1.1
        
        return {
            "type": screen_type,
            "scale_factor": scale_factor,
            "width": width,
            "height": height,
            "dpi": dpi,
            "optimal_width": self.optimal_width,
            "optimal_height": self.optimal_height
        }
    
    def auto_adjust_window(self, window):
        """🎯 Automatically adjust window to optimal size"""
        try:
            if not window:
                self.logger.warning("⚠️ No window provided for adjustment")
                return False
            
            # Set optimal size
            window.resize(self.optimal_width, self.optimal_height)
            
            # Center window automatically
            self.center_window(window)
            
            # Emit signal
            self.window_adjusted.emit(self.optimal_width, self.optimal_height)
            
            self.logger.info(f"✅ Window auto-adjusted: {self.optimal_width}x{self.optimal_height}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Auto adjust window error: {e}")
            return False
    
    def center_window(self, window):
        """🎯 Center window on screen automatically"""
        try:
            # Get window frame geometry
            window_geometry = window.frameGeometry()
            
            # Get available geometry center
            center_point = self.available_geometry.center()
            
            # Move window center to screen center
            window_geometry.moveCenter(center_point)
            
            # Apply position
            window.move(window_geometry.topLeft())
            
            self.logger.info(f"🎯 Window auto-centered at: {window_geometry.topLeft().x()}, {window_geometry.topLeft().y()}")
            
        except Exception as e:
            self.logger.error(f"❌ Center window error: {e}")
    
    def check_screen_changes(self):
        """🔍 Check for screen changes automatically"""
        try:
            # Get current screen info
            current_screen = self.app.primaryScreen()
            current_geometry = current_screen.geometry()
            
            # Check if screen changed
            if (current_geometry.width() != self.screen_width or 
                current_geometry.height() != self.screen_height):
                
                self.logger.info("📺 Screen change detected!")
                
                # Update screen information
                self.primary_screen = current_screen
                self.screen_geometry = current_geometry
                self.available_geometry = current_screen.availableGeometry()
                
                self.screen_width = self.screen_geometry.width()
                self.screen_height = self.screen_geometry.height()
                self.available_width = self.available_geometry.width()
                self.available_height = self.available_geometry.height()
                
                # Recalculate optimal size
                self.calculate_optimal_size()
                
                # Emit screen detected signal
                screen_info = self.detect_screen_type()
                self.screen_detected.emit(screen_info)
                
                self.logger.info(f"📺 New screen: {self.screen_width}x{self.screen_height}")
                
        except Exception as e:
            self.logger.error(f"❌ Check screen changes error: {e}")
    
    def get_screen_info(self):
        """📊 Get complete screen information"""
        screen_info = self.detect_screen_type()
        
        return {
            "screen_size": f"{self.screen_width}x{self.screen_height}",
            "available_size": f"{self.available_width}x{self.available_height}",
            "optimal_size": f"{self.optimal_width}x{self.optimal_height}",
            "screen_type": screen_info["type"],
            "scale_factor": screen_info["scale_factor"],
            "dpi": self.logical_dpi,
            "device_pixel_ratio": self.device_pixel_ratio,
            "multiple_screens": len(self.all_screens) > 1,
            "screen_count": len(self.all_screens)
        }
    
    def force_window_adjustment(self, window):
        """⚡ Force immediate window adjustment"""
        try:
            # Recalculate optimal size
            self.calculate_optimal_size()
            
            # Apply adjustment
            success = self.auto_adjust_window(window)
            
            if success:
                self.logger.info("⚡ Force window adjustment completed")
            else:
                self.logger.error("❌ Force window adjustment failed")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Force window adjustment error: {e}")
            return False
    
    def start_monitoring(self):
        """🔍 Start screen monitoring"""
        if not self.detection_timer.isActive():
            self.detection_timer.start(2000)
            self.logger.info("🔍 Screen monitoring started")
    
    def stop_monitoring(self):
        """🛑 Stop screen monitoring"""
        if self.detection_timer.isActive():
            self.detection_timer.stop()
            self.logger.info("🛑 Screen monitoring stopped")
    
    def is_monitoring(self):
        """❓ Check if monitoring is active"""
        return self.detection_timer.isActive()

class AutoScreenManager:
    """
    🎛️ AUTO SCREEN MANAGER
    📐 Manages automatic screen detection and window adjustment
    """
    
    def __init__(self):
        self.logger = logging.getLogger("AutoScreenManager")
        self.detector = None
        self.managed_windows = []
        
    def initialize(self):
        """🚀 Initialize auto screen manager"""
        try:
            self.detector = AutoScreenDetector()
            
            # Connect signals
            self.detector.screen_detected.connect(self.on_screen_detected)
            self.detector.window_adjusted.connect(self.on_window_adjusted)
            
            self.logger.info("🚀 Auto Screen Manager initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Auto Screen Manager initialization error: {e}")
            return False
    
    def register_window(self, window):
        """📝 Register window for auto management"""
        try:
            if window not in self.managed_windows:
                self.managed_windows.append(window)
                
                # Immediately adjust the window
                if self.detector:
                    self.detector.auto_adjust_window(window)
                
                self.logger.info(f"📝 Window registered for auto management")
            
        except Exception as e:
            self.logger.error(f"❌ Register window error: {e}")
    
    def on_screen_detected(self, screen_info):
        """📺 Handle screen detection"""
        self.logger.info(f"📺 Screen detected: {screen_info['type']}")
        
        # Auto-adjust all managed windows
        for window in self.managed_windows:
            if window and hasattr(window, 'resize'):
                self.detector.auto_adjust_window(window)
    
    def on_window_adjusted(self, width, height):
        """📐 Handle window adjustment"""
        self.logger.info(f"📐 Window adjusted to: {width}x{height}")
    
    def get_detector(self):
        """🔍 Get detector instance"""
        return self.detector
