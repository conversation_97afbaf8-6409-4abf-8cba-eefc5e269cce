"""
VIP BIG BANG Enterprise - Signal Manager
Advanced signal processing and filtering for trading decisions
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional
import logging
from datetime import datetime, timedelta
import threading
from collections import deque

class SignalManager:
    """
    Enterprise-level signal management system
    Combines, filters, and validates trading signals
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("SignalManager")
        
        # Signal processing parameters
        self.min_confidence = settings.signal_processing.min_confidence
        self.confirmation_required = settings.signal_processing.confirmation_required
        self.signal_timeout = settings.signal_processing.signal_timeout
        
        # Signal history and tracking
        self.signal_history = deque(maxlen=1000)
        self.active_signals = {}
        self.confirmed_signals = deque(maxlen=100)
        
        # Performance tracking
        self.signal_stats = {
            'total_signals': 0,
            'confirmed_signals': 0,
            'successful_signals': 0,
            'failed_signals': 0
        }
        
        # Thread safety
        self.lock = threading.Lock()
        
        self.logger.info("Signal Manager initialized")
    
    def set_analysis_engine(self, analysis_engine):
        """Set reference to analysis engine"""
        self.analysis_engine = analysis_engine
    
    def validate_signal_quality(self, signals: Dict) -> Dict:
        """Validate the quality of incoming signals"""
        if not signals or 'signals' not in signals:
            return {'valid': False, 'reason': 'No signals provided'}
        
        signal_data = signals['signals']
        
        # Check if we have ALL required signals for 8-signal confirmation
        required_signals = [
            'momentum', 'vortex', 'trap_candle', 'strong_level',
            'buyer_seller_power', 'moving_average', 'rsi', 'macd'
        ]
        available_signals = [name for name in required_signals if name in signal_data]

        if len(available_signals) < 8:
            missing_signals = [s for s in required_signals if s not in signal_data]
            return {
                'valid': False,
                'reason': f'Missing signals: {missing_signals}. Need all 8 signals for confirmation.'
            }
        
        # Check signal confidence levels - ALL 8 signals must meet threshold
        valid_signals = []
        invalid_signals = []

        for signal_name in required_signals:
            if signal_name in signal_data:
                signal_info = signal_data[signal_name]
                if isinstance(signal_info, dict) and 'confidence' in signal_info:
                    confidence = signal_info['confidence']
                    direction = signal_info.get('direction', 'NEUTRAL')

                    if confidence >= self.min_confidence and direction != 'NEUTRAL':
                        valid_signals.append({
                            'name': signal_name,
                            'confidence': confidence,
                            'direction': direction
                        })
                    else:
                        invalid_signals.append({
                            'name': signal_name,
                            'confidence': confidence,
                            'direction': direction,
                            'reason': f'Low confidence ({confidence:.2f}) or neutral direction'
                        })
                else:
                    invalid_signals.append({
                        'name': signal_name,
                        'reason': 'Missing confidence or invalid format'
                    })

        # ALL 8 signals must be valid for 8-signal confirmation
        if len(valid_signals) < 8:
            return {
                'valid': False,
                'reason': f'Only {len(valid_signals)}/8 signals meet criteria. Need ALL 8 signals to confirm.',
                'valid_signals': valid_signals,
                'invalid_signals': invalid_signals
            }

        # Check signal direction consensus - all must agree
        directions = [s['direction'] for s in valid_signals]
        unique_directions = set(directions)

        if len(unique_directions) > 1:
            direction_count = {}
            for d in directions:
                direction_count[d] = direction_count.get(d, 0) + 1
            return {
                'valid': False,
                'reason': f'Signal direction conflict. All 8 signals must agree. Current: {direction_count}',
                'valid_signals': valid_signals
            }

        # Calculate average confidence
        total_confidence = sum(s['confidence'] for s in valid_signals)
        average_confidence = total_confidence / len(valid_signals)

        return {
            'valid': True,
            'reason': f'All 8 signals confirmed with {directions[0]} direction',
            'average_confidence': average_confidence,
            'signal_count': len(valid_signals),
            'consensus_direction': directions[0],
            'valid_signals': valid_signals,
            'all_signals_confirmed': True
        }
    
    def calculate_signal_agreement(self, signals: Dict) -> Dict:
        """Calculate agreement between different signal sources"""
        if not signals or 'signals' not in signals:
            return {'agreement': 0.0, 'consensus': 'NONE'}
        
        signal_data = signals['signals']
        directions = []
        scores = []
        confidences = []
        
        for name, signal in signal_data.items():
            if 'direction' in signal and 'score' in signal:
                directions.append(signal['direction'])
                scores.append(signal['score'])
                confidences.append(signal.get('confidence', 0))
        
        if not directions:
            return {'agreement': 0.0, 'consensus': 'NONE'}
        
        # Count direction votes
        call_votes = directions.count('CALL')
        put_votes = directions.count('PUT')
        neutral_votes = directions.count('NEUTRAL')
        total_votes = len(directions)
        
        # Calculate consensus - For 8-signal confirmation, need unanimous agreement
        consensus = 'NONE'
        agreement = 0.0

        # For 8-signal system, we need at least 7/8 agreement (87.5%)
        min_required_votes = 7

        if call_votes >= min_required_votes and call_votes > put_votes:
            consensus = 'CALL'
            agreement = call_votes / total_votes
        elif put_votes >= min_required_votes and put_votes > call_votes:
            consensus = 'PUT'
            agreement = put_votes / total_votes
        else:
            consensus = 'NEUTRAL'
            agreement = max(call_votes, put_votes, neutral_votes) / total_votes
        
        # Weight agreement by confidence
        weighted_agreement = agreement * np.mean(confidences) if confidences else agreement
        
        return {
            'agreement': weighted_agreement,
            'consensus': consensus,
            'vote_distribution': {
                'CALL': call_votes,
                'PUT': put_votes,
                'NEUTRAL': neutral_votes
            },
            'average_confidence': np.mean(confidences) if confidences else 0
        }
    
    def apply_signal_filters(self, signals: Dict) -> Dict:
        """Apply various filters to improve signal quality"""
        filtered_signals = signals.copy()
        
        # Filter 1: Minimum score threshold
        if filtered_signals.get('overall_score', 0) < 0.3:
            filtered_signals['filtered'] = True
            filtered_signals['filter_reason'] = 'Score below minimum threshold'
            return filtered_signals
        
        # Filter 2: Confidence threshold
        if filtered_signals.get('confidence', 0) < self.min_confidence:
            filtered_signals['filtered'] = True
            filtered_signals['filter_reason'] = 'Confidence below threshold'
            return filtered_signals
        
        # Filter 3: Signal agreement - For 8-signal system, need 87.5% agreement (7/8)
        agreement = self.calculate_signal_agreement(signals)
        if agreement['agreement'] < 0.875:  # 7/8 = 87.5%
            filtered_signals['filtered'] = True
            filtered_signals['filter_reason'] = f'Insufficient signal agreement: {agreement["agreement"]:.1%} (need 87.5%)'
            return filtered_signals
        
        # Filter 4: Recent signal frequency (avoid overtrading)
        recent_signals = [s for s in self.signal_history 
                         if (datetime.now() - s['timestamp']).total_seconds() < 300]  # Last 5 minutes
        
        if len(recent_signals) > 5:
            filtered_signals['filtered'] = True
            filtered_signals['filter_reason'] = 'Too many recent signals'
            return filtered_signals
        
        # Filter 5: Market volatility check
        if hasattr(self, 'analysis_engine') and self.analysis_engine.current_data:
            # Add volatility-based filtering logic here
            pass
        
        filtered_signals['filtered'] = False
        filtered_signals['filter_reason'] = 'Passed all filters'
        
        return filtered_signals
    
    def confirm_signal(self, signal: Dict) -> bool:
        """Check if signal meets confirmation requirements"""
        signal_id = signal.get('id', str(datetime.now().timestamp()))
        
        # Check if this signal type has been confirmed recently
        if signal_id in self.active_signals:
            self.active_signals[signal_id]['confirmations'] += 1
            
            if self.active_signals[signal_id]['confirmations'] >= self.confirmation_required:
                # Signal confirmed
                confirmed_signal = self.active_signals[signal_id].copy()
                confirmed_signal['confirmed_at'] = datetime.now()
                
                with self.lock:
                    self.confirmed_signals.append(confirmed_signal)
                    del self.active_signals[signal_id]
                
                self.logger.info(f"Signal {signal_id} confirmed after {confirmed_signal['confirmations']} confirmations")
                return True
        else:
            # New signal - add to active tracking
            self.active_signals[signal_id] = {
                **signal,
                'id': signal_id,
                'first_seen': datetime.now(),
                'confirmations': 1
            }
        
        return False
    
    def cleanup_expired_signals(self):
        """Remove expired signals from active tracking"""
        current_time = datetime.now()
        expired_signals = []
        
        for signal_id, signal in self.active_signals.items():
            if (current_time - signal['first_seen']).total_seconds() > self.signal_timeout:
                expired_signals.append(signal_id)
        
        for signal_id in expired_signals:
            del self.active_signals[signal_id]
            self.logger.debug(f"Signal {signal_id} expired")
    
    def process_signals(self, raw_signals: Dict) -> Dict:
        """
        Main signal processing function
        Returns processed and validated signals ready for trading
        """
        try:
            with self.lock:
                self.signal_stats['total_signals'] += 1
            
            # Validate signal quality
            quality_check = self.validate_signal_quality(raw_signals)
            if not quality_check['valid']:
                self.logger.debug(f"Signal rejected: {quality_check['reason']}")
                return {
                    'valid': False,
                    'reason': quality_check['reason'],
                    'timestamp': datetime.now().isoformat()
                }
            
            # Apply filters
            filtered_signals = self.apply_signal_filters(raw_signals)
            if filtered_signals.get('filtered', False):
                self.logger.debug(f"Signal filtered: {filtered_signals['filter_reason']}")
                return {
                    'valid': False,
                    'reason': filtered_signals['filter_reason'],
                    'timestamp': datetime.now().isoformat()
                }
            
            # Calculate signal agreement
            agreement = self.calculate_signal_agreement(raw_signals)
            
            # Create processed signal
            processed_signal = {
                'id': f"signal_{int(datetime.now().timestamp())}",
                'timestamp': datetime.now().isoformat(),
                'direction': agreement['consensus'],
                'score': raw_signals.get('overall_score', 0.5),
                'confidence': agreement['average_confidence'],
                'agreement': agreement['agreement'],
                'raw_signals': raw_signals,
                'vote_distribution': agreement['vote_distribution'],
                'valid': True,
                'processing_time': raw_signals.get('processing_time', 0)
            }
            
            # Add to signal history
            with self.lock:
                self.signal_history.append(processed_signal)
            
            # Check for confirmation if required
            if self.confirmation_required > 1:
                is_confirmed = self.confirm_signal(processed_signal)
                processed_signal['confirmed'] = is_confirmed
                
                if is_confirmed:
                    with self.lock:
                        self.signal_stats['confirmed_signals'] += 1
            else:
                processed_signal['confirmed'] = True
                with self.lock:
                    self.signal_stats['confirmed_signals'] += 1
            
            # Cleanup expired signals
            self.cleanup_expired_signals()
            
            self.logger.info(f"Signal processed: {processed_signal['direction']} "
                           f"(Score: {processed_signal['score']:.3f}, "
                           f"Confidence: {processed_signal['confidence']:.3f}, "
                           f"Agreement: {processed_signal['agreement']:.3f})")
            
            return processed_signal
            
        except Exception as e:
            self.logger.error(f"Signal processing failed: {e}")
            return {
                'valid': False,
                'reason': f'Processing error: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_latest_confirmed_signal(self) -> Optional[Dict]:
        """Get the most recent confirmed signal"""
        with self.lock:
            if self.confirmed_signals:
                return self.confirmed_signals[-1]
        return None
    
    def get_signal_statistics(self) -> Dict:
        """Get signal processing statistics"""
        with self.lock:
            stats = self.signal_stats.copy()
            stats['active_signals'] = len(self.active_signals)
            stats['signal_history_size'] = len(self.signal_history)
            stats['confirmed_signals_size'] = len(self.confirmed_signals)
            
            if stats['confirmed_signals'] > 0:
                stats['success_rate'] = stats['successful_signals'] / stats['confirmed_signals']
            else:
                stats['success_rate'] = 0.0
        
        return stats
    
    def update_signal_result(self, signal_id: str, success: bool):
        """Update the result of a signal for performance tracking"""
        with self.lock:
            if success:
                self.signal_stats['successful_signals'] += 1
            else:
                self.signal_stats['failed_signals'] += 1
        
        self.logger.info(f"Signal {signal_id} result: {'SUCCESS' if success else 'FAILURE'}")
