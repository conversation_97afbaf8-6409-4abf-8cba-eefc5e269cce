"""
Professional Header Component for VIP BIG BANG
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from .base_component import BaseComponent, VIPButton, VIPLabel

class VIPHeader(BaseComponent):
    """Professional header component exactly matching the design"""
    
    # Signals
    currency_changed = Signal(str)
    mode_changed = Signal(str)
    buy_clicked = Signal()
    sell_clicked = Signal()
    menu_clicked = Signal()
    
    def __init__(self, parent=None):
        self.current_currency = "BUG/USD"
        self.current_mode = "LIVE"
        super().__init__(parent)
    
    def _setup_component(self):
        """Setup the header component"""
        self.apply_style("vip-panel")
        self.setFixedHeight(60)
        
        # Main horizontal layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(20)
        
        # Left section - Currency pairs
        left_section = self._create_left_section()
        layout.addLayout(left_section)
        
        # Center section - Title
        center_section = self._create_center_section()
        layout.addWidget(center_section)
        
        # Right section - Trading controls
        right_section = self._create_right_section()
        layout.addLayout(right_section)
    
    def _create_left_section(self):
        """Create left section with currency pairs"""
        layout = QHBoxLayout()
        layout.setSpacing(10)
        
        # Currency pairs
        currencies = [
            ("✓ BUG/USD", "BUG/USD", True),
            ("GBP/USD", "GBP/USD", False),
            ("EUR/JPY", "EUR/JPY", False),
            ("LIVE", "LIVE", False)
        ]
        
        self.currency_buttons = {}
        
        for display_text, value, is_active in currencies:
            btn = VIPButton(display_text, button_type="active" if is_active else "default")
            btn.clicked.connect(lambda checked, v=value: self._on_currency_clicked(v))
            self.currency_buttons[value] = btn
            layout.addWidget(btn)
        
        return layout
    
    def _create_center_section(self):
        """Create center section with VIP BIG BANG title"""
        title = VIPLabel("VIP BIG BANG", "title")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            color: white;
            font-size: 18px;
            font-weight: bold;
            background: transparent;
        """)
        return title
    
    def _create_right_section(self):
        """Create right section with trading controls"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # BUY label
        buy_label = VIPLabel("BUY")
        buy_label.setStyleSheet("color: white; font-weight: bold; background: transparent;")
        layout.addWidget(buy_label)
        
        # BUY button
        self.buy_btn = VIPButton("BUY", button_type="buy")
        self.buy_btn.clicked.connect(self.buy_clicked.emit)
        layout.addWidget(self.buy_btn)
        
        # SELL button
        self.sell_btn = VIPButton("SELL", button_type="sell")
        self.sell_btn.clicked.connect(self.sell_clicked.emit)
        layout.addWidget(self.sell_btn)
        
        # Spacer
        layout.addSpacing(20)
        
        # Mode buttons
        modes = ["OTC", "LIVE", "DEMO"]
        self.mode_buttons = {}
        
        for mode in modes:
            btn = VIPButton(mode, button_type="active" if mode == self.current_mode else "default")
            btn.clicked.connect(lambda checked, m=mode: self._on_mode_clicked(m))
            self.mode_buttons[mode] = btn
            layout.addWidget(btn)
        
        # Menu button
        self.menu_btn = VIPButton("≡")
        self.menu_btn.setFixedSize(40, 30)
        self.menu_btn.clicked.connect(self.menu_clicked.emit)
        layout.addWidget(self.menu_btn)
        
        return layout
    
    def _on_currency_clicked(self, currency):
        """Handle currency button click"""
        # Update button states
        for curr, btn in self.currency_buttons.items():
            if curr == currency:
                btn.setProperty("class", "vip-btn-active")
                if currency == "BUG/USD":
                    btn.setText("✓ BUG/USD")
            else:
                btn.setProperty("class", "vip-btn")
                if curr == "BUG/USD":
                    btn.setText("BUG/USD")
            
            # Refresh button style
            btn.style().unpolish(btn)
            btn.style().polish(btn)
        
        self.current_currency = currency
        self.currency_changed.emit(currency)
    
    def _on_mode_clicked(self, mode):
        """Handle mode button click"""
        # Update button states
        for m, btn in self.mode_buttons.items():
            if m == mode:
                btn.setProperty("class", "vip-btn-active")
            else:
                btn.setProperty("class", "vip-btn")
            
            # Refresh button style
            btn.style().unpolish(btn)
            btn.style().polish(btn)
        
        self.current_mode = mode
        self.mode_changed.emit(mode)
    
    def set_currency(self, currency: str):
        """Programmatically set the current currency"""
        if currency in self.currency_buttons:
            self._on_currency_clicked(currency)
    
    def set_mode(self, mode: str):
        """Programmatically set the current mode"""
        if mode in self.mode_buttons:
            self._on_mode_clicked(mode)
    
    def get_current_currency(self) -> str:
        """Get the currently selected currency"""
        return self.current_currency
    
    def get_current_mode(self) -> str:
        """Get the currently selected mode"""
        return self.current_mode
