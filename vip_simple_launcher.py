#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Simple Launcher
💎 Main System + Beautiful UI Only
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")
    except:
        pass

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("Installing PySide6...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *

class VIPSimpleLauncher(QMainWindow):
    """Simple VIP BIG BANG Launcher - Main System + Beautiful UI"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VIP BIG BANG - Simple Launcher")
        self.setFixedSize(450, 300)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup simple launcher UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Modern dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:1 #1a1a2e);
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:1 #8b5cf6);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.2);
                padding: 20px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
                min-height: 50px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                border: 2px solid rgba(255, 255, 255, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:1 #5b21b6);
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
            #title {
                font-size: 22px;
                color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:1 #8b5cf6);
                margin: 15px;
            }
            #subtitle {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin: 10px;
            }
            #exitBtn {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ef4444, stop:1 #dc2626);
                min-height: 35px;
                font-size: 14px;
            }
            #exitBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f87171, stop:1 #ef4444);
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("VIP BIG BANG")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("Professional Trading System")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        layout.addSpacing(20)
        
        # Main launch button
        launch_btn = QPushButton("Launch VIP BIG BANG\n(Main System + Beautiful UI)")
        launch_btn.clicked.connect(self.launch_vip_system)
        layout.addWidget(launch_btn)
        
        layout.addStretch()
        
        # Exit button
        exit_btn = QPushButton("Exit")
        exit_btn.setObjectName("exitBtn")
        exit_btn.clicked.connect(self.close)
        layout.addWidget(exit_btn)
        
    def launch_vip_system(self):
        """Launch VIP BIG BANG Main System + Beautiful UI"""
        print("=" * 50)
        print("Launching VIP BIG BANG System...")
        print("=" * 50)
        
        try:
            # Close launcher
            self.close()
            
            # Launch main system first
            print("1. Starting Main System...")
            main_process = subprocess.Popen([sys.executable, "vip_real_quotex_main.py"])
            
            # Wait a moment for main system to initialize
            time.sleep(3)
            
            # Launch beautiful UI
            print("2. Starting Beautiful UI...")
            ui_process = subprocess.Popen([sys.executable, "vip_beautiful_main_ui.py"])
            
            print("✅ VIP BIG BANG System launched successfully!")
            print("✅ Main System: Running")
            print("✅ Beautiful UI: Running")
            print("=" * 50)
            
        except Exception as e:
            print(f"❌ Error launching system: {e}")

def main():
    """Main entry point"""
    print("=" * 50)
    print("VIP BIG BANG - Simple Launcher")
    print("Main System + Beautiful UI")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    launcher = VIPSimpleLauncher()
    launcher.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
