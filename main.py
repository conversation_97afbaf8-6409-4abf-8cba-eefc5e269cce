#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG Enterprise Trading Robot - Main Entry Point
Ultra-fast 15-second analysis with 5-second trades
Enterprise-level security and optimization
"""

import sys
import os
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")
    except:
        pass

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Main entry point - launches the professional launcher"""
    print("=" * 60)
    print("🚀 VIP BIG BANG Enterprise Trading Robot")
    print("Ultra-fast 15s analysis | 5s trades")
    print("Enterprise Security & Optimization")
    print("=" * 60)

    try:
        # Import and run the professional launcher
        from vip_big_bang_launcher import main as launcher_main
        launcher_main()

    except ImportError as e:
        print(f"❌ Failed to import launcher: {e}")
        print("🔧 Trying alternative launch method...")

        # Fallback to direct system launch
        try:
            from vip_real_quotex_main import main as quotex_main
            quotex_main()
        except ImportError:
            print("❌ All launch methods failed")
            print("📋 Please check your installation:")
            print("   1. Run: pip install -r requirements.txt")
            print("   2. Ensure all core modules are present")
            print("   3. Check Python version (3.8+ required)")
            sys.exit(1)

    except Exception as e:
        print(f"❌ Critical error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
