"""
VIP BIG BANG Enterprise - Encryption Manager
Advanced encryption utilities for data protection
"""

import base64
import hashlib
import hmac
import os
import secrets
from typing import Dict, Op<PERSON>, Tuple, Union
import logging
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import json

class EncryptionManager:
    """
    Enterprise-level encryption manager
    Provides symmetric and asymmetric encryption capabilities
    """
    
    def __init__(self):
        self.logger = logging.getLogger("EncryptionManager")
        
        # Encryption keys
        self.master_key = None
        self.session_keys = {}
        
        # RSA key pair for asymmetric encryption
        self.private_key = None
        self.public_key = None
        
        # Initialize encryption
        self._initialize_encryption()
        
        self.logger.info("Encryption Manager initialized")
    
    def _initialize_encryption(self):
        """Initialize encryption components"""
        try:
            # Generate RSA key pair
            self.private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048
            )
            self.public_key = self.private_key.public_key()
            
            self.logger.debug("RSA key pair generated")
            
        except Exception as e:
            self.logger.error(f"Encryption initialization failed: {e}")
    
    def generate_key(self, password: str = None, salt: bytes = None) -> bytes:
        """Generate encryption key from password or random"""
        if password:
            # Generate key from password using PBKDF2
            if salt is None:
                salt = os.urandom(16)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            return key
        else:
            # Generate random key
            return Fernet.generate_key()
    
    def encrypt_symmetric(self, data: Union[str, bytes], key: bytes = None) -> Dict[str, str]:
        """Encrypt data using symmetric encryption (Fernet)"""
        try:
            if key is None:
                key = self.master_key or self.generate_key()
            
            # Convert string to bytes if necessary
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Create Fernet instance
            fernet = Fernet(key)
            
            # Encrypt data
            encrypted_data = fernet.encrypt(data)
            
            return {
                'encrypted_data': base64.urlsafe_b64encode(encrypted_data).decode('utf-8'),
                'key': base64.urlsafe_b64encode(key).decode('utf-8'),
                'algorithm': 'Fernet'
            }
            
        except Exception as e:
            self.logger.error(f"Symmetric encryption failed: {e}")
            raise
    
    def decrypt_symmetric(self, encrypted_data: str, key: str) -> str:
        """Decrypt data using symmetric encryption"""
        try:
            # Decode from base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            key_bytes = base64.urlsafe_b64decode(key.encode('utf-8'))
            
            # Create Fernet instance
            fernet = Fernet(key_bytes)
            
            # Decrypt data
            decrypted_data = fernet.decrypt(encrypted_bytes)
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Symmetric decryption failed: {e}")
            raise
    
    def encrypt_asymmetric(self, data: Union[str, bytes], public_key=None) -> str:
        """Encrypt data using asymmetric encryption (RSA)"""
        try:
            if public_key is None:
                public_key = self.public_key
            
            # Convert string to bytes if necessary
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # RSA can only encrypt small amounts of data
            # For larger data, use hybrid encryption
            if len(data) > 190:  # RSA 2048-bit can encrypt max ~245 bytes
                return self._hybrid_encrypt(data, public_key)
            
            # Encrypt with RSA
            encrypted_data = public_key.encrypt(
                data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Asymmetric encryption failed: {e}")
            raise
    
    def decrypt_asymmetric(self, encrypted_data: str, private_key=None) -> str:
        """Decrypt data using asymmetric encryption"""
        try:
            if private_key is None:
                private_key = self.private_key
            
            # Check if it's hybrid encryption
            if encrypted_data.startswith('HYBRID:'):
                return self._hybrid_decrypt(encrypted_data, private_key)
            
            # Decode from base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            
            # Decrypt with RSA
            decrypted_data = private_key.decrypt(
                encrypted_bytes,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Asymmetric decryption failed: {e}")
            raise
    
    def _hybrid_encrypt(self, data: bytes, public_key) -> str:
        """Hybrid encryption for large data (RSA + AES)"""
        try:
            # Generate random AES key
            aes_key = os.urandom(32)  # 256-bit key
            iv = os.urandom(16)       # 128-bit IV
            
            # Encrypt data with AES
            cipher = Cipher(algorithms.AES(aes_key), modes.CBC(iv))
            encryptor = cipher.encryptor()
            
            # Pad data to multiple of 16 bytes
            padded_data = self._pad_data(data)
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            
            # Encrypt AES key with RSA
            encrypted_key = public_key.encrypt(
                aes_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Combine encrypted key, IV, and encrypted data
            hybrid_data = {
                'encrypted_key': base64.urlsafe_b64encode(encrypted_key).decode('utf-8'),
                'iv': base64.urlsafe_b64encode(iv).decode('utf-8'),
                'encrypted_data': base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            }
            
            hybrid_json = json.dumps(hybrid_data)
            return 'HYBRID:' + base64.urlsafe_b64encode(hybrid_json.encode()).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Hybrid encryption failed: {e}")
            raise
    
    def _hybrid_decrypt(self, encrypted_data: str, private_key) -> str:
        """Hybrid decryption for large data"""
        try:
            # Remove HYBRID prefix and decode
            hybrid_b64 = encrypted_data[7:]  # Remove 'HYBRID:'
            hybrid_json = base64.urlsafe_b64decode(hybrid_b64.encode()).decode('utf-8')
            hybrid_data = json.loads(hybrid_json)
            
            # Decode components
            encrypted_key = base64.urlsafe_b64decode(hybrid_data['encrypted_key'].encode())
            iv = base64.urlsafe_b64decode(hybrid_data['iv'].encode())
            encrypted_content = base64.urlsafe_b64decode(hybrid_data['encrypted_data'].encode())
            
            # Decrypt AES key with RSA
            aes_key = private_key.decrypt(
                encrypted_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Decrypt data with AES
            cipher = Cipher(algorithms.AES(aes_key), modes.CBC(iv))
            decryptor = cipher.decryptor()
            padded_data = decryptor.update(encrypted_content) + decryptor.finalize()
            
            # Remove padding
            data = self._unpad_data(padded_data)
            
            return data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Hybrid decryption failed: {e}")
            raise
    
    def _pad_data(self, data: bytes) -> bytes:
        """Add PKCS7 padding to data"""
        padding_length = 16 - (len(data) % 16)
        padding = bytes([padding_length] * padding_length)
        return data + padding
    
    def _unpad_data(self, padded_data: bytes) -> bytes:
        """Remove PKCS7 padding from data"""
        padding_length = padded_data[-1]
        return padded_data[:-padding_length]
    
    def hash_data(self, data: Union[str, bytes], algorithm: str = 'sha256') -> str:
        """Hash data using specified algorithm"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            if algorithm.lower() == 'sha256':
                hash_obj = hashlib.sha256(data)
            elif algorithm.lower() == 'sha512':
                hash_obj = hashlib.sha512(data)
            elif algorithm.lower() == 'md5':
                hash_obj = hashlib.md5(data)
            else:
                raise ValueError(f"Unsupported hash algorithm: {algorithm}")
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"Hashing failed: {e}")
            raise
    
    def create_hmac(self, data: Union[str, bytes], key: Union[str, bytes], 
                   algorithm: str = 'sha256') -> str:
        """Create HMAC for data integrity verification"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            if isinstance(key, str):
                key = key.encode('utf-8')
            
            if algorithm.lower() == 'sha256':
                hmac_obj = hmac.new(key, data, hashlib.sha256)
            elif algorithm.lower() == 'sha512':
                hmac_obj = hmac.new(key, data, hashlib.sha512)
            else:
                raise ValueError(f"Unsupported HMAC algorithm: {algorithm}")
            
            return hmac_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"HMAC creation failed: {e}")
            raise
    
    def verify_hmac(self, data: Union[str, bytes], key: Union[str, bytes], 
                   expected_hmac: str, algorithm: str = 'sha256') -> bool:
        """Verify HMAC for data integrity"""
        try:
            calculated_hmac = self.create_hmac(data, key, algorithm)
            return hmac.compare_digest(calculated_hmac, expected_hmac)
            
        except Exception as e:
            self.logger.error(f"HMAC verification failed: {e}")
            return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure random token"""
        try:
            token_bytes = secrets.token_bytes(length)
            return base64.urlsafe_b64encode(token_bytes).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Token generation failed: {e}")
            raise
    
    def encrypt_config(self, config_data: Dict, password: str) -> str:
        """Encrypt configuration data"""
        try:
            # Convert config to JSON
            config_json = json.dumps(config_data, indent=2)
            
            # Generate salt
            salt = os.urandom(16)
            
            # Generate key from password
            key = self.generate_key(password, salt)
            
            # Encrypt config
            result = self.encrypt_symmetric(config_json, key)
            
            # Add salt to result
            result['salt'] = base64.urlsafe_b64encode(salt).decode('utf-8')
            
            return json.dumps(result)
            
        except Exception as e:
            self.logger.error(f"Config encryption failed: {e}")
            raise
    
    def decrypt_config(self, encrypted_config: str, password: str) -> Dict:
        """Decrypt configuration data"""
        try:
            # Parse encrypted config
            config_data = json.loads(encrypted_config)
            
            # Extract components
            salt = base64.urlsafe_b64decode(config_data['salt'].encode())
            encrypted_data = config_data['encrypted_data']
            
            # Generate key from password and salt
            key = self.generate_key(password, salt)
            key_str = base64.urlsafe_b64encode(key).decode('utf-8')
            
            # Decrypt config
            decrypted_json = self.decrypt_symmetric(encrypted_data, key_str)
            
            return json.loads(decrypted_json)
            
        except Exception as e:
            self.logger.error(f"Config decryption failed: {e}")
            raise
    
    def export_public_key(self) -> str:
        """Export public key in PEM format"""
        try:
            pem = self.public_key.public_key_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            return pem.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Public key export failed: {e}")
            raise
    
    def import_public_key(self, pem_data: str):
        """Import public key from PEM format"""
        try:
            public_key = serialization.load_pem_public_key(pem_data.encode('utf-8'))
            return public_key
            
        except Exception as e:
            self.logger.error(f"Public key import failed: {e}")
            raise
    
    def secure_delete(self, data: Union[str, bytes]) -> None:
        """Securely delete sensitive data from memory"""
        try:
            if isinstance(data, str):
                # Overwrite string data (limited effectiveness in Python)
                data = 'X' * len(data)
            elif isinstance(data, bytes):
                # Overwrite bytes data
                for i in range(len(data)):
                    data[i] = 0
            
            # Force garbage collection
            import gc
            gc.collect()
            
        except Exception as e:
            self.logger.error(f"Secure delete failed: {e}")

# Example usage and testing
if __name__ == "__main__":
    # Test the encryption manager
    encryption = EncryptionManager()
    
    # Test symmetric encryption
    test_data = "This is sensitive trading data that needs protection"
    encrypted = encryption.encrypt_symmetric(test_data)
    decrypted = encryption.decrypt_symmetric(encrypted['encrypted_data'], encrypted['key'])
    
    print(f"Original: {test_data}")
    print(f"Decrypted: {decrypted}")
    print(f"Match: {test_data == decrypted}")
    
    # Test asymmetric encryption
    encrypted_asym = encryption.encrypt_asymmetric(test_data)
    decrypted_asym = encryption.decrypt_asymmetric(encrypted_asym)
    
    print(f"Asymmetric match: {test_data == decrypted_asym}")
    
    # Test hashing
    hash_value = encryption.hash_data(test_data)
    print(f"Hash: {hash_value}")
    
    # Test HMAC
    hmac_key = "secret_key"
    hmac_value = encryption.create_hmac(test_data, hmac_key)
    hmac_valid = encryption.verify_hmac(test_data, hmac_key, hmac_value)
    
    print(f"HMAC: {hmac_value}")
    print(f"HMAC Valid: {hmac_valid}")
    
    # Test token generation
    token = encryption.generate_secure_token()
    print(f"Secure Token: {token}")
    
    print("Encryption tests completed successfully!")
