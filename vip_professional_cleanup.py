#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
VIP BIG BANG Professional Project Cleanup Tool
Organizes and optimizes the project structure for maximum efficiency
"""

import os
import shutil
import glob
import json
from pathlib import Path
from datetime import datetime

class VIPProjectCleaner:
    def __init__(self):
        self.root_dir = Path(".")
        self.archive_dir = Path("archive")
        self.essential_files = [
            "main.py",
            "vip_real_quotex_main.py", 
            "vip_auto_extension_quotex.py",
            "requirements.txt",
            "config.json",
            "README.md",
            "install.bat",
            "VIP_BIG_BANG.bat",
            "VIP_BIG_BANG.ps1"
        ]
        self.essential_dirs = [
            "core",
            "ui", 
            "trading",
            "utils",
            "chrome_extension",
            "logs",
            "archive"
        ]
        
    def create_archive_structure(self):
        """Create organized archive structure"""
        archive_dirs = [
            "archive/ui_variations",
            "archive/experiments", 
            "archive/demos",
            "archive/tests",
            "archive/quantum_systems",
            "archive/enterprise_systems",
            "archive/complete_systems",
            "archive/documentation",
            "archive/scripts",
            "archive/temp_files"
        ]
        
        for dir_path in archive_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            print(f"Created: {dir_path}")
            
    def move_files_to_archive(self):
        """Move non-essential files to archive"""
        
        # Files to move to different archive categories
        file_categories = {
            "ui_variations": [
                "vip_professional_ui.py",
                "vip_ultimate_professional_ui.py", 
                "vip_cartoon_ultimate.py",
                "vip_exact_ui.py",
                "vip_fixed_ui.py",
                "vip_perfect_ui.py",
                "vip_working_professional_ui.py",
                "vip_ultimate_complex_ui.py",
                "vip_ultra_professional_ui.py",
                "vip_complete_professional_ui.py",
                "vip_fixed_ui_system.py",
                "vip_ultimate_gaming_dashboard.py",
                "cartoon_gaming_ui.py",
                "gaming_robotic_ui.py",
                "qml_modern_ui.py",
                "vip_cartoon_gaming_ui.py",
                "vip_dashboard_tkinter.py",
                "vip_big_bang_ultimate_dashboard.py"
            ],
            "experiments": [
                "test_*.py",
                "*_test.py", 
                "copilot_*.py",
                "figma_*.py",
                "drag_drop_*.py",
                "console_paste_*.py",
                "clean_css_*.py",
                "css_error_*.py",
                "error_scanner.py",
                "run_*.py",
                "launch_*.py",
                "start_*.py",
                "auto_install_*.py",
                "install_extension*.py",
                "instant_extension*.py"
            ],
            "demos": [
                "*_demo.py",
                "demo_*.py",
                "simple_*.py"
            ],
            "quantum_systems": [
                "quantum_*.py",
                "vip_quantum_*.py",
                "*_quantum_*.py"
            ],
            "enterprise_systems": [
                "vip_ultimate_trading_system.py",
                "vip_global_trading_robot.py",
                "vip_learning_system.py",
                "vip_5_second_trader.py",
                "vip_anti_detection_system.py",
                "ultimate_quotex_access.py"
            ],
            "complete_systems": [
                "vip_complete_*.py",
                "vip_big_bang_complete.py",
                "vip_live_trading_system.py",
                "vip_dynamic_timeframe_system.py",
                "vip_complete_with_all_analyzers.py",
                "vip_*_center.py",
                "vip_*_dashboard.py",
                "vip_*_main.py"
            ],
            "documentation": [
                "*_README.md",
                "*_GUIDE.md",
                "*_REPORT.md",
                "*_SUMMARY.md",
                "*_STATUS.md",
                "*_LIST.md",
                "*.txt",
                "augment_iran_fix.md"
            ],
            "scripts": [
                "run_*.bat",
                "install_*.bat",
                "setup_*.bat",
                "start_*.bat",
                "instant_*.bat",
                "VIP_PERFECT.bat",
                "VIP_ULTIMATE_PROFESSIONAL.bat",
                "*.ps1"
            ],
            "temp_files": [
                "cleanup_project.py",
                "quotex_security_bypass_tool.py",
                "adaptive_decision_system.py",
                "vip_direct_quotex_professional.py",
                "vip_main_page_professional.py",
                "vip_professional_quotex_robot.py",
                "vip_real_quotex_embedded.py",
                "vip_system_launcher.py",
                "vip_trading_controller.py",
                "mypy.ini",
                "py.typed",
                "hardware.id"
            ]
        }
        
        moved_count = 0
        
        for category, patterns in file_categories.items():
            category_dir = self.archive_dir / category
            
            for pattern in patterns:
                if "*" in pattern:
                    # Handle wildcard patterns
                    for file_path in glob.glob(pattern):
                        if (os.path.isfile(file_path) and 
                            os.path.basename(file_path) not in self.essential_files and
                            not any(file_path.startswith(d) for d in self.essential_dirs)):
                            try:
                                dest_path = category_dir / os.path.basename(file_path)
                                if not dest_path.exists():
                                    shutil.move(file_path, dest_path)
                                    moved_count += 1
                                    print(f"Moved {file_path} to {category}/")
                                else:
                                    os.remove(file_path)
                                    print(f"Removed duplicate {file_path}")
                            except Exception as e:
                                print(f"Could not move {file_path}: {e}")
                else:
                    # Handle exact file names
                    if (os.path.isfile(pattern) and 
                        pattern not in self.essential_files):
                        try:
                            dest_path = category_dir / pattern
                            if not dest_path.exists():
                                shutil.move(pattern, dest_path)
                                moved_count += 1
                                print(f"Moved {pattern} to {category}/")
                            else:
                                os.remove(pattern)
                                print(f"Removed duplicate {pattern}")
                        except Exception as e:
                            print(f"Could not move {pattern}: {e}")
        
        return moved_count
    
    def create_clean_structure_report(self):
        """Generate report of clean structure"""
        
        remaining_files = []
        for item in os.listdir("."):
            if os.path.isfile(item) and item.endswith(".py"):
                remaining_files.append(item)
        
        report = {
            "cleanup_timestamp": datetime.now().isoformat(),
            "remaining_python_files": remaining_files,
            "essential_directories": [d for d in self.essential_dirs if os.path.exists(d)],
            "archive_created": os.path.exists("archive"),
            "total_remaining_files": len(remaining_files)
        }
        
        with open("cleanup_report.json", "w") as f:
            json.dump(report, f, indent=2)
            
        return report
    
    def run_cleanup(self):
        """Execute complete cleanup process"""
        print("Starting VIP BIG BANG Professional Cleanup...")
        print("=" * 60)
        
        # Step 1: Create archive structure
        print("\nCreating archive structure...")
        self.create_archive_structure()
        print("Archive structure created")
        
        # Step 2: Move files to archive
        print("\nMoving files to archive...")
        moved_count = self.move_files_to_archive()
        print(f"Moved {moved_count} files to archive")
        
        # Step 3: Generate report
        print("\nGenerating cleanup report...")
        report = self.create_clean_structure_report()
        print("Cleanup report generated")
        
        # Step 4: Show results
        print("\n" + "=" * 60)
        print("CLEANUP COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"Remaining Python files: {report['total_remaining_files']}")
        print(f"Files archived: {moved_count}")
        print(f"Report saved: cleanup_report.json")
        
        print("\nEssential files remaining:")
        for file in report['remaining_python_files']:
            print(f"  {file}")
            
        print("\nEssential directories:")
        for dir_name in report['essential_directories']:
            print(f"  {dir_name}/")
        
        return True

if __name__ == "__main__":
    cleaner = VIPProjectCleaner()
    cleaner.run_cleanup()
