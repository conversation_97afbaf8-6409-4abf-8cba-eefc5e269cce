# 🚀 VIP BIG BANG - Ultimate PowerShell Launcher
# پیشرفته‌ترین سیستم راه‌اندازی VIP BIG BANG با PowerShell

param(
    [string]$Mode = "interactive",
    [switch]$QuickLaunch,
    [switch]$FullTest,
    [switch]$Silent,
    [switch]$ForceUpdate
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "🚀 VIP BIG BANG - Ultimate Launcher"

# Color scheme
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
    Accent = "Blue"
}

function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White",
        [switch]$NoNewline
    )
    
    if ($NoNewline) {
        Write-Host $Text -ForegroundColor $Color -NoNewline
    } else {
        Write-Host $Text -ForegroundColor $Color
    }
}

function Show-Banner {
    Clear-Host
    Write-ColorText "████████████████████████████████████████████████████████████████████" $Colors.Header
    Write-ColorText "██                                                                ██" $Colors.Header
    Write-ColorText "██    🚀 VIP BIG BANG - ULTIMATE POWERSHELL LAUNCHER 🚀          ██" $Colors.Header
    Write-ColorText "██                                                                ██" $Colors.Header
    Write-ColorText "██    ⚡ Quantum Trading System ⚡                                ██" $Colors.Header
    Write-ColorText "██    🎯 Real Data Extraction 🎯                                 ██" $Colors.Header
    Write-ColorText "██    🤖 AI-Powered Analysis 🤖                                  ██" $Colors.Header
    Write-ColorText "██    🔥 Professional Grade 🔥                                   ██" $Colors.Header
    Write-ColorText "██                                                                ██" $Colors.Header
    Write-ColorText "████████████████████████████████████████████████████████████████████" $Colors.Header
    Write-Host ""
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Test-PythonInstallation {
    Write-ColorText "🐍 Checking Python installation..." $Colors.Info
    
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Python is installed: $pythonVersion" $Colors.Success
            return $true
        }
    } catch {
        Write-ColorText "❌ Python is not installed or not in PATH" $Colors.Error
        Write-ColorText "💡 Please install Python 3.8+ from https://python.org" $Colors.Warning
        return $false
    }
}

function Test-VirtualEnvironment {
    Write-ColorText "🔧 Checking virtual environment..." $Colors.Info
    
    if (Test-Path "venv\Scripts\activate.ps1") {
        Write-ColorText "✅ Virtual environment found" $Colors.Success
        return $true
    } else {
        Write-ColorText "⚠️  Virtual environment not found" $Colors.Warning
        Write-ColorText "🔧 Creating virtual environment..." $Colors.Info
        
        python -m venv venv
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Virtual environment created" $Colors.Success
            return $true
        } else {
            Write-ColorText "❌ Failed to create virtual environment" $Colors.Error
            return $false
        }
    }
}

function Activate-VirtualEnvironment {
    Write-ColorText "🚀 Activating virtual environment..." $Colors.Info
    
    try {
        & ".\venv\Scripts\Activate.ps1"
        Write-ColorText "✅ Virtual environment activated" $Colors.Success
        return $true
    } catch {
        Write-ColorText "❌ Failed to activate virtual environment" $Colors.Error
        return $false
    }
}

function Test-Dependencies {
    Write-ColorText "📦 Checking dependencies..." $Colors.Info
    
    $dependencies = @("playwright", "pandas", "cryptography", "PySide6")
    $missing = @()
    
    foreach ($dep in $dependencies) {
        try {
            $result = pip show $dep 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "✅ $dep is installed" $Colors.Success
            } else {
                Write-ColorText "❌ $dep is missing" $Colors.Warning
                $missing += $dep
            }
        } catch {
            Write-ColorText "❌ $dep is missing" $Colors.Warning
            $missing += $dep
        }
    }
    
    if ($missing.Count -gt 0) {
        Write-ColorText "🔧 Installing missing dependencies..." $Colors.Info
        foreach ($dep in $missing) {
            Write-ColorText "📦 Installing $dep..." $Colors.Info
            pip install $dep --quiet
        }
        
        # Special handling for Playwright
        if ($missing -contains "playwright") {
            Write-ColorText "🌐 Installing Playwright browsers..." $Colors.Info
            python -m playwright install
        }
    }
    
    # Install all requirements
    Write-ColorText "📋 Installing all requirements..." $Colors.Info
    pip install -r requirements.txt --quiet
    
    return $true
}

function Show-SystemStatus {
    Write-ColorText "`n📋 System Status Report" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    # Administrator status
    if (Test-Administrator) {
        Write-ColorText "🔑 Administrator: ✅ Yes" $Colors.Success
    } else {
        Write-ColorText "🔑 Administrator: ⚠️  No" $Colors.Warning
    }
    
    # Python version
    try {
        $pythonVersion = python --version 2>&1
        Write-ColorText "🐍 Python: ✅ $pythonVersion" $Colors.Success
    } catch {
        Write-ColorText "🐍 Python: ❌ Not found" $Colors.Error
    }
    
    # Virtual environment
    if (Test-Path "venv") {
        Write-ColorText "🔧 Virtual Environment: ✅ Present" $Colors.Success
    } else {
        Write-ColorText "🔧 Virtual Environment: ❌ Missing" $Colors.Error
    }
    
    # Project files
    $projectFiles = @("main.py", "vip_real_quotex_main.py", "requirements.txt")
    foreach ($file in $projectFiles) {
        if (Test-Path $file) {
            Write-ColorText "📁 $file`: ✅ Present" $Colors.Success
        } else {
            Write-ColorText "📁 $file`: ❌ Missing" $Colors.Error
        }
    }
    
    # Chrome extension
    if (Test-Path "chrome_extension") {
        Write-ColorText "🌐 Chrome Extension: ✅ Present" $Colors.Success
    } else {
        Write-ColorText "🌐 Chrome Extension: ❌ Missing" $Colors.Error
    }
    
    # Network connectivity
    try {
        Test-NetConnection -ComputerName "google.com" -Port 80 -InformationLevel Quiet | Out-Null
        if ($?) {
            Write-ColorText "🌐 Internet: ✅ Connected" $Colors.Success
        } else {
            Write-ColorText "🌐 Internet: ❌ Disconnected" $Colors.Error
        }
    } catch {
        Write-ColorText "🌐 Internet: ❌ Unknown" $Colors.Warning
    }
}

function Start-FullSystemTest {
    Write-ColorText "`n🔧 Full System Test" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    Write-ColorText "🧪 Running comprehensive system tests..." $Colors.Info
    
    try {
        python test_real_data_system.py
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ All tests passed!" $Colors.Success
            Write-ColorText "🚀 System is ready for launch" $Colors.Success
            return $true
        } else {
            Write-ColorText "❌ Some tests failed" $Colors.Error
            return $false
        }
    } catch {
        Write-ColorText "❌ Test execution failed" $Colors.Error
        return $false
    }
}

function Start-QuickLaunch {
    Write-ColorText "`n🚀 Quick Launch Mode" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    Write-ColorText "⚡ Starting VIP BIG BANG Quantum System..." $Colors.Info
    
    try {
        python main.py
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ VIP BIG BANG launched successfully" $Colors.Success
        } else {
            Write-ColorText "❌ Launch failed, trying alternative method..." $Colors.Warning
            python vip_real_quotex_main.py
        }
    } catch {
        Write-ColorText "❌ Launch failed" $Colors.Error
        Write-ColorText "🔧 Please check the system status" $Colors.Warning
    }
}

function Show-ChromeExtensionGuide {
    Write-ColorText "`n🌐 Chrome Extension Setup Guide" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    Write-ColorText "📋 Installation Steps:" $Colors.Info
    Write-ColorText "1. Open Chrome browser" $Colors.Accent
    Write-ColorText "2. Go to chrome://extensions/" $Colors.Accent
    Write-ColorText "3. Enable 'Developer mode' (top right)" $Colors.Accent
    Write-ColorText "4. Click 'Load unpacked'" $Colors.Accent
    Write-ColorText "5. Select the 'chrome_extension' folder" $Colors.Accent
    Write-ColorText "6. Extension will be installed" $Colors.Accent
    
    Write-ColorText "`n📂 Opening chrome_extension folder..." $Colors.Info
    Start-Process explorer "chrome_extension"
    
    Write-ColorText "🌐 Opening Chrome Extensions page..." $Colors.Info
    Start-Process chrome "chrome://extensions/"
}

function Show-MainMenu {
    Write-ColorText "`n🎯 Advanced Launch Options" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    Write-Host ""
    Write-ColorText "[1] 🚀 Quick Launch (Recommended)" $Colors.Accent
    Write-ColorText "[2] 🔧 Full System Test" $Colors.Accent
    Write-ColorText "[3] 🌐 Chrome Extension Setup" $Colors.Accent
    Write-ColorText "[4] 🎭 Playwright Test" $Colors.Accent
    Write-ColorText "[5] 📊 Real Data Test" $Colors.Accent
    Write-ColorText "[6] 📋 System Status" $Colors.Accent
    Write-ColorText "[7] 🔄 System Update" $Colors.Accent
    Write-ColorText "[8] 🧹 System Cleanup" $Colors.Accent
    Write-ColorText "[9] 🆘 Emergency Recovery" $Colors.Accent
    Write-ColorText "[0] ❌ Exit" $Colors.Accent
    Write-Host ""
    
    $choice = Read-Host "🎯 Select option (0-9)"
    
    switch ($choice) {
        "1" { Start-QuickLaunch }
        "2" { Start-FullSystemTest; Read-Host "Press Enter to continue" }
        "3" { Show-ChromeExtensionGuide; Read-Host "Press Enter to continue" }
        "4" { Test-PlaywrightInstallation; Read-Host "Press Enter to continue" }
        "5" { Test-RealDataSystem; Read-Host "Press Enter to continue" }
        "6" { Show-SystemStatus; Read-Host "Press Enter to continue" }
        "7" { Update-System; Read-Host "Press Enter to continue" }
        "8" { Clean-System; Read-Host "Press Enter to continue" }
        "9" { Start-EmergencyRecovery; Read-Host "Press Enter to continue" }
        "0" { 
            Write-ColorText "`n👋 Goodbye! VIP BIG BANG Ultimate Launcher closing..." $Colors.Info
            Start-Sleep 2
            exit 0
        }
        default { 
            Write-ColorText "❌ Invalid choice. Please select a valid option." $Colors.Error
            Read-Host "Press Enter to continue"
        }
    }
}

function Test-PlaywrightInstallation {
    Write-ColorText "`n🎭 Playwright Test" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    try {
        $testScript = @"
from playwright.sync_api import sync_playwright
p = sync_playwright().start()
browser = p.chromium.launch()
page = browser.new_page()
page.goto('https://www.google.com')
print('✅ Playwright working - Title:', page.title())
browser.close()
p.stop()
"@
        
        $testScript | python
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Playwright test successful" $Colors.Success
        } else {
            Write-ColorText "❌ Playwright test failed" $Colors.Error
            Write-ColorText "🔧 Reinstalling Playwright..." $Colors.Info
            pip install --upgrade playwright
            python -m playwright install
        }
    } catch {
        Write-ColorText "❌ Playwright test failed" $Colors.Error
    }
}

function Test-RealDataSystem {
    Write-ColorText "`n📊 Real Data System Test" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    try {
        $testScript = @"
from core.professional_quotex_real_extractor import ProfessionalQuotexRealExtractor
extractor = ProfessionalQuotexRealExtractor()
print('✅ Professional Quotex Extractor loaded successfully')
"@
        
        $testScript | python
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Real data system test successful" $Colors.Success
        } else {
            Write-ColorText "❌ Real data system test failed" $Colors.Error
        }
    } catch {
        Write-ColorText "❌ Real data system test failed" $Colors.Error
    }
}

function Update-System {
    Write-ColorText "`n🔄 System Update" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    Write-ColorText "🔧 Updating all dependencies..." $Colors.Info
    pip install --upgrade -r requirements.txt
    python -m playwright install
    Write-ColorText "✅ System updated successfully" $Colors.Success
}

function Clean-System {
    Write-ColorText "`n🧹 System Cleanup" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    # Clean Python cache
    if (Test-Path "__pycache__") {
        Remove-Item -Recurse -Force "__pycache__"
        Write-ColorText "✅ Python cache cleared" $Colors.Success
    }
    
    # Clean log files
    if (Test-Path "logs\*.log") {
        Remove-Item "logs\*.log"
        Write-ColorText "✅ Log files cleared" $Colors.Success
    }
    
    # Clean temporary files
    if (Test-Path "temp_extension") {
        Remove-Item -Recurse -Force "temp_extension"
        Write-ColorText "✅ Temporary files cleared" $Colors.Success
    }
    
    Write-ColorText "✅ System cleanup completed" $Colors.Success
}

function Start-EmergencyRecovery {
    Write-ColorText "`n🆘 Emergency Recovery" $Colors.Header
    Write-ColorText "═══════════════════════════════════════" $Colors.Header
    
    Write-ColorText "🔧 Attempting system recovery..." $Colors.Info
    
    # Recreate virtual environment
    if (Test-Path "venv") {
        Remove-Item -Recurse -Force "venv"
    }
    python -m venv venv
    & ".\venv\Scripts\Activate.ps1"
    
    # Reinstall core dependencies
    pip install playwright pandas cryptography PySide6
    python -m playwright install
    pip install -r requirements.txt
    
    Write-ColorText "✅ Emergency recovery completed" $Colors.Success
}

# Main execution logic
function Main {
    Show-Banner
    
    # Handle command line parameters
    if ($QuickLaunch) {
        if (Test-PythonInstallation -and Test-VirtualEnvironment -and (Activate-VirtualEnvironment) -and (Test-Dependencies)) {
            Start-QuickLaunch
        }
        return
    }
    
    if ($FullTest) {
        if (Test-PythonInstallation -and Test-VirtualEnvironment -and (Activate-VirtualEnvironment) -and (Test-Dependencies)) {
            Start-FullSystemTest
        }
        return
    }
    
    # Interactive mode
    if (-not $Silent) {
        # System diagnostics
        Write-ColorText "🔍 System Diagnostics Starting..." $Colors.Info
        Write-ColorText "═══════════════════════════════════════" $Colors.Info
        
        if (Test-Administrator) {
            Write-ColorText "✅ Administrator privileges detected" $Colors.Success
        } else {
            Write-ColorText "⚠️  Running without administrator privileges" $Colors.Warning
            Write-ColorText "💡 Some features may be limited" $Colors.Warning
        }
        
        # Check all prerequisites
        if (-not (Test-PythonInstallation)) { return }
        if (-not (Test-VirtualEnvironment)) { return }
        if (-not (Activate-VirtualEnvironment)) { return }
        if (-not (Test-Dependencies)) { return }
        
        Write-ColorText "`n✅ All system checks passed!" $Colors.Success
        
        # Show main menu loop
        while ($true) {
            Show-MainMenu
        }
    }
}

# Execute main function
Main
