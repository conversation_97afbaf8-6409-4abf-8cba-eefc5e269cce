#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Real-time System Launcher
🔗 Launch complete system with Real-time Quotex connection
💎 Professional Dashboard + Real-time Connector + Chrome Extension
"""

import sys
import os
import asyncio
import logging
import time
import threading
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/vip_realtime_system.log')
    ]
)

logger = logging.getLogger("VIPRealtimeSystem")

class VIPRealtimeSystemLauncher:
    """🚀 VIP BIG BANG Real-time System Launcher"""
    
    def __init__(self):
        self.logger = logging.getLogger("VIPRealtimeSystemLauncher")
        self.start_time = datetime.now()
        
        # System components
        self.dashboard = None
        self.connector = None
        self.integration = None
        
        # Threading
        self.ui_thread = None
        self.connector_thread = None
        self.running = False
        
        # Import components
        self._import_components()
        
        self.logger.info("🚀 VIP BIG BANG Real-time System Launcher initialized")
    
    def _import_components(self):
        """Import system components"""
        try:
            # Try to import Tkinter dashboard (always available)
            self.tkinter_available = True
            
            # Try to import PySide6 components
            try:
                from ui.vip_main_dashboard import VIPMainDashboard
                from core.realtime_quotex_connector import RealtimeQuotexConnector
                from core.dashboard_quotex_integration import DashboardQuotexIntegration, IntegrationSettings
                
                self.VIPMainDashboard = VIPMainDashboard
                self.RealtimeQuotexConnector = RealtimeQuotexConnector
                self.DashboardQuotexIntegration = DashboardQuotexIntegration
                self.IntegrationSettings = IntegrationSettings
                self.pyside6_available = True
                
                self.logger.info("✅ PySide6 components imported successfully")
                
            except ImportError as e:
                self.logger.warning(f"⚠️ PySide6 not available: {e}")
                self.pyside6_available = False
            
            # Import Tkinter dashboard as fallback
            try:
                import tkinter as tk
                self.tkinter_available = True
                self.logger.info("✅ Tkinter available as fallback")
            except ImportError:
                self.logger.error("❌ Tkinter not available")
                self.tkinter_available = False
                
        except Exception as e:
            self.logger.error(f"❌ Component import failed: {e}")
    
    async def launch_system(self, ui_type="auto", demo_mode=True):
        """
        🚀 Launch the complete VIP BIG BANG Real-time system
        
        Args:
            ui_type: "pyside6", "tkinter", or "auto"
            demo_mode: True for demo, False for live trading
        """
        try:
            self.logger.info("🚀 Launching VIP BIG BANG Real-time System...")
            self.logger.info("="*60)
            
            # Determine UI type
            if ui_type == "auto":
                if self.pyside6_available:
                    ui_type = "pyside6"
                elif self.tkinter_available:
                    ui_type = "tkinter"
                else:
                    raise Exception("No UI framework available")
            
            self.logger.info(f"🎮 UI Framework: {ui_type.upper()}")
            self.logger.info(f"🎯 Mode: {'DEMO' if demo_mode else 'LIVE'}")
            
            # Launch based on UI type
            if ui_type == "pyside6" and self.pyside6_available:
                await self._launch_pyside6_system(demo_mode)
            elif ui_type == "tkinter" and self.tkinter_available:
                await self._launch_tkinter_system(demo_mode)
            else:
                raise Exception(f"UI type {ui_type} not available")
                
        except Exception as e:
            self.logger.error(f"❌ System launch failed: {e}")
            raise
    
    async def _launch_pyside6_system(self, demo_mode=True):
        """Launch PySide6 system with full integration"""
        try:
            self.logger.info("🎮 Launching PySide6 Professional System...")
            
            # Create QApplication
            from PySide6.QtWidgets import QApplication
            
            if not QApplication.instance():
                app = QApplication(sys.argv)
            else:
                app = QApplication.instance()
            
            # Create components
            self.logger.info("🔧 Creating system components...")
            
            # 1. Create Dashboard
            self.dashboard = self.VIPMainDashboard()
            self.logger.info("✅ Dashboard created")
            
            # 2. Create Integration
            settings = self.IntegrationSettings(
                auto_connect=True,
                auto_reconnect=True,
                demo_mode=demo_mode,
                price_update_interval=0.5,
                analysis_update_interval=5.0
            )
            
            self.integration = self.DashboardQuotexIntegration(settings)
            self.logger.info("✅ Integration layer created")
            
            # 3. Initialize Integration
            success = await self.integration.initialize(
                dashboard=self.dashboard,
                connector_settings={}
            )
            
            if success:
                self.logger.info("✅ Integration initialized successfully")
                
                # 4. Connect to Quotex
                if demo_mode:
                    self.logger.info("🎮 Connecting in DEMO mode...")
                else:
                    self.logger.info("💰 Connecting in LIVE mode...")
                
                connect_success = await self.integration.connect_to_quotex()
                
                if connect_success:
                    self.logger.info("✅ Connected to Quotex successfully")
                    
                    # 5. Show Dashboard
                    self.dashboard.show()
                    self.logger.info("🎮 Dashboard displayed")
                    
                    # 6. Start monitoring
                    self._start_system_monitoring()
                    
                    # 7. Run application
                    self.logger.info("🚀 System ready! Starting main loop...")
                    self.logger.info("="*60)
                    self._print_system_status()
                    
                    # Run Qt application
                    app.exec()
                    
                else:
                    self.logger.error("❌ Failed to connect to Quotex")
                    
            else:
                self.logger.error("❌ Integration initialization failed")
                
        except Exception as e:
            self.logger.error(f"❌ PySide6 system launch failed: {e}")
            raise
    
    async def _launch_tkinter_system(self, demo_mode=True):
        """Launch Tkinter system as fallback"""
        try:
            self.logger.info("🎮 Launching Tkinter Fallback System...")
            
            # Import and run Tkinter dashboard
            from vip_dashboard_tkinter import VIPDashboard
            
            self.logger.info("✅ Creating Tkinter Dashboard...")
            dashboard = VIPDashboard()
            
            # Set demo mode
            dashboard.is_demo_mode = demo_mode
            # Note: mode_label will be set by dashboard internally
            
            self.logger.info("🚀 System ready! Starting Tkinter interface...")
            self.logger.info("="*60)
            self._print_system_status()
            
            # Run Tkinter application
            dashboard.run()
            
        except Exception as e:
            self.logger.error(f"❌ Tkinter system launch failed: {e}")
            raise
    
    def _start_system_monitoring(self):
        """Start system monitoring in background"""
        try:
            def monitoring_loop():
                while self.running:
                    try:
                        # Get system stats
                        if self.integration:
                            stats = self.integration.get_integration_stats()
                            
                            # Log stats every 60 seconds
                            if int(time.time()) % 60 == 0:
                                self.logger.info(f"📊 System Stats: {stats}")
                        
                        time.sleep(10)  # Check every 10 seconds
                        
                    except Exception as e:
                        self.logger.error(f"❌ Monitoring error: {e}")
                        time.sleep(30)
            
            self.running = True
            monitor_thread = threading.Thread(
                target=monitoring_loop,
                daemon=True,
                name="SystemMonitor"
            )
            monitor_thread.start()
            
            self.logger.info("📊 System monitoring started")
            
        except Exception as e:
            self.logger.error(f"❌ Monitoring start failed: {e}")
    
    def _print_system_status(self):
        """Print current system status"""
        try:
            uptime = (datetime.now() - self.start_time).total_seconds()
            
            print("\n" + "🚀 VIP BIG BANG REAL-TIME SYSTEM STATUS")
            print("="*60)
            print(f"⏱️  System Uptime: {uptime:.1f} seconds")
            print(f"🎮 Dashboard: {'✅ Active' if self.dashboard else '❌ Not Active'}")
            print(f"🔗 Integration: {'✅ Active' if self.integration else '❌ Not Active'}")
            print(f"📡 Real-time Data: {'✅ Streaming' if self.integration and self.integration.is_connected else '❌ Not Connected'}")
            print("="*60)
            print("📊 FEATURES ACTIVE:")
            print("  ✅ Professional Gaming UI")
            print("  ✅ Real-time Price Streaming")
            print("  ✅ 8 Analysis Modules")
            print("  ✅ Auto-Trade System")
            print("  ✅ Chrome Extension Bridge")
            print("  ✅ Multi-Connection Fallback")
            print("  ✅ Performance Monitoring")
            print("  ✅ Error Recovery System")
            print("="*60)
            print("🎯 READY FOR TRADING!")
            print("="*60)
            
        except Exception as e:
            self.logger.error(f"❌ Status print failed: {e}")
    
    async def shutdown(self):
        """Shutdown the system gracefully"""
        try:
            self.logger.info("🛑 Shutting down VIP BIG BANG Real-time System...")
            
            self.running = False
            
            # Shutdown integration
            if self.integration:
                await self.integration.shutdown()
                self.logger.info("✅ Integration shutdown complete")
            
            # Close dashboard
            if self.dashboard:
                self.dashboard.close()
                self.logger.info("✅ Dashboard closed")
            
            self.logger.info("✅ System shutdown complete")
            
        except Exception as e:
            self.logger.error(f"❌ Shutdown error: {e}")


async def main():
    """🚀 Main launcher function"""
    print("🚀 VIP BIG BANG REAL-TIME SYSTEM LAUNCHER")
    print("🔗 Professional Dashboard + Real-time Quotex Connection")
    print("💎 Enterprise-level Trading System")
    print()
    
    launcher = VIPRealtimeSystemLauncher()
    
    try:
        # Launch system
        await launcher.launch_system(
            ui_type="auto",  # Auto-detect best UI
            demo_mode=True   # Start in demo mode
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ Stopping system...")
        await launcher.shutdown()
        
    except Exception as e:
        print(f"❌ Critical error: {e}")
        logger.error(f"Critical error: {e}")
        
        # Try to shutdown gracefully
        try:
            await launcher.shutdown()
        except:
            pass


def run_sync():
    """Synchronous wrapper for async main"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n✅ System stopped by user")
    except Exception as e:
        print(f"❌ System error: {e}")


if __name__ == "__main__":
    print("🎯 Starting VIP BIG BANG Real-time System...")
    print("⚡ Loading components...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        sys.exit(1)
    
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run the system
    run_sync()
