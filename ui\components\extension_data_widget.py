"""
🔌 VIP BIG BANG - Extension Data Display Widget
Real-time data display connected to Chrome Extension
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime
import json

# Add paths
sys.path.append(str(Path(__file__).parent.parent.parent))

class ExtensionDataWidget(QFrame):
    """
    🔌 Real-time Extension Data Display Widget
    
    Features:
    - Live balance updates from Chrome Extension
    - Real-time asset information
    - Connection status indicators
    - Data extraction monitoring
    - Professional gaming-style UI
    """
    
    # Signals
    data_updated = Signal(dict)
    connection_status_changed = Signal(bool)
    
    def __init__(self):
        super().__init__()
        
        # Data storage
        self.current_data = {}
        self.is_connected = False
        self.last_update = None
        self.extraction_count = 0
        
        # Setup widget
        self._setup_widget()
        self._apply_styling()
        
        # Start update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._check_connection_status)
        self.update_timer.start(1000)  # Check every second
        
    def _setup_widget(self):
        """Setup widget layout"""
        self.setObjectName("extensionDataWidget")
        self.setFixedHeight(300)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Title with connection status
        title_layout = QHBoxLayout()
        
        self.title_label = QLabel("🔌 Extension Data")
        self.title_label.setObjectName("extensionTitle")
        title_layout.addWidget(self.title_label)
        
        title_layout.addStretch()
        
        self.connection_status = QLabel("🔴 Offline")
        self.connection_status.setObjectName("connectionStatus")
        title_layout.addWidget(self.connection_status)
        
        layout.addLayout(title_layout)
        
        # Data display sections
        self._create_balance_section(layout)
        self._create_asset_section(layout)
        self._create_extraction_section(layout)
        self._create_status_section(layout)
        
        layout.addStretch()
        
    def _create_balance_section(self, layout):
        """Create balance display section"""
        balance_frame = QFrame()
        balance_frame.setObjectName("dataSection")
        balance_layout = QVBoxLayout(balance_frame)
        
        # Balance title
        balance_title = QLabel("💰 Account Balance")
        balance_title.setObjectName("sectionTitle")
        balance_layout.addWidget(balance_title)
        
        # Balance value
        self.balance_value = QLabel("$0.00")
        self.balance_value.setObjectName("balanceValue")
        self.balance_value.setAlignment(Qt.AlignCenter)
        balance_layout.addWidget(self.balance_value)
        
        layout.addWidget(balance_frame)
        
    def _create_asset_section(self, layout):
        """Create asset information section"""
        asset_frame = QFrame()
        asset_frame.setObjectName("dataSection")
        asset_layout = QVBoxLayout(asset_frame)
        
        # Asset title
        asset_title = QLabel("📊 Current Asset")
        asset_title.setObjectName("sectionTitle")
        asset_layout.addWidget(asset_title)
        
        # Asset value
        self.asset_value = QLabel("No Asset")
        self.asset_value.setObjectName("assetValue")
        self.asset_value.setAlignment(Qt.AlignCenter)
        asset_layout.addWidget(self.asset_value)
        
        # Payout info
        self.payout_value = QLabel("Payout: N/A")
        self.payout_value.setObjectName("payoutValue")
        self.payout_value.setAlignment(Qt.AlignCenter)
        asset_layout.addWidget(self.payout_value)
        
        layout.addWidget(asset_frame)
        
    def _create_extraction_section(self, layout):
        """Create data extraction monitoring section"""
        extraction_frame = QFrame()
        extraction_frame.setObjectName("dataSection")
        extraction_layout = QVBoxLayout(extraction_frame)
        
        # Extraction title
        extraction_title = QLabel("🔍 Data Extraction")
        extraction_title.setObjectName("sectionTitle")
        extraction_layout.addWidget(extraction_title)
        
        # Extraction count
        self.extraction_count_label = QLabel("Count: 0")
        self.extraction_count_label.setObjectName("extractionCount")
        self.extraction_count_label.setAlignment(Qt.AlignCenter)
        extraction_layout.addWidget(self.extraction_count_label)
        
        layout.addWidget(extraction_frame)
        
    def _create_status_section(self, layout):
        """Create status information section"""
        status_frame = QFrame()
        status_frame.setObjectName("dataSection")
        status_layout = QVBoxLayout(status_frame)
        
        # Status title
        status_title = QLabel("📡 Status")
        status_title.setObjectName("sectionTitle")
        status_layout.addWidget(status_title)
        
        # Last update time
        self.last_update_label = QLabel("Last Update: Never")
        self.last_update_label.setObjectName("lastUpdate")
        self.last_update_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.last_update_label)
        
        layout.addWidget(status_frame)
        
    def update_data(self, data: dict):
        """Update widget with new extension data"""
        try:
            self.current_data = data
            self.last_update = datetime.now()
            self.is_connected = True
            
            # Update balance
            if 'balance' in data and data['balance']:
                balance_str = data['balance']
                if balance_str != 'N/A' and '$' in balance_str:
                    self.balance_value.setText(balance_str)
                    self.balance_value.setStyleSheet("color: #10B981; font-weight: bold; font-size: 18px;")
                else:
                    self.balance_value.setText("$0.00")
                    self.balance_value.setStyleSheet("color: #6B7280; font-weight: bold; font-size: 18px;")
            
            # Update asset
            if 'currentAsset' in data and data['currentAsset']:
                asset = data['currentAsset']
                if asset and asset != 'Market' and asset != 'None':
                    self.asset_value.setText(asset)
                    self.asset_value.setStyleSheet("color: #3B82F6; font-weight: bold;")
                else:
                    self.asset_value.setText("No Asset")
                    self.asset_value.setStyleSheet("color: #6B7280;")
            
            # Update payout
            if 'payout' in data and data['payout']:
                payout = data['payout']
                if payout and payout != 'N/A':
                    self.payout_value.setText(f"Payout: {payout}")
                    self.payout_value.setStyleSheet("color: #F59E0B; font-weight: bold;")
                else:
                    self.payout_value.setText("Payout: N/A")
                    self.payout_value.setStyleSheet("color: #6B7280;")
            
            # Update extraction count
            if 'extractionCount' in data:
                count = data['extractionCount']
                self.extraction_count = count
                self.extraction_count_label.setText(f"Count: {count}")
                self.extraction_count_label.setStyleSheet("color: #8B5CF6; font-weight: bold;")
            
            # Update connection status
            self.connection_status.setText("🟢 Online")
            self.connection_status.setStyleSheet("color: #10B981; font-weight: bold;")
            
            # Update last update time
            time_str = self.last_update.strftime("%H:%M:%S")
            self.last_update_label.setText(f"Last Update: {time_str}")
            self.last_update_label.setStyleSheet("color: #10B981;")
            
            # Emit signal
            self.data_updated.emit(data)
            
            print(f"✅ Extension data widget updated: Balance={data.get('balance', 'N/A')}, Asset={data.get('currentAsset', 'N/A')}")
            
        except Exception as e:
            print(f"❌ Error updating extension data widget: {e}")

    def update_from_extension_data(self, data: dict):
        """Update widget from extension data (compatibility method)"""
        self.update_data(data)

    def _check_connection_status(self):
        """Check connection status based on last update time"""
        if self.last_update:
            time_diff = (datetime.now() - self.last_update).total_seconds()
            if time_diff > 10:  # No update for 10 seconds
                self.is_connected = False
                self.connection_status.setText("🔴 Offline")
                self.connection_status.setStyleSheet("color: #EF4444; font-weight: bold;")
                self.connection_status_changed.emit(False)
        else:
            self.is_connected = False
            self.connection_status.setText("🔴 Offline")
            self.connection_status.setStyleSheet("color: #EF4444; font-weight: bold;")
            
    def _apply_styling(self):
        """Apply professional gaming-style CSS"""
        self.setStyleSheet("""
            QFrame#extensionDataWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(17, 24, 39, 0.95),
                    stop:1 rgba(31, 41, 55, 0.95));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 15px;
                margin: 5px;
            }
            
            QLabel#extensionTitle {
                color: #F3F4F6;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
            }
            
            QLabel#connectionStatus {
                font-size: 12px;
                font-weight: bold;
                background: transparent;
            }
            
            QFrame#dataSection {
                background: rgba(55, 65, 81, 0.6);
                border: 1px solid rgba(75, 85, 99, 0.8);
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
            
            QLabel#sectionTitle {
                color: #D1D5DB;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 5px;
            }
            
            QLabel#balanceValue {
                color: #10B981;
                font-size: 18px;
                font-weight: bold;
                background: transparent;
            }
            
            QLabel#assetValue, QLabel#payoutValue {
                color: #3B82F6;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
            
            QLabel#extractionCount {
                color: #8B5CF6;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
            
            QLabel#lastUpdate {
                color: #6B7280;
                font-size: 11px;
                background: transparent;
            }
        """)
