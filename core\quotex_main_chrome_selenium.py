#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Quotex Main Chrome with Selenium
🌐 استفاده از Selenium در کروم اصلی
⚡ خواندن داده‌ها زیر 1 ثانیه
💎 سیستم دو صفحه‌ای حرفه‌ای
"""

import tkinter as tk
from tkinter import messagebox
import time
import threading
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

class QuotexMainChromeSelenium:
    """
    🚀 Quotex Main Chrome with Selenium
    🌐 استفاده از کروم اصلی
    ⚡ خواندن داده‌ها
    💎 سیستم حرفه‌ای
    """

    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.driver = None
        self.is_logged_in = False
        self.is_reading = False
        
        # Login data
        self.email = ""
        self.password = ""
        
        print("🚀 Quotex Main Chrome Selenium initialized")

    def show_login_page(self):
        """🔐 Show Login Page"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Login page container
            self.login_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            self.login_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(self.login_container, bg='#1E88E5', height=120)
            header.pack(fill=tk.X, pady=(0, 50))
            header.pack_propagate(False)

            tk.Label(header, text="🌐 QUOTEX IN MAIN CHROME", 
                    font=("Arial", 28, "bold"), fg="#FFFFFF", bg="#1E88E5").pack(pady=40)

            # Login form
            form_frame = tk.Frame(self.login_container, bg='#1A1A2E', relief=tk.RAISED, bd=5)
            form_frame.pack(padx=200, pady=100)

            tk.Label(form_frame, text="📧 EMAIL", 
                    font=("Arial", 16, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=20)

            self.email_entry = tk.Entry(form_frame, font=("Arial", 16), width=35, 
                                      bg="#FFFFFF", fg="#000000")
            self.email_entry.pack(pady=15)

            tk.Label(form_frame, text="🔒 PASSWORD", 
                    font=("Arial", 16, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=20)

            self.password_entry = tk.Entry(form_frame, font=("Arial", 16), width=35, 
                                         bg="#FFFFFF", fg="#000000", show="*")
            self.password_entry.pack(pady=15)

            # Login button
            self.login_btn = tk.Button(form_frame, text="🚀 CONNECT TO MAIN CHROME", 
                                     font=("Arial", 18, "bold"), bg="#00FF88", fg="#000000",
                                     padx=60, pady=25, command=self.connect_to_main_chrome)
            self.login_btn.pack(pady=40)

            # Status
            self.login_status = tk.Label(form_frame, text="🔴 Ready to connect", 
                                       font=("Arial", 14), fg="#FF4444", bg="#1A1A2E")
            self.login_status.pack(pady=15)

            # Instructions
            instructions = tk.Label(form_frame, 
                                  text="💡 This will connect to your main Chrome browser\n"
                                       "🌐 Quotex will open in a new tab in your existing Chrome",
                                  font=("Arial", 12), fg="#A0AEC0", bg="#1A1A2E", justify=tk.CENTER)
            instructions.pack(pady=20)

            # Load saved credentials
            self.load_credentials()

            return True

        except Exception as e:
            print(f"❌ Login page error: {e}")
            return False

    def connect_to_main_chrome(self):
        """🌐 Connect to Main Chrome"""
        try:
            self.email = self.email_entry.get().strip()
            self.password = self.password_entry.get().strip()

            if not self.email or not self.password:
                messagebox.showwarning("Warning", "Please enter email and password!")
                return

            # Save credentials
            self.save_credentials()

            # Update status
            self.login_status.config(text="🔄 Connecting to main Chrome...", fg="#FFD700")
            self.login_btn.config(state=tk.DISABLED, text="🔄 CONNECTING...")

            def connect_thread():
                try:
                    if self.setup_chrome_connection():
                        # Connection successful - switch to main page
                        self.show_main_page()
                    else:
                        # Connection failed
                        self.login_status.config(text="❌ Connection failed", fg="#FF4444")
                        self.login_btn.config(state=tk.NORMAL, text="🚀 CONNECT TO MAIN CHROME")

                except Exception as e:
                    print(f"❌ Connect thread error: {e}")
                    self.login_status.config(text="❌ Connection error", fg="#FF4444")
                    self.login_btn.config(state=tk.NORMAL, text="🚀 CONNECT TO MAIN CHROME")

            thread = threading.Thread(target=connect_thread, daemon=True)
            thread.start()

        except Exception as e:
            print(f"❌ Connect error: {e}")

    def setup_chrome_connection(self):
        """🔧 Setup Chrome Connection"""
        try:
            print("🔧 Setting up Chrome connection...")

            # Try to connect to existing Chrome instance first
            try:
                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
                
                self.driver = webdriver.Chrome(options=chrome_options)
                print("✅ Connected to existing Chrome instance")
                
            except Exception as e:
                print(f"⚠️ Existing Chrome not found: {e}")
                print("🚀 Starting new Chrome instance...")
                
                # Start new Chrome instance with debugging port
                chrome_options = Options()
                chrome_options.add_argument("--remote-debugging-port=9222")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                self.driver = webdriver.Chrome(options=chrome_options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Navigate to Quotex
            self.driver.get("https://qxbroker.com/en/sign-in")
            
            # Wait for page load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            time.sleep(2)

            # Perform auto login
            if self.perform_auto_login():
                self.is_logged_in = True
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ Chrome connection error: {e}")
            return False

    def perform_auto_login(self):
        """🔐 Perform Auto Login with Better Detection"""
        try:
            print("🔐 Performing auto login...")

            # Wait for page to fully load
            time.sleep(3)

            # Try multiple email field selectors
            email_field = None
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[placeholder*='email']",
                "input[placeholder*='Email']",
                "input[placeholder*='E-mail']",
                "#email",
                ".email",
                "[data-test='email']",
                "input[autocomplete='email']"
            ]

            print("🔍 Looking for email field...")
            for selector in email_selectors:
                try:
                    email_field = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if email_field.is_displayed() and email_field.is_enabled():
                        print(f"✅ Found email field with selector: {selector}")
                        break
                    else:
                        email_field = None
                except:
                    continue

            if not email_field:
                # Try to find any input field that might be email
                try:
                    all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                    for inp in all_inputs:
                        if inp.is_displayed() and inp.is_enabled():
                            input_type = inp.get_attribute("type")
                            placeholder = inp.get_attribute("placeholder") or ""
                            name = inp.get_attribute("name") or ""

                            if (input_type in ["email", "text"] or
                                "email" in placeholder.lower() or
                                "email" in name.lower()):
                                email_field = inp
                                print(f"✅ Found email field by attributes: type={input_type}, placeholder={placeholder}")
                                break
                except:
                    pass

            if email_field:
                try:
                    # Clear and enter email
                    email_field.click()
                    email_field.clear()
                    time.sleep(0.5)
                    email_field.send_keys(self.email)
                    time.sleep(1)
                    print(f"✅ Email entered: {self.email}")
                except Exception as e:
                    print(f"❌ Error entering email: {e}")
                    return False
            else:
                print("❌ Email field not found")
                # Show manual login option
                result = messagebox.askquestion("Manual Login",
                                              "Email field not found automatically.\n\n"
                                              "Please login manually in the browser and then click 'Yes' when done.\n\n"
                                              "Have you completed the login?")
                if result == 'yes':
                    return True
                else:
                    return False

            # Find password field
            password_field = None
            password_selectors = [
                "input[type='password']",
                "input[name='password']",
                "input[placeholder*='password']",
                "input[placeholder*='Password']",
                "#password",
                ".password",
                "[data-test='password']",
                "input[autocomplete='current-password']"
            ]

            print("🔍 Looking for password field...")
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_field.is_displayed() and password_field.is_enabled():
                        print(f"✅ Found password field with selector: {selector}")
                        break
                except:
                    continue

            if password_field:
                try:
                    password_field.click()
                    password_field.clear()
                    time.sleep(0.5)
                    password_field.send_keys(self.password)
                    time.sleep(1)
                    print("✅ Password entered")
                except Exception as e:
                    print(f"❌ Error entering password: {e}")
                    return False
            else:
                print("❌ Password field not found")
                return False

            # Find login button
            login_button = None
            login_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button[class*='login']",
                "button[class*='sign']",
                "button[class*='submit']",
                ".login-btn",
                ".submit-btn",
                ".sign-in-btn",
                "[data-test='login']",
                "button:contains('Login')",
                "button:contains('Sign')"
            ]

            print("🔍 Looking for login button...")
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if login_button.is_displayed() and login_button.is_enabled():
                        print(f"✅ Found login button with selector: {selector}")
                        break
                except:
                    continue

            if not login_button:
                # Try to find any button that might be login
                try:
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for btn in all_buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            btn_text = btn.text.lower()
                            if any(word in btn_text for word in ["login", "sign", "enter", "submit"]):
                                login_button = btn
                                print(f"✅ Found login button by text: {btn.text}")
                                break
                except:
                    pass

            if login_button:
                try:
                    login_button.click()
                    print("✅ Login button clicked")
                    time.sleep(5)

                    # Check if login successful
                    current_url = self.driver.current_url
                    print(f"🔍 Current URL after login: {current_url}")

                    if ("trade" in current_url.lower() or
                        "dashboard" in current_url.lower() or
                        "platform" in current_url.lower() or
                        "trading" in current_url.lower()):
                        print("✅ Login successful!")
                        return True
                    else:
                        print("❌ Login may have failed - checking page content...")

                        # Check for error messages
                        try:
                            error_elements = self.driver.find_elements(By.CSS_SELECTOR,
                                ".error, .alert, [class*='error'], [class*='alert']")
                            for error in error_elements:
                                if error.is_displayed():
                                    print(f"❌ Error found: {error.text}")
                        except:
                            pass

                        # Ask user if login was successful
                        result = messagebox.askquestion("Login Check",
                                                      "Auto-login completed but couldn't verify success.\n\n"
                                                      "Please check the browser - are you logged in?\n\n"
                                                      "Click 'Yes' if logged in successfully.")
                        return result == 'yes'

                except Exception as e:
                    print(f"❌ Error clicking login button: {e}")
                    return False
            else:
                print("❌ Login button not found")
                return False

        except Exception as e:
            print(f"❌ Auto login error: {e}")
            return False

    def show_main_page(self):
        """📊 Show Main Page"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Main page container
            main_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            main_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(main_container, bg='#00C851', height=80)
            header.pack(fill=tk.X, pady=(0, 10))
            header.pack_propagate(False)

            tk.Label(header, text="🌐 VIP BIG BANG - MAIN CHROME CONNECTED", 
                    font=("Arial", 20, "bold"), fg="#FFFFFF", bg="#00C851").pack(pady=25)

            # Create 3-column layout
            content_frame = tk.Frame(main_container, bg='#0A0A0F')
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Left panel (Analysis)
            left_panel = tk.Frame(content_frame, bg='#1A1A2E', width=300, relief=tk.RAISED, bd=2)
            left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
            left_panel.pack_propagate(False)

            tk.Label(left_panel, text="📈 ANALYSIS", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.analysis_text = tk.Text(left_panel, bg="#0D1117", fg="#00FFFF", 
                                       font=("Consolas", 9), wrap=tk.WORD)
            self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Center panel (Quotex Data)
            center_panel = tk.Frame(content_frame, bg='#2D3748', relief=tk.RAISED, bd=3)
            center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

            # Center header
            center_header = tk.Frame(center_panel, bg='#FF6B35', height=60)
            center_header.pack(fill=tk.X, pady=(0, 10))
            center_header.pack_propagate(False)

            tk.Label(center_header, text="📊 QUOTEX LIVE DATA", 
                    font=("Arial", 18, "bold"), fg="#FFFFFF", bg="#FF6B35").pack(pady=15)

            # Control buttons
            control_frame = tk.Frame(center_panel, bg='#2D3748')
            control_frame.pack(fill=tk.X, pady=(0, 10))

            self.start_btn = tk.Button(control_frame, text="▶️ START READING", 
                                     font=("Arial", 12, "bold"), bg="#00FF88", fg="#000000",
                                     padx=20, pady=10, command=self.start_reading_data)
            self.start_btn.pack(side=tk.LEFT, padx=10)

            self.stop_btn = tk.Button(control_frame, text="⏹️ STOP", 
                                    font=("Arial", 12, "bold"), bg="#FF4444", fg="#FFFFFF",
                                    padx=20, pady=10, command=self.stop_reading_data, state=tk.DISABLED)
            self.stop_btn.pack(side=tk.LEFT, padx=10)

            # Quotex data display
            self.quotex_data_text = tk.Text(center_panel, bg="#0D1117", fg="#00FFFF", 
                                          font=("Consolas", 11), wrap=tk.WORD)
            self.quotex_data_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Right panel (Settings)
            right_panel = tk.Frame(content_frame, bg='#1A1A2E', width=300, relief=tk.RAISED, bd=2)
            right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
            right_panel.pack_propagate(False)

            tk.Label(right_panel, text="⚙️ SETTINGS", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.settings_text = tk.Text(right_panel, bg="#0D1117", fg="#00FFFF", 
                                       font=("Consolas", 9), wrap=tk.WORD)
            self.settings_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            # Initial messages
            self.add_quotex_log("🌐 Connected to main Chrome successfully")
            self.add_quotex_log("✅ Quotex loaded in your main browser")
            self.add_quotex_log("📊 Click 'START READING' to begin")

            return True

        except Exception as e:
            print(f"❌ Main page error: {e}")
            return False

    def start_reading_data(self):
        """📊 Start Reading Data"""
        try:
            if not self.is_logged_in or not self.driver:
                messagebox.showwarning("Warning", "Please connect first!")
                return

            self.is_reading = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)

            self.add_quotex_log("📊 Starting live data reading...")

            def reading_thread():
                try:
                    while self.is_reading:
                        start_time = time.time()
                        
                        # Read Quotex data
                        data = self.read_quotex_data()
                        
                        # Calculate read time
                        read_time = time.time() - start_time
                        
                        # Display data
                        if data:
                            self.display_quotex_data(data, read_time)
                        
                        # Wait before next read
                        time.sleep(1)

                except Exception as e:
                    self.add_quotex_log(f"❌ Reading error: {e}")

            thread = threading.Thread(target=reading_thread, daemon=True)
            thread.start()

        except Exception as e:
            self.add_quotex_log(f"❌ Start reading error: {e}")

    def read_quotex_data(self):
        """📈 Read Quotex Data"""
        try:
            # Execute JavaScript to get data
            data = self.driver.execute_script("""
                return {
                    timestamp: new Date().toLocaleTimeString(),
                    balance: document.querySelector('.balance, [class*="balance"]')?.innerText || 'N/A',
                    currentAsset: document.querySelector('.asset-name, [class*="asset"]')?.innerText || 'N/A',
                    currentPrice: document.querySelector('.price, [class*="price"]')?.innerText || 'N/A',
                    currentProfit: document.querySelector('.profit, [class*="profit"]')?.innerText || 'N/A',
                    
                    // Starred assets
                    starredAssets: Array.from(document.querySelectorAll('.starred, [class*="starred"], .favorite')).map(el => ({
                        name: el.querySelector('.name, .symbol')?.innerText || el.innerText,
                        price: el.querySelector('.price')?.innerText || 'N/A',
                        profit: el.querySelector('.profit')?.innerText || 'N/A',
                        isOTC: el.querySelector('.otc') !== null
                    })),
                    
                    // OTC assets
                    otcAssets: Array.from(document.querySelectorAll('.otc, [class*="otc"]')).map(el => {
                        const parent = el.closest('.asset-item') || el.parentElement;
                        return {
                            name: parent.querySelector('.name, .symbol')?.innerText || 'N/A',
                            price: parent.querySelector('.price')?.innerText || 'N/A',
                            profit: parent.querySelector('.profit')?.innerText || 'N/A',
                            isStarred: parent.querySelector('.star, .favorite') !== null
                        };
                    }),
                    
                    marketStatus: 'OPEN',
                    callEnabled: document.querySelector('.call-btn')?.disabled === false,
                    putEnabled: document.querySelector('.put-btn')?.disabled === false,
                    url: window.location.href
                };
            """)
            
            return data

        except Exception as e:
            self.add_quotex_log(f"❌ Data read error: {e}")
            return None

    def display_quotex_data(self, data, read_time):
        """📊 Display Data"""
        try:
            # Clear previous data
            self.quotex_data_text.delete(1.0, tk.END)
            
            # Format data display
            display_text = f"""
{'='*60}
⏰ TIME: {data.get('timestamp', 'N/A')} | ⚡ READ: {read_time:.3f}s
💳 BALANCE: {data.get('balance', 'N/A')}
📊 ASSET: {data.get('currentAsset', 'N/A')} | 💰 PRICE: {data.get('currentPrice', 'N/A')}
💎 PROFIT: {data.get('currentProfit', 'N/A')}
🔴 CALL: {'✅' if data.get('callEnabled') else '❌'} | 🔵 PUT: {'✅' if data.get('putEnabled') else '❌'}

⭐ STARRED ASSETS ({len(data.get('starredAssets', []))}):{self.format_assets(data.get('starredAssets', []))}

🏷️ OTC ASSETS ({len(data.get('otcAssets', []))}):{self.format_otc_assets(data.get('otcAssets', []))}

🌐 BROWSER: Main Chrome Connected
📊 STATUS: Reading Live Data
⚡ SPEED: Under 1 Second
{'='*60}
"""

            self.quotex_data_text.insert(tk.END, display_text)

        except Exception as e:
            self.add_quotex_log(f"❌ Display error: {e}")

    def format_assets(self, assets):
        """⭐ Format Assets"""
        if not assets:
            return "\n   No assets found"
        
        formatted = ""
        for asset in assets[:5]:
            otc = "🏷️" if asset.get('isOTC') else "📊"
            formatted += f"\n   {otc} ⭐ {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"
        
        return formatted

    def format_otc_assets(self, assets):
        """🏷️ Format OTC Assets"""
        if not assets:
            return "\n   No OTC assets found"
        
        formatted = ""
        for asset in assets[:5]:
            star = "⭐" if asset.get('isStarred') else "☆"
            formatted += f"\n   🏷️ {star} {asset.get('name')} | 💰 {asset.get('price')} | 💎 {asset.get('profit')}"
        
        return formatted

    def stop_reading_data(self):
        """⏹️ Stop Reading"""
        try:
            self.is_reading = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.add_quotex_log("⏹️ Data reading stopped")

        except Exception as e:
            self.add_quotex_log(f"❌ Stop error: {e}")

    def add_quotex_log(self, message):
        """📝 Add Log"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.quotex_data_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.quotex_data_text.see(tk.END)
        except:
            pass

    def save_credentials(self):
        """💾 Save Credentials"""
        try:
            credentials = {"email": self.email, "password": self.password}
            with open("quotex_credentials.json", "w") as f:
                json.dump(credentials, f)
        except Exception as e:
            print(f"❌ Save error: {e}")

    def load_credentials(self):
        """📂 Load Credentials"""
        try:
            if os.path.exists("quotex_credentials.json"):
                with open("quotex_credentials.json", "r") as f:
                    credentials = json.load(f)
                self.email_entry.insert(0, credentials.get("email", ""))
                self.password_entry.insert(0, credentials.get("password", ""))
        except Exception as e:
            print(f"❌ Load error: {e}")

# Test function
def test_main_chrome_selenium():
    """🧪 Test Main Chrome Selenium"""
    print("🧪 Testing Quotex Main Chrome Selenium...")
    
    root = tk.Tk()
    root.title("🌐 Quotex Main Chrome Selenium")
    root.geometry("1600x900")
    root.configure(bg='#0A0A0F')
    
    system = QuotexMainChromeSelenium(root)
    system.show_login_page()
    
    root.mainloop()

if __name__ == "__main__":
    test_main_chrome_selenium()
