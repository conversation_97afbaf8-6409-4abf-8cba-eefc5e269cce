#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎨 VIP BIG BANG - Advanced Styles
استایل‌های پیشرفته VIP BIG BANG
"""

# Gaming Theme Colors
GAMING_COLORS = {
    'primary_purple': '#7c3aed',
    'secondary_purple': '#581c87',
    'accent_purple': '#9333ea',
    'neon_green': '#10b981',
    'neon_red': '#ef4444',
    'neon_orange': '#f97316',
    'neon_yellow': '#eab308',
    'neon_blue': '#3b82f6',
    'dark_bg': '#1f2937',
    'card_bg': 'rgba(88, 28, 135, 0.9)',
    'border_glow': 'rgba(147, 51, 234, 0.6)',
    'text_primary': '#ffffff',
    'text_secondary': 'rgba(255, 255, 255, 0.7)',
    'success': '#22c55e',
    'danger': '#dc2626',
    'warning': '#f59e0b',
    'info': '#06b6d4'
}

# Advanced Gradients
GRADIENTS = {
    'main_bg': """
        qlineargradient(x1:0, y1:0, x2:1, y2:1,
            stop:0 #4c1d95, stop:0.3 #5b21b6, 
            stop:0.7 #6d28d9, stop:1 #7c3aed)
    """,
    'card_bg': """
        qlineargradient(x1:0, y1:0, x2:1, y2:1,
            stop:0 rgba(88, 28, 135, 0.9),
            stop:1 rgba(124, 58, 237, 0.8))
    """,
    'card_hover': """
        qlineargradient(x1:0, y1:0, x2:1, y2:1,
            stop:0 rgba(88, 28, 135, 1.0),
            stop:1 rgba(124, 58, 237, 0.9))
    """,
    'buy_gradient': """
        qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #10b981, stop:1 #059669)
    """,
    'sell_gradient': """
        qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #ef4444, stop:1 #dc2626)
    """,
    'glow_effect': """
        qlineargradient(x1:0, y1:0, x2:1, y2:1,
            stop:0 rgba(147, 51, 234, 0.3),
            stop:1 rgba(124, 58, 237, 0.5))
    """
}

# Advanced Animations CSS
ANIMATIONS = """
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    
    @keyframes glow {
        0% { box-shadow: 0 0 5px rgba(147, 51, 234, 0.5); }
        50% { box-shadow: 0 0 20px rgba(147, 51, 234, 0.8); }
        100% { box-shadow: 0 0 5px rgba(147, 51, 234, 0.5); }
    }
    
    @keyframes slideIn {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
"""

# Main Window Style
MAIN_WINDOW_STYLE = f"""
    QMainWindow {{
        background: {GRADIENTS['main_bg']};
        color: {GAMING_COLORS['text_primary']};
        font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
    }}
    
    QWidget {{
        background: transparent;
        color: {GAMING_COLORS['text_primary']};
        font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
    }}
"""

# Modern Card Style
MODERN_CARD_STYLE = f"""
    QFrame {{
        background: {GRADIENTS['card_bg']};
        border: 2px solid {GAMING_COLORS['border_glow']};
        border-radius: 20px;
        padding: 15px;
    }}
    
    QFrame:hover {{
        background: {GRADIENTS['card_hover']};
        border: 2px solid rgba(147, 51, 234, 0.8);
    }}
"""

# Button Styles
BUTTON_STYLES = {
    'primary': f"""
        QPushButton {{
            background: {GRADIENTS['card_bg']};
            color: {GAMING_COLORS['text_primary']};
            border: 2px solid {GAMING_COLORS['border_glow']};
            border-radius: 20px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        QPushButton:hover {{
            background: {GRADIENTS['card_hover']};
            border: 2px solid rgba(147, 51, 234, 0.8);
        }}
        QPushButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(88, 28, 135, 1.0),
                stop:1 rgba(67, 20, 102, 1.0));
        }}
    """,
    
    'buy': f"""
        QPushButton {{
            background: {GRADIENTS['buy_gradient']};
            color: white;
            border: none;
            border-radius: 20px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #34d399, stop:1 #10b981);
        }}
        QPushButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #059669, stop:1 #047857);
        }}
    """,
    
    'sell': f"""
        QPushButton {{
            background: {GRADIENTS['sell_gradient']};
            color: white;
            border: none;
            border-radius: 20px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f87171, stop:1 #ef4444);
        }}
        QPushButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #dc2626, stop:1 #b91c1c);
        }}
    """
}

# Chart Area Style
CHART_STYLE = f"""
    QFrame {{
        background: {GRADIENTS['card_bg']};
        border: 2px solid {GAMING_COLORS['border_glow']};
        border-radius: 20px;
        padding: 20px;
    }}
    
    QLabel {{
        color: {GAMING_COLORS['text_primary']};
        background: transparent;
    }}
"""

# Progress Bar Style
PROGRESS_BAR_STYLE = f"""
    QProgressBar {{
        border: none;
        border-radius: 4px;
        background-color: {GAMING_COLORS['dark_bg']};
        text-align: center;
        color: {GAMING_COLORS['text_primary']};
        font-weight: bold;
    }}
    
    QProgressBar::chunk {{
        border-radius: 4px;
        background: {GRADIENTS['buy_gradient']};
    }}
"""

# Toggle Switch Style
TOGGLE_STYLE = f"""
    QCheckBox {{
        spacing: 10px;
        color: {GAMING_COLORS['text_primary']};
        font-weight: bold;
    }}
    
    QCheckBox::indicator {{
        width: 60px;
        height: 30px;
        border-radius: 15px;
        background-color: {GAMING_COLORS['dark_bg']};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {GAMING_COLORS['neon_green']};
    }}
"""

# Scrollbar Style
SCROLLBAR_STYLE = f"""
    QScrollBar:vertical {{
        background: rgba(55, 65, 81, 0.5);
        width: 12px;
        border-radius: 6px;
        margin: 0;
    }}
    
    QScrollBar::handle:vertical {{
        background: {GAMING_COLORS['accent_purple']};
        border-radius: 6px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background: {GAMING_COLORS['primary_purple']};
    }}
    
    QScrollBar::add-line:vertical,
    QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
"""

# Tooltip Style
TOOLTIP_STYLE = f"""
    QToolTip {{
        background: {GRADIENTS['card_bg']};
        color: {GAMING_COLORS['text_primary']};
        border: 1px solid {GAMING_COLORS['border_glow']};
        border-radius: 8px;
        padding: 8px;
        font-size: 12px;
    }}
"""

# Complete Style Sheet
COMPLETE_STYLESHEET = f"""
    {MAIN_WINDOW_STYLE}
    {PROGRESS_BAR_STYLE}
    {TOGGLE_STYLE}
    {SCROLLBAR_STYLE}
    {TOOLTIP_STYLE}
"""

# Gaming Fonts
GAMING_FONTS = [
    "Orbitron",
    "Exo 2", 
    "Rajdhani",
    "Audiowide",
    "Electrolize",
    "Segment7",
    "Digital-7",
    "Roboto Mono",
    "Fira Code",
    "JetBrains Mono"
]

# Icon Mappings
GAMING_ICONS = {
    'rocket': '🚀',
    'fire': '🔥',
    'lightning': '⚡',
    'target': '🎯',
    'gem': '💎',
    'crown': '👑',
    'star': '⭐',
    'trophy': '🏆',
    'shield': '🛡️',
    'sword': '⚔️',
    'magic': '✨',
    'robot': '🤖',
    'alien': '👽',
    'space': '🌌',
    'planet': '🪐',
    'satellite': '🛰️',
    'radar': '📡',
    'chart': '📊',
    'graph': '📈',
    'money': '💰',
    'dollar': '💵',
    'bank': '🏦',
    'card': '💳',
    'safe': '🔒',
    'key': '🔑',
    'gear': '⚙️',
    'tool': '🔧',
    'hammer': '🔨',
    'wrench': '🔩'
}

def get_gaming_style():
    """دریافت استایل گیمینگ کامل"""
    return COMPLETE_STYLESHEET

def get_button_style(style_type="primary"):
    """دریافت استایل دکمه"""
    return BUTTON_STYLES.get(style_type, BUTTON_STYLES['primary'])

def get_gaming_icon(icon_name):
    """دریافت آیکون گیمینگ"""
    return GAMING_ICONS.get(icon_name, '🎮')
