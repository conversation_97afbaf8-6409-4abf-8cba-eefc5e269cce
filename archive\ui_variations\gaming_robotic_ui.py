"""
🎮 Cartoonish Gaming App UI - بهترین تکنیک‌های مدرن گیمینگ و کارتونی
Advanced Cartoon/Gaming Interface with App-like Design
Professional Trading Bot with Fun Gaming Elements
"""

import sys
import math
import random
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# 🎨 Cartoonish Gaming Color Palette
class CartoonColors:
    # Bright Cartoon Colors
    CARTOON_BLUE = "#4A90E2"
    CARTOON_GREEN = "#7ED321"
    CARTOON_ORANGE = "#F5A623"
    CARTOON_RED = "#D0021B"
    CARTOON_PURPLE = "#9013FE"
    CARTOON_PINK = "#FF6B9D"
    CARTOON_YELLOW = "#FFD700"
    CARTOON_CYAN = "#00BCD4"

    # Neon Gaming Colors
    NEON_CYAN = "#00FFFF"
    NEON_MAGENTA = "#FF00FF"
    NEON_GREEN = "#00FF41"
    NEON_ORANGE = "#FF8000"
    NEON_PURPLE = "#8000FF"
    NEON_BLUE = "#0080FF"
    NEON_PINK = "#FF0080"
    NEON_YELLOW = "#FFFF00"

    # App-like Backgrounds
    APP_PRIMARY = "#1E1E2E"
    APP_SECONDARY = "#2A2A3E"
    APP_TERTIARY = "#3A3A5E"
    APP_ACCENT = "#4A4A7E"
    APP_SURFACE = "#2D2D4D"

    # Cartoon Gradients
    GRADIENT_BLUE = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4A90E2, stop:1 #357ABD)"
    GRADIENT_GREEN = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7ED321, stop:1 #5BA617)"
    GRADIENT_ORANGE = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #F5A623, stop:1 #E8931A)"
    GRADIENT_PURPLE = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9013FE, stop:1 #7B1FA2)"

    # Glowing Effects
    GLOW_CYAN = "rgba(0, 255, 255, 0.4)"
    GLOW_MAGENTA = "rgba(255, 0, 255, 0.4)"
    GLOW_GREEN = "rgba(126, 211, 33, 0.4)"
    GLOW_BLUE = "rgba(74, 144, 226, 0.4)"

# 🎮 Cartoonish Gaming Button
class CartoonButton(QPushButton):
    """Cartoonish gaming button with fun animations and app-like design"""

    def __init__(self, text="", icon="", button_type="default", size=(140, 70)):
        super().__init__()
        self.button_text = text
        self.button_icon = icon
        self.button_type = button_type
        self.button_size = size

        # Animation properties
        self.glow_animation = QPropertyAnimation(self, b"glow_intensity")
        self.bounce_animation = QPropertyAnimation(self, b"bounce_scale")
        self.color_animation = QPropertyAnimation(self, b"border_color")

        self.glow_intensity = 0.0
        self.bounce_scale = 1.0
        self.border_color = QColor(CartoonColors.CARTOON_BLUE)

        self.setup_button()
        self.setup_animations()
    
    def setup_button(self):
        """Setup advanced button styling"""
        self.setFixedSize(*self.button_size)
        
        # Button content
        if self.button_icon and self.button_text:
            content = f"""
            <div style="text-align: center; line-height: 1.1;">
                <div style="font-size: 24px; margin-bottom: 2px;">{self.button_icon}</div>
                <div style="font-size: 11px; font-weight: 700; color: white; letter-spacing: 1px;">{self.button_text}</div>
            </div>
            """
            
            content_label = QLabel(content)
            content_label.setAlignment(Qt.AlignCenter)
            content_label.setStyleSheet("background: transparent; border: none;")
            
            layout = QVBoxLayout(self)
            layout.setContentsMargins(4, 4, 4, 4)
            layout.addWidget(content_label)
        else:
            self.setText(self.button_text or self.button_icon)
        
        # Apply cyberpunk styling
        self.apply_cyberpunk_style()
        
        # Add advanced shadow
        self.add_cyberpunk_shadow()
    
    def apply_cyberpunk_style(self):
        """Apply cyberpunk button styling"""
        if self.button_type == "primary":
            bg_color = CyberpunkColors.NEON_CYAN
            border_color = CyberpunkColors.NEON_CYAN
            glow_color = CyberpunkColors.GLOW_CYAN
        elif self.button_type == "danger":
            bg_color = CyberpunkColors.NEON_MAGENTA
            border_color = CyberpunkColors.NEON_MAGENTA
            glow_color = CyberpunkColors.GLOW_MAGENTA
        elif self.button_type == "success":
            bg_color = CyberpunkColors.NEON_GREEN
            border_color = CyberpunkColors.NEON_GREEN
            glow_color = CyberpunkColors.GLOW_GREEN
        else:
            bg_color = "rgba(0, 255, 255, 0.1)"
            border_color = CyberpunkColors.NEON_CYAN
            glow_color = CyberpunkColors.GLOW_CYAN
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {bg_color},
                    stop:0.5 rgba(0, 0, 0, 0.3),
                    stop:1 {bg_color});
                border: 2px solid {border_color};
                border-radius: 15px;
                color: white;
                font-family: 'Orbitron', 'Consolas', monospace;
                font-weight: 700;
                font-size: 12px;
                letter-spacing: 1px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {border_color},
                    stop:0.5 rgba(255, 255, 255, 0.1),
                    stop:1 {border_color});
                border: 3px solid {border_color};
            }}
            QPushButton:pressed {{
                background: rgba(0, 0, 0, 0.5);
                border: 1px solid {border_color};
            }}
        """)
    
    def add_cyberpunk_shadow(self):
        """Add cyberpunk glow effect"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 255, 255, 80))
        self.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """Setup button animations"""
        # Glow animation
        self.glow_animation.setDuration(2000)
        self.glow_animation.setStartValue(0.0)
        self.glow_animation.setEndValue(1.0)
        self.glow_animation.setLoopCount(-1)
        self.glow_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        
        # Pulse animation
        self.pulse_animation.setDuration(1500)
        self.pulse_animation.setStartValue(1.0)
        self.pulse_animation.setEndValue(1.05)
        self.pulse_animation.setLoopCount(-1)
        self.pulse_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        # Start animations on hover
        self.enterEvent = self.start_animations
        self.leaveEvent = self.stop_animations
    
    def start_animations(self, event):
        """Start hover animations"""
        self.glow_animation.start()
        self.pulse_animation.start()
    
    def stop_animations(self, event):
        """Stop hover animations"""
        self.glow_animation.stop()
        self.pulse_animation.stop()
    
    # Animation properties
    @Property(float)
    def glow_intensity(self):
        return self._glow_intensity
    
    @glow_intensity.setter
    def glow_intensity(self, value):
        self._glow_intensity = value
        self.update()
    
    @Property(float)
    def pulse_scale(self):
        return self._pulse_scale
    
    @pulse_scale.setter
    def pulse_scale(self, value):
        self._pulse_scale = value
        self.update()

# 🎮 Advanced Gaming Panel
class CyberpunkPanel(QFrame):
    """Advanced gaming panel with holographic effects"""
    
    def __init__(self, title="", panel_type="default"):
        super().__init__()
        self.panel_title = title
        self.panel_type = panel_type
        self.setup_panel()
    
    def setup_panel(self):
        """Setup cyberpunk panel styling"""
        if self.panel_type == "primary":
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 255, 255, 0.15),
                    stop:0.5 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(0, 255, 255, 0.15));
            """
            border_color = CyberpunkColors.NEON_CYAN
        elif self.panel_type == "danger":
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 0, 255, 0.15),
                    stop:0.5 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(255, 0, 255, 0.15));
            """
            border_color = CyberpunkColors.NEON_MAGENTA
        elif self.panel_type == "success":
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 255, 65, 0.15),
                    stop:0.5 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(0, 255, 65, 0.15));
            """
            border_color = CyberpunkColors.NEON_GREEN
        else:
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(0, 0, 0, 0.9),
                    stop:1 rgba(255, 255, 255, 0.08));
            """
            border_color = CyberpunkColors.NEON_CYAN
        
        self.setStyleSheet(f"""
            QFrame {{
                {bg_gradient}
                border: 2px solid {border_color};
                border-radius: 20px;
                padding: 20px;
            }}
        """)
        
        # Add holographic glow
        self.add_holographic_glow()
    
    def add_holographic_glow(self):
        """Add holographic glow effect"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 255, 255, 60))
        self.setGraphicsEffect(shadow)

# 📊 Animated Stats Display
class AnimatedStatsWidget(QWidget):
    """Animated statistics display with real-time updates"""
    
    def __init__(self, title="", value="", icon="", color=CyberpunkColors.NEON_CYAN):
        super().__init__()
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.current_value = 0
        self.target_value = 0
        
        self.setup_widget()
        self.setup_animations()
    
    def setup_widget(self):
        """Setup animated stats widget"""
        self.setFixedSize(200, 120)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)
        
        # Header with icon and title
        header_layout = QHBoxLayout()
        
        if self.icon:
            icon_label = QLabel(self.icon)
            icon_label.setStyleSheet(f"""
                font-size: 24px;
                color: {self.color};
                margin-right: 8px;
            """)
            header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-family: 'Orbitron', 'Consolas', monospace;
            font-size: 12px;
            color: rgba(255,255,255,0.8);
            font-weight: 600;
            letter-spacing: 1px;
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Animated value display
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-family: 'Orbitron', 'Consolas', monospace;
            font-size: 28px;
            font-weight: 800;
            color: {self.color};
            letter-spacing: 2px;
            margin: 8px 0;
        """)
        layout.addWidget(self.value_label)
        
        # Progress indicator
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(random.randint(60, 95))
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {self.color};
                border-radius: 8px;
                background: rgba(0, 0, 0, 0.5);
                height: 8px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.color}, stop:1 rgba(255,255,255,0.3));
                border-radius: 6px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # Widget styling
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(0, 0, 0, 0.6));
                border: 1px solid {self.color};
                border-radius: 15px;
            }}
        """)
        
        # Add glow effect
        self.add_glow_effect()
    
    def add_glow_effect(self):
        """Add glow effect to stats widget"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(self.color).darker(150))
        self.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """Setup value animations"""
        self.value_animation = QPropertyAnimation(self, b"animated_value")
        self.value_animation.setDuration(1000)
        self.value_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Auto-update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_value)
        self.update_timer.start(3000)  # Update every 3 seconds
    
    def update_value(self):
        """Update value with animation"""
        if "$" in self.value:
            new_value = random.uniform(1000, 2500)
            self.animate_to_value(f"${new_value:.2f}")
        elif "%" in self.value:
            new_value = random.uniform(80, 98)
            self.animate_to_value(f"{new_value:.1f}%")
        else:
            new_value = random.randint(10, 50)
            self.animate_to_value(str(new_value))
    
    def animate_to_value(self, new_value):
        """Animate to new value"""
        self.value = new_value
        self.value_label.setText(new_value)
        
        # Update progress bar
        new_progress = random.randint(60, 95)
        self.progress_bar.setValue(new_progress)
    
    @Property(float)
    def animated_value(self):
        return self.current_value
    
    @animated_value.setter
    def animated_value(self, value):
        self.current_value = value

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Test window
    window = QMainWindow()
    window.setWindowTitle("🤖 Cyberpunk UI Test")
    window.setGeometry(100, 100, 800, 600)
    
    # Dark cyberpunk background
    window.setStyleSheet(f"""
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {CyberpunkColors.DARK_PRIMARY},
                stop:0.3 {CyberpunkColors.DARK_SECONDARY},
                stop:0.7 {CyberpunkColors.DARK_TERTIARY},
                stop:1 {CyberpunkColors.DARK_ACCENT});
            color: white;
        }}
    """)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setSpacing(20)
    layout.setContentsMargins(30, 30, 30, 30)
    
    # Test buttons
    buttons_layout = QHBoxLayout()
    
    primary_btn = CyberpunkButton("ACTIVATE", "🚀", "primary", (140, 70))
    danger_btn = CyberpunkButton("TERMINATE", "💥", "danger", (140, 70))
    success_btn = CyberpunkButton("EXECUTE", "✓", "success", (140, 70))
    
    buttons_layout.addWidget(primary_btn)
    buttons_layout.addWidget(danger_btn)
    buttons_layout.addWidget(success_btn)
    
    layout.addLayout(buttons_layout)
    
    # Test stats
    stats_layout = QHBoxLayout()
    
    balance_stats = AnimatedStatsWidget("BALANCE", "$1,251.76", "💰", CyberpunkColors.NEON_GREEN)
    power_stats = AnimatedStatsWidget("POWER", "87%", "⚡", CyberpunkColors.NEON_CYAN)
    level_stats = AnimatedStatsWidget("LEVEL", "42", "🎯", CyberpunkColors.NEON_MAGENTA)
    
    stats_layout.addWidget(balance_stats)
    stats_layout.addWidget(power_stats)
    stats_layout.addWidget(level_stats)
    
    layout.addLayout(stats_layout)
    
    window.show()
    sys.exit(app.exec())
