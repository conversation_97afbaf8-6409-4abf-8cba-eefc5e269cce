"""
VIP BIG BANG Enterprise - Settings Manager
مدیریت کامل تنظیمات سیستم تصمیم‌گیری
"""

import logging
from adaptive_decision_system import AdaptiveDecisionSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s: %(message)s', datefmt='%H:%M:%S')

class SettingsManager:
    """مدیر تنظیمات سیستم"""
    
    def __init__(self):
        self.logger = logging.getLogger("SettingsManager")
        self.adaptive_system = AdaptiveDecisionSystem()
        
    def show_main_menu(self):
        """نمایش منوی اصلی"""
        while True:
            print("\n" + "=" * 60)
            print("🎛️  VIP BIG BANG - مدیریت تنظیمات")
            print("=" * 60)
            print("1️⃣  انتخاب پروفایل تصمیم‌گیری")
            print("2️⃣  تنظیم تایم‌فریم و مدت ترید")
            print("3️⃣  ایجاد پروفایل سفارشی")
            print("4️⃣  تنظیمات دستی پیشرفته")
            print("5️⃣  نمایش تنظیمات فعلی")
            print("6️⃣  تست سیستم با تنظیمات فعلی")
            print("7️⃣  ذخیره/بارگذاری تنظیمات")
            print("8️⃣  بازگشت به حالت پیش‌فرض")
            print("0️⃣  خروج")
            
            choice = input("\n🔢 انتخاب کنید: ").strip()
            
            if choice == '1':
                self.select_profile()
            elif choice == '2':
                self.configure_timeframe()
            elif choice == '3':
                self.create_custom_profile()
            elif choice == '4':
                self.advanced_settings()
            elif choice == '5':
                self.show_current_settings()
            elif choice == '6':
                self.test_current_settings()
            elif choice == '7':
                self.save_load_menu()
            elif choice == '8':
                self.reset_to_default()
            elif choice == '0':
                print("👋 خروج از برنامه")
                break
            else:
                print("❌ انتخاب نامعتبر!")
    
    def select_profile(self):
        """انتخاب پروفایل تصمیم‌گیری"""
        print("\n📋 پروفایل‌های تصمیم‌گیری:")
        print("-" * 40)
        
        profiles = list(self.adaptive_system.decision_profiles.keys())
        for i, key in enumerate(profiles, 1):
            profile = self.adaptive_system.decision_profiles[key]
            print(f"{i}. {profile['name']}")
            print(f"   هدف وین ریت: {profile['win_rate_target']:.0%}")
            print(f"   حداقل امتیاز: {profile['min_signal_strength']:.0%}")
            print(f"   ترید/روز: {profile['max_daily_trades']}")
            print()
        
        try:
            choice = int(input("🔢 شماره پروفایل: ")) - 1
            if 0 <= choice < len(profiles):
                selected = profiles[choice]
                self.adaptive_system.set_decision_profile(selected)
                print(f"✅ پروفایل '{self.adaptive_system.decision_profiles[selected]['name']}' انتخاب شد")
            else:
                print("❌ شماره نامعتبر!")
        except ValueError:
            print("❌ لطفاً عدد وارد کنید!")
    
    def configure_timeframe(self):
        """تنظیم تایم‌فریم و مدت ترید"""
        print("\n⏰ تنظیم تایم‌فریم و مدت ترید:")
        print("-" * 40)
        
        # انتخاب تایم‌فریم
        timeframes = list(self.adaptive_system.timeframe_configs.keys())
        print("📊 تایم‌فریم‌های موجود:")
        for i, tf in enumerate(timeframes, 1):
            config = self.adaptive_system.timeframe_configs[tf]
            print(f"{i}. {tf} (پیش‌فرض ترید: {config['trade_duration']} دقیقه)")
        
        try:
            tf_choice = int(input("🔢 شماره تایم‌فریم: ")) - 1
            if 0 <= tf_choice < len(timeframes):
                selected_tf = timeframes[tf_choice]
                
                # انتخاب مدت ترید
                print(f"\n⏱️  مدت ترید برای {selected_tf}:")
                print("1. 1 دقیقه")
                print("2. 2 دقیقه") 
                print("3. 5 دقیقه")
                print("4. 10 دقیقه")
                print("5. 15 دقیقه")
                print("6. 30 دقیقه")
                print("7. 60 دقیقه")
                print("8. مقدار سفارشی")
                
                duration_choice = int(input("🔢 انتخاب مدت: "))
                
                durations = [1, 2, 5, 10, 15, 30, 60]
                if 1 <= duration_choice <= 7:
                    trade_duration = durations[duration_choice - 1]
                elif duration_choice == 8:
                    trade_duration = int(input("⏱️  مدت ترید (دقیقه): "))
                else:
                    print("❌ انتخاب نامعتبر!")
                    return
                
                # تنظیم خودکار
                settings = self.adaptive_system.auto_adjust_for_timeframe(selected_tf, trade_duration)
                
                print(f"\n✅ تنظیمات خودکار اعمال شد:")
                print(f"   تایم‌فریم: {selected_tf}")
                print(f"   مدت ترید: {trade_duration} دقیقه")
                print(f"   پروفایل انتخابی: {settings['name']}")
                print(f"   حداقل امتیاز: {settings['min_signal_strength']:.1%}")
                
            else:
                print("❌ شماره نامعتبر!")
        except ValueError:
            print("❌ لطفاً عدد وارد کنید!")
    
    def create_custom_profile(self):
        """ایجاد پروفایل سفارشی"""
        print("\n🎨 ایجاد پروفایل سفارشی:")
        print("-" * 40)
        
        try:
            name = input("📝 نام پروفایل: ").strip()
            if not name:
                print("❌ نام نمی‌تواند خالی باشد!")
                return
            
            display_name = input("🏷️  نام نمایشی: ").strip() or name
            
            print("\n⚙️  تنظیمات پروفایل:")
            min_signal = float(input("📊 حداقل امتیاز سیگنال (0.5-1.0): "))
            min_alignment = float(input("🎯 حداقل هم‌راستایی (0.5-1.0): "))
            confirmations = int(input("🔢 تعداد تأیید لازم (1-10): "))
            max_daily = int(input("📅 حداکثر ترید روزانه (1-100): "))
            cooldown = int(input("⏰ زمان استراحت (دقیقه): "))
            win_target = float(input("🏆 هدف وین ریت (0.5-0.99): "))
            
            # اعتبارسنجی
            if not (0.5 <= min_signal <= 1.0):
                print("❌ حداقل امتیاز باید بین 0.5 تا 1.0 باشد!")
                return
            
            if not (0.5 <= min_alignment <= 1.0):
                print("❌ حداقل هم‌راستایی باید بین 0.5 تا 1.0 باشد!")
                return
            
            if not (0.5 <= win_target <= 0.99):
                print("❌ هدف وین ریت باید بین 0.5 تا 0.99 باشد!")
                return
            
            # ایجاد پروفایل
            custom_settings = {
                'name': display_name,
                'min_signal_strength': min_signal,
                'min_alignment': min_alignment,
                'required_confirmations': confirmations,
                'max_daily_trades': max_daily,
                'cooldown_minutes': cooldown,
                'win_rate_target': win_target
            }
            
            self.adaptive_system.create_custom_profile(name, custom_settings)
            self.adaptive_system.set_decision_profile(name)
            
            print(f"\n✅ پروفایل '{display_name}' ایجاد و فعال شد!")
            
        except ValueError:
            print("❌ لطفاً مقادیر معتبر وارد کنید!")
    
    def advanced_settings(self):
        """تنظیمات پیشرفته دستی"""
        print("\n🔧 تنظیمات پیشرفته:")
        print("-" * 40)
        
        current = self.adaptive_system.get_adaptive_settings()
        
        print("1. تغییر حداقل امتیاز سیگنال")
        print("2. تغییر حداقل هم‌راستایی")
        print("3. تغییر تعداد تأیید لازم")
        print("4. تغییر حداکثر ترید روزانه")
        print("5. تغییر زمان استراحت")
        print("6. تغییر هدف وین ریت")
        print("7. بازگشت")
        
        try:
            choice = int(input("🔢 انتخاب: "))
            
            if choice == 1:
                new_val = float(input(f"📊 حداقل امتیاز فعلی: {current['min_signal_strength']:.1%} | جدید: "))
                if 0.5 <= new_val <= 1.0:
                    self.adaptive_system.custom_settings['min_signal_strength'] = new_val
                    print("✅ تغییر اعمال شد")
                else:
                    print("❌ مقدار باید بین 0.5 تا 1.0 باشد!")
            
            elif choice == 2:
                new_val = float(input(f"🎯 هم‌راستایی فعلی: {current['min_alignment']:.1%} | جدید: "))
                if 0.5 <= new_val <= 1.0:
                    self.adaptive_system.custom_settings['min_alignment'] = new_val
                    print("✅ تغییر اعمال شد")
                else:
                    print("❌ مقدار باید بین 0.5 تا 1.0 باشد!")
            
            elif choice == 3:
                new_val = int(input(f"🔢 تأیید فعلی: {current['required_confirmations']} | جدید: "))
                if 1 <= new_val <= 15:
                    self.adaptive_system.custom_settings['required_confirmations'] = new_val
                    print("✅ تغییر اعمال شد")
                else:
                    print("❌ مقدار باید بین 1 تا 15 باشد!")
            
            elif choice == 4:
                new_val = int(input(f"📅 ترید روزانه فعلی: {current['max_daily_trades']} | جدید: "))
                if 1 <= new_val <= 200:
                    self.adaptive_system.custom_settings['max_daily_trades'] = new_val
                    print("✅ تغییر اعمال شد")
                else:
                    print("❌ مقدار باید بین 1 تا 200 باشد!")
            
            elif choice == 5:
                new_val = int(input(f"⏰ استراحت فعلی: {current['cooldown_minutes']} دقیقه | جدید: "))
                if 0 <= new_val <= 1440:
                    self.adaptive_system.custom_settings['cooldown_minutes'] = new_val
                    print("✅ تغییر اعمال شد")
                else:
                    print("❌ مقدار باید بین 0 تا 1440 دقیقه باشد!")
            
            elif choice == 6:
                new_val = float(input(f"🏆 هدف وین ریت فعلی: {current['win_rate_target']:.1%} | جدید: "))
                if 0.5 <= new_val <= 0.99:
                    self.adaptive_system.custom_settings['win_rate_target'] = new_val
                    print("✅ تغییر اعمال شد")
                else:
                    print("❌ مقدار باید بین 0.5 تا 0.99 باشد!")
            
            elif choice == 7:
                return
            else:
                print("❌ انتخاب نامعتبر!")
                
        except ValueError:
            print("❌ لطفاً مقدار معتبر وارد کنید!")
    
    def show_current_settings(self):
        """نمایش تنظیمات فعلی"""
        settings = self.adaptive_system.get_adaptive_settings()
        
        print("\n📊 تنظیمات فعلی سیستم:")
        print("=" * 50)
        print(f"🎯 پروفایل: {settings['name']}")
        print(f"⏰ تایم‌فریم: {settings['timeframe']}")
        print(f"⏱️  مدت ترید: {settings['trade_duration']} دقیقه")
        print(f"📊 حداقل امتیاز: {settings['min_signal_strength']:.1%}")
        print(f"🎯 حداقل هم‌راستایی: {settings['min_alignment']:.1%}")
        print(f"🔢 تأیید لازم: {settings['required_confirmations']}")
        print(f"📅 حداکثر ترید/روز: {settings['max_daily_trades']}")
        print(f"⏰ زمان استراحت: {settings['cooldown_minutes']} دقیقه")
        print(f"🏆 هدف وین ریت: {settings['win_rate_target']:.1%}")
        print(f"📈 حساسیت ترند: {settings['trend_sensitivity']}")
        print(f"📊 ضریب حجم: {settings['volume_multiplier']}")
        
        input("\n⏎ برای ادامه Enter بزنید...")
    
    def test_current_settings(self):
        """تست تنظیمات فعلی"""
        print("\n🧪 تست سیستم با تنظیمات فعلی:")
        print("-" * 50)
        
        # شبیه‌سازی داده‌های تست
        test_primary = {
            'ma6': {'score': 0.85, 'direction': 'UP'},
            'vortex': {'score': 0.82, 'direction': 'UP'},
            'volume_per_candle': {'score': 0.78, 'direction': 'UP'},
            'momentum': {'score': 0.88, 'direction': 'UP'},
            'trend_analyzer': {'score': 0.90, 'direction': 'UP'}
        }
        
        test_complementary = {
            'economic_news_filter': {'allow_trading': True, 'score': 0.9},
            'account_safety': {'allow_trading': True, 'score': 0.95},
            'live_signal_scanner': {'allow_trading': True, 'score': 0.85}
        }
        
        # اجرای تست
        result = self.adaptive_system.validate_signal_adaptive(
            test_primary, test_complementary, None
        )
        
        print(f"\n🎯 نتیجه تست:")
        print(f"   تصمیم: {result['decision']}")
        print(f"   اجازه ترید: {'بله' if result['allow_trading'] else 'خیر'}")
        print(f"   امتیاز: {result['score']:.1f}%")
        print(f"   احتمال برد: {result['win_probability']:.1%}")
        
        input("\n⏎ برای ادامه Enter بزنید...")
    
    def save_load_menu(self):
        """منوی ذخیره/بارگذاری"""
        print("\n💾 ذخیره/بارگذاری تنظیمات:")
        print("-" * 40)
        print("1. ذخیره تنظیمات فعلی")
        print("2. بارگذاری تنظیمات")
        print("3. بازگشت")
        
        choice = input("🔢 انتخاب: ").strip()
        
        if choice == '1':
            filename = input("📁 نام فایل (بدون پسوند): ").strip()
            if filename:
                self.adaptive_system.save_configuration(f"{filename}.json")
            else:
                print("❌ نام فایل نمی‌تواند خالی باشد!")
        
        elif choice == '2':
            filename = input("📁 نام فایل (بدون پسوند): ").strip()
            if filename:
                if self.adaptive_system.load_configuration(f"{filename}.json"):
                    print("✅ تنظیمات بارگذاری شد")
                else:
                    print("❌ خطا در بارگذاری")
            else:
                print("❌ نام فایل نمی‌تواند خالی باشد!")
    
    def reset_to_default(self):
        """بازگشت به تنظیمات پیش‌فرض"""
        confirm = input("⚠️  آیا مطمئنید که می‌خواهید به تنظیمات پیش‌فرض برگردید؟ (y/N): ")
        if confirm.lower() == 'y':
            self.adaptive_system = AdaptiveDecisionSystem()
            print("✅ تنظیمات به حالت پیش‌فرض بازگشت")

def main():
    """اجرای مدیر تنظیمات"""
    manager = SettingsManager()
    manager.show_main_menu()

if __name__ == "__main__":
    main()
