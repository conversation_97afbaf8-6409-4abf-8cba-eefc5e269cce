#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Complete System Status Monitor
Real-time monitoring of all system components
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import json
import asyncio
import websockets
from datetime import datetime
import subprocess
import psutil

class VIPCompleteStatusMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title('🚀 VIP BIG BANG - Complete System Status')
        self.root.geometry('1000x700')
        self.root.configure(bg='#0a0a0a')
        
        # System status
        self.server_running = False
        self.ui_running = False
        self.extension_connected = False
        self.quotex_open = False
        
        # Data counters
        self.total_connections = 0
        self.data_extractions = 0
        self.last_balance = "$0.00"
        self.last_asset = "None"
        self.last_price = "-"
        
        self.create_ui()
        self.start_monitoring()
        
    def create_ui(self):
        # Title
        title_frame = tk.Frame(self.root, bg='#0a0a0a')
        title_frame.pack(fill='x', pady=10)
        
        title = tk.Label(title_frame, text='🚀 VIP BIG BANG - COMPLETE SYSTEM STATUS', 
                        font=('Arial', 24, 'bold'), fg='#00d4ff', bg='#0a0a0a')
        title.pack()
        
        subtitle = tk.Label(title_frame, text='Enterprise Trading Robot - Real-time System Monitor', 
                           font=('Arial', 14), fg='#888888', bg='#0a0a0a')
        subtitle.pack()
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#0a0a0a')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Left panel - System Status
        left_frame = tk.LabelFrame(main_frame, text='🖥️ System Components', 
                                  font=('Arial', 14, 'bold'), fg='#00d4ff', bg='#1a1a1a')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # Server status
        self.create_status_item(left_frame, '📡 Real Data Server', 'server')
        self.create_status_item(left_frame, '🎮 Main UI System', 'ui')
        self.create_status_item(left_frame, '🔌 Chrome Extension', 'extension')
        self.create_status_item(left_frame, '🌐 Quotex Platform', 'quotex')
        
        # Right panel - Live Data
        right_frame = tk.LabelFrame(main_frame, text='📊 Live Trading Data', 
                                   font=('Arial', 14, 'bold'), fg='#00d4ff', bg='#1a1a1a')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # Data display
        self.create_data_item(right_frame, '💰 Balance', 'balance')
        self.create_data_item(right_frame, '📈 Current Asset', 'asset')
        self.create_data_item(right_frame, '💲 Current Price', 'price')
        self.create_data_item(right_frame, '🔗 Total Connections', 'connections')
        self.create_data_item(right_frame, '📊 Data Extractions', 'extractions')
        
        # Bottom panel - Activity Log
        log_frame = tk.LabelFrame(self.root, text='📋 System Activity Log', 
                                 font=('Arial', 14, 'bold'), fg='#00d4ff', bg='#1a1a1a')
        log_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Log text area
        log_container = tk.Frame(log_frame, bg='#1a1a1a')
        log_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.log_text = tk.Text(log_container, height=8, bg='#0a0a0a', fg='#ffffff', 
                               font=('Consolas', 10), wrap='word')
        
        scrollbar = tk.Scrollbar(log_container, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Control buttons
        button_frame = tk.Frame(self.root, bg='#0a0a0a')
        button_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(button_frame, text='🔄 Refresh Status', font=('Arial', 12, 'bold'),
                 bg='#00d4ff', fg='#000000', command=self.refresh_status).pack(side='left', padx=5)
        
        tk.Button(button_frame, text='🌐 Open Quotex', font=('Arial', 12, 'bold'),
                 bg='#4ade80', fg='#000000', command=self.open_quotex).pack(side='left', padx=5)
        
        tk.Button(button_frame, text='🔧 Open Extensions', font=('Arial', 12, 'bold'),
                 bg='#f59e0b', fg='#000000', command=self.open_extensions).pack(side='left', padx=5)
        
        # Initial log
        self.add_log('🚀 VIP BIG BANG Complete System Monitor Started')
        self.add_log('📊 Monitoring all system components...')
        self.add_log('')
        
    def create_status_item(self, parent, label, key):
        frame = tk.Frame(parent, bg='#1a1a1a')
        frame.pack(fill='x', padx=10, pady=8)
        
        tk.Label(frame, text=label, font=('Arial', 12, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        
        status_label = tk.Label(frame, text='🔴 OFFLINE', font=('Arial', 12), 
                               fg='#ef4444', bg='#1a1a1a')
        status_label.pack(side='right')
        
        setattr(self, f'{key}_status_label', status_label)
        
    def create_data_item(self, parent, label, key):
        frame = tk.Frame(parent, bg='#1a1a1a')
        frame.pack(fill='x', padx=10, pady=8)
        
        tk.Label(frame, text=label, font=('Arial', 12, 'bold'), 
                fg='white', bg='#1a1a1a').pack(side='left')
        
        value_label = tk.Label(frame, text='N/A', font=('Arial', 12), 
                              fg='#00d4ff', bg='#1a1a1a')
        value_label.pack(side='right')
        
        setattr(self, f'{key}_value_label', value_label)
        
    def add_log(self, message):
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f'[{timestamp}] {message}\n'
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def update_status(self, component, status):
        label = getattr(self, f'{component}_status_label')
        
        if status:
            label.config(text='🟢 ONLINE', fg='#4ade80')
        else:
            label.config(text='🔴 OFFLINE', fg='#ef4444')
            
    def update_data(self, key, value):
        label = getattr(self, f'{key}_value_label')
        label.config(text=str(value))
        
    def check_processes(self):
        # Check if server is running (port 8765)
        server_running = False
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == 8765:
                    server_running = True
                    break
        except:
            pass
            
        if server_running != self.server_running:
            self.server_running = server_running
            self.update_status('server', server_running)
            if server_running:
                self.add_log('✅ Real Data Server detected on port 8765')
            else:
                self.add_log('❌ Real Data Server not detected')
                
        # Check Chrome processes
        chrome_running = False
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if 'chrome' in proc.info['name'].lower():
                    chrome_running = True
                    break
        except:
            pass
            
        if chrome_running != self.quotex_open:
            self.quotex_open = chrome_running
            self.update_status('quotex', chrome_running)
            
    def start_monitoring(self):
        def monitor_loop():
            while True:
                try:
                    self.check_processes()
                    time.sleep(2)
                except Exception as e:
                    self.add_log(f'❌ Monitor error: {e}')
                    time.sleep(5)
                    
        thread = threading.Thread(target=monitor_loop, daemon=True)
        thread.start()
        
        # Update data periodically
        def update_loop():
            while True:
                try:
                    self.update_data('connections', self.total_connections)
                    self.update_data('extractions', self.data_extractions)
                    self.update_data('balance', self.last_balance)
                    self.update_data('asset', self.last_asset)
                    self.update_data('price', self.last_price)
                    time.sleep(1)
                except:
                    time.sleep(5)
                    
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
        
    def refresh_status(self):
        self.add_log('🔄 Refreshing system status...')
        self.check_processes()
        
    def open_quotex(self):
        subprocess.Popen(['start', 'https://qxbroker.com/en/trade'], shell=True)
        self.add_log('🌐 Opening Quotex platform...')
        
    def open_extensions(self):
        subprocess.Popen(['start', 'chrome://extensions/'], shell=True)
        self.add_log('🔧 Opening Chrome Extensions page...')
        
    def run(self):
        self.root.mainloop()

if __name__ == '__main__':
    print('🚀 Starting VIP BIG BANG Complete Status Monitor...')
    monitor = VIPCompleteStatusMonitor()
    monitor.run()
