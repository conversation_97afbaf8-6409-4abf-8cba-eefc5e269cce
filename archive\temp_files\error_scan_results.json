{"errors": [], "warnings": [], "info": ["Python version OK: 3.12.0", "Module PySide6 OK", "Module cryptography OK", "<PERSON><PERSON><PERSON> requests OK", "Mo<PERSON>le json OK", "Module subprocess OK", "Module pathlib OK", "File vip_complete_professional_ui.py OK", "File chrome_extension/manifest.json OK", "File chrome_extension/background.js OK", "File chrome_extension/content.js OK", "File simple_extension_installer.py OK", "Chrome found: C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "Manifest key name OK", "Manifest key version OK", "Manifest key manifest_version OK", "Manifest key background OK", "Manifest key content_scripts OK", "Extension file background.js OK", "Extension file content.js OK", "Extension file popup.html OK", "Extension file popup.js OK", "UI syntax OK", "Write permissions OK", "Memory OK", "Disk space OK"], "status": "healthy"}