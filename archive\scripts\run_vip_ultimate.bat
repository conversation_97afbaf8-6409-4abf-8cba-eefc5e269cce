@echo off
title VIP BIG BANG Ultimate Dashboard Launcher
color 0A

echo.
echo ========================================
echo   VIP BIG BANG ULTIMATE DASHBOARD
echo   Professional Trading System
echo ========================================
echo.

echo [INFO] Starting VIP BIG BANG Ultimate Dashboard...
echo [INFO] Checking Python installation...

python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo [INFO] Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo [INFO] Python found, launching dashboard...
echo.

python run_vip_ultimate_dashboard.py

if errorlevel 1 (
    echo.
    echo [ERROR] Dashboard launch failed
    echo [INFO] Check the error messages above
    pause
    exit /b 1
)

echo.
echo [INFO] Dashboard closed successfully
pause
