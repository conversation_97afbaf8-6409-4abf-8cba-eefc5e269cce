"""
Professional Center Panel Component for VIP BIG BANG
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from .base_component import BaseComponent, VIPPanel, VIPLabel, VIPProgressBar

class VIPCenterPanel(BaseComponent):
    """Professional center panel component exactly matching the design"""
    
    def __init__(self, parent=None):
        self.current_price = 1.07329
        self.vortex_value = 0.0436
        self.buy_percentage = 71
        self.sell_percentage = 29
        self.buyer_power = 34
        self.seller_power = 66
        super().__init__(parent)
    
    def _setup_component(self):
        """Setup the center panel component"""
        self.apply_style("vip-panel")
        
        # Main vertical layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Chart section
        chart_section = self._create_chart_section()
        layout.addWidget(chart_section)
        
        # Vortex section
        vortex_section = self._create_vortex_section()
        layout.addWidget(vortex_section)
        
        # Bottom section with signals
        bottom_section = self._create_bottom_section()
        layout.addLayout(bottom_section)
    
    def _create_chart_section(self):
        """Create chart section with price display"""
        panel = VIPPanel()
        panel.setFixedHeight(250)
        
        # Price header with alert and current price
        price_header = QHBoxLayout()
        
        # Alert bell icon
        alert_icon = VIPLabel("🔔")
        alert_icon.setStyleSheet("font-size: 24px; color: #FFD700; background: transparent;")
        price_header.addWidget(alert_icon)
        
        price_header.addStretch()
        
        # Current price with green background
        self.price_label = VIPLabel(f"{self.current_price:.5f}")
        self.price_label.setStyleSheet("""
            font-size: 18px; 
            color: white; 
            background: rgba(50, 200, 50, 0.8); 
            padding: 5px 10px; 
            border-radius: 5px;
        """)
        price_header.addWidget(self.price_label)
        
        # Price levels on right
        price_levels = VIPLabel("1.07325\\n1.07320\\n1.07320\\n1.07330")
        price_levels.setStyleSheet("font-size: 10px; color: #888888; background: transparent;")
        price_header.addWidget(price_levels)
        
        panel.add_layout(price_header)
        
        # Chart area placeholder
        chart_area = VIPLabel("📈 CANDLESTICK CHART")
        chart_area.setAlignment(Qt.AlignCenter)
        chart_area.setStyleSheet("""
            background: rgba(0, 0, 0, 0.3); 
            border: 1px solid #444; 
            border-radius: 5px; 
            color: #888; 
            font-size: 14px;
            min-height: 180px;
        """)
        panel.add_widget(chart_area)
        
        return panel
    
    def _create_vortex_section(self):
        """Create vortex indicator section"""
        panel = VIPPanel()
        
        # Vortex title
        title = VIPLabel("VORTEX", "title")
        title.setStyleSheet("color: white; font-weight: bold; font-size: 14px; background: transparent;")
        panel.add_widget(title)
        
        # Vortex wave indicator
        self.vortex_indicator = VIPLabel("〰️〰️〰️〰️〰️〰️〰️〰️")
        self.vortex_indicator.setStyleSheet("color: #4488FF; font-size: 16px; background: transparent;")
        self.vortex_indicator.setAlignment(Qt.AlignCenter)
        panel.add_widget(self.vortex_indicator)
        
        # Vortex value
        self.vortex_value_label = VIPLabel(f"{self.vortex_value:.4f}")
        self.vortex_value_label.setStyleSheet("color: white; font-size: 12px; background: transparent;")
        self.vortex_value_label.setAlignment(Qt.AlignCenter)
        panel.add_widget(self.vortex_value_label)
        
        return panel
    
    def _create_bottom_section(self):
        """Create bottom section with economic news, signals, and power"""
        layout = QHBoxLayout()
        layout.setSpacing(10)
        
        # Economic News
        news_panel = self._create_economic_news()
        layout.addWidget(news_panel)
        
        # Live Signals
        signals_panel = self._create_live_signals()
        layout.addWidget(signals_panel)
        
        # Buyer/Seller Power
        power_panel = self._create_power_panel()
        layout.addWidget(power_panel)
        
        return layout
    
    def _create_economic_news(self):
        """Create economic news section"""
        panel = VIPPanel()
        panel.setFixedSize(80, 80)
        
        # News icon
        news_icon = VIPLabel("📊")
        news_icon.setAlignment(Qt.AlignCenter)
        news_icon.setStyleSheet("font-size: 24px; background: transparent;")
        panel.add_widget(news_icon)
        
        # News text
        news_text = VIPLabel("Economic\\nNews")
        news_text.setAlignment(Qt.AlignCenter)
        news_text.setStyleSheet("font-size: 10px; color: white; background: transparent;")
        panel.add_widget(news_text)
        
        return panel
    
    def _create_live_signals(self):
        """Create live signals section"""
        panel = VIPPanel()
        
        # Title
        title = VIPLabel("LIVE SIGNALS", "title")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white; font-weight: bold; font-size: 14px; margin-bottom: 10px; background: transparent;")
        panel.add_widget(title)
        
        # BUY signal row
        buy_layout = QHBoxLayout()
        
        buy_label = VIPLabel("BUY")
        buy_label.setStyleSheet("color: #32C832; font-weight: bold; font-size: 16px; background: transparent;")
        buy_layout.addWidget(buy_label)
        
        self.buy_71_label = VIPLabel(f"{self.buy_percentage}%")
        self.buy_71_label.setStyleSheet("color: #32C832; font-weight: bold; font-size: 16px; background: transparent;")
        buy_layout.addWidget(self.buy_71_label)
        
        self.sell_29_label = VIPLabel(f"{self.sell_percentage}%")
        self.sell_29_label.setStyleSheet("color: #FF8844; font-weight: bold; font-size: 16px; background: transparent;")
        buy_layout.addWidget(self.sell_29_label)
        
        panel.add_layout(buy_layout)
        
        # Large percentages row
        large_layout = QHBoxLayout()
        
        self.large_buy_label = VIPLabel(f"{self.buy_percentage}%", "percentage")
        self.large_buy_label.setStyleSheet("color: #32C832; font-weight: bold; font-size: 24px; background: transparent;")
        large_layout.addWidget(self.large_buy_label)
        
        self.large_sell_label = VIPLabel(f"{self.sell_percentage}%", "percentage")
        self.large_sell_label.setStyleSheet("color: #FF8844; font-weight: bold; font-size: 24px; background: transparent;")
        large_layout.addWidget(self.large_sell_label)
        
        panel.add_layout(large_layout)
        
        return panel
    
    def _create_power_panel(self):
        """Create buyer/seller power panel"""
        panel = VIPPanel()
        
        # Title
        title = VIPLabel("Buyer/Seller Power", "subtitle")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white; font-weight: bold; font-size: 12px; margin-bottom: 10px; background: transparent;")
        panel.add_widget(title)
        
        # Power progress bar
        self.power_bar = VIPProgressBar()
        self.power_bar.setValue(self.buyer_power)
        panel.add_widget(self.power_bar)
        
        # Power percentages
        self.power_percentages = VIPLabel(f"{self.buyer_power}%        {self.seller_power}%")
        self.power_percentages.setAlignment(Qt.AlignCenter)
        self.power_percentages.setStyleSheet("font-size: 18px; font-weight: bold; color: white; background: transparent;")
        panel.add_widget(self.power_percentages)
        
        return panel
    
    def update_price(self, price: float):
        """Update current price"""
        self.current_price = price
        self.price_label.setText(f"{price:.5f}")
    
    def update_vortex(self, value: float):
        """Update vortex value"""
        self.vortex_value = value
        self.vortex_value_label.setText(f"{value:.4f}")
    
    def update_signals(self, buy_pct: int, sell_pct: int):
        """Update live signals"""
        self.buy_percentage = buy_pct
        self.sell_percentage = sell_pct
        
        self.buy_71_label.setText(f"{buy_pct}%")
        self.sell_29_label.setText(f"{sell_pct}%")
        self.large_buy_label.setText(f"{buy_pct}%")
        self.large_sell_label.setText(f"{sell_pct}%")
    
    def update_power(self, buyer_power: int):
        """Update buyer/seller power"""
        self.buyer_power = buyer_power
        self.seller_power = 100 - buyer_power
        
        self.power_bar.setValue(buyer_power)
        self.power_percentages.setText(f"{buyer_power}%        {self.seller_power}%")
