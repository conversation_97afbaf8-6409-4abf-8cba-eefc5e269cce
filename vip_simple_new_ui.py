#!/usr/bin/env python3
"""
VIP BIG BANG ULTIMATE - SIMPLE NEW UI
Connect to existing Chrome Extension only
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import requests
import json
from datetime import datetime

class VIPSimpleNewUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_ui()
        self.start_data_connection()
        
    def setup_window(self):
        """Setup main window"""
        self.root.title("VIP BIG BANG ULTIMATE - NEW UI")
        self.root.geometry("1400x800")
        self.root.configure(bg='#1a1a1a')
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1400x800+{x}+{y}")
        
    def create_ui(self):
        """Create the new UI structure"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Professional Header (Horizontal Layout)
        self.create_professional_header(main_frame)
        
        # Content area with 3 columns
        content_frame = tk.Frame(main_frame, bg='#1a1a1a')
        content_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        # Left Panel
        self.create_left_panel(content_frame)
        
        # Center Panel
        self.create_center_panel(content_frame)
        
        # Right Panel
        self.create_right_panel(content_frame)
        
        # Bottom Status
        self.create_bottom_status(main_frame)
        
    def create_professional_header(self, parent):
        """Create professional horizontal header"""
        header_frame = tk.Frame(parent, bg='#2d2d2d', height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # Left section - Logo and title
        left_section = tk.Frame(header_frame, bg='#2d2d2d')
        left_section.pack(side='left', fill='y', padx=20)
        
        title_label = tk.Label(left_section, text="VIP BIG BANG ULTIMATE", 
                              font=('Arial', 16, 'bold'), fg='#00ff88', bg='#2d2d2d')
        title_label.pack(anchor='w', pady=10)
        
        subtitle_label = tk.Label(left_section, text="Professional Trading System", 
                                 font=('Arial', 10), fg='#888888', bg='#2d2d2d')
        subtitle_label.pack(anchor='w')
        
        # Center section - Live data feed
        center_section = tk.Frame(header_frame, bg='#2d2d2d')
        center_section.pack(side='left', fill='both', expand=True, padx=20)
        
        self.live_data_label = tk.Label(center_section, text="Connecting to Chrome Extension...", 
                                       font=('Arial', 12, 'bold'), fg='#9966ff', bg='#2d2d2d')
        self.live_data_label.pack(expand=True)
        
        # Right section - Controls
        right_section = tk.Frame(header_frame, bg='#2d2d2d')
        right_section.pack(side='right', fill='y', padx=20)
        
        control_btn = tk.Button(right_section, text="Settings", bg='#444444', fg='white',
                               font=('Arial', 10), relief='flat', padx=20)
        control_btn.pack(side='right', pady=25, padx=5)
        
        status_btn = tk.Button(right_section, text="Status", bg='#444444', fg='white',
                              font=('Arial', 10), relief='flat', padx=20)
        status_btn.pack(side='right', pady=25, padx=5)
        
    def create_left_panel(self, parent):
        """Create left analysis panel"""
        left_frame = tk.Frame(parent, bg='#2d2d2d', width=350)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        left_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(left_frame, text="ANALYSIS MODULES", 
                              font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2d2d2d')
        title_label.pack(pady=10)
        
        # Analysis boxes
        analyses = ["MA6 Signal", "Vortex Indicator", "Volume Analysis", "Trend Analyzer"]
        
        for analysis in analyses:
            box_frame = tk.Frame(left_frame, bg='#3d3d3d', relief='raised', bd=1)
            box_frame.pack(fill='x', padx=10, pady=5)
            
            name_label = tk.Label(box_frame, text=analysis, font=('Arial', 10, 'bold'), 
                                 fg='white', bg='#3d3d3d')
            name_label.pack(pady=5)
            
            status_label = tk.Label(box_frame, text="Waiting for data...", 
                                   font=('Arial', 9), fg='#888888', bg='#3d3d3d')
            status_label.pack(pady=(0, 5))
            
    def create_center_panel(self, parent):
        """Create center panel for Quotex data"""
        center_frame = tk.Frame(parent, bg='#2d2d2d')
        center_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        # Title
        title_label = tk.Label(center_frame, text="QUOTEX LIVE DATA", 
                              font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2d2d2d')
        title_label.pack(pady=10)
        
        # Data display area
        self.data_display = tk.Text(center_frame, bg='#1a1a1a', fg='#00ff88', 
                                   font=('Courier', 10), height=25, wrap='word')
        self.data_display.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Insert initial message
        self.data_display.insert('end', "Connecting to Chrome Extension...\n")
        self.data_display.insert('end', "Waiting for Quotex data...\n\n")
        
    def create_right_panel(self, parent):
        """Create right panel - matching left panel style"""
        right_frame = tk.Frame(parent, bg='#2d2d2d', width=350)
        right_frame.pack(side='right', fill='y', padx=(5, 0))
        right_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(right_frame, text="TRADING SYSTEMS", 
                              font=('Arial', 12, 'bold'), fg='#00ff88', bg='#2d2d2d')
        title_label.pack(pady=10)
        
        # Trading system boxes - same style as left
        systems = ["Auto Trade", "Signal Scanner", "Risk Manager", "Performance"]
        
        for system in systems:
            box_frame = tk.Frame(right_frame, bg='#3d3d3d', relief='raised', bd=1)
            box_frame.pack(fill='x', padx=10, pady=5)
            
            name_label = tk.Label(box_frame, text=system, font=('Arial', 10, 'bold'), 
                                 fg='white', bg='#3d3d3d')
            name_label.pack(pady=5)
            
            status_label = tk.Label(box_frame, text="Ready", 
                                   font=('Arial', 9), fg='#888888', bg='#3d3d3d')
            status_label.pack(pady=(0, 5))
            
    def create_bottom_status(self, parent):
        """Create bottom status bar"""
        status_frame = tk.Frame(parent, bg='#2d2d2d', height=40)
        status_frame.pack(fill='x', pady=(10, 0))
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="Status: Connecting to Chrome Extension...", 
                                    font=('Arial', 10), fg='#9966ff', bg='#2d2d2d')
        self.status_label.pack(side='left', padx=20, pady=10)
        
        self.connection_label = tk.Label(status_frame, text="Chrome Extension: Disconnected", 
                                        font=('Arial', 10), fg='#ff6666', bg='#2d2d2d')
        self.connection_label.pack(side='right', padx=20, pady=10)
        
    def start_data_connection(self):
        """Start connection to existing Chrome Extension"""
        def connect_loop():
            while True:
                try:
                    # Try to connect to existing Real Data Server
                    response = requests.get("http://localhost:8765/status", timeout=2)
                    if response.status_code == 200:
                        data = response.json()
                        self.update_ui_with_data(data)
                        self.update_status("Connected", True)
                    else:
                        self.update_status("Server Error", False)
                except requests.exceptions.ConnectionError:
                    self.update_status("Connecting...", False)
                except Exception as e:
                    self.update_status(f"Error: {str(e)[:30]}", False)
                
                time.sleep(2)
        
        # Start connection thread
        connection_thread = threading.Thread(target=connect_loop, daemon=True)
        connection_thread.start()
        
    def update_ui_with_data(self, data):
        """Update UI with received data"""
        def update():
            # Update header live data
            balance = data.get('balance', 'N/A')
            asset = data.get('currentAsset', 'N/A')
            price = data.get('currentPrice', 'N/A')
            
            live_text = f"Balance: {balance} | Asset: {asset} | Price: {price}"
            self.live_data_label.config(text=live_text)
            
            # Update data display
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.data_display.insert('end', f"[{timestamp}] Data received:\n")
            self.data_display.insert('end', f"Balance: {balance}\n")
            self.data_display.insert('end', f"Asset: {asset}\n")
            self.data_display.insert('end', f"Price: {price}\n")
            self.data_display.insert('end', f"Source: {data.get('source', 'Unknown')}\n\n")
            
            # Auto-scroll to bottom
            self.data_display.see('end')
            
        self.root.after(0, update)
        
    def update_status(self, message, connected):
        """Update status labels"""
        def update():
            self.status_label.config(text=f"Status: {message}")
            
            if connected:
                self.connection_label.config(text="Chrome Extension: Connected", fg='#00ff88')
            else:
                self.connection_label.config(text="Chrome Extension: Disconnected", fg='#ff6666')
                
        self.root.after(0, update)
        
    def run(self):
        """Run the application"""
        print("Starting VIP BIG BANG ULTIMATE - Simple New UI")
        print("Connecting to existing Chrome Extension...")
        self.root.mainloop()

if __name__ == "__main__":
    app = VIPSimpleNewUI()
    app.run()
