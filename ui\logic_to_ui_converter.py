"""
🔌 VIP BIG BANG - Logic to UI Converter
تبدیل منطق تحلیلی به ویجت‌های UI
"""

import sys
from pathlib import Path
from typing import Dict, Any, List
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Add core to path
sys.path.append(str(Path(__file__).parent.parent))

class LogicToUIConverter:
    """تبدیل کننده منطق به UI"""
    
    def __init__(self):
        self.signal_widgets = {}
        self.analysis_widgets = {}
        self.data_connections = {}
        
    def analyze_logic_output(self, logic_function):
        """تحلیل خروجی تابع منطقی"""
        try:
            # اجرای تابع برای دریافت نمونه خروجی
            sample_output = logic_function()
            
            analysis = {
                "type": type(sample_output).__name__,
                "structure": self._analyze_structure(sample_output),
                "ui_suggestion": self._suggest_ui_widget(sample_output),
                "display_format": self._suggest_display_format(sample_output)
            }
            
            return analysis
            
        except Exception as e:
            return {
                "type": "error",
                "error": str(e),
                "ui_suggestion": "QLabel",
                "display_format": "text"
            }
    
    def _analyze_structure(self, data):
        """تحلیل ساختار داده"""
        if isinstance(data, dict):
            return {
                "keys": list(data.keys()),
                "value_types": {k: type(v).__name__ for k, v in data.items()}
            }
        elif isinstance(data, list):
            return {
                "length": len(data),
                "item_type": type(data[0]).__name__ if data else "unknown"
            }
        elif isinstance(data, (int, float)):
            return {"numeric": True, "value": data}
        elif isinstance(data, str):
            return {"text": True, "length": len(data)}
        else:
            return {"custom": True, "type": type(data).__name__}
    
    def _suggest_ui_widget(self, data):
        """پیشنهاد ویجت UI مناسب"""
        if isinstance(data, dict):
            if "signal" in str(data).lower():
                return "SignalWidget"
            elif "chart" in str(data).lower():
                return "ChartWidget"
            else:
                return "InfoCardWidget"
        elif isinstance(data, list):
            return "ListWidget"
        elif isinstance(data, (int, float)):
            return "NumericDisplayWidget"
        elif isinstance(data, str):
            return "TextDisplayWidget"
        else:
            return "CustomWidget"
    
    def _suggest_display_format(self, data):
        """پیشنهاد فرمت نمایش"""
        if isinstance(data, dict):
            if "direction" in data:
                return "signal_indicator"
            elif "value" in data:
                return "metric_card"
            else:
                return "info_panel"
        elif isinstance(data, (int, float)):
            return "numeric_display"
        elif isinstance(data, str):
            return "text_label"
        else:
            return "custom_display"

class VIPSignalWidget(QFrame):
    """ویجت نمایش سیگنال"""
    
    def __init__(self, signal_name="Signal", parent=None):
        super().__init__(parent)
        self.signal_name = signal_name
        self.current_signal = None
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        self.setFixedSize(200, 100)
        self.setFrameStyle(QFrame.Shape.Box)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # Header
        self.header_label = QLabel(self.signal_name)
        self.header_label.setStyleSheet("""
            font-size: 12px;
            font-weight: bold;
            color: #4A90E2;
        """)
        layout.addWidget(self.header_label)
        
        # Signal Display
        self.signal_label = QLabel("No Signal")
        self.signal_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.signal_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
            padding: 5px;
        """)
        layout.addWidget(self.signal_label)
        
        # Confidence Bar
        self.confidence_bar = QProgressBar()
        self.confidence_bar.setRange(0, 100)
        self.confidence_bar.setValue(0)
        self.confidence_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #666;
                border-radius: 3px;
                background: rgba(0,0,0,0.3);
                height: 8px;
            }
            QProgressBar::chunk {
                background: #7ED321;
                border-radius: 2px;
            }
        """)
        layout.addWidget(self.confidence_bar)
        
        # Default styling
        self.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.7);
                border: 2px solid #666;
                border-radius: 10px;
            }
        """)
    
    def update_signal(self, signal_data):
        """به‌روزرسانی سیگنال"""
        self.current_signal = signal_data
        
        if isinstance(signal_data, dict):
            # Extract signal info
            direction = signal_data.get("direction", "NONE")
            confidence = signal_data.get("confidence", 0)
            
            # Update display
            self.signal_label.setText(direction)
            self.confidence_bar.setValue(int(confidence))
            
            # Update colors based on direction
            if direction == "CALL":
                color = "#7ED321"  # Green
                self.signal_label.setStyleSheet(f"""
                    font-size: 16px;
                    font-weight: bold;
                    color: white;
                    background: {color};
                    border-radius: 5px;
                    padding: 5px;
                """)
                self.setStyleSheet(f"""
                    QFrame {{
                        background: rgba(0,0,0,0.7);
                        border: 2px solid {color};
                        border-radius: 10px;
                    }}
                """)
            elif direction == "PUT":
                color = "#D0021B"  # Red
                self.signal_label.setStyleSheet(f"""
                    font-size: 16px;
                    font-weight: bold;
                    color: white;
                    background: {color};
                    border-radius: 5px;
                    padding: 5px;
                """)
                self.setStyleSheet(f"""
                    QFrame {{
                        background: rgba(0,0,0,0.7);
                        border: 2px solid {color};
                        border-radius: 10px;
                    }}
                """)
            else:
                # No signal
                self.signal_label.setStyleSheet("""
                    font-size: 16px;
                    font-weight: bold;
                    color: white;
                    background: rgba(0,0,0,0.3);
                    border-radius: 5px;
                    padding: 5px;
                """)
                self.setStyleSheet("""
                    QFrame {
                        background: rgba(0,0,0,0.7);
                        border: 2px solid #666;
                        border-radius: 10px;
                    }
                """)

class VIPMetricWidget(QFrame):
    """ویجت نمایش متریک"""
    
    def __init__(self, metric_name="Metric", icon="📊", parent=None):
        super().__init__(parent)
        self.metric_name = metric_name
        self.icon = icon
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        self.setFixedSize(150, 80)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(3)
        
        # Header
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet("font-size: 16px;")
        header_layout.addWidget(icon_label)
        
        name_label = QLabel(self.metric_name)
        name_label.setStyleSheet("""
            font-size: 10px;
            color: rgba(255,255,255,0.8);
        """)
        header_layout.addWidget(name_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Value
        self.value_label = QLabel("0")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #4A90E2;
        """)
        layout.addWidget(self.value_label)
        
        # Styling
        self.setStyleSheet("""
            QFrame {
                background: rgba(0,0,0,0.6);
                border: 1px solid #4A90E2;
                border-radius: 8px;
            }
        """)
    
    def update_value(self, value, color="#4A90E2"):
        """به‌روزرسانی مقدار"""
        if isinstance(value, (int, float)):
            if isinstance(value, float):
                display_value = f"{value:.2f}"
            else:
                display_value = str(value)
        else:
            display_value = str(value)
        
        self.value_label.setText(display_value)
        self.value_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: bold;
            color: {color};
        """)

class VIPAnalysisPanel(QWidget):
    """پنل نمایش تحلیل‌ها"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.analysis_widgets = {}
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(10)
        
        # Header
        header = QLabel("🔍 تحلیل‌های VIP BIG BANG")
        header.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #00BCD4;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
        """)
        self.main_layout.addWidget(header)
        
        # Scroll area for analyses
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
        """)
        
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_layout.setSpacing(5)
        
        scroll_area.setWidget(self.scroll_widget)
        self.main_layout.addWidget(scroll_area)
    
    def add_analysis_widget(self, name, widget):
        """افزودن ویجت تحلیل"""
        self.analysis_widgets[name] = widget
        self.scroll_layout.addWidget(widget)
    
    def update_analysis(self, name, data):
        """به‌روزرسانی تحلیل"""
        if name in self.analysis_widgets:
            widget = self.analysis_widgets[name]
            if hasattr(widget, 'update_signal'):
                widget.update_signal(data)
            elif hasattr(widget, 'update_value'):
                widget.update_value(data)

class UILogicConnector:
    """اتصال‌دهنده منطق به UI"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.connections = {}
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_all_widgets)
        
    def connect_logic_to_widget(self, logic_function, widget, update_method="update_signal"):
        """اتصال تابع منطقی به ویجت"""
        connection_id = f"{logic_function.__name__}_{id(widget)}"
        self.connections[connection_id] = {
            "logic": logic_function,
            "widget": widget,
            "method": update_method
        }
    
    def start_updates(self, interval_ms=1000):
        """شروع به‌روزرسانی‌های خودکار"""
        self.update_timer.start(interval_ms)
    
    def stop_updates(self):
        """توقف به‌روزرسانی‌ها"""
        self.update_timer.stop()
    
    def update_all_widgets(self):
        """به‌روزرسانی همه ویجت‌ها"""
        for connection_id, connection in self.connections.items():
            try:
                # اجرای تابع منطقی
                data = connection["logic"]()
                
                # به‌روزرسانی ویجت
                widget = connection["widget"]
                method_name = connection["method"]
                
                if hasattr(widget, method_name):
                    method = getattr(widget, method_name)
                    method(data)
                    
            except Exception as e:
                print(f"❌ Error updating {connection_id}: {e}")

# Demo usage
def demo_logic_to_ui():
    """نمایش تبدیل منطق به UI"""
    
    # Sample logic functions
    def get_ma6_signal():
        import random
        return {
            "direction": random.choice(["CALL", "PUT", "NONE"]),
            "confidence": random.randint(60, 95),
            "strength": random.uniform(0.5, 1.0)
        }
    
    def get_volume_data():
        import random
        return random.randint(1000000, 5000000)
    
    def get_vortex_signal():
        import random
        return {
            "direction": random.choice(["CALL", "PUT"]),
            "confidence": random.randint(70, 98)
        }
    
    app = QApplication(sys.argv)
    
    # Main window
    window = QWidget()
    window.setWindowTitle("🔌 Logic to UI Demo")
    window.setGeometry(100, 100, 800, 600)
    window.setStyleSheet("""
        QWidget {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1A1A2E, stop:1 #0F3460);
            color: white;
        }
    """)
    
    layout = QHBoxLayout(window)
    
    # Left panel - Signals
    signals_layout = QVBoxLayout()
    
    ma6_widget = VIPSignalWidget("MA6 Analysis")
    vortex_widget = VIPSignalWidget("Vortex Analysis")
    
    signals_layout.addWidget(ma6_widget)
    signals_layout.addWidget(vortex_widget)
    signals_layout.addStretch()
    
    layout.addLayout(signals_layout)
    
    # Right panel - Metrics
    metrics_layout = QVBoxLayout()
    
    volume_widget = VIPMetricWidget("Volume", "📊")
    
    metrics_layout.addWidget(volume_widget)
    metrics_layout.addStretch()
    
    layout.addLayout(metrics_layout)
    
    # Connect logic to UI
    connector = UILogicConnector(window)
    connector.connect_logic_to_widget(get_ma6_signal, ma6_widget)
    connector.connect_logic_to_widget(get_vortex_signal, vortex_widget)
    connector.connect_logic_to_widget(get_volume_data, volume_widget, "update_value")
    
    # Start updates
    connector.start_updates(2000)  # Every 2 seconds
    
    window.show()
    return app.exec()

if __name__ == "__main__":
    demo_logic_to_ui()
