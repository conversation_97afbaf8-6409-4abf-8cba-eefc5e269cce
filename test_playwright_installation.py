#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Playwright Installation Test
تست نصب Playwright browsers
"""

from playwright.sync_api import sync_playwright
import sys

def test_playwright_browsers():
    """تست تمام browsers نصب شده"""
    print("🚀 شروع تست Playwright browsers...")
    
    with sync_playwright() as p:
        browsers_to_test = [
            ('chromium', p.chromium),
            ('firefox', p.firefox),
            ('webkit', p.webkit)
        ]
        
        successful_browsers = []
        failed_browsers = []
        
        for browser_name, browser_type in browsers_to_test:
            try:
                print(f"📱 تست {browser_name}...")
                browser = browser_type.launch(headless=True)
                page = browser.new_page()
                page.goto("https://www.google.com")
                title = page.title()
                print(f"✅ {browser_name}: موفق - عنوان صفحه: {title}")
                browser.close()
                successful_browsers.append(browser_name)
            except Exception as e:
                print(f"❌ {browser_name}: خطا - {str(e)}")
                failed_browsers.append(browser_name)
        
        print("\n" + "="*50)
        print("📊 نتایج تست:")
        print(f"✅ موفق: {len(successful_browsers)} browser")
        for browser in successful_browsers:
            print(f"   - {browser}")
        
        if failed_browsers:
            print(f"❌ ناموفق: {len(failed_browsers)} browser")
            for browser in failed_browsers:
                print(f"   - {browser}")
        else:
            print("🎉 همه browsers با موفقیت نصب و تست شدند!")
        
        return len(failed_browsers) == 0

if __name__ == "__main__":
    success = test_playwright_browsers()
    sys.exit(0 if success else 1)
