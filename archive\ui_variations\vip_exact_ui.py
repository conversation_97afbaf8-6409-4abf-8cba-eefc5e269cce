#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎮 VIP BIG BANG - Exact UI Recreation
بازسازی دقیق UI مطابق تصویر
"""

import sys
import os
import random
import math
from datetime import datetime
from typing import Dict, List, Optional

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

# Set Qt environment
os.environ['QT_QPA_PLATFORM'] = 'windows'

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Import advanced styles
from vip_styles import (
    GAMING_COLORS, GRADIENTS, COMPLETE_STYLESHEET,
    get_gaming_style, get_button_style, get_gaming_icon
)

class ModernCard(QFrame):
    """
    💜 Modern Purple Card Component
    کامپوننت کارت مدرن بنفش
    """
    
    clicked = Signal()
    
    def __init__(self, title="", icon="", content_widget=None, width=200, height=150):
        super().__init__()
        self.title = title
        self.icon = icon
        self.content_widget = content_widget
        
        self.setFixedSize(width, height)
        self.setup_ui()
    
    def setup_ui(self):
        """تنظیم UI کارت"""
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 0.9),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(147, 51, 234, 0.6);
                border-radius: 20px;
                padding: 15px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 1.0),
                    stop:1 rgba(124, 58, 237, 0.9));
                border: 2px solid rgba(147, 51, 234, 0.8);
            }
        """)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Title with icon
        if self.title:
            title_layout = QHBoxLayout()
            
            if self.icon:
                icon_label = QLabel(self.icon)
                icon_label.setFont(QFont("Segoe UI Emoji", 16))
                icon_label.setStyleSheet("color: white; background: transparent;")
                title_layout.addWidget(icon_label)
            
            title_label = QLabel(self.title)
            title_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
            title_label.setStyleSheet("color: white; background: transparent;")
            title_layout.addWidget(title_label)
            
            title_layout.addStretch()
            layout.addLayout(title_layout)
        
        # Content
        if self.content_widget:
            layout.addWidget(self.content_widget)
        
        layout.addStretch()
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(124, 58, 237, 100))
        shadow.setOffset(0, 8)
        self.setGraphicsEffect(shadow)
        
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def mousePressEvent(self, event):
        """کلیک موس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

class ModernButton(QPushButton):
    """
    🎮 Modern Button Component
    دکمه مدرن
    """
    
    def __init__(self, text="", style="primary", size="normal"):
        super().__init__(text)
        self.button_style = style
        self.button_size = size
        self.setup_style()
    
    def setup_style(self):
        """تنظیم استایل"""
        if self.button_size == "large":
            padding = "15px 30px"
            font_size = "16px"
            border_radius = "25px"
        elif self.button_size == "small":
            padding = "8px 16px"
            font_size = "12px"
            border_radius = "15px"
        else:  # normal
            padding = "12px 24px"
            font_size = "14px"
            border_radius = "20px"
        
        if self.button_style == "buy":
            self.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: none;
                    border-radius: {border_radius};
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: bold;
                    font-family: 'Segoe UI', Arial, sans-serif;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #34d399, stop:1 #10b981);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #059669, stop:1 #047857);
                }}
            """)
        elif self.button_style == "sell":
            self.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #ef4444, stop:1 #dc2626);
                    color: white;
                    border: none;
                    border-radius: {border_radius};
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: bold;
                    font-family: 'Segoe UI', Arial, sans-serif;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #f87171, stop:1 #ef4444);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #dc2626, stop:1 #b91c1c);
                }}
            """)
        else:  # default purple
            self.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(88, 28, 135, 0.9));
                    color: white;
                    border: 2px solid rgba(147, 51, 234, 0.6);
                    border-radius: {border_radius};
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: bold;
                    font-family: 'Segoe UI', Arial, sans-serif;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(147, 51, 234, 0.9),
                        stop:1 rgba(124, 58, 237, 1.0));
                    border: 2px solid rgba(147, 51, 234, 0.8);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(88, 28, 135, 1.0),
                        stop:1 rgba(67, 20, 102, 1.0));
                }}
            """)
        
        self.setCursor(Qt.CursorShape.PointingHandCursor)

class ToggleSwitch(QWidget):
    """
    🔄 Toggle Switch Component
    کلید تغییر وضعیت
    """
    
    toggled = Signal(bool)
    
    def __init__(self, checked=False):
        super().__init__()
        self.checked = checked
        self.setFixedSize(60, 30)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def paintEvent(self, event):
        """رسم کلید"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Background
        if self.checked:
            painter.setBrush(QBrush(QColor(34, 197, 94)))  # Green
        else:
            painter.setBrush(QBrush(QColor(107, 114, 128)))  # Gray
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(0, 0, 60, 30, 15, 15)
        
        # Circle
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        if self.checked:
            painter.drawEllipse(32, 3, 24, 24)
        else:
            painter.drawEllipse(4, 3, 24, 24)
    
    def mousePressEvent(self, event):
        """کلیک موس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.checked = not self.checked
            self.toggled.emit(self.checked)
            self.update()
        super().mousePressEvent(event)

class ProgressBar(QWidget):
    """
    📊 Custom Progress Bar
    نوار پیشرفت سفارشی
    """
    
    def __init__(self, value=0, max_value=100, color="#10b981"):
        super().__init__()
        self.value = value
        self.max_value = max_value
        self.color = color
        self.setFixedHeight(8)
    
    def setValue(self, value):
        """تنظیم مقدار"""
        self.value = max(0, min(self.max_value, value))
        self.update()
    
    def paintEvent(self, event):
        """رسم نوار پیشرفت"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Background
        painter.setBrush(QBrush(QColor(55, 65, 81)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(0, 0, self.width(), self.height(), 4, 4)
        
        # Progress
        if self.value > 0:
            progress_width = int(self.width() * self.value / self.max_value)
            painter.setBrush(QBrush(QColor(self.color)))
            painter.drawRoundedRect(0, 0, progress_width, self.height(), 4, 4)

class VIPExactUI(QMainWindow):
    """
    🎮 VIP BIG BANG - Exact UI Recreation
    بازسازی دقیق UI
    """
    
    def __init__(self):
        super().__init__()
        
        # Window setup
        self.setWindowTitle("🎮 VIP BIG BANG")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        
        # Apply exact theme
        self.apply_exact_theme()
        
        # Setup UI exactly like image
        self.setup_exact_ui()
        
        # Setup data and timers
        self.setup_data_systems()
        
        print("🎮 VIP Exact UI launched!")
    
    def apply_exact_theme(self):
        """اعمال تم دقیق مطابق تصویر"""
        # Apply advanced gaming theme
        self.setStyleSheet(get_gaming_style())
    
    def setup_exact_ui(self):
        """راه‌اندازی UI دقیق مطابق تصویر"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Top header
        self.create_top_header(main_layout)
        
        # Main content area
        self.create_main_content(main_layout)
    
    def create_top_header(self, layout):
        """ایجاد هدر بالایی"""
        header_layout = QHBoxLayout()
        
        # Left: Avatar and title
        left_section = QHBoxLayout()
        
        # Avatar
        avatar_frame = QFrame()
        avatar_frame.setFixedSize(60, 60)
        avatar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(88, 28, 135, 0.9));
                border: 2px solid rgba(147, 51, 234, 0.6);
                border-radius: 30px;
            }
        """)
        
        avatar_layout = QVBoxLayout(avatar_frame)
        avatar_layout.setContentsMargins(0, 0, 0, 0)
        
        avatar_icon = QLabel("😊")
        avatar_icon.setFont(QFont("Segoe UI Emoji", 24))
        avatar_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar_icon.setStyleSheet("background: transparent;")
        avatar_layout.addWidget(avatar_icon)
        
        left_section.addWidget(avatar_frame)
        
        # Title section
        title_section = QVBoxLayout()
        
        manual_trad_label = QLabel("Manual Trad")
        manual_trad_label.setFont(QFont("Segoe UI", 10))
        manual_trad_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        title_section.addWidget(manual_trad_label)
        
        vip_title = QLabel("VIP BIG BANG")
        vip_title.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        vip_title.setStyleSheet("color: white;")
        title_section.addWidget(vip_title)
        
        left_section.addLayout(title_section)
        
        header_layout.addLayout(left_section)
        
        # Center: Currency pairs
        center_section = QHBoxLayout()
        
        pairs = [
            ("✅ BUG/USD", True),
            ("GBP/USD", False),
            ("EUR/JPY", False),
            ("LIVE", False)
        ]
        
        for pair, active in pairs:
            pair_btn = ModernButton(pair, size="small")
            if active:
                pair_btn.setStyleSheet(pair_btn.styleSheet() + """
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #10b981, stop:1 #059669);
                    }
                """)
            center_section.addWidget(pair_btn)
        
        header_layout.addLayout(center_section)
        
        # Right: Mode buttons and Buy/Sell
        right_section = QHBoxLayout()
        
        # Mode buttons
        mode_layout = QHBoxLayout()
        
        otc_btn = ModernButton("OTC", size="small")
        live_btn = ModernButton("LIVE", size="small")
        demo_btn = ModernButton("DEMO", size="small")
        
        mode_layout.addWidget(otc_btn)
        mode_layout.addWidget(live_btn)
        mode_layout.addWidget(demo_btn)
        
        right_section.addLayout(mode_layout)
        
        # Buy/Sell buttons
        buy_sell_layout = QVBoxLayout()
        
        buy_label = QLabel("BUY")
        buy_label.setFont(QFont("Segoe UI", 10))
        buy_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        buy_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        buy_sell_layout.addWidget(buy_label)
        
        buttons_row = QHBoxLayout()
        
        self.buy_btn = ModernButton("BUY", style="buy", size="normal")
        self.sell_btn = ModernButton("SELL", style="sell", size="normal")
        
        buttons_row.addWidget(self.buy_btn)
        buttons_row.addWidget(self.sell_btn)
        
        buy_sell_layout.addLayout(buttons_row)
        right_section.addLayout(buy_sell_layout)
        
        header_layout.addLayout(right_section)
        
        layout.addLayout(header_layout)
    
    def create_main_content(self, layout):
        """ایجاد محتوای اصلی"""
        content_layout = QHBoxLayout()
        
        # Left panel
        left_panel = self.create_left_panel()
        content_layout.addWidget(left_panel)
        
        # Center panel (Chart)
        center_panel = self.create_center_panel()
        content_layout.addWidget(center_panel)
        
        # Right panel
        right_panel = self.create_right_panel()
        content_layout.addWidget(right_panel)
        
        # Set proportions
        content_layout.setStretch(0, 1)  # Left: 25%
        content_layout.setStretch(1, 2)  # Center: 50%
        content_layout.setStretch(2, 1)  # Right: 25%
        
        layout.addLayout(content_layout)

    def create_left_panel(self):
        """ایجاد پنل چپ"""
        panel = QWidget()
        panel.setFixedWidth(250)
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # Manual Trading card
        manual_content = QWidget()
        manual_layout = QVBoxLayout(manual_content)
        manual_layout.setContentsMargins(0, 0, 0, 0)

        manual_trading_label = QLabel("Manual Trading")
        manual_trading_label.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        manual_trading_label.setStyleSheet("color: white;")
        manual_layout.addWidget(manual_trading_label)

        # Toggle switch
        self.manual_toggle = ToggleSwitch(True)
        manual_layout.addWidget(self.manual_toggle)

        manual_card = ModernCard("", "🖱️", manual_content, 220, 120)
        layout.addWidget(manual_card)

        # Account Summary card
        account_content = QWidget()
        account_layout = QVBoxLayout(account_content)
        account_layout.setContentsMargins(0, 0, 0, 0)

        account_label = QLabel("Account Summary")
        account_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        account_label.setStyleSheet("color: white;")
        account_layout.addWidget(account_label)

        self.balance_label = QLabel("$1251,76")
        self.balance_label.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        self.balance_label.setStyleSheet("color: #10b981;")
        account_layout.addWidget(self.balance_label)

        account_card = ModernCard("", "", account_content, 220, 100)
        layout.addWidget(account_card)

        # AutoTrade card
        autotrade_content = QWidget()
        autotrade_layout = QVBoxLayout(autotrade_content)
        autotrade_layout.setContentsMargins(0, 0, 0, 0)

        autotrade_label = QLabel("AutoTrade ON")
        autotrade_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        autotrade_label.setStyleSheet("color: #10b981;")
        autotrade_layout.addWidget(autotrade_label)

        # Trade stats
        stats_layout = QHBoxLayout()

        trade_label = QLabel("Trade $")
        trade_label.setFont(QFont("Segoe UI", 10))
        trade_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        stats_layout.addWidget(trade_label)

        trade_value = QLabel("+5")
        trade_value.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        trade_value.setStyleSheet("color: #10b981;")
        stats_layout.addWidget(trade_value)

        autotrade_layout.addLayout(stats_layout)

        profit_layout = QHBoxLayout()

        profit_label = QLabel("Profit / Loss")
        profit_label.setFont(QFont("Segoe UI", 10))
        profit_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        profit_layout.addWidget(profit_label)

        profit_value = QLabel("+10")
        profit_value.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        profit_value.setStyleSheet("color: #10b981;")
        profit_layout.addWidget(profit_value)

        autotrade_layout.addLayout(profit_layout)

        autotrade_card = ModernCard("", "", autotrade_content, 220, 120)
        layout.addWidget(autotrade_card)

        # PulseBar card
        pulsebar_content = QWidget()
        pulsebar_layout = QVBoxLayout(pulsebar_content)
        pulsebar_layout.setContentsMargins(0, 0, 0, 0)

        pulsebar_label = QLabel("PulseBar")
        pulsebar_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        pulsebar_label.setStyleSheet("color: white;")
        pulsebar_layout.addWidget(pulsebar_label)

        # Color bars
        colors = ["#ef4444", "#f97316", "#eab308", "#22c55e"]
        for color in colors:
            bar = ProgressBar(random.randint(60, 90), 100, color)
            pulsebar_layout.addWidget(bar)

        pulsebar_card = ModernCard("", "", pulsebar_content, 220, 140)
        layout.addWidget(pulsebar_card)

        # Economic News card
        news_content = QWidget()
        news_layout = QVBoxLayout(news_content)
        news_layout.setContentsMargins(0, 0, 0, 0)

        news_label = QLabel("Economic News")
        news_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        news_label.setStyleSheet("color: white;")
        news_layout.addWidget(news_label)

        # News icon
        news_icon = QLabel("📊")
        news_icon.setFont(QFont("Segoe UI Emoji", 24))
        news_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        news_layout.addWidget(news_icon)

        news_card = ModernCard("", "", news_content, 220, 100)
        layout.addWidget(news_card)

        layout.addStretch()
        return panel

    def create_center_panel(self):
        """ایجاد پنل مرکزی (چارت)"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # Chart area
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 0.9),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(147, 51, 234, 0.6);
                border-radius: 20px;
                padding: 20px;
            }
        """)

        chart_layout = QVBoxLayout(chart_frame)

        # Price display
        price_layout = QHBoxLayout()

        # Bell icon
        bell_icon = QLabel("🔔")
        bell_icon.setFont(QFont("Segoe UI Emoji", 20))
        price_layout.addWidget(bell_icon)

        price_layout.addStretch()

        # Current price
        self.current_price = QLabel("1.07329")
        self.current_price.setFont(QFont("Segoe UI", 24, QFont.Weight.Bold))
        self.current_price.setStyleSheet("color: white; background: rgba(59, 130, 246, 0.8); padding: 8px 16px; border-radius: 10px;")
        price_layout.addWidget(self.current_price)

        price_layout.addStretch()

        # Price levels
        levels_layout = QVBoxLayout()

        levels = ["1.07325", "1.07320", "1.07320", "1.07330"]
        for level in levels:
            level_label = QLabel(level)
            level_label.setFont(QFont("Segoe UI", 10))
            level_label.setStyleSheet("color: rgba(255,255,255,0.7);")
            level_label.setAlignment(Qt.AlignmentFlag.AlignRight)
            levels_layout.addWidget(level_label)

        price_layout.addLayout(levels_layout)

        chart_layout.addLayout(price_layout)

        # Chart simulation area
        chart_sim = QLabel("📈 LIVE CHART SIMULATION")
        chart_sim.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        chart_sim.setStyleSheet("color: rgba(255,255,255,0.8);")
        chart_sim.setAlignment(Qt.AlignmentFlag.AlignCenter)
        chart_sim.setFixedHeight(200)
        chart_layout.addWidget(chart_sim)

        # VORTEX indicator
        vortex_layout = QHBoxLayout()

        vortex_label = QLabel("VORTEX")
        vortex_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        vortex_label.setStyleSheet("color: white;")
        vortex_layout.addWidget(vortex_label)

        vortex_layout.addStretch()

        vortex_value = QLabel("0.0436")
        vortex_value.setFont(QFont("Segoe UI", 12))
        vortex_value.setStyleSheet("color: rgba(255,255,255,0.7);")
        vortex_layout.addWidget(vortex_value)

        chart_layout.addLayout(vortex_layout)

        # Vortex wave simulation
        vortex_wave = QLabel("〰️〰️〰️〰️〰️")
        vortex_wave.setFont(QFont("Segoe UI Emoji", 16))
        vortex_wave.setAlignment(Qt.AlignmentFlag.AlignCenter)
        vortex_wave.setStyleSheet("color: #3b82f6;")
        chart_layout.addWidget(vortex_wave)

        layout.addWidget(chart_frame)

        # Bottom signals area
        signals_frame = QFrame()
        signals_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 0.9),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(147, 51, 234, 0.6);
                border-radius: 20px;
                padding: 15px;
            }
        """)

        signals_layout = QHBoxLayout(signals_frame)

        # Economic News icon
        econ_icon = QLabel("🎯")
        econ_icon.setFont(QFont("Segoe UI Emoji", 24))
        signals_layout.addWidget(econ_icon)

        # Live Signals
        live_signals_layout = QVBoxLayout()

        live_signals_label = QLabel("LIVE SIGNALS")
        live_signals_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        live_signals_label.setStyleSheet("color: white;")
        live_signals_layout.addWidget(live_signals_label)

        buy_layout = QHBoxLayout()
        buy_signal_label = QLabel("BUY")
        buy_signal_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        buy_signal_label.setStyleSheet("color: #10b981;")
        buy_layout.addWidget(buy_signal_label)

        buy_percent = QLabel("71%")
        buy_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        buy_percent.setStyleSheet("color: #10b981;")
        buy_layout.addWidget(buy_percent)

        live_signals_layout.addLayout(buy_layout)

        sell_layout = QHBoxLayout()
        sell_signal_label = QLabel("71%")
        sell_signal_label.setFont(QFont("Segoe UI", 10))
        sell_signal_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        sell_layout.addWidget(sell_signal_label)

        sell_percent = QLabel("29%")
        sell_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        sell_percent.setStyleSheet("color: #f97316;")
        sell_layout.addWidget(sell_percent)

        live_signals_layout.addLayout(sell_layout)

        signals_layout.addLayout(live_signals_layout)

        # Buyer/Seller Power
        power_layout = QVBoxLayout()

        power_label = QLabel("Buyer/Seller Power")
        power_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        power_label.setStyleSheet("color: white;")
        power_layout.addWidget(power_label)

        # Power bar
        power_bar_bg = QFrame()
        power_bar_bg.setFixedHeight(20)
        power_bar_bg.setStyleSheet("""
            QFrame {
                background: #374151;
                border-radius: 10px;
            }
        """)
        power_layout.addWidget(power_bar_bg)

        # Power percentages
        power_percent_layout = QHBoxLayout()

        buyer_percent = QLabel("34%")
        buyer_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        buyer_percent.setStyleSheet("color: #10b981;")
        power_percent_layout.addWidget(buyer_percent)

        power_percent_layout.addStretch()

        seller_percent = QLabel("66%")
        seller_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        seller_percent.setStyleSheet("color: #ef4444;")
        power_percent_layout.addWidget(seller_percent)

        power_layout.addLayout(power_percent_layout)

        signals_layout.addLayout(power_layout)

        # Action icons
        icons_layout = QHBoxLayout()

        action_icons = ["✅", "〰️", "📊", "⚡"]
        for icon in action_icons:
            icon_label = QLabel(icon)
            icon_label.setFont(QFont("Segoe UI Emoji", 20))
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setFixedSize(40, 40)
            icon_label.setStyleSheet("""
                QLabel {
                    background: rgba(255,255,255,0.1);
                    border-radius: 20px;
                    padding: 5px;
                }
            """)
            icons_layout.addWidget(icon_label)

        signals_layout.addLayout(icons_layout)

        layout.addWidget(signals_frame)

        return panel

    def create_right_panel(self):
        """ایجاد پنل راست"""
        panel = QWidget()
        panel.setFixedWidth(250)
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # First row: AutoTrade and Confirm Mode
        row1_layout = QHBoxLayout()

        # AutoTrade card
        autotrade_content = QWidget()
        autotrade_layout = QVBoxLayout(autotrade_content)
        autotrade_layout.setContentsMargins(0, 0, 0, 0)

        autotrade_icon = QLabel("🚀")
        autotrade_icon.setFont(QFont("Segoe UI Emoji", 20))
        autotrade_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        autotrade_layout.addWidget(autotrade_icon)

        autotrade_label = QLabel("AutoTrade")
        autotrade_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        autotrade_label.setStyleSheet("color: white;")
        autotrade_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        autotrade_layout.addWidget(autotrade_label)

        autotrade_card = ModernCard("", "", autotrade_content, 110, 100)
        row1_layout.addWidget(autotrade_card)

        # Confirm Mode card
        confirm_content = QWidget()
        confirm_layout = QVBoxLayout(confirm_content)
        confirm_layout.setContentsMargins(0, 0, 0, 0)

        confirm_icon = QLabel("✅")
        confirm_icon.setFont(QFont("Segoe UI Emoji", 20))
        confirm_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        confirm_layout.addWidget(confirm_icon)

        confirm_label = QLabel("Confirm Mode")
        confirm_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        confirm_label.setStyleSheet("color: white;")
        confirm_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        confirm_layout.addWidget(confirm_label)

        confirm_card = ModernCard("", "", confirm_content, 110, 100)
        row1_layout.addWidget(confirm_card)

        layout.addLayout(row1_layout)

        # Second row: Confirm Mode and Heatmap
        row2_layout = QHBoxLayout()

        # Confirm Mode card (duplicate for layout)
        confirm2_content = QWidget()
        confirm2_layout = QVBoxLayout(confirm2_content)
        confirm2_layout.setContentsMargins(0, 0, 0, 0)

        confirm2_icon = QLabel("🚀")
        confirm2_icon.setFont(QFont("Segoe UI Emoji", 20))
        confirm2_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        confirm2_layout.addWidget(confirm2_icon)

        confirm2_label = QLabel("Confirm Mode")
        confirm2_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        confirm2_label.setStyleSheet("color: white;")
        confirm2_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        confirm2_layout.addWidget(confirm2_label)

        confirm2_card = ModernCard("", "", confirm2_content, 110, 100)
        row2_layout.addWidget(confirm2_card)

        # Heatmap card
        heatmap_content = QWidget()
        heatmap_layout = QVBoxLayout(heatmap_content)
        heatmap_layout.setContentsMargins(0, 0, 0, 0)

        heatmap_icon = QLabel("🔥")
        heatmap_icon.setFont(QFont("Segoe UI Emoji", 20))
        heatmap_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        heatmap_layout.addWidget(heatmap_icon)

        heatmap_label = QLabel("Heatmap")
        heatmap_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        heatmap_label.setStyleSheet("color: white;")
        heatmap_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        heatmap_layout.addWidget(heatmap_label)

        heatmap_card = ModernCard("", "", heatmap_content, 110, 100)
        row2_layout.addWidget(heatmap_card)

        layout.addLayout(row2_layout)

        # Third row: Economic News and Can
        row3_layout = QHBoxLayout()

        # Economic News card
        econ_news_content = QWidget()
        econ_news_layout = QVBoxLayout(econ_news_content)
        econ_news_layout.setContentsMargins(0, 0, 0, 0)

        econ_news_icon = QLabel("📊")
        econ_news_icon.setFont(QFont("Segoe UI Emoji", 20))
        econ_news_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        econ_news_layout.addWidget(econ_news_icon)

        econ_news_label = QLabel("Economic News")
        econ_news_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        econ_news_label.setStyleSheet("color: white;")
        econ_news_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        econ_news_layout.addWidget(econ_news_label)

        econ_news_card = ModernCard("", "", econ_news_content, 110, 100)
        row3_layout.addWidget(econ_news_card)

        # Can card
        can_content = QWidget()
        can_layout = QVBoxLayout(can_content)
        can_layout.setContentsMargins(0, 0, 0, 0)

        can_icon = QLabel("😊")
        can_icon.setFont(QFont("Segoe UI Emoji", 20))
        can_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        can_layout.addWidget(can_icon)

        can_label = QLabel("Can")
        can_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        can_label.setStyleSheet("color: white;")
        can_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        can_layout.addWidget(can_label)

        can_card = ModernCard("", "", can_content, 110, 100)
        row3_layout.addWidget(can_card)

        layout.addLayout(row3_layout)

        # Fourth row: Settings and Secures
        row4_layout = QHBoxLayout()

        # Settings card
        settings_content = QWidget()
        settings_layout = QVBoxLayout(settings_content)
        settings_layout.setContentsMargins(0, 0, 0, 0)

        settings_icon = QLabel("⚙️")
        settings_icon.setFont(QFont("Segoe UI Emoji", 20))
        settings_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(settings_icon)

        settings_label = QLabel("Settings")
        settings_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        settings_label.setStyleSheet("color: white;")
        settings_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(settings_label)

        settings_card = ModernCard("", "", settings_content, 110, 100)
        row4_layout.addWidget(settings_card)

        # Secures card
        secures_content = QWidget()
        secures_layout = QVBoxLayout(secures_content)
        secures_layout.setContentsMargins(0, 0, 0, 0)

        secures_icon = QLabel("🔒")
        secures_icon.setFont(QFont("Segoe UI Emoji", 20))
        secures_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        secures_layout.addWidget(secures_icon)

        secures_label = QLabel("Secures")
        secures_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        secures_label.setStyleSheet("color: white;")
        secures_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        secures_layout.addWidget(secures_label)

        secures_card = ModernCard("", "", secures_content, 110, 100)
        row4_layout.addWidget(secures_card)

        layout.addLayout(row4_layout)

        layout.addStretch()
        return panel

    def setup_data_systems(self):
        """راه‌اندازی سیستم‌های داده"""
        # Timer for real-time updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_real_time_data)
        self.update_timer.start(1000)  # Update every second

        # Connect signals
        self.buy_btn.clicked.connect(self.execute_buy_trade)
        self.sell_btn.clicked.connect(self.execute_sell_trade)
        self.manual_toggle.toggled.connect(self.toggle_manual_mode)

        print("✅ Data systems initialized")

    def update_real_time_data(self):
        """به‌روزرسانی داده‌های زمان واقعی"""
        # Update price with small random changes
        current = float(self.current_price.text())
        change = random.uniform(-0.0001, 0.0001)
        new_price = current + change
        self.current_price.setText(f"{new_price:.5f}")

        # Update balance with small random changes
        current_balance = float(self.balance_label.text().replace('$', '').replace(',', ''))
        balance_change = random.uniform(-0.5, 1.0)
        new_balance = current_balance + balance_change
        self.balance_label.setText(f"${new_balance:,.2f}")

        # Change color based on profit/loss
        if balance_change > 0:
            self.balance_label.setStyleSheet("color: #10b981;")
        else:
            self.balance_label.setStyleSheet("color: #ef4444;")

    def execute_buy_trade(self):
        """اجرای معامله خرید"""
        print("🟢 BUY trade executed!")
        # Add trade execution logic here

    def execute_sell_trade(self):
        """اجرای معامله فروش"""
        print("🔴 SELL trade executed!")
        # Add trade execution logic here

    def toggle_manual_mode(self, enabled):
        """تغییر حالت دستی"""
        if enabled:
            print("🖱️ Manual mode enabled")
        else:
            print("🤖 Auto mode enabled")

def main():
    """تابع اصلی"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("VIP BIG BANG")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("VIP Trading")

    # Apply global font
    font = QFont("Segoe UI", 10)
    app.setFont(font)

    # Create and show main window
    window = VIPExactUI()
    window.show()

    # Center window on screen
    screen = app.primaryScreen().geometry()
    window.move(
        (screen.width() - window.width()) // 2,
        (screen.height() - window.height()) // 2
    )

    print("🎮 VIP BIG BANG UI launched successfully!")

    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
