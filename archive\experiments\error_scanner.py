#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 VIP BIG BANG - Error <PERSON>anne<PERSON>
اسکن کامل ارورها و مشکلات سیستم
"""

import os
import sys
import subprocess
import traceback
import json
from pathlib import Path

class VIPErrorScanner:
    """
    🔍 اسکنر ارورهای VIP BIG BANG
    """
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
        
    def log_error(self, message):
        """ثبت ارور"""
        self.errors.append(message)
        print(f"❌ ERROR: {message}")
    
    def log_warning(self, message):
        """ثبت هشدار"""
        self.warnings.append(message)
        print(f"⚠️ WARNING: {message}")
    
    def log_info(self, message):
        """ثبت اطلاعات"""
        self.info.append(message)
        print(f"ℹ️ INFO: {message}")
    
    def check_python_version(self):
        """بررسی نسخه Python"""
        try:
            version = sys.version_info
            if version.major < 3 or (version.major == 3 and version.minor < 8):
                self.log_error(f"Python version too old: {version.major}.{version.minor}")
            else:
                self.log_info(f"Python version OK: {version.major}.{version.minor}.{version.micro}")
        except Exception as e:
            self.log_error(f"Python version check failed: {e}")
    
    def check_required_modules(self):
        """بررسی ماژول‌های مورد نیاز"""
        required_modules = [
            "PySide6",
            "cryptography",
            "requests",
            "json",
            "subprocess",
            "pathlib"
        ]
        
        for module in required_modules:
            try:
                __import__(module)
                self.log_info(f"Module {module} OK")
            except ImportError:
                self.log_error(f"Module {module} not found")
            except Exception as e:
                self.log_error(f"Module {module} error: {e}")
    
    def check_file_structure(self):
        """بررسی ساختار فایل‌ها"""
        required_files = [
            "vip_complete_professional_ui.py",
            "chrome_extension/manifest.json",
            "chrome_extension/background.js",
            "chrome_extension/content.js",
            "simple_extension_installer.py"
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                self.log_info(f"File {file_path} OK")
            else:
                self.log_error(f"File {file_path} missing")
    
    def check_chrome_installation(self):
        """بررسی نصب Chrome"""
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        chrome_found = False
        for path in chrome_paths:
            if os.path.exists(path):
                self.log_info(f"Chrome found: {path}")
                chrome_found = True
                break
        
        if not chrome_found:
            self.log_error("Chrome not found")
    
    def check_extension_files(self):
        """بررسی فایل‌های اکستنشن"""
        extension_dir = Path("chrome_extension")
        
        if not extension_dir.exists():
            self.log_error("Extension directory missing")
            return
        
        # Check manifest.json
        manifest_path = extension_dir / "manifest.json"
        if manifest_path.exists():
            try:
                with open(manifest_path, 'r', encoding='utf-8') as f:
                    manifest = json.load(f)
                
                required_keys = ["name", "version", "manifest_version", "background", "content_scripts"]
                for key in required_keys:
                    if key in manifest:
                        self.log_info(f"Manifest key {key} OK")
                    else:
                        self.log_error(f"Manifest key {key} missing")
                        
            except json.JSONDecodeError as e:
                self.log_error(f"Manifest JSON error: {e}")
            except Exception as e:
                self.log_error(f"Manifest read error: {e}")
        else:
            self.log_error("manifest.json missing")
        
        # Check other files
        extension_files = ["background.js", "content.js", "popup.html", "popup.js"]
        for file in extension_files:
            file_path = extension_dir / file
            if file_path.exists():
                self.log_info(f"Extension file {file} OK")
            else:
                self.log_warning(f"Extension file {file} missing")
    
    def check_ui_syntax(self):
        """بررسی syntax فایل UI"""
        try:
            with open("vip_complete_professional_ui.py", 'r', encoding='utf-8') as f:
                code = f.read()
            
            # Try to compile
            compile(code, "vip_complete_professional_ui.py", "exec")
            self.log_info("UI syntax OK")
            
        except SyntaxError as e:
            self.log_error(f"UI syntax error: {e}")
        except Exception as e:
            self.log_error(f"UI check error: {e}")
    
    def check_permissions(self):
        """بررسی مجوزها"""
        try:
            # Test write permission
            test_file = "test_permission.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            self.log_info("Write permissions OK")
            
        except Exception as e:
            self.log_error(f"Permission error: {e}")
    
    def check_system_resources(self):
        """بررسی منابع سیستم"""
        try:
            import psutil
            
            # Memory
            memory = psutil.virtual_memory()
            if memory.available < 1024 * 1024 * 1024:  # 1GB
                self.log_warning("Low memory available")
            else:
                self.log_info("Memory OK")
            
            # Disk space
            disk = psutil.disk_usage('.')
            if disk.free < 1024 * 1024 * 1024:  # 1GB
                self.log_warning("Low disk space")
            else:
                self.log_info("Disk space OK")
                
        except ImportError:
            self.log_warning("psutil not available - skipping system check")
        except Exception as e:
            self.log_warning(f"System check error: {e}")
    
    def run_full_scan(self):
        """اجرای اسکن کامل"""
        print("="*60)
        print("🔍 VIP BIG BANG Error Scanner")
        print("="*60)
        
        print("\n1️⃣ Checking Python version...")
        self.check_python_version()
        
        print("\n2️⃣ Checking required modules...")
        self.check_required_modules()
        
        print("\n3️⃣ Checking file structure...")
        self.check_file_structure()
        
        print("\n4️⃣ Checking Chrome installation...")
        self.check_chrome_installation()
        
        print("\n5️⃣ Checking extension files...")
        self.check_extension_files()
        
        print("\n6️⃣ Checking UI syntax...")
        self.check_ui_syntax()
        
        print("\n7️⃣ Checking permissions...")
        self.check_permissions()
        
        print("\n8️⃣ Checking system resources...")
        self.check_system_resources()
        
        # Summary
        print("\n" + "="*60)
        print("📊 SCAN RESULTS")
        print("="*60)
        
        print(f"❌ Errors: {len(self.errors)}")
        for error in self.errors:
            print(f"   • {error}")
        
        print(f"\n⚠️ Warnings: {len(self.warnings)}")
        for warning in self.warnings:
            print(f"   • {warning}")
        
        print(f"\nℹ️ Info: {len(self.info)}")
        
        # Overall status
        if len(self.errors) == 0:
            print("\n✅ SYSTEM STATUS: HEALTHY")
            print("🎯 No critical errors found")
        elif len(self.errors) <= 2:
            print("\n⚠️ SYSTEM STATUS: MINOR ISSUES")
            print("🔧 Some issues need attention")
        else:
            print("\n❌ SYSTEM STATUS: CRITICAL ISSUES")
            print("🚨 Multiple errors need fixing")
        
        print("="*60)
        
        return {
            "errors": self.errors,
            "warnings": self.warnings,
            "info": self.info,
            "status": "healthy" if len(self.errors) == 0 else "issues"
        }

def main():
    """اجرای اسکنر"""
    scanner = VIPErrorScanner()
    results = scanner.run_full_scan()
    
    # Save results
    with open("error_scan_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: error_scan_results.json")

if __name__ == "__main__":
    main()
