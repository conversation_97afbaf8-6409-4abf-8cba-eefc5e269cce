"""
🎯 VIP BIG BANG - Simple Dashboard Launcher
Launch the clean and simple trading interface
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QPalette, QColor

# Add paths
sys.path.append(str(Path(__file__).parent))

# Import simple dashboard
from ui.vip_simple_dashboard import VIPSimpleDashboard

def setup_application():
    """Setup application with clean styling"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("VIP BIG BANG Simple Trading")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("VIP BIG BANG")
    
    # Set application style
    app.setStyle('Fusion')
    
    # Apply clean dark theme
    palette = QPalette()
    
    # Window colors
    palette.setColor(QPalette.ColorRole.Window, QColor(15, 15, 15))
    palette.setColor(QPalette.ColorRole.WindowText, QColor(255, 255, 255))
    
    # Base colors
    palette.setColor(QPalette.ColorRole.Base, QColor(20, 20, 20))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(30, 30, 30))
    
    # Text colors
    palette.setColor(QPalette.ColorRole.Text, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.BrightText, QColor(255, 255, 255))
    
    # Button colors
    palette.setColor(QPalette.ColorRole.Button, QColor(55, 65, 81))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor(255, 255, 255))
    
    # Highlight colors
    palette.setColor(QPalette.ColorRole.Highlight, QColor(59, 130, 246))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(255, 255, 255))
    
    app.setPalette(palette)
    
    return app

def main():
    """Main function"""
    print("🎯 VIP BIG BANG - Simple Dashboard")
    print("=" * 50)
    print("🎮 Clean Trading Interface")
    print("🔌 Extension Integration")
    print("💎 Professional Design")
    print("=" * 50)
    
    # Setup application
    app = setup_application()
    
    try:
        # Create simple dashboard
        print("🎯 Creating Simple Dashboard...")
        dashboard = VIPSimpleDashboard()
        
        # Show dashboard
        print("✅ Showing dashboard...")
        dashboard.show()
        
        print("🎉 Simple Dashboard launched successfully!")
        print("🔌 Extension integration active")
        print("📊 Clean interface ready")
        print("💰 Trading controls available")
        print("=" * 50)
        print("🎯 Ready for clean trading experience!")
        
        # Run application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ Error launching simple dashboard: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main()
