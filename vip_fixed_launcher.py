#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Fixed System Launcher
💎 Clear Button Text and Professional Design
"""

import sys
import os
import subprocess
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")
    except:
        pass

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("Installing PySide6...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *

class VIPFixedLauncher(QMainWindow):
    """Fixed VIP BIG BANG System Launcher with Clear Button Text"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VIP BIG BANG - System Launcher")
        self.setFixedSize(600, 500)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup launcher UI with clear button text"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Enhanced dark theme with better text visibility
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:1 #1a1a2e);
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:1 #8b5cf6);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                padding: 20px;
                border-radius: 15px;
                font-size: 18px;
                font-weight: bold;
                margin: 10px;
                text-align: center;
                min-height: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:1 #7c3aed);
                border: 2px solid rgba(255, 255, 255, 0.5);
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:1 #5b21b6);
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
            #titleLabel {
                font-size: 24px;
                font-weight: bold;
                color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:1 #8b5cf6);
                margin: 20px;
            }
            #subtitleLabel {
                font-size: 16px;
                color: rgba(255, 255, 255, 0.8);
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("VIP BIG BANG System Launcher")
        title.setObjectName("titleLabel")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("Choose your launch option:")
        subtitle.setObjectName("subtitleLabel")
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        layout.addSpacing(20)
        
        # Launch buttons with clear text
        btn1 = QPushButton()
        btn1.setText("Complete Integrated System\n(Main System + Dashboard + Beautiful UI)")
        btn1.clicked.connect(self.launch_integrated)
        layout.addWidget(btn1)
        
        btn2 = QPushButton()
        btn2.setText("Beautiful UI Only\n(Modern Glass Design Interface)")
        btn2.clicked.connect(self.launch_beautiful_only)
        layout.addWidget(btn2)
        
        btn3 = QPushButton()
        btn3.setText("Main System Only\n(Core Trading Engine)")
        btn3.clicked.connect(self.launch_main_only)
        layout.addWidget(btn3)
        
        btn4 = QPushButton()
        btn4.setText("Dashboard Only\n(System Monitoring)")
        btn4.clicked.connect(self.launch_dashboard_only)
        layout.addWidget(btn4)
        
        layout.addStretch()
        
        # Exit button
        exit_btn = QPushButton()
        exit_btn.setText("Exit Launcher")
        exit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ef4444, stop:1 #dc2626);
                min-height: 40px;
                font-size: 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f87171, stop:1 #ef4444);
            }
        """)
        exit_btn.clicked.connect(self.close)
        layout.addWidget(exit_btn)
        
    def launch_integrated(self):
        """Launch complete integrated system"""
        print("Launching Complete Integrated System...")
        self.close()
        try:
            # Start main system
            subprocess.Popen([sys.executable, "vip_real_quotex_main.py"])
            # Start dashboard
            subprocess.Popen([sys.executable, "vip_comprehensive_dashboard.py"])
            # Start beautiful UI
            subprocess.Popen([sys.executable, "vip_beautiful_main_ui.py"])
            print("All systems launched successfully!")
        except Exception as e:
            print(f"Error launching systems: {e}")
            
    def launch_beautiful_only(self):
        """Launch beautiful UI only"""
        print("Launching Beautiful UI Only...")
        self.close()
        subprocess.Popen([sys.executable, "vip_beautiful_main_ui.py"])
        
    def launch_main_only(self):
        """Launch main system only"""
        print("Launching Main System Only...")
        self.close()
        subprocess.Popen([sys.executable, "vip_real_quotex_main.py"])
        
    def launch_dashboard_only(self):
        """Launch dashboard only"""
        print("Launching Dashboard Only...")
        self.close()
        subprocess.Popen([sys.executable, "vip_comprehensive_dashboard.py"])

def main():
    """Main entry point"""
    print("VIP BIG BANG - Fixed System Launcher")
    print("Clear button text and professional design")
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    launcher = VIPFixedLauncher()
    launcher.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
