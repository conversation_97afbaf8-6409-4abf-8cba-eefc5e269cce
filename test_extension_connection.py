"""
🔍 VIP BIG BANG - Extension Connection Test
Test the connection between Extension and UI
"""

import sys
import time
import json
from pathlib import Path
from PySide6.QtWidgets import QApp<PERSON>, QMainWindow, QVBoxLayout, QWidget, QLabel, QTextEdit, QPushButton
from PySide6.QtCore import QTimer, Signal, QObject

# Add paths
sys.path.append(str(Path(__file__).parent))

class ExtensionConnectionTest(QMainWindow):
    """
    🔍 Test Extension Connection
    """
    
    def __init__(self):
        super().__init__()
        
        # Window setup
        self.setWindowTitle("🔍 VIP BIG BANG - Extension Connection Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Real Data Server
        self.real_data_server = None
        
        # Setup UI
        self._setup_ui()
        
        # Setup Real Data Server
        self._setup_real_data_server()
        
        # Setup timer for status updates
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(2000)  # Update every 2 seconds
        
    def _setup_ui(self):
        """Setup test UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Title
        title = QLabel("🔍 VIP BIG BANG Extension Connection Test")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #3B82F6; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Status labels
        self.server_status = QLabel("🔴 Real Data Server: Not Started")
        self.server_status.setStyleSheet("font-size: 14px; padding: 5px;")
        layout.addWidget(self.server_status)
        
        self.connection_status = QLabel("🔴 Extension Connection: Disconnected")
        self.connection_status.setStyleSheet("font-size: 14px; padding: 5px;")
        layout.addWidget(self.connection_status)
        
        self.data_status = QLabel("📊 Last Data: None")
        self.data_status.setStyleSheet("font-size: 14px; padding: 5px;")
        layout.addWidget(self.data_status)
        
        # Data display
        self.data_display = QTextEdit()
        self.data_display.setMaximumHeight(300)
        self.data_display.setStyleSheet("""
            QTextEdit {
                background: #1F2937;
                color: #F3F4F6;
                border: 1px solid #374151;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.data_display)
        
        # Control buttons
        button_layout = QVBoxLayout()
        
        self.start_server_btn = QPushButton("🚀 Start Real Data Server")
        self.start_server_btn.clicked.connect(self._start_server)
        self.start_server_btn.setStyleSheet("""
            QPushButton {
                background: #3B82F6;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2563EB;
            }
        """)
        button_layout.addWidget(self.start_server_btn)
        
        self.test_connection_btn = QPushButton("🔍 Test Extension Connection")
        self.test_connection_btn.clicked.connect(self._test_connection)
        self.test_connection_btn.setStyleSheet("""
            QPushButton {
                background: #10B981;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #059669;
            }
        """)
        button_layout.addWidget(self.test_connection_btn)
        
        layout.addLayout(button_layout)
        
        # Initial log
        self._log("🔍 Extension Connection Test Started")
        self._log("📋 Instructions:")
        self._log("1. Click 'Start Real Data Server'")
        self._log("2. Open Chrome with VIP BIG BANG extension")
        self._log("3. Navigate to Quotex and start extraction")
        self._log("4. Watch for connection and data")
        
    def _setup_real_data_server(self):
        """Setup Real Data Server"""
        try:
            from core.real_data_server import get_real_data_server, start_real_data_server
            
            # Check if server already exists
            self.real_data_server = get_real_data_server()
            
            if self.real_data_server:
                self._log("✅ Found existing Real Data Server")
                self.real_data_server.add_data_callback(self._on_data_received)
                self.server_status.setText("🟢 Real Data Server: Connected to Existing")
                self.server_status.setStyleSheet("font-size: 14px; padding: 5px; color: #10B981;")
            else:
                self._log("⚠️ No existing Real Data Server found")
                
        except Exception as e:
            self._log(f"❌ Error setting up Real Data Server: {e}")
            
    def _start_server(self):
        """Start Real Data Server"""
        try:
            from core.real_data_server import start_real_data_server, get_real_data_server
            
            self._log("🚀 Starting Real Data Server...")
            
            if start_real_data_server(8765):
                self.real_data_server = get_real_data_server()
                
                if self.real_data_server:
                    self.real_data_server.add_data_callback(self._on_data_received)
                    self._log("✅ Real Data Server started successfully")
                    self.server_status.setText("🟢 Real Data Server: Running on port 8765")
                    self.server_status.setStyleSheet("font-size: 14px; padding: 5px; color: #10B981;")
                    self.start_server_btn.setText("✅ Server Running")
                    self.start_server_btn.setEnabled(False)
                else:
                    self._log("❌ Failed to get server instance")
            else:
                self._log("❌ Failed to start Real Data Server")
                
        except Exception as e:
            self._log(f"❌ Error starting server: {e}")
            
    def _test_connection(self):
        """Test extension connection"""
        try:
            if self.real_data_server:
                connection_count = self.real_data_server.get_connection_count()
                self._log(f"🔍 Connection test: {connection_count} clients connected")
                
                if connection_count > 0:
                    self._log("✅ Extension is connected!")
                    latest_data = self.real_data_server.get_latest_data()
                    if latest_data:
                        self._log(f"📊 Latest data available: {latest_data.get('source', 'Unknown')}")
                    else:
                        self._log("⚠️ No data received yet")
                else:
                    self._log("❌ No extension connected")
                    self._log("📋 Make sure:")
                    self._log("   • Chrome extension is loaded and enabled")
                    self._log("   • You're on Quotex page (qxbroker.com)")
                    self._log("   • Extension extraction is started")
            else:
                self._log("❌ Real Data Server not running")
                
        except Exception as e:
            self._log(f"❌ Connection test error: {e}")
            
    def _on_data_received(self, data):
        """Handle data received from extension"""
        try:
            timestamp = time.strftime("%H:%M:%S")
            source = data.get('source', 'Unknown')
            balance = data.get('balance', 'N/A')
            asset = data.get('currentAsset', 'N/A')
            
            self._log(f"[{timestamp}] 📊 Data from {source}: Balance={balance}, Asset={asset}")
            
            # Update status
            self.connection_status.setText("🟢 Extension Connection: Active")
            self.connection_status.setStyleSheet("font-size: 14px; padding: 5px; color: #10B981;")
            
            self.data_status.setText(f"📊 Last Data: {timestamp} - {source}")
            self.data_status.setStyleSheet("font-size: 14px; padding: 5px; color: #10B981;")
            
        except Exception as e:
            self._log(f"❌ Error handling data: {e}")
            
    def _update_status(self):
        """Update connection status"""
        try:
            if self.real_data_server:
                connection_count = self.real_data_server.get_connection_count()
                
                if connection_count > 0:
                    self.connection_status.setText(f"🟢 Extension Connection: {connection_count} client(s)")
                    self.connection_status.setStyleSheet("font-size: 14px; padding: 5px; color: #10B981;")
                else:
                    self.connection_status.setText("🔴 Extension Connection: Disconnected")
                    self.connection_status.setStyleSheet("font-size: 14px; padding: 5px; color: #EF4444;")
                    
        except Exception as e:
            self._log(f"❌ Status update error: {e}")
            
    def _log(self, message):
        """Log message to display"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.data_display.append(log_message)
        print(log_message)
        
    def closeEvent(self, event):
        """Handle window close"""
        if self.status_timer:
            self.status_timer.stop()
        event.accept()


def main():
    """Main function"""
    print("🔍 VIP BIG BANG - Extension Connection Test")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Create and show test window
    test_window = ExtensionConnectionTest()
    test_window.show()
    
    print("✅ Extension Connection Test launched")
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
