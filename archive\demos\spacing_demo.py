"""
📐 Perfect Spacing Demo - تکنیک‌های فاصله‌گذاری ChatGPT
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import Qt

class SpacingDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📐 Perfect Spacing Demo")
        self.setGeometry(100, 100, 1200, 800)
        
        # ChatGPT background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:1 #1a1a2e);
                color: white;
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Perfect spacing system
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(40, 40, 40, 40)  # 8-base system
        main_layout.setSpacing(32)  # 8 * 4
        
        # Title
        title = QLabel("📐 ChatGPT Spacing System")
        title.setStyleSheet("""
            font-size: 36px;
            font-weight: 700;
            color: white;
            margin-bottom: 16px;
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # Spacing Scale Demo
        scale_widget = self.create_spacing_scale()
        main_layout.addWidget(scale_widget)
        
        # Layout Examples
        examples_layout = QHBoxLayout()
        examples_layout.setSpacing(32)
        
        # Bad Spacing Example
        bad_example = self.create_bad_spacing_example()
        examples_layout.addWidget(bad_example)
        
        # Good Spacing Example
        good_example = self.create_good_spacing_example()
        examples_layout.addWidget(good_example)
        
        main_layout.addLayout(examples_layout)
        
        # Spacing Rules
        rules_widget = self.create_spacing_rules()
        main_layout.addWidget(rules_widget)
    
    def create_spacing_scale(self):
        """Create spacing scale demonstration"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setSpacing(16)
        
        title = QLabel("🎯 8-Point Spacing Scale")
        title.setStyleSheet("""
            font-size: 20px;
            font-weight: 600;
            color: #4CAF50;
            margin-bottom: 12px;
        """)
        layout.addWidget(title)
        
        # Spacing scale
        scale_container = QWidget()
        scale_layout = QHBoxLayout(scale_container)
        scale_layout.setSpacing(24)
        
        spacing_values = [4, 8, 12, 16, 20, 24, 32, 40, 48, 64]
        
        for value in spacing_values:
            item_container = QWidget()
            item_layout = QVBoxLayout(item_container)
            item_layout.setContentsMargins(0, 0, 0, 0)
            item_layout.setSpacing(4)
            
            # Visual representation
            visual = QWidget()
            visual.setFixedSize(value, 40)
            visual.setStyleSheet(f"""
                background: #4CAF50;
                border-radius: 4px;
            """)
            
            # Center the visual
            visual_container = QWidget()
            visual_layout = QHBoxLayout(visual_container)
            visual_layout.addWidget(visual)
            visual_layout.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(visual_container)
            
            # Label
            label = QLabel(f"{value}px")
            label.setStyleSheet("""
                font-size: 11px;
                color: rgba(255,255,255,0.8);
                font-weight: 500;
            """)
            label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(label)
            
            scale_layout.addWidget(item_container)
        
        layout.addWidget(scale_container)
        
        return container
    
    def create_bad_spacing_example(self):
        """Create bad spacing example"""
        container = QWidget()
        container.setFixedWidth(350)
        container.setStyleSheet("""
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,0,0,0.3);
            border-radius: 16px;
            padding: 20px;
        """)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(5, 3, 7, 9)  # Inconsistent margins
        layout.setSpacing(13)  # Odd spacing
        
        # Title
        title = QLabel("❌ Bad Spacing")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: #F44336;
            margin-bottom: 3px;
        """)
        layout.addWidget(title)
        
        # Inconsistent elements
        elements = [
            ("Header", "font-size: 16px; margin: 7px 0 3px 0;"),
            ("Subheader", "font-size: 14px; margin: 2px 0 11px 0;"),
            ("Content", "font-size: 12px; margin: 5px 0 1px 0;"),
            ("Footer", "font-size: 11px; margin: 9px 0 4px 0;")
        ]
        
        for text, style in elements:
            label = QLabel(text)
            label.setStyleSheet(f"""
                color: rgba(255,255,255,0.8);
                {style}
                background: rgba(255,255,255,0.05);
                padding: 6px 11px 4px 9px;
                border-radius: 7px;
            """)
            layout.addWidget(label)
        
        # Problems list
        problems = QLabel("""
Problems:
• Inconsistent margins (3px, 7px, 9px)
• Odd spacing values (13px, 11px)
• Random padding values
• No systematic approach
        """)
        problems.setStyleSheet("""
            font-size: 11px;
            color: rgba(255,100,100,0.8);
            margin-top: 15px;
            padding: 12px;
            background: rgba(255,0,0,0.1);
            border-radius: 8px;
            line-height: 1.4;
        """)
        problems.setWordWrap(True)
        layout.addWidget(problems)
        
        return container
    
    def create_good_spacing_example(self):
        """Create good spacing example"""
        container = QWidget()
        container.setFixedWidth(350)
        container.setStyleSheet("""
            background: rgba(255,255,255,0.08);
            border: 1px solid rgba(76,175,80,0.3);
            border-radius: 16px;
            padding: 24px;
        """)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)  # Consistent margins
        layout.setSpacing(16)  # 8-base spacing
        
        # Title
        title = QLabel("✅ Good Spacing")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: #4CAF50;
            margin-bottom: 8px;
        """)
        layout.addWidget(title)
        
        # Consistent elements
        elements = [
            ("Header", "font-size: 16px; margin: 8px 0;"),
            ("Subheader", "font-size: 14px; margin: 8px 0;"),
            ("Content", "font-size: 12px; margin: 8px 0;"),
            ("Footer", "font-size: 11px; margin: 8px 0;")
        ]
        
        for text, style in elements:
            label = QLabel(text)
            label.setStyleSheet(f"""
                color: rgba(255,255,255,0.9);
                {style}
                background: rgba(255,255,255,0.08);
                padding: 12px 16px;
                border-radius: 8px;
            """)
            layout.addWidget(label)
        
        # Benefits list
        benefits = QLabel("""
Benefits:
• Consistent 8px base unit
• Predictable spacing (8, 16, 24, 32)
• Clean visual hierarchy
• Professional appearance
        """)
        benefits.setStyleSheet("""
            font-size: 11px;
            color: rgba(100,255,100,0.8);
            margin-top: 16px;
            padding: 16px;
            background: rgba(76,175,80,0.1);
            border-radius: 8px;
            line-height: 1.4;
        """)
        benefits.setWordWrap(True)
        layout.addWidget(benefits)
        
        return container
    
    def create_spacing_rules(self):
        """Create spacing rules widget"""
        rules_widget = QWidget()
        rules_layout = QVBoxLayout(rules_widget)
        
        rules_title = QLabel("📝 Spacing Rules:")
        rules_title.setStyleSheet("""
            font-size: 20px;
            font-weight: 600;
            color: #4CAF50;
            margin: 16px 0;
        """)
        rules_layout.addWidget(rules_title)
        
        rules_text = QLabel("""
📐 ChatGPT Spacing System:

🎯 8-Point Base System:
• Base Unit: 8px
• Scale: 4px, 8px, 12px, 16px, 20px, 24px, 32px, 40px, 48px, 64px
• Formula: 8 × n (where n = 0.5, 1, 1.5, 2, 2.5, 3, 4, 5, 6, 8)

📏 Layout Spacing:
• Container Margins: 24px, 32px, 40px
• Element Spacing: 8px, 16px, 24px
• Section Spacing: 32px, 40px, 48px
• Page Margins: 40px, 48px, 64px

🎨 Component Spacing:
• Button Padding: 8px 16px, 12px 24px
• Panel Padding: 16px, 20px, 24px
• Card Spacing: 16px, 20px
• Grid Gaps: 12px, 16px, 20px, 24px

✅ Best Practices:
• Stick to the 8px system
• Use consistent spacing throughout
• Larger spacing for more separation
• Smaller spacing for related elements
• Test on different screen sizes
        """)
        rules_text.setStyleSheet("""
            font-size: 13px;
            color: rgba(255,255,255,0.8);
            line-height: 1.6;
            padding: 24px;
            background: rgba(255,255,255,0.05);
            border-radius: 16px;
            border: 1px solid rgba(255,255,255,0.1);
        """)
        rules_text.setWordWrap(True)
        rules_layout.addWidget(rules_text)
        
        return rules_widget

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SpacingDemo()
    window.show()
    sys.exit(app.exec())
