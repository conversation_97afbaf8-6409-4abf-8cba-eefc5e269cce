"""
🎯 SIMPLE QUOTEX LAUNCHER
🚀 OPENS ONLY THE REAL QUOTEX SITE
🔥 NO PROXY, NO ALTERNATIVE SITES
"""

import os
import subprocess
import time
import logging
import random
import tempfile

class SimpleQuotexLauncher:
    """
    🎯 SIMPLE QUOTEX LAUNCHER
    🚀 Opens only the real Quotex site
    """
    
    def __init__(self):
        self.logger = logging.getLogger("SimpleQuotexLauncher")
        
        # Chrome paths
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        # ONLY REAL QUOTEX URLS
        self.real_quotex_urls = [
            "https://quotex.io/en/sign-in",
            "https://quotex.io/",
            "https://quotex.io/en/"
        ]
        
        self.logger.info("🎯 Simple Quotex Launcher initialized")
    
    def find_chrome(self):
        """🔍 Find Chrome executable"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                return path
        return None
    
    def get_stealth_flags(self):
        """🛡️ Get stealth flags for Quotex"""
        return [
            # Basic stealth
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "--disable-popup-blocking",
            "--disable-translate",
            "--disable-sync",
            
            # Anti-detection
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-automation",
            "--disable-infobars",
            "--enable-automation=false",
            
            # Security
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--ignore-certificate-errors",
            "--ignore-ssl-errors",
            
            # Performance
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            
            # User agent
            "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            
            # DevTools
            "--remote-debugging-port=9222"
        ]
    
    def launch_real_quotex(self):
        """🚀 Launch Chrome to REAL Quotex site only"""
        try:
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                self.logger.error("❌ Chrome not found")
                return False
            
            # Create unique profile
            profile_name = f"RealQuotex_{random.randint(10000, 99999)}"
            user_data_dir = os.path.join(tempfile.gettempdir(), f"ChromeRealQuotex_{random.randint(1000, 9999)}")
            
            # Get stealth flags
            flags = self.get_stealth_flags()
            
            # Use ONLY the main Quotex URL
            main_quotex_url = "https://quotex.io/en/sign-in"
            
            self.logger.info(f"🚀 Launching Chrome to REAL Quotex: {main_quotex_url}")
            
            # Build command
            cmd = [chrome_exe] + flags + [
                f"--user-data-dir={user_data_dir}",
                f"--profile-directory={profile_name}",
                "--window-size=1366,768",
                "--start-maximized",
                main_quotex_url
            ]
            
            # Launch Chrome
            process = subprocess.Popen(cmd, shell=False)
            
            # Wait for Chrome to start
            time.sleep(5)
            
            self.logger.info("✅ Chrome launched to REAL Quotex!")
            self.logger.info("🎯 URL: https://quotex.io/en/sign-in")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to launch Chrome to Quotex: {e}")
            return False

def main():
    """🚀 Main function"""
    print("🎯 SIMPLE QUOTEX LAUNCHER")
    print("🚀 OPENING REAL QUOTEX SITE ONLY")
    print("=" * 40)
    
    launcher = SimpleQuotexLauncher()
    
    print("🚀 Launching Chrome to REAL Quotex...")
    print("🎯 URL: https://quotex.io/en/sign-in")
    
    if launcher.launch_real_quotex():
        print("\n✅ SUCCESS!")
        print("🎯 Chrome opened to REAL Quotex site!")
        print("🔗 URL: https://quotex.io/en/sign-in")
        print("🛡️ Stealth mode active!")
        print("\n💡 Next steps:")
        print("1️⃣ Wait for Quotex to load")
        print("2️⃣ Login to your account")
        print("3️⃣ Start trading!")
    else:
        print("\n❌ Failed to open Quotex")
        print("🔧 Check Chrome installation")

if __name__ == "__main__":
    main()
