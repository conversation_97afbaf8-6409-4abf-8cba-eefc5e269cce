"""
🎮 VIP BIG BANG - Analysis Box Component
Gaming-style analysis module with animations and visual effects
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class AnalysisBox(QFrame):
    """
    Gaming-style analysis box with icon, title, value and visual effects
    
    Features:
    - Animated hover effects
    - Color-coded status indicators
    - Progress bars and visual elements
    - Click interactions
    """
    
    # Signals
    clicked = Signal(str)  # box_name
    value_changed = Signal(str, str)  # box_name, new_value
    
    def __init__(self, icon: str, title: str, initial_value: str = "", color: str = "#8B5CF6"):
        super().__init__()
        
        self.box_name = title
        self.icon_text = icon
        self.title_text = title
        self.current_value = initial_value
        self.primary_color = color
        
        # Animation properties
        self.hover_scale = 1.0
        self.glow_opacity = 0.0
        
        # Setup box
        self._setup_box()
        self._setup_animations()
        
        # Set initial value
        self.update_value(initial_value)
    
    def _setup_box(self):
        """Setup the analysis box layout and components"""
        self.setFixedSize(100, 100)
        self.setObjectName("analysisBox")
        
        # Enable mouse tracking for hover effects
        self.setMouseTracking(True)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)
        
        # Icon
        self.icon_label = QLabel(self.icon_text)
        self.icon_label.setObjectName("analysisIcon")
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setStyleSheet(f"""
            QLabel#analysisIcon {{
                font-size: 24px;
                color: {self.primary_color};
                font-weight: bold;
            }}
        """)
        layout.addWidget(self.icon_label)
        
        # Title
        self.title_label = QLabel(self.title_text)
        self.title_label.setObjectName("analysisTitle")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setWordWrap(True)
        self.title_label.setStyleSheet("""
            QLabel#analysisTitle {
                font-size: 10px;
                color: white;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.title_label)
        
        # Value
        self.value_label = QLabel(self.current_value)
        self.value_label.setObjectName("analysisValue")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setStyleSheet(f"""
            QLabel#analysisValue {{
                font-size: 12px;
                color: {self.primary_color};
                font-weight: bold;
            }}
        """)
        layout.addWidget(self.value_label)
        
        # Progress indicator (optional)
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("analysisProgress")
        self.progress_bar.setFixedHeight(4)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar#analysisProgress {{
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 2px;
            }}
            QProgressBar#analysisProgress::chunk {{
                background-color: {self.primary_color};
                border-radius: 2px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # Apply base styling
        self._apply_base_style()
    
    def _apply_base_style(self):
        """Apply base styling to the analysis box"""
        self.setStyleSheet(f"""
            QFrame#analysisBox {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(139, 92, 246, 0.1),
                    stop:1 rgba(139, 92, 246, 0.05));
                border: 2px solid rgba(139, 92, 246, 0.3);
                border-radius: 12px;
                padding: 4px;
            }}
            QFrame#analysisBox:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(139, 92, 246, 0.2),
                    stop:1 rgba(139, 92, 246, 0.1));
                border: 2px solid rgba(139, 92, 246, 0.6);
            }}
        """)
    
    def _setup_animations(self):
        """Setup hover and click animations"""
        # Scale animation
        self.scale_animation = QPropertyAnimation(self, b"hover_scale")
        self.scale_animation.setDuration(200)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Glow animation
        self.glow_animation = QPropertyAnimation(self, b"glow_opacity")
        self.glow_animation.setDuration(300)
        self.glow_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
    
    def update_value(self, new_value: str, progress: int = None):
        """Update the value and optional progress"""
        old_value = self.current_value
        self.current_value = new_value
        self.value_label.setText(new_value)
        
        # Update progress bar if provided
        if progress is not None:
            self.progress_bar.setValue(progress)
            self.progress_bar.setVisible(True)
        else:
            # Try to extract percentage from value
            if "%" in new_value:
                try:
                    percent = int(new_value.replace("%", ""))
                    self.progress_bar.setValue(percent)
                    self.progress_bar.setVisible(True)
                except:
                    self.progress_bar.setVisible(False)
            else:
                self.progress_bar.setVisible(False)
        
        # Emit value changed signal
        self.value_changed.emit(self.box_name, new_value)
        
        # Trigger update animation if value changed
        if old_value != new_value:
            self._animate_value_change()
    
    def update_color(self, new_color: str):
        """Update the primary color of the box"""
        self.primary_color = new_color
        
        # Update icon color
        self.icon_label.setStyleSheet(f"""
            QLabel#analysisIcon {{
                font-size: 24px;
                color: {new_color};
                font-weight: bold;
            }}
        """)
        
        # Update value color
        self.value_label.setStyleSheet(f"""
            QLabel#analysisValue {{
                font-size: 12px;
                color: {new_color};
                font-weight: bold;
            }}
        """)
        
        # Update progress bar color
        self.progress_bar.setStyleSheet(f"""
            QProgressBar#analysisProgress {{
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 2px;
            }}
            QProgressBar#analysisProgress::chunk {{
                background-color: {new_color};
                border-radius: 2px;
            }}
        """)
    
    def _animate_value_change(self):
        """Animate value change with glow effect"""
        self.glow_animation.setStartValue(0.0)
        self.glow_animation.setEndValue(1.0)
        self.glow_animation.finished.connect(self._fade_glow)
        self.glow_animation.start()
    
    def _fade_glow(self):
        """Fade out the glow effect"""
        self.glow_animation.setStartValue(1.0)
        self.glow_animation.setEndValue(0.0)
        self.glow_animation.finished.disconnect()
        self.glow_animation.start()
    
    def enterEvent(self, event):
        """Handle mouse enter event"""
        self.scale_animation.setStartValue(1.0)
        self.scale_animation.setEndValue(1.05)
        self.scale_animation.start()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Handle mouse leave event"""
        self.scale_animation.setStartValue(1.05)
        self.scale_animation.setEndValue(1.0)
        self.scale_animation.start()
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Handle mouse press event"""
        if event.button() == Qt.MouseButton.LeftButton:
            # Quick scale down animation
            self.scale_animation.setStartValue(1.05)
            self.scale_animation.setEndValue(0.95)
            self.scale_animation.setDuration(100)
            self.scale_animation.start()
            
            # Emit clicked signal
            self.clicked.emit(self.box_name)
        
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """Handle mouse release event"""
        if event.button() == Qt.MouseButton.LeftButton:
            # Scale back up
            self.scale_animation.setStartValue(0.95)
            self.scale_animation.setEndValue(1.05)
            self.scale_animation.setDuration(100)
            self.scale_animation.start()
        
        super().mouseReleaseEvent(event)
    
    # Property for animations
    @Property(float)
    def hover_scale(self):
        return self._hover_scale
    
    @hover_scale.setter
    def hover_scale(self, value):
        self._hover_scale = value
        # Skip transform for now - Qt doesn't support CSS transform
        pass
    
    @Property(float)
    def glow_opacity(self):
        return self._glow_opacity
    
    @glow_opacity.setter
    def glow_opacity(self, value):
        self._glow_opacity = value
        # Apply glow effect (simplified)
        self.setGraphicsEffect(None)
        if value > 0:
            glow = QGraphicsDropShadowEffect()
            glow.setBlurRadius(20 * value)
            glow.setColor(QColor(self.primary_color))
            glow.setOffset(0, 0)
            self.setGraphicsEffect(glow)
