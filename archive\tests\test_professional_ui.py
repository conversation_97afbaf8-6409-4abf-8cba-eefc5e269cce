"""
Test Professional VIP BIG BANG UI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

try:
    from ui.professional_ui import VIPBigBangProfessionalUI
    
    if __name__ == "__main__":
        app = QApplication(sys.argv)
        window = VIPBigBangProfessionalUI()
        window.show()
        sys.exit(app.exec())
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
