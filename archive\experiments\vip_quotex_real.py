#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Real Quotex Dashboard
💎 اتصال مستقیم به Quotex بدون دمو
🔗 صفحه Quotex + دکمه‌های اتصال واقعی
"""

import sys
import os
import webbrowser
import subprocess
from datetime import datetime
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWebEngineWidgets import QWebEngineView

class VIPQuotexReal(QMainWindow):
    """🚀 VIP BIG BANG - Real Quotex Dashboard"""
    
    def __init__(self):
        super().__init__()
        
        # Window setup
        self.setWindowTitle("🚀 VIP BIG BANG - Real Quotex Connection")
        self.setGeometry(100, 100, 1600, 1000)
        
        # State
        self.is_connected = False
        self.extension_installed = False
        self.chrome_process = None
        
        # Setup UI
        self._setup_ui()
        self._apply_vip_style()
        
        # Auto-load Quotex
        self._load_quotex()
        
        print("🚀 VIP BIG BANG Real Quotex Dashboard initialized")
        print("💎 No demo mode - Real trading only")
    
    def _setup_ui(self):
        """🎨 Setup main UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Header
        header = self._create_header()
        layout.addWidget(header)
        
        # Main content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)
        
        # Left panel - Controls
        left_panel = self._create_control_panel()
        content_layout.addWidget(left_panel)
        
        # Right panel - Quotex browser
        right_panel = self._create_quotex_panel()
        content_layout.addWidget(right_panel, 3)
        
        layout.addLayout(content_layout)
        
        # Status bar
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("🔴 آماده برای اتصال به Quotex")
    
    def _create_header(self):
        """🎯 Create header"""
        header = QFrame()
        header.setObjectName("vip-header")
        header.setFixedHeight(70)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # Logo and title
        logo_label = QLabel("🚀")
        logo_label.setObjectName("vip-logo")
        logo_label.setFixedSize(40, 40)
        layout.addWidget(logo_label)
        
        title_label = QLabel("VIP BIG BANG - Real Quotex Trading")
        title_label.setObjectName("vip-title")
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # Connection status
        self.connection_status = QLabel("🔴 غیرمتصل")
        self.connection_status.setObjectName("vip-status")
        layout.addWidget(self.connection_status)
        
        # Time
        self.time_label = QLabel()
        self.time_label.setObjectName("vip-time")
        layout.addWidget(self.time_label)
        
        # Update time
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_time)
        self.timer.start(1000)
        
        return header
    
    def _create_control_panel(self):
        """🎮 Create control panel"""
        panel = QFrame()
        panel.setObjectName("vip-control-panel")
        panel.setFixedWidth(300)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Title
        title = QLabel("🎯 کنترل اتصال")
        title.setObjectName("vip-panel-title")
        layout.addWidget(title)
        
        # Chrome Extension section
        ext_group = QGroupBox("🔧 Chrome Extension")
        ext_group.setObjectName("vip-group")
        ext_layout = QVBoxLayout(ext_group)
        
        self.install_ext_btn = QPushButton("📥 نصب و راه‌اندازی Extension")
        self.install_ext_btn.setObjectName("vip-primary-btn")
        self.install_ext_btn.clicked.connect(self._setup_chrome_extension)
        ext_layout.addWidget(self.install_ext_btn)
        
        self.ext_status_label = QLabel("❌ Extension آماده نیست")
        self.ext_status_label.setObjectName("vip-status-label")
        ext_layout.addWidget(self.ext_status_label)
        
        layout.addWidget(ext_group)
        
        # Quotex Connection section
        conn_group = QGroupBox("🔗 اتصال Quotex")
        conn_group.setObjectName("vip-group")
        conn_layout = QVBoxLayout(conn_group)
        
        self.connect_quotex_btn = QPushButton("🚀 اتصال مستقیم به Quotex")
        self.connect_quotex_btn.setObjectName("vip-connect-btn")
        self.connect_quotex_btn.clicked.connect(self._connect_real_quotex)
        conn_layout.addWidget(self.connect_quotex_btn)
        
        self.open_new_tab_btn = QPushButton("🌐 باز کردن در تب جدید")
        self.open_new_tab_btn.setObjectName("vip-secondary-btn")
        self.open_new_tab_btn.clicked.connect(self._open_quotex_new_tab)
        conn_layout.addWidget(self.open_new_tab_btn)
        
        self.refresh_btn = QPushButton("🔄 بازخوانی صفحه")
        self.refresh_btn.setObjectName("vip-secondary-btn")
        self.refresh_btn.clicked.connect(self._refresh_quotex)
        conn_layout.addWidget(self.refresh_btn)

        self.test_trader_btn = QPushButton("🧪 تست اتصال Trader")
        self.test_trader_btn.setObjectName("vip-test-btn")
        self.test_trader_btn.clicked.connect(self._test_trader_connection)
        conn_layout.addWidget(self.test_trader_btn)
        
        layout.addWidget(conn_group)
        
        # Account section
        acc_group = QGroupBox("💰 حساب کاربری")
        acc_group.setObjectName("vip-group")
        acc_layout = QVBoxLayout(acc_group)
        
        self.balance_label = QLabel("موجودی: در حال بارگذاری...")
        self.balance_label.setObjectName("vip-balance")
        acc_layout.addWidget(self.balance_label)
        
        self.account_type_label = QLabel("نوع حساب: در حال تشخیص...")
        self.account_type_label.setObjectName("vip-account-type")
        acc_layout.addWidget(self.account_type_label)
        
        layout.addWidget(acc_group)
        
        # Trading section
        trade_group = QGroupBox("📊 معاملات")
        trade_group.setObjectName("vip-group")
        trade_layout = QVBoxLayout(trade_group)
        
        # Asset selection
        asset_layout = QHBoxLayout()
        asset_layout.addWidget(QLabel("دارایی:"))
        self.asset_combo = QComboBox()
        self.asset_combo.setObjectName("vip-combo")
        self.asset_combo.addItems([
            "EUR/USD OTC", "GBP/USD OTC", "USD/JPY OTC", 
            "AUD/USD OTC", "USD/CAD OTC"
        ])
        asset_layout.addWidget(self.asset_combo)
        trade_layout.addLayout(asset_layout)
        
        # Amount
        amount_layout = QHBoxLayout()
        amount_layout.addWidget(QLabel("مبلغ:"))
        self.amount_spin = QSpinBox()
        self.amount_spin.setObjectName("vip-spinbox")
        self.amount_spin.setRange(1, 1000)
        self.amount_spin.setValue(10)
        self.amount_spin.setSuffix(" $")
        amount_layout.addWidget(self.amount_spin)
        trade_layout.addLayout(amount_layout)
        
        # Duration
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("مدت:"))
        self.duration_combo = QComboBox()
        self.duration_combo.setObjectName("vip-combo")
        self.duration_combo.addItems(["5s", "15s", "30s", "1m", "5m"])
        self.duration_combo.setCurrentText("5s")
        duration_layout.addWidget(self.duration_combo)
        trade_layout.addLayout(duration_layout)
        
        # Trade buttons
        buttons_layout = QHBoxLayout()
        
        self.call_btn = QPushButton("📈 CALL")
        self.call_btn.setObjectName("vip-call-btn")
        self.call_btn.clicked.connect(self._place_call)
        buttons_layout.addWidget(self.call_btn)
        
        self.put_btn = QPushButton("📉 PUT")
        self.put_btn.setObjectName("vip-put-btn")
        self.put_btn.clicked.connect(self._place_put)
        buttons_layout.addWidget(self.put_btn)
        
        trade_layout.addLayout(buttons_layout)
        
        layout.addWidget(trade_group)
        
        layout.addStretch()
        
        return panel
    
    def _create_quotex_panel(self):
        """🌐 Create Quotex browser panel"""
        panel = QFrame()
        panel.setObjectName("vip-quotex-panel")
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Panel header
        header_layout = QHBoxLayout()
        
        panel_title = QLabel("🌐 Quotex Trading Platform")
        panel_title.setObjectName("vip-panel-title")
        header_layout.addWidget(panel_title)
        
        header_layout.addStretch()
        
        # URL bar
        self.url_label = QLabel("https://quotex.io/en/trade")
        self.url_label.setObjectName("vip-url")
        header_layout.addWidget(self.url_label)
        
        layout.addLayout(header_layout)
        
        # Web view
        self.web_view = QWebEngineView()
        self.web_view.setObjectName("vip-webview")
        layout.addWidget(self.web_view)
        
        return panel
    
    def _load_quotex(self):
        """🌐 Load Quotex page"""
        try:
            self.web_view.setUrl(QUrl("https://quotex.io/en/trade"))
            self.web_view.loadFinished.connect(self._on_page_loaded)
            print("🌐 Loading Quotex trading platform...")
        except Exception as e:
            print(f"❌ Failed to load Quotex: {e}")
    
    def _on_page_loaded(self, success):
        """Handle page load completion"""
        if success:
            self.connection_status.setText("🟢 صفحه Quotex بارگذاری شد")
            self.status_bar.showMessage("🟢 Quotex آماده - Extension را نصب کنید")
            print("✅ Quotex page loaded successfully")
        else:
            self.connection_status.setText("🔴 خطا در بارگذاری")
            print("❌ Failed to load Quotex page")
    
    def _setup_chrome_extension(self):
        """📥 Setup Chrome extension"""
        try:
            self.install_ext_btn.setText("📥 در حال راه‌اندازی...")
            self.install_ext_btn.setEnabled(False)
            
            # Create extension directory if not exists
            ext_dir = os.path.join(os.getcwd(), "chrome_extension")
            if not os.path.exists(ext_dir):
                os.makedirs(ext_dir)
                print(f"📁 Created extension directory: {ext_dir}")
            
            # Open Chrome with extension
            chrome_cmd = [
                "chrome.exe",
                "--load-extension=" + ext_dir,
                "--disable-web-security",
                "--user-data-dir=" + os.path.join(os.getcwd(), "chrome_profile"),
                "https://quotex.io/en/trade"
            ]
            
            try:
                self.chrome_process = subprocess.Popen(chrome_cmd)
                self.extension_installed = True
                self.ext_status_label.setText("✅ Chrome با Extension راه‌اندازی شد")
                self.install_ext_btn.setText("✅ راه‌اندازی شده")
                print("✅ Chrome launched with extension")
            except FileNotFoundError:
                # Try alternative Chrome paths
                webbrowser.open("https://quotex.io/en/trade")
                self.ext_status_label.setText("🌐 Quotex در مرورگر پیش‌فرض باز شد")
                self.install_ext_btn.setText("🌐 باز شده")
                print("🌐 Opened in default browser")
            
        except Exception as e:
            self.ext_status_label.setText(f"❌ خطا: {str(e)}")
            self.install_ext_btn.setText("📥 نصب و راه‌اندازی Extension")
            self.install_ext_btn.setEnabled(True)
            print(f"❌ Extension setup failed: {e}")
    
    def _connect_real_quotex(self):
        """🚀 Connect to real Quotex"""
        try:
            self.connect_quotex_btn.setText("🔄 در حال اتصال...")
            
            # Refresh the web view to ensure fresh connection
            self.web_view.reload()
            
            self.is_connected = True
            self.connection_status.setText("🟢 متصل به Quotex")
            self.status_bar.showMessage("🟢 اتصال برقرار - آماده معاملات")
            self.connect_quotex_btn.setText("✅ متصل شده")
            
            # Update account info
            self.balance_label.setText("موجودی: در حال دریافت...")
            self.account_type_label.setText("نوع حساب: در حال تشخیص...")
            
            print("✅ Connected to real Quotex")
            
        except Exception as e:
            self.connection_status.setText("🔴 خطا در اتصال")
            self.connect_quotex_btn.setText("🚀 اتصال مستقیم به Quotex")
            print(f"❌ Connection failed: {e}")
    
    def _open_quotex_new_tab(self):
        """🌐 Open Quotex in new tab"""
        webbrowser.open("https://quotex.io/en/trade")
        print("🌐 Quotex opened in new browser tab")
    
    def _refresh_quotex(self):
        """🔄 Refresh Quotex page"""
        self.web_view.reload()
        print("🔄 Quotex page refreshed")

    def _test_trader_connection(self):
        """🧪 Test trader connection"""
        try:
            self.test_trader_btn.setText("🧪 در حال تست...")
            self.test_trader_btn.setEnabled(False)

            # Test JavaScript execution
            js_code = """
            if (window.vipTrader) {
                window.vipTrader.showNotification('✅ VIP Trader متصل است!', 'success');
                console.log('✅ VIP Trader connection test successful');
                'CONNECTED';
            } else {
                console.log('❌ VIP Trader not found');
                'NOT_CONNECTED';
            }
            """

            def handle_result(result):
                if result == 'CONNECTED':
                    self.test_trader_btn.setText("✅ Trader متصل است")
                    self.status_bar.showMessage("✅ VIP Trader آماده معاملات")
                    print("✅ VIP Trader connection test successful")
                else:
                    self.test_trader_btn.setText("❌ Trader یافت نشد")
                    self.status_bar.showMessage("❌ VIP Trader یافت نشد")
                    print("❌ VIP Trader not found")

                # Re-enable button after 3 seconds
                QTimer.singleShot(3000, lambda: (
                    self.test_trader_btn.setText("🧪 تست اتصال Trader"),
                    self.test_trader_btn.setEnabled(True)
                ))

            # Execute test
            self.web_view.page().runJavaScript(js_code, handle_result)

        except Exception as e:
            self.test_trader_btn.setText("❌ خطا در تست")
            self.test_trader_btn.setEnabled(True)
            print(f"❌ Trader test failed: {e}")
    
    def _place_call(self):
        """📈 Place CALL trade"""
        asset = self.asset_combo.currentText()
        amount = self.amount_spin.value()
        duration = self.duration_combo.currentText()

        print(f"📈 CALL Trade: {asset} - ${amount} - {duration}")

        if self.is_connected:
            # Send trade command to Quotex page via JavaScript
            self._execute_trade_on_quotex('CALL', amount, duration)
            self.status_bar.showMessage(f"📈 CALL: {asset} ${amount} {duration}")
        else:
            QMessageBox.warning(self, "هشدار", "ابتدا به Quotex متصل شوید!")

    def _place_put(self):
        """📉 Place PUT trade"""
        asset = self.asset_combo.currentText()
        amount = self.amount_spin.value()
        duration = self.duration_combo.currentText()

        print(f"📉 PUT Trade: {asset} - ${amount} - {duration}")

        if self.is_connected:
            # Send trade command to Quotex page via JavaScript
            self._execute_trade_on_quotex('PUT', amount, duration)
            self.status_bar.showMessage(f"📉 PUT: {asset} ${amount} {duration}")
        else:
            QMessageBox.warning(self, "هشدار", "ابتدا به Quotex متصل شوید!")

    def _execute_trade_on_quotex(self, direction, amount, duration):
        """🎯 Execute trade on Quotex page"""
        try:
            # Convert duration to seconds
            duration_seconds = self._convert_duration_to_seconds(duration)

            # JavaScript code to execute trade
            js_code = f"""
            if (window.vipTrader) {{
                window.vipTrader.executeTrade('{direction}', {amount}, {duration_seconds});
                console.log('🎯 Trade executed via VIP Trader: {direction} ${amount} {duration_seconds}s');
            }} else {{
                console.log('❌ VIP Trader not found - trying direct execution');

                // Try direct execution
                const amountInput = document.querySelector('input[type="number"]') ||
                                  document.querySelector('.amount-input input') ||
                                  document.querySelector('input[name="amount"]');

                if (amountInput) {{
                    amountInput.value = '{amount}';
                    amountInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                }}

                // Find and click trade button
                const buttons = document.querySelectorAll('button');
                for (const button of buttons) {{
                    const text = button.textContent.toLowerCase();
                    if (('{direction}' === 'CALL' && (text.includes('call') || text.includes('higher') || text.includes('up'))) ||
                        ('{direction}' === 'PUT' && (text.includes('put') || text.includes('lower') || text.includes('down')))) {{
                        button.click();
                        console.log('🎯 Direct trade button clicked: {direction}');
                        break;
                    }}
                }}
            }}
            """

            # Execute JavaScript in the web view
            self.web_view.page().runJavaScript(js_code)
            print(f"✅ Trade command sent to Quotex: {direction} ${amount} {duration}")

        except Exception as e:
            print(f"❌ Failed to execute trade: {e}")
            QMessageBox.warning(self, "خطا", f"خطا در اجرای معامله: {str(e)}")

    def _convert_duration_to_seconds(self, duration_text):
        """Convert duration text to seconds"""
        duration_map = {
            "5s": 5,
            "15s": 15,
            "30s": 30,
            "1m": 60,
            "5m": 300
        }
        return duration_map.get(duration_text, 5)
    
    def _update_time(self):
        """🕐 Update time display"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"🕐 {current_time}")
    
    def _apply_vip_style(self):
        """🎨 Apply VIP styling"""
        style = """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0F0F23, stop:0.5 #1A1A2E, stop:1 #16213E);
            color: #FFFFFF;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        QFrame#vip-header {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #8B5CF6, stop:1 #EC4899);
            border-radius: 10px;
            border: 2px solid #A855F7;
        }
        
        QLabel#vip-logo {
            font-size: 28px;
            font-weight: bold;
        }
        
        QLabel#vip-title {
            font-size: 18px;
            font-weight: bold;
            color: #FFFFFF;
        }
        
        QFrame#vip-control-panel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1F2937, stop:1 #374151);
            border: 2px solid #6366F1;
            border-radius: 10px;
        }
        
        QFrame#vip-quotex-panel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #111827, stop:1 #1F2937);
            border: 2px solid #8B5CF6;
            border-radius: 10px;
        }
        
        QGroupBox#vip-group {
            font-weight: bold;
            border: 2px solid #8B5CF6;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            color: #A855F7;
        }
        
        QGroupBox#vip-group::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton#vip-primary-btn, QPushButton#vip-connect-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #8B5CF6, stop:1 #7C3AED);
            border: 2px solid #8B5CF6;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            padding: 12px;
            font-size: 14px;
        }
        
        QPushButton#vip-primary-btn:hover, QPushButton#vip-connect-btn:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #7C3AED, stop:1 #6D28D9);
        }
        
        QPushButton#vip-secondary-btn, QPushButton#vip-test-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #374151, stop:1 #4B5563);
            border: 2px solid #6B7280;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            padding: 8px;
        }

        QPushButton#vip-test-btn:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #4B5563, stop:1 #6B7280);
        }
        
        QPushButton#vip-call-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #10B981, stop:1 #059669);
            border: 2px solid #10B981;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            padding: 10px;
        }
        
        QPushButton#vip-put-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #EF4444, stop:1 #DC2626);
            border: 2px solid #EF4444;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            padding: 10px;
        }
        
        QLabel#vip-panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #A855F7;
        }
        
        QLabel#vip-status {
            font-size: 14px;
            font-weight: bold;
            padding: 5px;
        }
        
        QComboBox#vip-combo, QSpinBox#vip-spinbox {
            background: #374151;
            border: 2px solid #6B7280;
            border-radius: 4px;
            color: white;
            padding: 5px;
        }
        """
        
        self.setStyleSheet(style)
    
    def closeEvent(self, event):
        """Handle close event"""
        if self.chrome_process:
            self.chrome_process.terminate()
        event.accept()


def main():
    """🚀 Main function"""
    app = QApplication(sys.argv)
    
    app.setApplicationName("VIP BIG BANG Real Quotex")
    app.setApplicationVersion("1.0.0")
    
    dashboard = VIPQuotexReal()
    dashboard.show()
    
    print("🚀 VIP BIG BANG Real Quotex Dashboard started")
    print("💎 Real Quotex connection ready")
    print("🔗 No demo mode - Real trading interface")
    print("📊 Quotex page loaded in dashboard")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
