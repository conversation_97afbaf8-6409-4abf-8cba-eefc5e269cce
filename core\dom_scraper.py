#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 VIP BIG BANG - DOM Scraper System
📊 Smart DOM Data Extraction
⚡ Real-Time Quotex Data Parser
💎 Professional JS Injection
"""

import time
import json
import re
from datetime import datetime

class SmartDOMScraper:
    """
    🔍 Smart DOM Scraper System
    📊 Real-Time Data Extraction
    ⚡ Professional JS Injection
    💎 Quotex Data Parser
    """

    def __init__(self, browser_core):
        self.browser = browser_core
        self.last_data = None
        self.data_history = []
        
        print("🔍 Smart DOM Scraper initialized")

    def inject_advanced_extractor(self):
        """💉 Inject Advanced Data Extractor"""
        try:
            print("💉 Injecting advanced DOM extractor...")
            
            extractor_script = """
            // Advanced Quotex Data Extractor
            window.VIP_EXTRACTOR = {
                // Selectors for different Quotex versions
                selectors: {
                    balance: [
                        '.balance', '[class*="balance"]', '[data-testid*="balance"]',
                        '.wallet-balance', '.account-balance', '.user-balance'
                    ],
                    asset: [
                        '.asset-name', '[class*="asset"]', '[class*="symbol"]',
                        '.current-asset', '.trading-asset', '.selected-asset'
                    ],
                    price: [
                        '.price', '[class*="price"]', '[class*="rate"]',
                        '.current-price', '.asset-price', '.live-price'
                    ],
                    profit: [
                        '.profit', '[class*="profit"]', '[class*="payout"]',
                        '.profit-percent', '.payout-rate', '.return-rate'
                    ],
                    otc: [
                        '.otc', '[class*="otc"]', '[title*="OTC"]',
                        '.otc-badge', '.otc-indicator', '[data-otc]'
                    ],
                    timeframe: [
                        '.timeframe', '[class*="timeframe"]', '[class*="period"]',
                        '.chart-period', '.time-period', '.interval'
                    ],
                    chart: [
                        '.chart', '[class*="chart"]', 'canvas',
                        '.trading-chart', '.price-chart', '#chart'
                    ],
                    buttons: {
                        call: [
                            '.call-btn', '[class*="call"]', '.buy-btn',
                            '.higher-btn', '.up-btn', '[data-direction="call"]'
                        ],
                        put: [
                            '.put-btn', '[class*="put"]', '.sell-btn',
                            '.lower-btn', '.down-btn', '[data-direction="put"]'
                        ]
                    }
                },
                
                // Find element by multiple selectors
                findElement: function(selectors) {
                    for (let selector of selectors) {
                        const el = document.querySelector(selector);
                        if (el) return el;
                    }
                    return null;
                },
                
                // Extract balance
                getBalance: function() {
                    try {
                        const el = this.findElement(this.selectors.balance);
                        if (el) {
                            const text = el.innerText || el.textContent;
                            const match = text.match(/[\d,]+\.?\d*/);
                            return match ? parseFloat(match[0].replace(',', '')) : null;
                        }
                        return null;
                    } catch(e) { return null; }
                },
                
                // Extract current asset
                getCurrentAsset: function() {
                    try {
                        const el = this.findElement(this.selectors.asset);
                        if (el) {
                            let text = el.innerText || el.textContent;
                            // Clean asset name
                            text = text.replace(/[^A-Z\/]/g, '');
                            return text || null;
                        }
                        return null;
                    } catch(e) { return null; }
                },
                
                // Extract current price
                getCurrentPrice: function() {
                    try {
                        const el = this.findElement(this.selectors.price);
                        if (el) {
                            const text = el.innerText || el.textContent;
                            const match = text.match(/\d+\.?\d*/);
                            return match ? parseFloat(match[0]) : null;
                        }
                        return null;
                    } catch(e) { return null; }
                },
                
                // Extract profit percentage
                getProfit: function() {
                    try {
                        const el = this.findElement(this.selectors.profit);
                        if (el) {
                            const text = el.innerText || el.textContent;
                            const match = text.match(/\d+/);
                            return match ? parseInt(match[0]) : null;
                        }
                        return null;
                    } catch(e) { return null; }
                },
                
                // Check if OTC mode
                isOTC: function() {
                    try {
                        const el = this.findElement(this.selectors.otc);
                        return el !== null;
                    } catch(e) { return false; }
                },
                
                // Get timeframe
                getTimeframe: function() {
                    try {
                        const el = this.findElement(this.selectors.timeframe);
                        if (el) {
                            const text = el.innerText || el.textContent;
                            return text.trim();
                        }
                        return null;
                    } catch(e) { return null; }
                },
                
                // Extract candle data from chart
                getCandleData: function() {
                    try {
                        const candles = [];
                        
                        // Try to find candle elements
                        const candleSelectors = [
                            '[class*="candle"]', '[class*="bar"]', 
                            '[class*="ohlc"]', '.chart-item'
                        ];
                        
                        for (let selector of candleSelectors) {
                            const elements = document.querySelectorAll(selector);
                            if (elements.length > 0) {
                                elements.forEach((el, index) => {
                                    const candleData = {
                                        index: index,
                                        open: el.getAttribute('data-open') || el.dataset.open,
                                        high: el.getAttribute('data-high') || el.dataset.high,
                                        low: el.getAttribute('data-low') || el.dataset.low,
                                        close: el.getAttribute('data-close') || el.dataset.close,
                                        volume: el.getAttribute('data-volume') || el.dataset.volume,
                                        time: el.getAttribute('data-time') || el.dataset.time,
                                        color: window.getComputedStyle(el).backgroundColor
                                    };
                                    
                                    // Only add if has valid data
                                    if (candleData.open || candleData.close) {
                                        candles.push(candleData);
                                    }
                                });
                                break;
                            }
                        }
                        
                        return candles;
                    } catch(e) { return []; }
                },
                
                // Get trading buttons status
                getButtonsStatus: function() {
                    try {
                        const callBtn = this.findElement(this.selectors.buttons.call);
                        const putBtn = this.findElement(this.selectors.buttons.put);
                        
                        return {
                            call: {
                                available: callBtn !== null,
                                enabled: callBtn ? !callBtn.disabled : false,
                                visible: callBtn ? callBtn.offsetParent !== null : false
                            },
                            put: {
                                available: putBtn !== null,
                                enabled: putBtn ? !putBtn.disabled : false,
                                visible: putBtn ? putBtn.offsetParent !== null : false
                            }
                        };
                    } catch(e) { 
                        return { call: { available: false }, put: { available: false } }; 
                    }
                },
                
                // Get market status
                getMarketStatus: function() {
                    try {
                        // Check for market closed indicators
                        const closedIndicators = [
                            '.market-closed', '[class*="closed"]', '.trading-disabled',
                            '.market-offline', '[class*="offline"]'
                        ];
                        
                        for (let selector of closedIndicators) {
                            if (document.querySelector(selector)) {
                                return 'closed';
                            }
                        }
                        
                        // Check for trading buttons availability
                        const buttons = this.getButtonsStatus();
                        if (buttons.call.available && buttons.put.available) {
                            return 'open';
                        }
                        
                        return 'unknown';
                    } catch(e) { return 'unknown'; }
                },
                
                // Get complete data
                getAllData: function() {
                    const data = {
                        balance: this.getBalance(),
                        asset: this.getCurrentAsset(),
                        price: this.getCurrentPrice(),
                        profit: this.getProfit(),
                        isOTC: this.isOTC(),
                        timeframe: this.getTimeframe(),
                        candles: this.getCandleData(),
                        buttons: this.getButtonsStatus(),
                        marketStatus: this.getMarketStatus(),
                        timestamp: Date.now(),
                        url: window.location.href,
                        title: document.title
                    };
                    
                    // Store in window for debugging
                    window.lastExtractedData = data;
                    
                    return data;
                },
                
                // Monitor for changes
                startMonitoring: function(callback, interval = 1000) {
                    if (this.monitorInterval) {
                        clearInterval(this.monitorInterval);
                    }
                    
                    this.monitorInterval = setInterval(() => {
                        const data = this.getAllData();
                        if (callback) callback(data);
                    }, interval);
                },
                
                stopMonitoring: function() {
                    if (this.monitorInterval) {
                        clearInterval(this.monitorInterval);
                        this.monitorInterval = null;
                    }
                }
            };
            
            console.log('🔍 VIP BIG BANG Advanced DOM Extractor Loaded');
            """
            
            self.browser.driver.execute_script(extractor_script)
            print("✅ Advanced DOM extractor injected successfully")
            return True
            
        except Exception as e:
            print(f"❌ DOM extractor injection error: {e}")
            return False

    def extract_real_time_data(self):
        """📊 Extract Real-Time Data"""
        try:
            if not self.browser.is_connected:
                print("⚠️ Browser not connected")
                return None
            
            # Execute data extraction
            data = self.browser.driver.execute_script("return window.VIP_EXTRACTOR.getAllData();")
            
            if data:
                # Process and validate data
                processed_data = self.process_extracted_data(data)
                
                # Store in history
                self.data_history.append(processed_data)
                if len(self.data_history) > 100:  # Keep last 100 records
                    self.data_history.pop(0)
                
                self.last_data = processed_data
                
                print(f"📊 Data extracted: {processed_data.get('asset', 'Unknown')} - ${processed_data.get('price', 'N/A')}")
                return processed_data
            else:
                print("⚠️ No data extracted from DOM")
                return None
                
        except Exception as e:
            print(f"❌ Real-time data extraction error: {e}")
            return None

    def process_extracted_data(self, raw_data):
        """🔄 Process Extracted Data"""
        try:
            processed = {
                'timestamp': datetime.now().isoformat(),
                'balance': self.clean_number(raw_data.get('balance')),
                'asset': self.clean_text(raw_data.get('asset')),
                'price': self.clean_number(raw_data.get('price')),
                'profit': self.clean_number(raw_data.get('profit')),
                'isOTC': bool(raw_data.get('isOTC', False)),
                'timeframe': self.clean_text(raw_data.get('timeframe')),
                'marketStatus': raw_data.get('marketStatus', 'unknown'),
                'candles': raw_data.get('candles', []),
                'buttons': raw_data.get('buttons', {}),
                'url': raw_data.get('url', ''),
                'title': raw_data.get('title', ''),
                'raw_timestamp': raw_data.get('timestamp')
            }
            
            # Add derived data
            processed['is_trading_available'] = (
                processed['marketStatus'] == 'open' and
                processed['buttons'].get('call', {}).get('available', False) and
                processed['buttons'].get('put', {}).get('available', False)
            )
            
            return processed
            
        except Exception as e:
            print(f"❌ Data processing error: {e}")
            return raw_data

    def clean_number(self, value):
        """🧹 Clean Number Value"""
        try:
            if value is None:
                return None
            if isinstance(value, (int, float)):
                return float(value)
            if isinstance(value, str):
                # Remove non-numeric characters except decimal point
                cleaned = re.sub(r'[^\d.]', '', value)
                return float(cleaned) if cleaned else None
            return None
        except:
            return None

    def clean_text(self, value):
        """🧹 Clean Text Value"""
        try:
            if value is None:
                return None
            if isinstance(value, str):
                return value.strip()
            return str(value).strip()
        except:
            return None

    def start_monitoring(self, callback=None, interval=1):
        """📡 Start Real-Time Monitoring"""
        try:
            print(f"📡 Starting real-time monitoring (interval: {interval}s)...")
            
            monitor_script = f"""
            window.VIP_EXTRACTOR.startMonitoring(function(data) {{
                window.monitoredData = data;
            }}, {interval * 1000});
            """
            
            self.browser.driver.execute_script(monitor_script)
            print("✅ Real-time monitoring started")
            return True
            
        except Exception as e:
            print(f"❌ Monitoring start error: {e}")
            return False

    def stop_monitoring(self):
        """⏹️ Stop Real-Time Monitoring"""
        try:
            self.browser.driver.execute_script("window.VIP_EXTRACTOR.stopMonitoring();")
            print("⏹️ Real-time monitoring stopped")
            return True
        except Exception as e:
            print(f"❌ Monitoring stop error: {e}")
            return False

    def get_monitored_data(self):
        """📊 Get Monitored Data"""
        try:
            data = self.browser.driver.execute_script("return window.monitoredData;")
            if data:
                return self.process_extracted_data(data)
            return None
        except Exception as e:
            print(f"❌ Monitored data retrieval error: {e}")
            return None

    def get_data_history(self, limit=10):
        """📈 Get Data History"""
        return self.data_history[-limit:] if self.data_history else []

    def get_status(self):
        """📊 Get Scraper Status"""
        return {
            "last_extraction": self.last_data.get('timestamp') if self.last_data else None,
            "history_count": len(self.data_history),
            "browser_connected": self.browser.is_connected,
            "last_asset": self.last_data.get('asset') if self.last_data else None,
            "last_price": self.last_data.get('price') if self.last_data else None
        }

# Test function
def test_dom_scraper():
    """🧪 Test DOM Scraper"""
    print("🧪 Testing Smart DOM Scraper...")
    
    from browser_core import StealthBrowserCore
    
    browser = StealthBrowserCore()
    if browser.create_stealth_browser() and browser.connect_to_quotex():
        scraper = SmartDOMScraper(browser)
        
        if scraper.inject_advanced_extractor():
            # Test data extraction
            for i in range(5):
                data = scraper.extract_real_time_data()
                if data:
                    print(f"📊 Test {i+1}: {json.dumps(data, indent=2)}")
                time.sleep(3)
        
        browser.close_browser()
    
    print("🧪 DOM Scraper test completed")

if __name__ == "__main__":
    test_dom_scraper()
