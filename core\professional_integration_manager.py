"""
VIP BIG BANG Enterprise - Professional Integration Manager
Unified Management of All Professional Components
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Any, List, Callable
from datetime import datetime
from pathlib import Path

# Import professional components (with fallbacks)
try:
    from .professional_playwright_controller import ProfessionalPlaywrightController
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright not available - using Selenium fallback")

try:
    from .professional_canvas_chart_reader import ProfessionalCanvasChartReader
    CANVAS_READER_AVAILABLE = True
except ImportError:
    CANVAS_READER_AVAILABLE = False
    print("⚠️ Canvas reader not available - using DOM fallback")

try:
    from .professional_websocket_monitor import ProfessionalWebSocketMonitor
    WEBSOCKET_MONITOR_AVAILABLE = True
except ImportError:
    WEBSOCKET_MONITOR_AVAILABLE = False
    print("⚠️ WebSocket monitor not available - using basic monitoring")

# Import existing components as fallbacks
from .browser_core import BrowserCore
from .data_extractor import DataExtractor

class ProfessionalIntegrationManager:
    """
    🚀 Professional Integration Manager
    
    Unified management of all professional components:
    - Playwright Browser Controller
    - Canvas Chart Reader
    - WebSocket Monitor
    - Data Synchronization
    - Performance Monitoring
    - Error Handling & Recovery
    """
    
    def __init__(self, settings: Optional[Dict] = None):
        self.settings = settings or {}
        self.logger = logging.getLogger(__name__)
        
        # Professional components
        self.browser_controller: Optional[ProfessionalPlaywrightController] = None
        self.chart_reader: Optional[ProfessionalCanvasChartReader] = None
        self.websocket_monitor: Optional[ProfessionalWebSocketMonitor] = None
        
        # Integration state
        self.is_initialized = False
        self.is_running = False
        self.components_status = {
            'browser': False,
            'chart_reader': False,
            'websocket_monitor': False
        }
        
        # Data synchronization
        self.unified_data = {
            'quotex_data': {},
            'chart_analysis': {},
            'websocket_data': {},
            'performance_metrics': {},
            'last_update': None
        }
        
        # Data callbacks
        self.data_callbacks: List[Callable] = []
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        
        # Performance tracking
        self.performance_metrics = {
            'total_data_extractions': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'average_extraction_time': 0,
            'last_extraction_time': 0,
            'uptime_start': time.time()
        }
        
        # Configuration
        self.config = {
            'data_extraction_interval': 2.0,  # seconds
            'chart_analysis_interval': 5.0,   # seconds
            'websocket_check_interval': 1.0,  # seconds
            'performance_log_interval': 30.0, # seconds
            'auto_recovery': True,
            'max_retry_attempts': 3
        }
        
        self.logger.info("🚀 Professional Integration Manager initialized")
    
    async def initialize(self) -> bool:
        """🔧 Initialize All Professional Components with Fallbacks"""
        try:
            if self.is_initialized:
                self.logger.warning("⚠️ Integration manager already initialized")
                return True

            self.logger.info("🔧 Initializing professional components...")

            # Initialize Browser Controller (with fallback)
            if PLAYWRIGHT_AVAILABLE:
                self.logger.info("🚀 Using Professional Playwright Controller")
                self.browser_controller = ProfessionalPlaywrightController(self.settings.get('browser', {}))
                browser_success = await self.browser_controller.initialize_browser()
            else:
                self.logger.info("🔄 Using Selenium Browser Controller (fallback)")
                self.browser_controller = BrowserCore()
                browser_success = await self.initialize_selenium_fallback()

            self.components_status['browser'] = browser_success

            if not browser_success:
                self.logger.warning("⚠️ Browser controller initialization failed, continuing with limited functionality")

            # Initialize Chart Reader (with fallback)
            if CANVAS_READER_AVAILABLE:
                self.logger.info("📊 Using Professional Canvas Chart Reader")
                self.chart_reader = ProfessionalCanvasChartReader(self.settings.get('chart_reader', {}))
                self.components_status['chart_reader'] = True
            else:
                self.logger.info("📊 Using DOM-based Chart Reader (fallback)")
                self.chart_reader = DataExtractor()
                self.components_status['chart_reader'] = True

            # Initialize WebSocket Monitor (with fallback)
            if WEBSOCKET_MONITOR_AVAILABLE:
                self.logger.info("🌐 Using Professional WebSocket Monitor")
                self.websocket_monitor = ProfessionalWebSocketMonitor(self.settings.get('websocket', {}))
                self.components_status['websocket_monitor'] = True
            else:
                self.logger.info("🌐 Using Basic WebSocket Monitor (fallback)")
                self.websocket_monitor = self.create_basic_websocket_monitor()
                self.components_status['websocket_monitor'] = True

            # Setup data callbacks
            await self.setup_data_callbacks()

            self.is_initialized = True
            self.logger.info("✅ Professional components initialized successfully (with fallbacks)")
            return True

        except Exception as e:
            self.logger.error(f"❌ Component initialization error: {e}")
            return False

    async def initialize_selenium_fallback(self) -> bool:
        """🔄 Initialize Selenium Fallback"""
        try:
            # Initialize existing browser core
            success = self.browser_controller.initialize()
            if success:
                self.logger.info("✅ Selenium fallback initialized")
            return success
        except Exception as e:
            self.logger.error(f"❌ Selenium fallback error: {e}")
            return False

    def create_basic_websocket_monitor(self):
        """🌐 Create Basic WebSocket Monitor"""
        class BasicWebSocketMonitor:
            def __init__(self):
                self.is_monitoring = False
                self.message_callbacks = []

            async def start_monitoring(self, page):
                self.is_monitoring = True
                return True

            async def stop_monitoring(self):
                self.is_monitoring = False

            def add_message_callback(self, callback):
                self.message_callbacks.append(callback)

            async def extract_websocket_data(self, page):
                return {'basic_mode': True, 'timestamp': datetime.now().isoformat()}

            def get_trading_data(self):
                return {'health_metrics': {}, 'basic_mode': True}

        return BasicWebSocketMonitor()
    
    async def setup_data_callbacks(self):
        """📡 Setup Data Callbacks Between Components"""
        try:
            # Browser controller data callback
            if self.browser_controller:
                self.browser_controller.add_data_callback(self.on_browser_data)
            
            # WebSocket monitor data callback
            if self.websocket_monitor:
                self.websocket_monitor.add_message_callback(self.on_websocket_message)
            
            self.logger.info("📡 Data callbacks setup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Data callback setup error: {e}")
    
    async def start_professional_monitoring(self, quotex_url: str = "https://qxbroker.com/en/trade") -> bool:
        """🚀 Start Professional Monitoring System"""
        try:
            if not self.is_initialized:
                self.logger.error("❌ Components not initialized")
                return False
            
            if self.is_running:
                self.logger.warning("⚠️ Professional monitoring already running")
                return True
            
            self.logger.info("🚀 Starting professional monitoring system...")
            
            # Navigate to Quotex
            navigation_success = await self.browser_controller.navigate_to_quotex(quotex_url)
            if not navigation_success:
                self.logger.error("❌ Failed to navigate to Quotex")
                return False
            
            # Start WebSocket monitoring
            websocket_success = await self.websocket_monitor.start_monitoring(self.browser_controller.page)
            if not websocket_success:
                self.logger.warning("⚠️ WebSocket monitoring failed to start")
            
            # Start background tasks
            self.background_tasks = [
                asyncio.create_task(self.data_extraction_loop()),
                asyncio.create_task(self.chart_analysis_loop()),
                asyncio.create_task(self.websocket_data_loop()),
                asyncio.create_task(self.performance_monitor_loop()),
                asyncio.create_task(self.human_behavior_loop())
            ]
            
            self.is_running = True
            self.performance_metrics['uptime_start'] = time.time()
            
            self.logger.info("✅ Professional monitoring system started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Professional monitoring start error: {e}")
            return False
    
    async def data_extraction_loop(self):
        """📊 Main Data Extraction Loop"""
        while self.is_running:
            try:
                start_time = time.time()
                
                # Extract Quotex data
                quotex_data = await self.browser_controller.extract_quotex_data()
                
                if quotex_data:
                    self.unified_data['quotex_data'] = quotex_data
                    self.unified_data['last_update'] = datetime.now().isoformat()
                    
                    # Update performance metrics
                    extraction_time = time.time() - start_time
                    self.performance_metrics['total_data_extractions'] += 1
                    self.performance_metrics['successful_extractions'] += 1
                    self.performance_metrics['last_extraction_time'] = extraction_time
                    
                    # Update average extraction time
                    total_extractions = self.performance_metrics['total_data_extractions']
                    current_avg = self.performance_metrics['average_extraction_time']
                    self.performance_metrics['average_extraction_time'] = (
                        (current_avg * (total_extractions - 1) + extraction_time) / total_extractions
                    )
                    
                    # Notify callbacks
                    await self.notify_data_callbacks('quotex_data', quotex_data)
                    
                else:
                    self.performance_metrics['failed_extractions'] += 1
                    self.logger.warning("⚠️ Failed to extract Quotex data")
                
                await asyncio.sleep(self.config['data_extraction_interval'])
                
            except Exception as e:
                self.logger.error(f"❌ Data extraction loop error: {e}")
                self.performance_metrics['failed_extractions'] += 1
                await asyncio.sleep(5)  # Wait longer on error
    
    async def chart_analysis_loop(self):
        """📊 Chart Analysis Loop"""
        while self.is_running:
            try:
                if self.browser_controller and self.browser_controller.page:
                    # Analyze chart canvas
                    chart_analysis = await self.chart_reader.analyze_chart_canvas(
                        self.browser_controller.page
                    )
                    
                    if chart_analysis:
                        self.unified_data['chart_analysis'] = chart_analysis
                        
                        # Notify callbacks
                        await self.notify_data_callbacks('chart_analysis', chart_analysis)
                        
                        self.logger.debug("📊 Chart analysis completed")
                    
                await asyncio.sleep(self.config['chart_analysis_interval'])
                
            except Exception as e:
                self.logger.error(f"❌ Chart analysis loop error: {e}")
                await asyncio.sleep(10)
    
    async def websocket_data_loop(self):
        """🌐 WebSocket Data Loop"""
        while self.is_running:
            try:
                if self.browser_controller and self.browser_controller.page:
                    # Extract WebSocket data
                    websocket_data = await self.websocket_monitor.extract_websocket_data(
                        self.browser_controller.page
                    )
                    
                    if websocket_data:
                        self.unified_data['websocket_data'] = websocket_data
                        
                        # Notify callbacks
                        await self.notify_data_callbacks('websocket_data', websocket_data)
                        
                        self.logger.debug("🌐 WebSocket data extracted")
                
                await asyncio.sleep(self.config['websocket_check_interval'])
                
            except Exception as e:
                self.logger.error(f"❌ WebSocket data loop error: {e}")
                await asyncio.sleep(5)
    
    async def performance_monitor_loop(self):
        """📊 Performance Monitoring Loop"""
        while self.is_running:
            try:
                # Collect performance metrics from all components
                browser_metrics = await self.browser_controller.get_performance_metrics()
                websocket_metrics = self.websocket_monitor.get_trading_data()
                chart_metrics = self.chart_reader.get_latest_analysis()
                
                # Compile unified performance metrics
                unified_metrics = {
                    'integration_manager': self.performance_metrics,
                    'browser_controller': browser_metrics,
                    'websocket_monitor': websocket_metrics.get('health_metrics', {}),
                    'chart_reader': chart_metrics,
                    'uptime': time.time() - self.performance_metrics['uptime_start'],
                    'components_status': self.components_status,
                    'timestamp': datetime.now().isoformat()
                }
                
                self.unified_data['performance_metrics'] = unified_metrics
                
                # Log performance summary
                uptime = unified_metrics['uptime']
                success_rate = (
                    self.performance_metrics['successful_extractions'] / 
                    max(self.performance_metrics['total_data_extractions'], 1) * 100
                )
                
                self.logger.info(
                    f"📊 Performance: {uptime:.1f}s uptime, "
                    f"{success_rate:.1f}% success rate, "
                    f"{self.performance_metrics['average_extraction_time']:.3f}s avg extraction"
                )
                
                await asyncio.sleep(self.config['performance_log_interval'])
                
            except Exception as e:
                self.logger.error(f"❌ Performance monitor error: {e}")
                await asyncio.sleep(30)
    
    async def human_behavior_loop(self):
        """🤖 Human Behavior Simulation Loop"""
        while self.is_running:
            try:
                if self.browser_controller:
                    await self.browser_controller.simulate_human_behavior()
                
                # Random interval for human-like behavior
                await asyncio.sleep(10 + (time.time() % 20))  # 10-30 seconds
                
            except Exception as e:
                self.logger.error(f"❌ Human behavior simulation error: {e}")
                await asyncio.sleep(30)
    
    async def on_browser_data(self, data: Dict[str, Any]):
        """📊 Handle Browser Data Callback"""
        try:
            self.logger.debug("📊 Browser data received")
            # Additional processing can be added here
            
        except Exception as e:
            self.logger.error(f"❌ Browser data callback error: {e}")
    
    async def on_websocket_message(self, message: Dict[str, Any]):
        """🌐 Handle WebSocket Message Callback"""
        try:
            self.logger.debug("🌐 WebSocket message received")
            # Additional processing can be added here
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket message callback error: {e}")
    
    async def notify_data_callbacks(self, data_type: str, data: Dict[str, Any]):
        """📡 Notify Data Callbacks"""
        try:
            for callback in self.data_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data_type, data)
                    else:
                        callback(data_type, data)
                except Exception as e:
                    self.logger.error(f"❌ Data callback error: {e}")
                    
        except Exception as e:
            self.logger.error(f"❌ Callback notification error: {e}")
    
    def add_data_callback(self, callback: Callable):
        """📡 Add Data Callback"""
        self.data_callbacks.append(callback)
        self.logger.info("📡 Data callback added to integration manager")
    
    def remove_data_callback(self, callback: Callable):
        """📡 Remove Data Callback"""
        if callback in self.data_callbacks:
            self.data_callbacks.remove(callback)
            self.logger.info("📡 Data callback removed from integration manager")
    
    def get_unified_data(self) -> Dict[str, Any]:
        """📊 Get Unified Data from All Components"""
        return {
            **self.unified_data,
            'components_status': self.components_status,
            'is_running': self.is_running,
            'extraction_timestamp': datetime.now().isoformat()
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """📊 Get Performance Summary"""
        uptime = time.time() - self.performance_metrics['uptime_start']
        success_rate = (
            self.performance_metrics['successful_extractions'] / 
            max(self.performance_metrics['total_data_extractions'], 1) * 100
        )
        
        return {
            'uptime_seconds': uptime,
            'success_rate_percent': success_rate,
            'total_extractions': self.performance_metrics['total_data_extractions'],
            'successful_extractions': self.performance_metrics['successful_extractions'],
            'failed_extractions': self.performance_metrics['failed_extractions'],
            'average_extraction_time': self.performance_metrics['average_extraction_time'],
            'components_status': self.components_status,
            'is_running': self.is_running
        }
    
    async def stop_professional_monitoring(self):
        """⏹️ Stop Professional Monitoring System"""
        try:
            self.logger.info("⏹️ Stopping professional monitoring system...")
            
            self.is_running = False
            
            # Cancel background tasks
            for task in self.background_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            self.background_tasks.clear()
            
            # Stop components
            if self.websocket_monitor:
                await self.websocket_monitor.stop_monitoring()
            
            if self.browser_controller:
                await self.browser_controller.close()
            
            self.logger.info("✅ Professional monitoring system stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Professional monitoring stop error: {e}")
    
    async def restart_component(self, component_name: str) -> bool:
        """🔄 Restart Specific Component"""
        try:
            self.logger.info(f"🔄 Restarting component: {component_name}")
            
            if component_name == 'browser' and self.browser_controller:
                await self.browser_controller.close()
                self.browser_controller = ProfessionalPlaywrightController(self.settings.get('browser', {}))
                success = await self.browser_controller.initialize_browser()
                self.components_status['browser'] = success
                return success
            
            elif component_name == 'websocket_monitor' and self.websocket_monitor:
                await self.websocket_monitor.stop_monitoring()
                self.websocket_monitor = ProfessionalWebSocketMonitor(self.settings.get('websocket', {}))
                if self.browser_controller and self.browser_controller.page:
                    success = await self.websocket_monitor.start_monitoring(self.browser_controller.page)
                    self.components_status['websocket_monitor'] = success
                    return success
            
            elif component_name == 'chart_reader':
                self.chart_reader = ProfessionalCanvasChartReader(self.settings.get('chart_reader', {}))
                self.components_status['chart_reader'] = True
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Component restart error: {e}")
            return False
    
    def __del__(self):
        """🗑️ Destructor"""
        if self.is_running:
            try:
                asyncio.create_task(self.stop_professional_monitoring())
            except:
                pass
