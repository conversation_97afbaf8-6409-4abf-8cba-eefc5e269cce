"""
🧪 BASIC TEST - Hardware & Browser Fingerprinting
🔍 تست پایه سیستم تشخیص (فقط کتابخانه‌های استاندارد پایتون)
"""

import sys
import platform
import json
import hashlib
from datetime import datetime
from pathlib import Path

def test_basic_system_info():
    """🔍 تست اطلاعات پایه سیستم"""
    print("🔍 Testing Basic System Information...")
    print("=" * 50)
    
    try:
        # Basic system information
        system_info = {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "platform": platform.platform(),
            "architecture": platform.architecture(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "python_implementation": platform.python_implementation()
        }
        
        # Display information
        print(f"🖥️ System: {system_info['system']} {system_info['release']}")
        print(f"📋 Platform: {system_info['platform']}")
        print(f"🏗️ Architecture: {system_info['architecture']}")
        print(f"💻 Machine: {system_info['machine']}")
        print(f"🔧 Processor: {system_info['processor']}")
        print(f"🐍 Python: {system_info['python_version']} ({system_info['python_implementation']})")
        
        print("✅ Basic system information collected")
        return system_info
        
    except Exception as e:
        print(f"❌ Basic system info test failed: {e}")
        return None

def test_vm_detection_basic():
    """🔍 تست پایه تشخیص محیط مجازی"""
    print("\n🔍 Testing Basic VM Detection...")
    print("=" * 50)
    
    try:
        vm_indicators = []
        confidence = 0
        
        # Check processor name for VM indicators
        processor = platform.processor().lower()
        vm_cpu_indicators = ["virtual", "vmware", "virtualbox", "qemu", "xen", "hyper-v"]
        
        for indicator in vm_cpu_indicators:
            if indicator in processor:
                vm_indicators.append(f"CPU contains '{indicator}'")
                confidence += 25
        
        # Check platform for VM indicators
        platform_info = platform.platform().lower()
        vm_platform_indicators = ["vmware", "virtualbox", "qemu", "hyper-v"]
        
        for indicator in vm_platform_indicators:
            if indicator in platform_info:
                vm_indicators.append(f"Platform contains '{indicator}'")
                confidence += 30
        
        # Check system for VM indicators
        system_info = platform.system().lower()
        if "linux" in system_info:
            # Additional checks for Linux VMs could be added here
            pass
        
        # Results
        is_vm = confidence >= 50
        vm_type = "Unknown"
        
        # Determine VM type
        indicator_text = " ".join(vm_indicators).lower()
        if "vmware" in indicator_text:
            vm_type = "VMware"
        elif "virtualbox" in indicator_text:
            vm_type = "VirtualBox"
        elif "qemu" in indicator_text:
            vm_type = "QEMU"
        elif "hyper-v" in indicator_text:
            vm_type = "Hyper-V"
        
        vm_detection = {
            "is_vm": is_vm,
            "vm_type": vm_type,
            "confidence": confidence,
            "indicators": vm_indicators
        }
        
        print(f"🔍 VM Detection Results:")
        print(f"   Is VM: {'Yes' if is_vm else 'No'}")
        print(f"   VM Type: {vm_type}")
        print(f"   Confidence: {confidence}%")
        
        if vm_indicators:
            print("   Indicators:")
            for indicator in vm_indicators:
                print(f"     - {indicator}")
        else:
            print("   ✅ No VM indicators found")
        
        if is_vm:
            print(f"🚨 Virtual Machine detected: {vm_type}")
        else:
            print("✅ Physical machine confirmed")
        
        print("✅ VM detection test completed")
        return vm_detection
        
    except Exception as e:
        print(f"❌ VM detection test failed: {e}")
        return None

def generate_browser_fingerprint_script():
    """🌐 تولید اسکریپت fingerprinting مرورگر"""
    print("\n🌐 Generating Browser Fingerprint Script...")
    print("=" * 50)
    
    try:
        script = '''
// 🔍 VIP BIG BANG Advanced Browser Fingerprinting Script
(function() {
    console.log('🔍 VIP BIG BANG Browser Fingerprinting Started...');
    
    const fingerprint = {
        timestamp: new Date().toISOString(),
        url: window.location.href
    };
    
    // === HARDWARE DETECTION === //
    
    // RAM Detection
    fingerprint.deviceMemory = navigator.deviceMemory || 'unknown';
    
    // CPU Detection  
    fingerprint.hardwareConcurrency = navigator.hardwareConcurrency || 'unknown';
    
    // === GPU DETECTION === //
    function getGPUInfo() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) return { error: 'WebGL not supported' };
            
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            const gpuInfo = {
                vendor: 'unknown',
                renderer: 'unknown',
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
            };
            
            if (debugInfo) {
                gpuInfo.vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                gpuInfo.renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            }
            
            return gpuInfo;
        } catch (e) {
            return { error: e.message };
        }
    }
    
    fingerprint.gpu = getGPUInfo();
    
    // === SCREEN DETECTION === //
    fingerprint.screen = {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth,
        availWidth: screen.availWidth,
        availHeight: screen.availHeight,
        devicePixelRatio: window.devicePixelRatio
    };
    
    // === BROWSER DETECTION === //
    fingerprint.browser = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        languages: navigator.languages,
        cookieEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack,
        maxTouchPoints: navigator.maxTouchPoints
    };
    
    // === TIMEZONE DETECTION === //
    fingerprint.timezone = {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: new Date().getTimezoneOffset()
    };
    
    // === AUTOMATION DETECTION === //
    fingerprint.automation = {
        webdriver: navigator.webdriver,
        phantom: !!(window.callPhantom || window._phantom),
        selenium: !!window.document.$cdc_asdjflasutopfhvcZLmcfl_
    };
    
    // === VM DETECTION === //
    function detectVM() {
        const indicators = [];
        let confidence = 0;
        
        // GPU-based VM detection
        const gpu = fingerprint.gpu;
        if (gpu && gpu.renderer) {
            const renderer = gpu.renderer.toLowerCase();
            const vmGpuIndicators = ['microsoft basic', 'vmware', 'virtualbox', 'swiftshader'];
            
            vmGpuIndicators.forEach(indicator => {
                if (renderer.includes(indicator)) {
                    indicators.push(`GPU: ${indicator}`);
                    confidence += 30;
                }
            });
        }
        
        // Memory-based detection
        if (fingerprint.deviceMemory && fingerprint.deviceMemory <= 4) {
            indicators.push('Low memory (≤4GB)');
            confidence += 20;
        }
        
        // CPU-based detection
        if (fingerprint.hardwareConcurrency && fingerprint.hardwareConcurrency <= 2) {
            indicators.push('Low CPU cores (≤2)');
            confidence += 15;
        }
        
        // Screen resolution detection
        if (fingerprint.screen.width === 1024 && fingerprint.screen.height === 768) {
            indicators.push('Common VM resolution (1024x768)');
            confidence += 10;
        }
        
        return {
            isVM: confidence >= 50,
            confidence: confidence,
            indicators: indicators
        };
    }
    
    fingerprint.vmDetection = detectVM();
    
    // === SEND TO PYTHON APPLICATION === //
    window.VIP_BIG_BANG_FINGERPRINT = fingerprint;
    
    // Try WebSocket
    try {
        const ws = new WebSocket('ws://localhost:8765');
        ws.onopen = function() {
            ws.send(JSON.stringify({
                type: 'browser_fingerprint',
                data: fingerprint
            }));
            ws.close();
        };
    } catch (e) {
        console.log('WebSocket failed:', e);
    }
    
    // Try Chrome extension
    if (window.chrome && window.chrome.runtime) {
        try {
            chrome.runtime.sendMessage({
                type: 'ADVANCED_FINGERPRINT',
                data: fingerprint
            });
        } catch (e) {
            console.log('Extension communication failed:', e);
        }
    }
    
    console.log('🔍 VIP BIG BANG Browser Fingerprint:', fingerprint);
    return fingerprint;
})();
'''
        
        # Save script to file
        script_file = "vip_browser_fingerprint.js"
        with open(script_file, "w", encoding="utf-8") as f:
            f.write(script)
        
        print(f"📜 Browser fingerprint script generated")
        print(f"💾 Script saved to: {script_file}")
        print(f"📊 Script size: {len(script)} characters")
        
        print("\n📋 Script Features:")
        print("   ✅ Hardware detection (RAM, CPU)")
        print("   ✅ GPU information (WebGL)")
        print("   ✅ Screen details")
        print("   ✅ Browser information")
        print("   ✅ Timezone detection")
        print("   ✅ Automation detection")
        print("   ✅ VM detection")
        print("   ✅ WebSocket communication")
        print("   ✅ Chrome extension support")
        
        print("✅ Browser fingerprint script generated")
        return script_file
        
    except Exception as e:
        print(f"❌ Browser script generation failed: {e}")
        return None

def generate_hardware_fingerprint(system_info, vm_detection):
    """🔐 تولید fingerprint سخت‌افزاری"""
    print("\n🔐 Generating Hardware Fingerprint...")
    print("=" * 50)
    
    try:
        # Create fingerprint data
        fingerprint_data = {
            "system": system_info.get("system", ""),
            "platform": system_info.get("platform", ""),
            "processor": system_info.get("processor", ""),
            "architecture": str(system_info.get("architecture", "")),
            "python_version": system_info.get("python_version", ""),
            "vm_detected": vm_detection.get("is_vm", False),
            "vm_confidence": vm_detection.get("confidence", 0)
        }
        
        # Generate hash
        fingerprint_string = json.dumps(fingerprint_data, sort_keys=True)
        fingerprint_hash = hashlib.sha256(fingerprint_string.encode()).hexdigest()
        
        fingerprint = {
            "data": fingerprint_data,
            "hash": fingerprint_hash,
            "short_hash": fingerprint_hash[:16],
            "generated_at": datetime.now().isoformat()
        }
        
        print(f"🔐 Hardware fingerprint generated")
        print(f"   Hash: {fingerprint['short_hash']}...")
        print(f"   VM Detected: {'Yes' if fingerprint_data['vm_detected'] else 'No'}")
        print(f"   Confidence: {fingerprint_data['vm_confidence']}%")
        
        print("✅ Hardware fingerprint completed")
        return fingerprint
        
    except Exception as e:
        print(f"❌ Hardware fingerprint generation failed: {e}")
        return None

def generate_complete_report(system_info, vm_detection, fingerprint, script_file):
    """📄 تولید گزارش کامل"""
    print("\n📄 Generating Complete Report...")
    print("=" * 50)
    
    try:
        report = {
            "report_info": {
                "generated_at": datetime.now().isoformat(),
                "version": "1.0.0",
                "generator": "VIP BIG BANG Basic Test",
                "test_type": "Basic Hardware & Browser Detection"
            },
            "system_information": system_info,
            "vm_detection": vm_detection,
            "hardware_fingerprint": fingerprint,
            "browser_script": {
                "file": script_file,
                "features": [
                    "Hardware detection",
                    "GPU information",
                    "Screen details",
                    "Browser information",
                    "Timezone detection",
                    "Automation detection",
                    "VM detection",
                    "WebSocket communication"
                ]
            },
            "security_analysis": {
                "risk_level": "HIGH" if vm_detection.get("is_vm", False) else "LOW",
                "vm_detected": vm_detection.get("is_vm", False),
                "confidence": vm_detection.get("confidence", 0),
                "indicators": vm_detection.get("indicators", [])
            },
            "recommendations": []
        }
        
        # Generate recommendations
        if vm_detection.get("is_vm", False):
            report["recommendations"].append("⚠️ Virtual Machine detected - Consider using physical hardware")
            report["recommendations"].append("🔍 Review VM configuration for security")
        else:
            report["recommendations"].append("✅ Physical machine confirmed - Good security baseline")
        
        report["recommendations"].append("🌐 Use generated browser script for web fingerprinting")
        report["recommendations"].append("🔄 Monitor system regularly for changes")
        
        # Save report
        report_file = "vip_complete_report.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Complete report saved to: {report_file}")
        
        # Show summary
        print("\n📊 Report Summary:")
        print(f"   System: {system_info.get('system', 'Unknown')}")
        print(f"   Platform: {system_info.get('platform', 'Unknown')}")
        print(f"   Security Risk: {report['security_analysis']['risk_level']}")
        print(f"   VM Detected: {'Yes' if vm_detection.get('is_vm', False) else 'No'}")
        
        if report["recommendations"]:
            print("   Recommendations:")
            for rec in report["recommendations"]:
                print(f"     {rec}")
        
        print("✅ Complete report generated")
        return report_file
        
    except Exception as e:
        print(f"❌ Complete report generation failed: {e}")
        return None

def main():
    """تابع اصلی تست"""
    print("🧪 VIP BIG BANG - Basic Fingerprinting Test")
    print("=" * 60)
    print("🔍 Testing basic hardware and browser detection")
    print("🚀 Using only Python standard libraries")
    print("=" * 60)
    
    # Test results
    results = {
        "system_info": None,
        "vm_detection": None,
        "fingerprint": None,
        "browser_script": None,
        "report": None
    }
    
    try:
        # Test 1: Basic System Information
        results["system_info"] = test_basic_system_info()
        
        # Test 2: VM Detection
        results["vm_detection"] = test_vm_detection_basic()
        
        # Test 3: Hardware Fingerprint
        if results["system_info"] and results["vm_detection"]:
            results["fingerprint"] = generate_hardware_fingerprint(
                results["system_info"], 
                results["vm_detection"]
            )
        
        # Test 4: Browser Script
        results["browser_script"] = generate_browser_fingerprint_script()
        
        # Test 5: Complete Report
        if all(results.values()):
            results["report"] = generate_complete_report(
                results["system_info"],
                results["vm_detection"],
                results["fingerprint"],
                results["browser_script"]
            )
        
        # Final results
        print("\n🏆 Test Results Summary:")
        print("=" * 50)
        
        success_count = sum(1 for result in results.values() if result is not None)
        total_tests = len(results)
        
        print(f"✅ Successful tests: {success_count}/{total_tests}")
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result is not None else "❌ FAILED"
            print(f"{test_name.upper().replace('_', ' ')}: {status}")
        
        if success_count == total_tests:
            print("\n🎉 All tests passed! Basic fingerprinting system is working.")
        else:
            print(f"\n⚠️ {total_tests - success_count} tests failed.")
        
        # Show generated files
        print("\n📁 Generated Files:")
        if results["browser_script"]:
            print(f"   📜 {results['browser_script']}")
        if results["report"]:
            print(f"   📄 {results['report']}")
        
        # Next steps
        print("\n📋 Next Steps:")
        print("1. Install advanced packages: pip install psutil wmi cpuinfo websockets")
        print("2. Run 'python quick_test_fingerprinting.py' for full test")
        print("3. Use generated browser script in web pages")
        print("4. Integrate with VIP BIG BANG main application")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
