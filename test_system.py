#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
VIP BIG BANG System Test
Quick test to verify all systems are working
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all critical imports"""
    print("Testing imports...")
    
    try:
        import PySide6
        print("OK PySide6 available")
    except ImportError as e:
        print(f"FAIL PySide6 error: {e}")
        return False

    try:
        from core.analysis_engine import AnalysisEngine
        print("OK Analysis Engine available")
    except ImportError as e:
        print(f"FAIL Analysis Engine error: {e}")
        return False

    try:
        from core.signal_manager import SignalManager
        print("OK Signal Manager available")
    except ImportError as e:
        print(f"FAIL Signal Manager error: {e}")
        return False

    try:
        from core.settings import Settings
        print("OK Settings available")
    except ImportError as e:
        print(f"FAIL Settings error: {e}")
        return False

    try:
        from trading.autotrade import AutoTrader
        print("OK AutoTrader available")
    except ImportError as e:
        print(f"FAIL AutoTrader error: {e}")
        return False

    try:
        from ui.vip_main_dashboard import VIPMainDashboard
        print("OK Main Dashboard available")
    except ImportError as e:
        print(f"FAIL Main Dashboard error: {e}")
        return False

    try:
        from utils.logger import setup_logger
        print("OK Logger available")
    except ImportError as e:
        print(f"FAIL Logger error: {e}")
        return False
    
    return True

def test_core_systems():
    """Test core system initialization"""
    print("\nTesting core systems...")
    
    try:
        from core.settings import Settings
        settings = Settings()
        print("✅ Settings initialized")
    except Exception as e:
        print(f"❌ Settings error: {e}")
        return False
    
    try:
        from core.analysis_engine import AnalysisEngine
        engine = AnalysisEngine(settings)
        print("✅ Analysis Engine initialized")
    except Exception as e:
        print(f"❌ Analysis Engine error: {e}")
        return False
    
    try:
        from core.signal_manager import SignalManager
        signal_mgr = SignalManager(settings)
        print("✅ Signal Manager initialized")
    except Exception as e:
        print(f"❌ Signal Manager error: {e}")
        return False
    
    try:
        from utils.logger import setup_logger
        logger = setup_logger("test")
        print("✅ Logger initialized")
    except Exception as e:
        print(f"❌ Logger error: {e}")
        return False
    
    return True

def test_ui_systems():
    """Test UI system availability"""
    print("\nTesting UI systems...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✅ PySide6 Qt modules available")
    except ImportError as e:
        print(f"❌ PySide6 Qt error: {e}")
        return False
    
    try:
        # Don't actually create QApplication in test
        print("✅ UI systems ready")
    except Exception as e:
        print(f"❌ UI error: {e}")
        return False
    
    return True

def test_file_structure():
    """Test file structure"""
    print("\nTesting file structure...")
    
    required_files = [
        "main.py",
        "vip_big_bang_launcher.py", 
        "vip_real_quotex_main.py",
        "config.json",
        "requirements.txt"
    ]
    
    required_dirs = [
        "core",
        "ui", 
        "trading",
        "utils",
        "chrome_extension"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} missing")
            return False
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ missing")
            return False
    
    return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("VIP BIG BANG System Test")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Core Systems", test_core_systems),
        ("UI Systems", test_ui_systems)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"PASS: {test_name} test PASSED")
            else:
                print(f"FAIL: {test_name} test FAILED")
        except Exception as e:
            print(f"ERROR: {test_name} test ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        icon = "OK" if result else "FAIL"
        print(f"{icon} {test_name}: {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("ALL TESTS PASSED! System is ready!")
        return True
    else:
        print("Some tests failed. Check errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
