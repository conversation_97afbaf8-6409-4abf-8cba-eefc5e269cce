"""
VIP BIG BANG Enterprise - Complete Adaptive Demo
نمایش کامل سیستم انطباقی و قابل تنظیم
"""

from datetime import datetime, timedelta
import logging
from adaptive_decision_system import AdaptiveDecisionSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s: %(message)s', datefmt='%H:%M:%S')

def demo_complete_adaptive_system():
    """نمایش کامل سیستم انطباقی"""
    
    print("🚀 VIP BIG BANG Enterprise - سیستم کاملاً انطباقی")
    print("=" * 70)
    print("🎯 قابلیت‌ها:")
    print("   ✅ تغییر کامل سیستم تصمیم‌گیری")
    print("   ✅ تنظیم خودکار بر اساس تایم‌فریم")
    print("   ✅ پروفایل‌های مختلف ترید")
    print("   ✅ تنظیمات سفارشی کاربر")
    print("   ✅ ذخیره و بارگذاری تنظیمات")
    print("-" * 70)
    
    system = AdaptiveDecisionSystem()
    
    # نمایش سناریوهای مختلف
    scenarios = [
        {
            'name': '🏃‍♂️ اسکالپینگ سریع',
            'timeframe': '15s',
            'trade_duration': 1,
            'description': 'ترید‌های سریع با سود کم اما تعداد زیاد'
        },
        {
            'name': '⚡ ترید کوتاه‌مدت',
            'timeframe': '30s', 
            'trade_duration': 5,
            'description': 'ترید‌های متوسط با تعادل ریسک و سود'
        },
        {
            'name': '📈 ترید استاندارد',
            'timeframe': '1m',
            'trade_duration': 15,
            'description': 'ترید‌های معمولی با کیفیت بالا'
        },
        {
            'name': '🎯 ترید محافظه‌کار',
            'timeframe': '5m',
            'trade_duration': 60,
            'description': 'ترید‌های بلندمدت با ریسک کم'
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print(f"   📊 تایم‌فریم: {scenario['timeframe']}")
        print(f"   ⏱️  مدت ترید: {scenario['trade_duration']} دقیقه")
        print(f"   📝 توضیح: {scenario['description']}")
        
        # تنظیم خودکار
        settings = system.auto_adjust_for_timeframe(
            scenario['timeframe'], 
            scenario['trade_duration']
        )
        
        print(f"   🎛️  تنظیمات خودکار:")
        print(f"      پروفایل: {settings['name']}")
        print(f"      حداقل امتیاز: {settings['min_signal_strength']:.0%}")
        print(f"      تأیید لازم: {settings['required_confirmations']}")
        print(f"      ترید/روز: {settings['max_daily_trades']}")
        print(f"      استراحت: {settings['cooldown_minutes']} دقیقه")
        print(f"      هدف وین ریت: {settings['win_rate_target']:.0%}")
        
        # تست سیگنال
        test_signal = create_test_signal(scenario['timeframe'])
        validation = system.validate_signal_adaptive(
            test_signal['primary'], 
            test_signal['complementary']
        )
        
        print(f"   🧪 تست سیگنال:")
        print(f"      تصمیم: {validation['decision']}")
        print(f"      امتیاز: {validation['score']:.1f}%")
        print(f"      احتمال برد: {validation['win_probability']:.0%}")
        print(f"      اجازه ترید: {'✅ بله' if validation['allow_trading'] else '❌ خیر'}")

def create_test_signal(timeframe):
    """ایجاد سیگنال تست بر اساس تایم‌فریم"""
    
    if timeframe in ['15s', '30s']:
        # سیگنال‌های قوی‌تر برای تایم‌فریم‌های کوتاه
        primary = {
            'ma6': {'score': 0.88, 'direction': 'UP'},
            'vortex': {'score': 0.85, 'direction': 'UP'},
            'volume_per_candle': {'score': 0.92, 'direction': 'UP'},
            'momentum': {'score': 0.90, 'direction': 'UP'},
            'trap_candle': {'score': 0.87, 'direction': 'UP'}
        }
    elif timeframe in ['1m', '5m']:
        # سیگنال‌های متعادل
        primary = {
            'ma6': {'score': 0.82, 'direction': 'UP'},
            'vortex': {'score': 0.80, 'direction': 'UP'},
            'volume_per_candle': {'score': 0.85, 'direction': 'UP'},
            'momentum': {'score': 0.83, 'direction': 'UP'},
            'trend_analyzer': {'score': 0.88, 'direction': 'UP'},
            'strong_level': {'score': 0.86, 'direction': 'UP'}
        }
    else:
        # سیگنال‌های محافظه‌کارانه برای تایم‌فریم‌های بلند
        primary = {
            'ma6': {'score': 0.78, 'direction': 'UP'},
            'vortex': {'score': 0.75, 'direction': 'UP'},
            'volume_per_candle': {'score': 0.80, 'direction': 'UP'},
            'momentum': {'score': 0.77, 'direction': 'UP'},
            'trend_analyzer': {'score': 0.85, 'direction': 'UP'},
            'strong_level': {'score': 0.82, 'direction': 'UP'},
            'fake_breakout': {'score': 0.79, 'direction': 'UP'}
        }
    
    complementary = {
        'economic_news_filter': {'allow_trading': True, 'score': 0.9},
        'otc_mode_detector': {'allow_trading': True, 'score': 0.85},
        'account_safety': {'allow_trading': True, 'score': 0.95},
        'live_signal_scanner': {'allow_trading': True, 'score': 0.88}
    }
    
    return {'primary': primary, 'complementary': complementary}

def demo_custom_profiles():
    """نمایش پروفایل‌های سفارشی"""
    
    print(f"\n" + "=" * 70)
    print("🎨 ایجاد پروفایل‌های سفارشی")
    print("=" * 70)
    
    system = AdaptiveDecisionSystem()
    
    # پروفایل‌های سفارشی مختلف
    custom_profiles = [
        {
            'name': 'ultra_safe',
            'settings': {
                'name': 'فوق‌العاده ایمن',
                'min_signal_strength': 0.98,
                'min_alignment': 0.95,
                'required_confirmations': 10,
                'max_daily_trades': 3,
                'cooldown_minutes': 120,
                'win_rate_target': 0.98
            }
        },
        {
            'name': 'day_trader',
            'settings': {
                'name': 'معامله‌گر روزانه',
                'min_signal_strength': 0.80,
                'min_alignment': 0.75,
                'required_confirmations': 5,
                'max_daily_trades': 25,
                'cooldown_minutes': 20,
                'win_rate_target': 0.80
            }
        },
        {
            'name': 'high_frequency',
            'settings': {
                'name': 'فرکانس بالا',
                'min_signal_strength': 0.65,
                'min_alignment': 0.60,
                'required_confirmations': 3,
                'max_daily_trades': 100,
                'cooldown_minutes': 2,
                'win_rate_target': 0.65
            }
        }
    ]
    
    for profile in custom_profiles:
        print(f"\n🎯 پروفایل: {profile['settings']['name']}")
        
        # ایجاد پروفایل
        system.create_custom_profile(profile['name'], profile['settings'])
        system.set_decision_profile(profile['name'])
        
        settings = system.get_adaptive_settings()
        
        print(f"   📊 مشخصات:")
        print(f"      حداقل امتیاز: {settings['min_signal_strength']:.0%}")
        print(f"      هم‌راستایی: {settings['min_alignment']:.0%}")
        print(f"      تأیید لازم: {settings['required_confirmations']}")
        print(f"      ترید/روز: {settings['max_daily_trades']}")
        print(f"      استراحت: {settings['cooldown_minutes']} دقیقه")
        print(f"      هدف وین ریت: {settings['win_rate_target']:.0%}")
        
        # تست سیگنال
        test_signal = create_test_signal('1m')
        validation = system.validate_signal_adaptive(
            test_signal['primary'], 
            test_signal['complementary']
        )
        
        print(f"   🧪 نتیجه تست:")
        print(f"      امتیاز: {validation['score']:.1f}%")
        print(f"      تصمیم: {validation['decision']}")
        print(f"      اجازه ترید: {'✅' if validation['allow_trading'] else '❌'}")

def demo_real_time_adaptation():
    """نمایش تطبیق زمان واقعی"""
    
    print(f"\n" + "=" * 70)
    print("⚡ تطبیق زمان واقعی با شرایط بازار")
    print("=" * 70)
    
    system = AdaptiveDecisionSystem()
    
    # شبیه‌سازی تغییرات شرایط بازار
    market_conditions = [
        {
            'name': 'بازار آرام',
            'volatility': 'LOW',
            'volume': 'NORMAL',
            'trend': 'SIDEWAYS',
            'recommended_timeframe': '5m',
            'recommended_duration': 30
        },
        {
            'name': 'بازار پرنوسان',
            'volatility': 'HIGH',
            'volume': 'HIGH',
            'trend': 'STRONG_UP',
            'recommended_timeframe': '15s',
            'recommended_duration': 2
        },
        {
            'name': 'بازار ترندی',
            'volatility': 'MEDIUM',
            'volume': 'HIGH',
            'trend': 'UP',
            'recommended_timeframe': '1m',
            'recommended_duration': 10
        }
    ]
    
    for condition in market_conditions:
        print(f"\n📊 شرایط بازار: {condition['name']}")
        print(f"   نوسان: {condition['volatility']}")
        print(f"   حجم: {condition['volume']}")
        print(f"   ترند: {condition['trend']}")
        
        # تنظیم خودکار بر اساس شرایط
        settings = system.auto_adjust_for_timeframe(
            condition['recommended_timeframe'],
            condition['recommended_duration']
        )
        
        print(f"   🎛️  تنظیمات توصیه شده:")
        print(f"      تایم‌فریم: {condition['recommended_timeframe']}")
        print(f"      مدت ترید: {condition['recommended_duration']} دقیقه")
        print(f"      پروفایل: {settings['name']}")
        print(f"      هدف وین ریت: {settings['win_rate_target']:.0%}")

def demo_settings_persistence():
    """نمایش ذخیره و بارگذاری تنظیمات"""
    
    print(f"\n" + "=" * 70)
    print("💾 ذخیره و بارگذاری تنظیمات")
    print("=" * 70)
    
    system = AdaptiveDecisionSystem()
    
    # ایجاد تنظیمات سفارشی
    system.create_custom_profile('my_strategy', {
        'name': 'استراتژی من',
        'min_signal_strength': 0.87,
        'min_alignment': 0.82,
        'required_confirmations': 6,
        'max_daily_trades': 15,
        'cooldown_minutes': 25,
        'win_rate_target': 0.87
    })
    
    system.set_decision_profile('my_strategy')
    system.set_timeframe('1m')
    
    # ذخیره تنظیمات
    config_file = 'my_trading_config.json'
    system.save_configuration(config_file)
    print(f"✅ تنظیمات در {config_file} ذخیره شد")
    
    # ایجاد سیستم جدید و بارگذاری
    new_system = AdaptiveDecisionSystem()
    if new_system.load_configuration(config_file):
        print(f"✅ تنظیمات از {config_file} بارگذاری شد")
        
        loaded_settings = new_system.get_adaptive_settings()
        print(f"   پروفایل بارگذاری شده: {loaded_settings['name']}")
        print(f"   تایم‌فریم: {loaded_settings['timeframe']}")
        print(f"   حداقل امتیاز: {loaded_settings['min_signal_strength']:.0%}")

def main():
    """اجرای نمایش کامل"""
    
    print("🎯 VIP BIG BANG Enterprise - نمایش کامل سیستم انطباقی")
    print("🚀 قابلیت تغییر کامل سیستم تصمیم‌گیری بر اساس:")
    print("   📊 تایم‌فریم تحلیل")
    print("   ⏱️  مدت ترید")
    print("   🎯 هدف وین ریت")
    print("   🛡️ سطح ریسک")
    print("   📈 شرایط بازار")
    
    # اجرای نمایش‌ها
    demo_complete_adaptive_system()
    demo_custom_profiles()
    demo_real_time_adaptation()
    demo_settings_persistence()
    
    print(f"\n" + "=" * 70)
    print("🎉 نمایش کامل سیستم انطباقی به پایان رسید!")
    print("✅ سیستم VIP BIG BANG حالا کاملاً قابل تنظیم است:")
    print("   🎛️  تغییر کامل سیستم تصمیم‌گیری")
    print("   ⚡ تنظیم خودکار بر اساس تایم‌فریم")
    print("   🎨 پروفایل‌های سفارشی")
    print("   💾 ذخیره و بارگذاری تنظیمات")
    print("   📊 تطبیق زمان واقعی")
    print("🚀 آماده برای هر نوع استراتژی ترید!")

if __name__ == "__main__":
    main()
