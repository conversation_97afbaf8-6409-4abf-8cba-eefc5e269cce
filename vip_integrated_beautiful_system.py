#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Integrated Beautiful System
💎 Complete System with Beautiful UI and Real Data
🎨 Professional Trading System with Stunning Design
"""

import sys
import os
import json
import time
import threading
import subprocess
from datetime import datetime
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")
    except:
        pass

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("Installing PySide6...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *

# Import beautiful UI components
from vip_beautiful_main_ui import VIPBeautifulMainUI

class VIPIntegratedSystem:
    """VIP BIG BANG Integrated System with Beautiful UI"""
    
    def __init__(self):
        self.main_system_process = None
        self.dashboard_process = None
        self.beautiful_ui = None
        self.app = None
        
    def start_main_system(self):
        """Start the main VIP BIG BANG system"""
        try:
            print("Starting VIP BIG BANG main system...")
            self.main_system_process = subprocess.Popen(
                [sys.executable, "vip_real_quotex_main.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            print("Main system started successfully")
            return True
        except Exception as e:
            print(f"Failed to start main system: {e}")
            return False
            
    def start_dashboard(self):
        """Start the comprehensive dashboard"""
        try:
            print("Starting comprehensive dashboard...")
            self.dashboard_process = subprocess.Popen(
                [sys.executable, "vip_comprehensive_dashboard.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            print("Dashboard started successfully")
            return True
        except Exception as e:
            print(f"Failed to start dashboard: {e}")
            return False
            
    def start_beautiful_ui(self):
        """Start the beautiful UI"""
        try:
            print("Starting beautiful UI...")
            
            # Create QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("VIP BIG BANG Integrated System")
            self.app.setApplicationVersion("2.0.0")
            self.app.setStyle('Fusion')
            
            # Create beautiful UI
            self.beautiful_ui = VIPBeautifulMainUI()
            self.beautiful_ui.show()
            
            print("Beautiful UI started successfully")
            return True
        except Exception as e:
            print(f"Failed to start beautiful UI: {e}")
            return False
            
    def run_integrated_system(self):
        """Run the complete integrated system"""
        print("=" * 60)
        print("VIP BIG BANG - Integrated Beautiful System")
        print("Complete Professional Trading Solution")
        print("=" * 60)
        print()
        
        # Start main system
        if self.start_main_system():
            print("✓ Main system running")
            time.sleep(3)  # Wait for initialization
        else:
            print("✗ Main system failed")
            
        # Start dashboard
        if self.start_dashboard():
            print("✓ Dashboard running")
            time.sleep(2)  # Wait for initialization
        else:
            print("✗ Dashboard failed")
            
        # Start beautiful UI
        if self.start_beautiful_ui():
            print("✓ Beautiful UI running")
            print()
            print("=" * 60)
            print("ALL SYSTEMS OPERATIONAL")
            print("=" * 60)
            print()
            print("Components running:")
            print("- Main VIP BIG BANG system (real data extraction)")
            print("- Comprehensive dashboard (system monitoring)")
            print("- Beautiful modern UI (trading interface)")
            print()
            print("Features available:")
            print("- Real-time Quotex data")
            print("- 20-indicator analysis")
            print("- Professional trading controls")
            print("- Beautiful glass morphism design")
            print("- Neon button animations")
            print("- Live performance metrics")
            print()
            
            # Run the beautiful UI
            return self.app.exec()
        else:
            print("✗ Beautiful UI failed")
            return 1
            
    def cleanup(self):
        """Cleanup all processes"""
        try:
            if self.main_system_process:
                self.main_system_process.terminate()
                print("Main system terminated")
                
            if self.dashboard_process:
                self.dashboard_process.terminate()
                print("Dashboard terminated")
                
        except Exception as e:
            print(f"Cleanup error: {e}")

class VIPSystemLauncher(QMainWindow):
    """System launcher with options"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VIP BIG BANG - System Launcher")
        self.setFixedSize(500, 400)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup launcher UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:1 #1a1a2e);
                color: white;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:1 #8b5cf6);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4f46e5, stop:1 #7c3aed);
            }
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("VIP BIG BANG System Launcher")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Segoe UI", 20, QFont.Bold))
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("Choose your launch option:")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Segoe UI", 12))
        layout.addWidget(subtitle)
        
        layout.addSpacing(20)
        
        # Launch options
        integrated_btn = QPushButton("🚀 Launch Complete Integrated System")
        integrated_btn.clicked.connect(self.launch_integrated)
        layout.addWidget(integrated_btn)
        
        beautiful_only_btn = QPushButton("🎨 Launch Beautiful UI Only")
        beautiful_only_btn.clicked.connect(self.launch_beautiful_only)
        layout.addWidget(beautiful_only_btn)
        
        main_only_btn = QPushButton("⚙️ Launch Main System Only")
        main_only_btn.clicked.connect(self.launch_main_only)
        layout.addWidget(main_only_btn)
        
        dashboard_only_btn = QPushButton("📊 Launch Dashboard Only")
        dashboard_only_btn.clicked.connect(self.launch_dashboard_only)
        layout.addWidget(dashboard_only_btn)
        
        layout.addStretch()
        
        # Exit button
        exit_btn = QPushButton("❌ Exit")
        exit_btn.clicked.connect(self.close)
        layout.addWidget(exit_btn)
        
    def launch_integrated(self):
        """Launch complete integrated system"""
        self.close()
        system = VIPIntegratedSystem()
        try:
            exit_code = system.run_integrated_system()
            sys.exit(exit_code)
        finally:
            system.cleanup()
            
    def launch_beautiful_only(self):
        """Launch beautiful UI only"""
        self.close()
        subprocess.Popen([sys.executable, "vip_beautiful_main_ui.py"])
        
    def launch_main_only(self):
        """Launch main system only"""
        self.close()
        subprocess.Popen([sys.executable, "vip_real_quotex_main.py"])
        
    def launch_dashboard_only(self):
        """Launch dashboard only"""
        self.close()
        subprocess.Popen([sys.executable, "vip_comprehensive_dashboard.py"])

def main():
    """Main entry point"""
    print("VIP BIG BANG - System Launcher")
    print("Choose your preferred launch method")
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    launcher = VIPSystemLauncher()
    launcher.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
