"""
VIP BIG BANG Enterprise - Test 10 Trades
تست 10 ترید در تایم‌فریم 1 دقیقه با ترید 5 ثانیه‌ای
"""

import pandas as pd  # type: ignore
from datetime import datetime, timedelta
import logging
import random
import time
from adaptive_decision_system import AdaptiveDecisionSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s: %(message)s', datefmt='%H:%M:%S')

class Trade10TestSystem:
    """سیستم تست 10 ترید"""
    
    def __init__(self):
        self.logger = logging.getLogger("Trade10Test")
        self.adaptive_system = AdaptiveDecisionSystem()
        
        # تنظیم سیستم برای 1m timeframe و 5 second trades
        self.setup_for_1m_5s()
        
        # آمار تست
        self.trades_executed = []
        self.total_trades = 0
        self.wins = 0
        self.losses = 0
        self.total_profit = 0.0
        
    def setup_for_1m_5s(self):
        """تنظیم سیستم برای تایم‌فریم 1 دقیقه و ترید 5 ثانیه‌ای"""
        
        print("🔧 تنظیم سیستم برای تست:")
        print("   📊 تایم‌فریم: 1 دقیقه")
        print("   ⏱️  مدت ترید: 5 ثانیه")
        print("   🎯 تعداد تست: 10 ترید")
        
        # تنظیم خودکار
        settings = self.adaptive_system.auto_adjust_for_timeframe('1m', 0.083)  # 5 seconds = 0.083 minutes
        
        # تنظیمات ویژه برای ترید 5 ثانیه‌ای
        self.adaptive_system.custom_settings.update({
            'min_signal_strength': 0.85,  # کمی کاهش برای ترید سریع
            'required_confirmations': 5,   # کاهش تأیید برای سرعت
            'cooldown_minutes': 1,         # 1 دقیقه استراحت
            'max_daily_trades': 50,        # افزایش برای تست
            'win_rate_target': 0.80        # هدف 80% برای ترید سریع
        })
        
        current_settings = self.adaptive_system.get_adaptive_settings()
        
        print(f"\n✅ تنظیمات اعمال شده:")
        print(f"   پروفایل: {current_settings['name']}")
        print(f"   حداقل امتیاز: {current_settings['min_signal_strength']:.0%}")
        print(f"   تأیید لازم: {current_settings['required_confirmations']}")
        print(f"   هدف وین ریت: {current_settings['win_rate_target']:.0%}")
        print(f"   استراحت: {current_settings['cooldown_minutes']} دقیقه")
    
    def generate_market_data(self, scenario_type='mixed'):
        """تولید داده‌های بازار برای تست"""
        
        scenarios = {
            'bullish': {
                'trend': 'UP',
                'strength': random.uniform(0.8, 0.95),
                'volatility': random.uniform(0.002, 0.005),
                'volume_multiplier': random.uniform(1.2, 2.0)
            },
            'bearish': {
                'trend': 'DOWN', 
                'strength': random.uniform(0.8, 0.95),
                'volatility': random.uniform(0.002, 0.005),
                'volume_multiplier': random.uniform(1.2, 2.0)
            },
            'sideways': {
                'trend': 'NEUTRAL',
                'strength': random.uniform(0.5, 0.7),
                'volatility': random.uniform(0.001, 0.003),
                'volume_multiplier': random.uniform(0.8, 1.2)
            },
            'volatile': {
                'trend': random.choice(['UP', 'DOWN']),
                'strength': random.uniform(0.6, 0.8),
                'volatility': random.uniform(0.005, 0.01),
                'volume_multiplier': random.uniform(1.5, 3.0)
            }
        }
        
        if scenario_type == 'mixed':
            scenario = random.choice(list(scenarios.values()))
        else:
            scenario = scenarios.get(scenario_type, scenarios['mixed'])
        
        return scenario
    
    def create_signal_data(self, market_scenario):
        """ایجاد داده‌های سیگنال بر اساس سناریو بازار"""
        
        trend = market_scenario['trend']
        strength = market_scenario['strength']
        volatility = market_scenario['volatility']
        volume_mult = market_scenario['volume_multiplier']
        
        # تنظیم امتیازها بر اساس قدرت ترند
        base_score = 0.7 + (strength * 0.2)
        
        # اضافه کردن نویز تصادفی
        noise = random.uniform(-0.1, 0.1)
        
        primary_results = {}
        
        # اندیکاتورهای اصلی
        indicators = [
            'ma6', 'vortex', 'volume_per_candle', 'trap_candle',
            'shadow_candle', 'strong_level', 'fake_breakout',
            'momentum', 'trend_analyzer', 'buyer_seller_power'
        ]
        
        for indicator in indicators:
            # امتیاز با نویز
            score = max(0.5, min(0.99, base_score + noise + random.uniform(-0.05, 0.05)))
            
            # جهت بر اساس ترند
            if trend == 'UP':
                direction = 'UP' if random.random() > 0.2 else random.choice(['DOWN', 'NEUTRAL'])
            elif trend == 'DOWN':
                direction = 'DOWN' if random.random() > 0.2 else random.choice(['UP', 'NEUTRAL'])
            else:
                direction = random.choice(['UP', 'DOWN', 'NEUTRAL'])
            
            primary_results[indicator] = {
                'score': score,
                'direction': direction,
                'confidence': score * 0.9
            }
        
        # فیلترهای مکمل
        complementary_results = {
            'economic_news_filter': {
                'allow_trading': random.random() > 0.1,  # 90% احتمال مجاز
                'score': random.uniform(0.8, 0.95)
            },
            'otc_mode_detector': {
                'allow_trading': True,
                'score': 0.9
            },
            'account_safety': {
                'allow_trading': random.random() > 0.05,  # 95% احتمال مجاز
                'score': random.uniform(0.85, 0.98)
            },
            'live_signal_scanner': {
                'allow_trading': True,
                'score': random.uniform(0.75, 0.95),
                'quality_check': {'signal_strength': 'STRONG' if strength > 0.8 else 'MEDIUM'}
            }
        }
        
        return {
            'primary': primary_results,
            'complementary': complementary_results,
            'market_scenario': market_scenario
        }
    
    def execute_trade(self, signal_data, trade_number):
        """اجرای یک ترید"""
        
        print(f"\n{'='*60}")
        print(f"🎯 ترید #{trade_number}/10")
        print(f"{'='*60}")
        
        # نمایش سناریو بازار
        scenario = signal_data['market_scenario']
        print(f"📊 سناریو بازار:")
        print(f"   ترند: {scenario['trend']}")
        print(f"   قدرت: {scenario['strength']:.1%}")
        print(f"   نوسان: {scenario['volatility']:.3f}")
        print(f"   حجم: {scenario['volume_multiplier']:.1f}x")
        
        # اعتبارسنجی سیگنال
        validation = self.adaptive_system.validate_signal_adaptive(
            signal_data['primary'],
            signal_data['complementary']
        )
        
        trade_result = {
            'trade_number': trade_number,
            'timestamp': datetime.now(),
            'market_scenario': scenario,
            'validation': validation,
            'executed': False,
            'result': None,
            'profit': 0.0
        }
        
        if validation['allow_trading']:
            print(f"\n🚀 ترید اجرا می‌شود!")
            
            # تعیین جهت ترید
            directions = [r.get('direction') for r in signal_data['primary'].values()]
            main_direction = max(set(directions), key=directions.count) if directions else 'UP'
            
            trade_result.update({
                'executed': True,
                'direction': main_direction,
                'entry_price': 1.20000 + random.uniform(-0.001, 0.001),
                'amount': 10.0,
                'win_probability': validation['win_probability']
            })
            
            # شبیه‌سازی نتیجه ترید (بر اساس احتمال برد)
            win_chance = validation['win_probability']
            
            # اضافه کردن فاکتور تصادفی برای واقعی‌تر بودن
            actual_win_chance = win_chance * random.uniform(0.8, 1.2)
            
            is_win = random.random() < actual_win_chance
            
            if is_win:
                profit = 8.5  # 85% سود
                trade_result.update({
                    'result': 'WIN',
                    'profit': profit,
                    'exit_price': trade_result['entry_price'] + (0.0001 if main_direction == 'UP' else -0.0001)
                })
                self.wins += 1
                self.total_profit += profit
                print(f"✅ نتیجه: برد!")
                print(f"💰 سود: ${profit}")
            else:
                loss = -10.0  # ضرر کامل
                trade_result.update({
                    'result': 'LOSS',
                    'profit': loss,
                    'exit_price': trade_result['entry_price'] + (-0.0001 if main_direction == 'UP' else 0.0001)
                })
                self.losses += 1
                self.total_profit += loss
                print(f"❌ نتیجه: باخت!")
                print(f"💸 ضرر: ${abs(loss)}")
            
            self.total_trades += 1
            
            print(f"\n📊 جزئیات ترید:")
            print(f"   جهت: {main_direction}")
            print(f"   قیمت ورود: {trade_result['entry_price']:.5f}")
            print(f"   قیمت خروج: {trade_result['exit_price']:.5f}")
            print(f"   مبلغ: ${trade_result['amount']}")
            print(f"   احتمال برد: {win_chance:.1%}")
            
        else:
            print(f"\n❌ ترید رد شد!")
            print(f"   دلیل: امتیاز {validation['score']:.1f}% کمتر از حد مجاز")
        
        self.trades_executed.append(trade_result)
        
        # نمایش آمار فعلی
        if self.total_trades > 0:
            current_winrate = (self.wins / self.total_trades) * 100
            print(f"\n📈 آمار فعلی:")
            print(f"   ترید‌های اجرا شده: {self.total_trades}")
            print(f"   برد: {self.wins} | باخت: {self.losses}")
            print(f"   وین ریت: {current_winrate:.1f}%")
            print(f"   سود/زیان کل: ${self.total_profit:.2f}")
        
        return trade_result
    
    def run_test(self):
        """اجرای تست کامل 10 ترید"""
        
        print("🚀 شروع تست 10 ترید - تایم‌فریم 1 دقیقه، ترید 5 ثانیه‌ای")
        print("=" * 70)
        
        start_time = datetime.now()
        
        for i in range(1, 11):
            # تولید داده‌های بازار
            market_data = self.generate_market_data('mixed')
            signal_data = self.create_signal_data(market_data)
            
            # اجرای ترید
            self.execute_trade(signal_data, i)
            
            # استراحت کوتاه (شبیه‌سازی زمان واقعی)
            if i < 10:
                print(f"\n⏳ استراحت 5 ثانیه تا ترید بعدی...")
                time.sleep(1)  # 1 ثانیه برای نمایش (به جای 5 ثانیه)
        
        end_time = datetime.now()
        test_duration = end_time - start_time
        
        # نمایش نتایج نهایی
        self.show_final_results(test_duration)
    
    def show_final_results(self, test_duration):
        """نمایش نتایج نهایی تست"""
        
        print(f"\n" + "=" * 70)
        print("🏁 نتایج نهایی تست 10 ترید")
        print("=" * 70)
        
        executed_trades = [t for t in self.trades_executed if t['executed']]
        rejected_trades = [t for t in self.trades_executed if not t['executed']]
        
        print(f"📊 خلاصه کلی:")
        print(f"   مدت تست: {test_duration.total_seconds():.1f} ثانیه")
        print(f"   کل سیگنال‌ها: 10")
        print(f"   ترید‌های اجرا شده: {len(executed_trades)}")
        print(f"   ترید‌های رد شده: {len(rejected_trades)}")
        
        if executed_trades:
            win_rate = (self.wins / len(executed_trades)) * 100
            avg_profit_per_trade = self.total_profit / len(executed_trades)
            
            print(f"\n💰 نتایج مالی:")
            print(f"   برد: {self.wins}")
            print(f"   باخت: {self.losses}")
            print(f"   وین ریت: {win_rate:.1f}%")
            print(f"   سود/زیان کل: ${self.total_profit:.2f}")
            print(f"   میانگین سود هر ترید: ${avg_profit_per_trade:.2f}")
            
            # تحلیل عملکرد
            print(f"\n📈 تحلیل عملکرد:")
            if win_rate >= 80:
                print(f"   🎉 عالی! وین ریت بالای 80%")
            elif win_rate >= 70:
                print(f"   ✅ خوب! وین ریت قابل قبول")
            elif win_rate >= 60:
                print(f"   ⚠️ متوسط! نیاز به بهینه‌سازی")
            else:
                print(f"   ❌ ضعیف! نیاز به بازنگری تنظیمات")
            
            if self.total_profit > 0:
                print(f"   💚 سودآور: ${self.total_profit:.2f}")
            else:
                print(f"   💔 زیان‌ده: ${abs(self.total_profit):.2f}")
        
        # جزئیات هر ترید
        print(f"\n📋 جزئیات ترید‌ها:")
        print("-" * 70)
        
        for trade in self.trades_executed:
            status = "✅ اجرا" if trade['executed'] else "❌ رد"
            result = ""
            profit = ""
            
            if trade['executed']:
                result = f"| {trade['result']}"
                profit = f"| ${trade['profit']:+.2f}"
            
            validation_score = trade['validation']['score']
            
            print(f"   ترید #{trade['trade_number']}: {status} | امتیاز: {validation_score:.1f}% {result} {profit}")
        
        # توصیه‌ها
        print(f"\n💡 توصیه‌ها:")
        
        rejection_rate = len(rejected_trades) / 10 * 100
        if rejection_rate > 50:
            print(f"   🔧 تنظیمات خیلی سختگیرانه - {rejection_rate:.0f}% رد شده")
            print(f"   💡 پیشنهاد: کاهش حداقل امتیاز یا تأیید لازم")
        elif rejection_rate < 20:
            print(f"   ⚠️ تنظیمات خیلی آسان - {rejection_rate:.0f}% رد شده")
            print(f"   💡 پیشنهاد: افزایش حداقل امتیاز برای کیفیت بهتر")
        else:
            print(f"   ✅ تنظیمات متعادل - {rejection_rate:.0f}% رد شده")
        
        if executed_trades and win_rate < 70:
            print(f"   📊 پیشنهاد: افزایش حداقل امتیاز سیگنال")
            print(f"   🎯 پیشنهاد: افزایش تعداد تأیید لازم")
        
        print(f"\n🎯 تست تایم‌فریم 1 دقیقه با ترید 5 ثانیه‌ای کامل شد!")

def main():
    """اجرای تست اصلی"""
    test_system = Trade10TestSystem()
    test_system.run_test()

if __name__ == "__main__":
    main()
