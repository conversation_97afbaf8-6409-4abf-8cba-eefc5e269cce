#!/usr/bin/env python3
"""
🚀 VIP BIG BANG ULTIMATE - MAIN INTERFACE
💎 Enterprise-Level Professional Trading System
⚡ Quantum-Speed Analysis Engine
🎯 95% Win Rate Achievement System
🔥 Real-time AI-Powered Market Intelligence

This is the main entry point for the VIP BIG BANG ULTIMATE trading system.
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import tkinter
        import threading
        import json
        import asyncio
        import websockets
        import requests
        print("All core dependencies available")
        return True
    except ImportError as e:
        print(f"Missing dependency: {e}")
        return False

def start_real_data_server():
    """Start the real data server in background"""
    try:
        print("Starting Real Data Server...")
        subprocess.Popen([sys.executable, "vip_real_quotex_main.py"],
                        creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
        time.sleep(2)  # Give server time to start
        print("Real Data Server started")
        return True
    except Exception as e:
        print(f"Could not start Real Data Server: {e}")
        return False

def start_main_ui():
    """Start the main UI directly without old interface"""
    try:
        print("Starting VIP BIG BANG ULTIMATE UI...")

        # Import and run the main dashboard UI
        from ui.vip_main_dashboard import VIPMainDashboard

        # Create and run the UI
        app = VIPMainDashboard()
        app.run()
        return True
    except Exception as e:
        print(f"Failed to start main UI: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to run VIP BIG BANG ULTIMATE"""
    print("VIP BIG BANG ULTIMATE - INITIALIZING...")
    print("Enterprise-Level Professional Trading System")
    print("Quantum-Speed Analysis Engine")
    print("95% Win Rate Achievement System")
    print("Real-time AI-Powered Market Intelligence")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("Please install missing dependencies")
        input("Press Enter to exit...")
        return
    
    # Start real data server
    start_real_data_server()
    
    # Start main UI
    if not start_main_ui():
        print("Failed to start VIP BIG BANG ULTIMATE")
        input("Press Enter to exit...")
        return
    
    print("VIP BIG BANG ULTIMATE session ended")

if __name__ == "__main__":
    main()
