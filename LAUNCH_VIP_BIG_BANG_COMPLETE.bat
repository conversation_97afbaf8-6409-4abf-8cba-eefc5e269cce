@echo off
chcp 65001 >nul
title VIP BIG BANG - Complete Professional Trading System
color 0B

echo.
echo ================================================================
echo                    VIP BIG BANG TRADING SYSTEM
echo                     COMPLETE PROFESSIONAL SUITE
echo ================================================================
echo.
echo                    Real-time Quotex Data Extraction
echo                    Advanced 20-Indicator Analysis Engine
echo                    Professional AutoTrade System
echo                    Comprehensive Dashboard Interface
echo                    Chrome Extension Integration
echo.
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo [INFO] Please install Python from https://python.org
    pause
    exit /b 1
)

echo [OK] Python found
echo.

REM Check if required packages are installed
echo [INFO] Checking required packages...
python -c "import PySide6, websockets, requests, psutil" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing required packages...
    python -m pip install PySide6 websockets requests psutil websocket-client
    if errorlevel 1 (
        echo [WARNING] Some packages may not be installed
    )
)

echo [OK] Required packages ready
echo.

echo ================================================================
echo                    LAUNCHING VIP BIG BANG SYSTEM
echo ================================================================
echo.

echo [INFO] Starting VIP BIG BANG Professional Trading System...
echo.
echo [INFO] This system includes:
echo        - Real-time Quotex data extraction
echo        - Professional 20-indicator analysis engine
echo        - Advanced AutoTrade system
echo        - WebSocket server on port 8765
echo        - Comprehensive dashboard interface
echo        - Chrome extension integration
echo        - Real data validation system
echo.

REM Launch the main VIP BIG BANG system
echo [STEP 1] Starting main VIP BIG BANG system...
start "VIP BIG BANG Main System" python vip_real_quotex_main.py

REM Wait a moment for the main system to initialize
timeout /t 3 /nobreak >nul

REM Launch the comprehensive dashboard
echo [STEP 2] Starting comprehensive dashboard...
start "VIP BIG BANG Dashboard" python vip_comprehensive_dashboard.py

REM Wait a moment for the dashboard to initialize
timeout /t 2 /nobreak >nul

echo.
echo ================================================================
echo                    VIP BIG BANG SYSTEM LAUNCHED
echo ================================================================
echo.
echo [SUCCESS] VIP BIG BANG Professional Trading System is now running!
echo.
echo [INFO] System Components:
echo        ✓ Main Trading System: Running
echo        ✓ WebSocket Server: Port 8765
echo        ✓ Comprehensive Dashboard: Active
echo        ✓ Real Data Extraction: Active
echo        ✓ Analysis Engine: 20 Indicators
echo        ✓ AutoTrade Engine: Ready
echo.
echo [INFO] Next Steps:
echo        1. Install Chrome Extension:
echo           - Open Chrome
echo           - Go to chrome://extensions/
echo           - Enable Developer mode
echo           - Click "Load unpacked"
echo           - Select chrome_extension folder
echo        2. Go to qxbroker.com/en/trade
echo        3. Click extension icon
echo        4. Click "Start Extraction"
echo        5. Monitor dashboard for real-time data
echo.
echo [INFO] Dashboard Features:
echo        - Real-time system status monitoring
echo        - Live Quotex data display
echo        - Trading controls (CALL/PUT)
echo        - Performance metrics
echo        - System logs
echo        - Quick actions
echo        - Chrome extension status
echo.
echo [WARNING] Important Notes:
echo          - Keep both windows open for full functionality
echo          - Dashboard shows real-time system status
echo          - All data is validated for authenticity
echo          - AutoTrade requires manual activation
echo          - Emergency stop available in dashboard
echo.
echo ================================================================
echo.

echo [INFO] Press any key to open Chrome extension folder...
pause >nul

REM Open Chrome extension folder
if exist "chrome_extension" (
    start "" "chrome_extension"
    echo [OK] Chrome extension folder opened
) else (
    echo [WARNING] Chrome extension folder not found
)

echo.
echo [INFO] VIP BIG BANG System is fully operational!
echo [INFO] Monitor the dashboard for real-time status updates.
echo.
pause
