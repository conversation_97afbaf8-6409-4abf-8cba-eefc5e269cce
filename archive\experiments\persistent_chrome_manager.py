"""
💾 PERSISTENT CHROME PROFILE MANAGER
🔄 KEEPS LOGIN SESSION AND REMEMBERS USER
🚀 SAME PROFILE EVERY TIME
"""

import os
import subprocess
import time
import logging
import json
from pathlib import Path

class PersistentChromeManager:
    """
    💾 PERSISTENT CHROME PROFILE MANAGER
    🔄 Manages persistent Chrome profile for Quotex
    """
    
    def __init__(self):
        self.logger = logging.getLogger("PersistentChromeManager")
        
        # Chrome paths
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        # Persistent profile settings
        self.profile_name = "VIP_BIG_BANG_Quotex"
        self.user_data_dir = os.path.join(os.path.expanduser("~"), "VIP_BIG_BANG_Chrome")
        self.profile_dir = os.path.join(self.user_data_dir, self.profile_name)
        
        self.logger.info("💾 Persistent Chrome Manager initialized")
        self.logger.info(f"📁 Profile location: {self.profile_dir}")
    
    def find_chrome(self):
        """🔍 Find Chrome executable"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                return path
        return None
    
    def create_persistent_profile(self):
        """📁 Create persistent profile that remembers login"""
        try:
            # Create directories
            os.makedirs(self.user_data_dir, exist_ok=True)
            os.makedirs(self.profile_dir, exist_ok=True)
            
            # Create optimized preferences for Quotex
            preferences = {
                "profile": {
                    "default_content_setting_values": {
                        "notifications": 1,  # Allow notifications
                        "geolocation": 1,    # Allow location
                        "media_stream": 1,   # Allow camera/mic
                        "plugins": 1,        # Allow plugins
                        "popups": 1,         # Allow popups
                        "mixed_script": 1,   # Allow mixed content
                        "cookies": 1         # Allow cookies
                    },
                    "default_content_settings": {
                        "popups": 1,
                        "cookies": 1
                    },
                    "managed_default_content_settings": {
                        "images": 1,
                        "cookies": 1
                    },
                    "cookie_controls_mode": 0,  # Allow all cookies
                    "block_third_party_cookies": False
                },
                "safebrowsing": {
                    "enabled": False,
                    "enhanced": False
                },
                "search": {
                    "suggest_enabled": True
                },
                "alternate_error_pages": {
                    "enabled": True
                },
                "autofill": {
                    "enabled": True,           # Enable autofill for login
                    "profile_enabled": True,
                    "credit_card_enabled": True
                },
                "password_manager": {
                    "enabled": True,           # Enable password saving
                    "auto_signin_enabled": True,
                    "save_passwords_enabled": True
                },
                "credentials_enable_service": True,
                "credentials_enable_autosignin": True,
                "plugins": {
                    "always_open_pdf_externally": False
                },
                "hardware_acceleration_mode": {
                    "enabled": True
                },
                "background_mode": {
                    "enabled": True
                },
                "translate": {
                    "enabled": True
                },
                "session": {
                    "restore_on_startup": 1,  # Restore previous session
                    "startup_urls": ["https://quotex.io/en/sign-in"]
                },
                "browser": {
                    "show_home_button": True,
                    "homepage": "https://quotex.io/en/sign-in",
                    "homepage_is_newtabpage": False
                },
                "bookmark_bar": {
                    "show_on_all_tabs": True
                },
                "extensions": {
                    "settings": {},
                    "alerts": {
                        "initialized": True
                    }
                }
            }
            
            # Save preferences
            prefs_file = os.path.join(self.profile_dir, "Preferences")
            with open(prefs_file, 'w', encoding='utf-8') as f:
                json.dump(preferences, f, indent=2)
            
            # Create Local State file for profile management
            local_state = {
                "background_mode": {
                    "enabled": True
                },
                "browser": {
                    "enabled_labs_experiments": []
                },
                "profile": {
                    "info_cache": {
                        self.profile_name: {
                            "active_time": time.time(),
                            "is_using_default_avatar": True,
                            "is_using_default_name": False,
                            "name": "VIP BIG BANG Quotex User",
                            "user_name": "VIP BIG BANG User"
                        }
                    },
                    "last_used": self.profile_name,
                    "last_active_profiles": [self.profile_name]
                }
            }
            
            local_state_file = os.path.join(self.user_data_dir, "Local State")
            with open(local_state_file, 'w', encoding='utf-8') as f:
                json.dump(local_state, f, indent=2)
            
            self.logger.info(f"✅ Persistent profile created: {self.profile_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create persistent profile: {e}")
            return False
    
    def get_persistent_chrome_flags(self):
        """🛡️ Get Chrome flags for persistent session with ULTIMATE ANTI-DETECTION"""
        return [
            # === SESSION PERSISTENCE === #
            "--restore-last-session",
            "--continue-running",
            "--keep-alive-for-test",

            # === CORE WEBDRIVER ELIMINATION === #
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-automation",
            "--disable-infobars",
            "--enable-automation=false",
            "--disable-dev-shm-usage",

            # === FINGERPRINTING PROTECTION === #
            "--disable-features=VizDisplayCompositor",
            "--disable-features=TranslateUI",
            "--disable-features=BlinkGenPropertyTrees",
            "--disable-canvas-aa",
            "--disable-2d-canvas-clip-aa",
            "--disable-gl-drawing-for-tests",
            "--disable-accelerated-2d-canvas",
            "--disable-accelerated-jpeg-decoding",
            "--disable-accelerated-mjpeg-decode",
            "--disable-accelerated-video-decode",

            # === WEBGL PROTECTION === #
            "--disable-webgl",
            "--disable-webgl2",
            "--disable-3d-apis",
            "--disable-gpu",
            "--disable-gpu-compositing",
            "--disable-gpu-rasterization",
            "--disable-gpu-sandbox",

            # === AUDIO FINGERPRINTING === #
            "--disable-audio-output",
            "--mute-audio",
            "--disable-audio-input",

            # === NETWORK FINGERPRINTING === #
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--ignore-certificate-errors",
            "--ignore-ssl-errors",
            "--ignore-certificate-errors-spki-list",
            "--ignore-certificate-errors-skip-list",

            # === BEHAVIORAL DETECTION === #
            "--disable-ipc-flooding-protection",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-domain-reliability",
            "--disable-component-update",
            "--disable-background-mode",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",

            # === PLUGIN DETECTION === #
            "--disable-plugins-discovery",
            "--disable-plugin-power-saver",

            # === EXTENSION DETECTION === #
            "--disable-extensions-http-throttling",
            "--disable-extensions-file-access-check",

            # === MEMORY FINGERPRINTING === #
            "--memory-pressure-off",
            "--max_old_space_size=4096",

            # === BASIC SETTINGS === #
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "--disable-popup-blocking",
            "--disable-translate",

            # === USER AGENT === #
            "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",

            # === DEVTOOLS === #
            "--remote-debugging-port=9222"
        ]
    
    def launch_persistent_chrome(self):
        """🚀 Launch Chrome with persistent profile"""
        try:
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                self.logger.error("❌ Chrome not found")
                return False
            
            # Create persistent profile
            if not self.create_persistent_profile():
                return False
            
            # Check if Chrome is already running with this profile
            if self.is_chrome_running():
                self.logger.info("⚠️ Chrome already running with this profile")
                # Try to focus existing window
                self.focus_existing_chrome()
                return True
            
            # Get persistent flags
            flags = self.get_persistent_chrome_flags()
            
            # Quotex URL
            quotex_url = "https://quotex.io/en/sign-in"
            
            self.logger.info(f"🚀 Launching persistent Chrome to: {quotex_url}")
            
            # Build command
            cmd = [chrome_exe] + flags + [
                f"--user-data-dir={self.user_data_dir}",
                f"--profile-directory={self.profile_name}",
                "--window-size=1366,768",
                "--start-maximized",
                quotex_url
            ]
            
            # Launch Chrome
            process = subprocess.Popen(cmd, shell=False)
            
            # Wait for Chrome to start
            time.sleep(5)
            
            self.logger.info("✅ Persistent Chrome launched!")
            self.logger.info("💾 Profile will remember login and settings")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to launch persistent Chrome: {e}")
            return False
    
    def is_chrome_running(self):
        """🔍 Check if Chrome is already running with our profile"""
        try:
            import psutil
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or [])
                        if self.profile_name in cmdline and self.user_data_dir in cmdline:
                            return True
                except:
                    continue
            
            return False
            
        except ImportError:
            # psutil not available, check via DevTools
            try:
                import requests
                response = requests.get("http://localhost:9222/json", timeout=2)
                return response.status_code == 200
            except:
                return False
    
    def focus_existing_chrome(self):
        """🎯 Focus existing Chrome window"""
        try:
            # Try to open new tab in existing Chrome
            import requests
            
            # Get existing tabs
            response = requests.get("http://localhost:9222/json", timeout=5)
            if response.status_code == 200:
                tabs = response.json()
                
                # Check if Quotex tab already exists
                quotex_tab = None
                for tab in tabs:
                    if 'quotex' in tab.get('url', '').lower():
                        quotex_tab = tab
                        break
                
                if quotex_tab:
                    # Activate existing Quotex tab
                    tab_id = quotex_tab['id']
                    activate_url = f"http://localhost:9222/json/activate/{tab_id}"
                    requests.get(activate_url, timeout=2)
                    self.logger.info("✅ Focused existing Quotex tab")
                else:
                    # Create new tab with Quotex
                    new_tab_url = "http://localhost:9222/json/new?https://quotex.io/en/sign-in"
                    requests.get(new_tab_url, timeout=2)
                    self.logger.info("✅ Created new Quotex tab")
                
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Focus existing Chrome error: {e}")
            return False
    
    def get_profile_info(self):
        """ℹ️ Get profile information"""
        return {
            "profile_name": self.profile_name,
            "user_data_dir": self.user_data_dir,
            "profile_dir": self.profile_dir,
            "profile_exists": os.path.exists(self.profile_dir),
            "chrome_running": self.is_chrome_running()
        }

def main():
    """🚀 Main function"""
    print("💾 PERSISTENT CHROME PROFILE MANAGER")
    print("🔄 KEEPS LOGIN SESSION AND REMEMBERS USER")
    print("=" * 50)
    
    manager = PersistentChromeManager()
    
    # Show profile info
    info = manager.get_profile_info()
    print(f"📁 Profile: {info['profile_name']}")
    print(f"📂 Location: {info['profile_dir']}")
    print(f"💾 Exists: {'✅ Yes' if info['profile_exists'] else '❌ No'}")
    print(f"🌐 Chrome Running: {'✅ Yes' if info['chrome_running'] else '❌ No'}")
    
    print("\n🚀 Launching persistent Chrome...")
    
    if manager.launch_persistent_chrome():
        print("\n✅ SUCCESS!")
        print("💾 Chrome launched with persistent profile!")
        print("🔄 Login will be remembered next time!")
        print("📊 Same profile will be used always!")
        print("\n💡 Benefits:")
        print("✅ Login stays logged in")
        print("✅ Settings are remembered")
        print("✅ No need to login every time")
        print("✅ Bookmarks and history saved")
    else:
        print("\n❌ Failed to launch persistent Chrome")

if __name__ == "__main__":
    main()
