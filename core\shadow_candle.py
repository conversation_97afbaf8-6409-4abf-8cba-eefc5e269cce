"""
VIP BIG BANG Enterprise - Shadow Candle Analyzer
Analysis of candle shadows (wicks) for market rejection signals
"""

import numpy as np
import pandas as pd
from typing import Dict
import logging
from datetime import datetime

class ShadowCandleAnalyzer:
    """
    Shadow Candle - Original VIP BIG BANG indicator
    Analyzes upper and lower shadows (wicks) to detect market rejection
    Long shadows indicate price rejection by the market
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("ShadowCandleAnalyzer")
        
        # Shadow analysis parameters
        self.long_shadow_threshold = 0.6   # 60% of total candle range
        self.medium_shadow_threshold = 0.4  # 40% of total candle range
        self.body_ratio_threshold = 0.3     # Small body threshold
        
        # Signal strength parameters
        self.strong_rejection_threshold = 0.8
        self.medium_rejection_threshold = 0.6
        
        self.logger.debug("Shadow Candle Analyzer initialized")
    
    def calculate_shadow_metrics(self, data: pd.DataFrame) -> Dict:
        """Calculate comprehensive shadow metrics for current candle"""
        if len(data) < 1:
            return {
                'upper_shadow': 0,
                'lower_shadow': 0,
                'body_size': 0,
                'total_range': 0,
                'upper_shadow_ratio': 0,
                'lower_shadow_ratio': 0,
                'body_ratio': 0
            }
        
        current = data.iloc[-1]
        
        # Extract OHLC data
        open_price = current.get('open', current.get('price', 0))
        high_price = current.get('high', current.get('price', 0))
        low_price = current.get('low', current.get('price', 0))
        close_price = current.get('close', current.get('price', 0))
        
        # Calculate shadow sizes
        body_top = max(open_price, close_price)
        body_bottom = min(open_price, close_price)
        
        upper_shadow = high_price - body_top
        lower_shadow = body_bottom - low_price
        body_size = abs(close_price - open_price)
        total_range = high_price - low_price
        
        # Calculate ratios
        if total_range > 0:
            upper_shadow_ratio = upper_shadow / total_range
            lower_shadow_ratio = lower_shadow / total_range
            body_ratio = body_size / total_range
        else:
            upper_shadow_ratio = 0
            lower_shadow_ratio = 0
            body_ratio = 0
        
        return {
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'body_size': body_size,
            'total_range': total_range,
            'upper_shadow_ratio': upper_shadow_ratio,
            'lower_shadow_ratio': lower_shadow_ratio,
            'body_ratio': body_ratio,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price
        }
    
    def detect_upper_shadow_signals(self, shadow_metrics: Dict) -> Dict:
        """Detect upper shadow rejection signals"""
        upper_ratio = shadow_metrics['upper_shadow_ratio']
        body_ratio = shadow_metrics['body_ratio']
        
        signal_type = 'NONE'
        signal_strength = 0.0
        rejection_level = shadow_metrics['high']
        
        # Long upper shadow with small body = strong bearish rejection
        if upper_ratio >= self.long_shadow_threshold and body_ratio <= self.body_ratio_threshold:
            signal_type = 'STRONG_BEARISH_REJECTION'
            signal_strength = min(upper_ratio * 1.2, 1.0)
        elif upper_ratio >= self.medium_shadow_threshold and body_ratio <= 0.5:
            signal_type = 'MEDIUM_BEARISH_REJECTION'
            signal_strength = min(upper_ratio, 0.8)
        elif upper_ratio >= 0.3:
            signal_type = 'WEAK_BEARISH_REJECTION'
            signal_strength = min(upper_ratio * 0.8, 0.6)
        
        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'rejection_level': rejection_level,
            'shadow_ratio': upper_ratio
        }
    
    def detect_lower_shadow_signals(self, shadow_metrics: Dict) -> Dict:
        """Detect lower shadow rejection signals"""
        lower_ratio = shadow_metrics['lower_shadow_ratio']
        body_ratio = shadow_metrics['body_ratio']
        
        signal_type = 'NONE'
        signal_strength = 0.0
        rejection_level = shadow_metrics['low']
        
        # Long lower shadow with small body = strong bullish rejection
        if lower_ratio >= self.long_shadow_threshold and body_ratio <= self.body_ratio_threshold:
            signal_type = 'STRONG_BULLISH_REJECTION'
            signal_strength = min(lower_ratio * 1.2, 1.0)
        elif lower_ratio >= self.medium_shadow_threshold and body_ratio <= 0.5:
            signal_type = 'MEDIUM_BULLISH_REJECTION'
            signal_strength = min(lower_ratio, 0.8)
        elif lower_ratio >= 0.3:
            signal_type = 'WEAK_BULLISH_REJECTION'
            signal_strength = min(lower_ratio * 0.8, 0.6)
        
        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'rejection_level': rejection_level,
            'shadow_ratio': lower_ratio
        }
    
    def analyze_shadow_context(self, data: pd.DataFrame) -> Dict:
        """Analyze shadow signals in market context"""
        if len(data) < 3:
            return {
                'context': 'INSUFFICIENT_DATA',
                'context_strength': 0.0
            }
        
        # Get recent trend
        prices = data['close'] if 'close' in data.columns else data['price']
        recent_trend = self.determine_recent_trend(prices.tail(5))
        
        # Current shadow metrics
        shadow_metrics = self.calculate_shadow_metrics(data)
        upper_signals = self.detect_upper_shadow_signals(shadow_metrics)
        lower_signals = self.detect_lower_shadow_signals(shadow_metrics)
        
        context = 'NEUTRAL'
        context_strength = 0.0
        
        # Upper shadow in uptrend = potential reversal
        if recent_trend == 'UP' and upper_signals['signal_type'] != 'NONE':
            context = 'POTENTIAL_REVERSAL_DOWN'
            context_strength = upper_signals['signal_strength'] * 0.8
        
        # Lower shadow in downtrend = potential reversal
        elif recent_trend == 'DOWN' and lower_signals['signal_type'] != 'NONE':
            context = 'POTENTIAL_REVERSAL_UP'
            context_strength = lower_signals['signal_strength'] * 0.8
        
        # Shadows in ranging market = support/resistance test
        elif recent_trend == 'SIDEWAYS':
            if upper_signals['signal_type'] != 'NONE':
                context = 'RESISTANCE_TEST'
                context_strength = upper_signals['signal_strength'] * 0.6
            elif lower_signals['signal_type'] != 'NONE':
                context = 'SUPPORT_TEST'
                context_strength = lower_signals['signal_strength'] * 0.6
        
        return {
            'context': context,
            'context_strength': context_strength,
            'recent_trend': recent_trend
        }
    
    def determine_recent_trend(self, prices: pd.Series) -> str:
        """Determine recent trend direction"""
        if len(prices) < 3:
            return 'SIDEWAYS'
        
        # Simple trend detection using linear regression
        x = np.arange(len(prices))
        y = prices.values
        slope = np.polyfit(x, y, 1)[0]
        
        # Normalize slope
        avg_price = prices.mean()
        normalized_slope = slope / avg_price if avg_price != 0 else 0
        
        if normalized_slope > 0.002:  # 0.2% upward slope
            return 'UP'
        elif normalized_slope < -0.002:  # 0.2% downward slope
            return 'DOWN'
        else:
            return 'SIDEWAYS'
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main shadow candle analysis function
        Returns comprehensive shadow analysis
        """
        try:
            if len(data) < 1:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for shadow analysis'
                }
            
            # Shadow metrics
            shadow_metrics = self.calculate_shadow_metrics(data)
            
            # Upper and lower shadow signals
            upper_signals = self.detect_upper_shadow_signals(shadow_metrics)
            lower_signals = self.detect_lower_shadow_signals(shadow_metrics)
            
            # Context analysis
            context_analysis = self.analyze_shadow_context(data)
            
            # Calculate overall score
            score = 0.5  # Neutral base
            confidence = 0.0
            direction = 'NEUTRAL'
            
            # Adjust score based on shadow signals
            if upper_signals['signal_type'] != 'NONE':
                # Upper shadow = bearish signal
                score -= 0.4 * upper_signals['signal_strength']
                confidence += upper_signals['signal_strength'] * 0.6
                if score < 0.4:
                    direction = 'DOWN'
            
            if lower_signals['signal_type'] != 'NONE':
                # Lower shadow = bullish signal
                score += 0.4 * lower_signals['signal_strength']
                confidence += lower_signals['signal_strength'] * 0.6
                if score > 0.6:
                    direction = 'UP'
            
            # Boost confidence based on context
            confidence += context_analysis['context_strength'] * 0.4
            
            # Ensure bounds
            score = max(0, min(1, score))
            confidence = max(0, min(1, confidence))
            
            # Determine final direction if not set
            if direction == 'NEUTRAL':
                if score > 0.6:
                    direction = 'UP'
                elif score < 0.4:
                    direction = 'DOWN'
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'shadow_metrics': shadow_metrics,
                'upper_signals': upper_signals,
                'lower_signals': lower_signals,
                'context_analysis': context_analysis,
                'details': f'Shadows: Upper={upper_signals["signal_type"]}, Lower={lower_signals["signal_type"]}'
            }
            
        except Exception as e:
            self.logger.error(f"Shadow candle analysis error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'details': f'Shadow analysis failed: {str(e)}'
            }
