/**
 * 🚀 VIP BIG BANG ULTIMATE - Real Quotex Connection
 * 🔗 DIRECT CONNECTION TO YOUR ACTUAL QUOTEX ACCOUNT
 * 💰 Real balance, live trades, actual results
 */

console.log('🚀 VIP BIG BANG Real Quotex Connection Loading...');

// Real Quotex Connection System
class RealQuotexBridge {
    constructor() {
        this.isConnected = false;
        this.accountData = null;
        this.currentPrices = {};
        this.serverPort = 8765;

        // Start connection
        this.initialize();
    }

    async initialize() {
        console.log('🔗 Initializing Real Quotex Bridge...');

        // Wait for page to load
        if (document.readyState !== 'complete') {
            window.addEventListener('load', () => this.initialize());
            return;
        }

        // Start monitoring
        this.startAccountMonitoring();
        this.startPriceMonitoring();
        this.startTradeMonitoring();
        this.startLocalServer();

        console.log('✅ Real Quotex Bridge initialized');
    }

// 🔍 Advanced Hardware Fingerprinting System
class VIPAdvancedFingerprinting {
    constructor() {
        this.fingerprintData = {};
        this.vmDetectionResults = {};
        this.init();
    }

    init() {
        console.log('🔍 Initializing Advanced Fingerprinting...');
        this.collectHardwareFingerprint();
        this.detectVirtualEnvironment();
        this.sendFingerprintToPython();
    }

    collectHardwareFingerprint() {
        try {
            this.fingerprintData = {
                // Hardware Detection
                deviceMemory: navigator.deviceMemory || 'unknown',
                hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',

                // GPU Detection
                gpu: this.getGPUInfo(),

                // Screen Detection
                screen: {
                    width: screen.width,
                    height: screen.height,
                    colorDepth: screen.colorDepth,
                    pixelDepth: screen.pixelDepth,
                    availWidth: screen.availWidth,
                    availHeight: screen.availHeight,
                    devicePixelRatio: window.devicePixelRatio
                },

                // Browser Detection
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                languages: navigator.languages,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack,
                maxTouchPoints: navigator.maxTouchPoints,

                // Timezone Detection
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                timezoneOffset: new Date().getTimezoneOffset(),

                // Canvas Fingerprinting
                canvasFingerprint: this.getCanvasFingerprint(),

                // WebGL Fingerprinting
                webglFingerprint: this.getWebGLFingerprint(),

                // Audio Fingerprinting
                audioFingerprint: this.getAudioFingerprint(),

                // Automation Detection
                webdriver: navigator.webdriver,
                phantom: window.callPhantom || window._phantom,
                selenium: window.document.$cdc_asdjflasutopfhvcZLmcfl_,

                // Chrome Specific
                chrome: !!window.chrome,
                chromeRuntime: !!(window.chrome && window.chrome.runtime),

                // Performance
                connection: navigator.connection ? {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt
                } : null,

                // Plugins
                plugins: Array.from(navigator.plugins).map(p => ({
                    name: p.name,
                    description: p.description,
                    filename: p.filename
                })),

                // Timestamp
                timestamp: new Date().toISOString(),
                url: window.location.href
            };

            console.log('🔍 Hardware fingerprint collected:', this.fingerprintData);

        } catch (error) {
            console.error('❌ Fingerprint collection error:', error);
        }
    }

    getGPUInfo() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

            if (!gl) return { error: 'WebGL not supported' };

            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            const gpuInfo = {
                vendor: 'unknown',
                renderer: 'unknown',
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                extensions: gl.getSupportedExtensions()
            };

            if (debugInfo) {
                gpuInfo.vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                gpuInfo.renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            }

            // Additional GPU parameters
            gpuInfo.maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
            gpuInfo.maxViewportDims = gl.getParameter(gl.MAX_VIEWPORT_DIMS);
            gpuInfo.maxVertexAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
            gpuInfo.maxFragmentUniformVectors = gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS);
            gpuInfo.maxVertexUniformVectors = gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS);

            return gpuInfo;
        } catch (e) {
            return { error: e.message };
        }
    }

    getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('VIP BIG BANG 🚀', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Advanced Fingerprinting', 4, 45);

            return canvas.toDataURL();
        } catch (e) {
            return 'canvas_error';
        }
    }

    getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl');

            if (!gl) return 'webgl_not_supported';

            const vertexShader = gl.createShader(gl.VERTEX_SHADER);
            gl.shaderSource(vertexShader, 'attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}');
            gl.compileShader(vertexShader);

            const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
            gl.shaderSource(fragmentShader, 'precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}');
            gl.compileShader(fragmentShader);

            const program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);
            gl.useProgram(program);

            program.attrVertex = gl.getAttribLocation(program, 'attrVertex');
            program.uniformOffset = gl.getUniformLocation(program, 'uniformOffset');

            const buffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
            gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([-0.2, -0.9, 0, 0.4, -0.26, 0, 0, 0.7321, 0]), gl.STATIC_DRAW);

            gl.enableVertexAttribArray(program.attrVertex);
            gl.vertexAttribPointer(program.attrVertex, 3, gl.FLOAT, false, 0, 0);
            gl.uniform2f(program.uniformOffset, 1, 1);
            gl.drawArrays(gl.TRIANGLES, 0, 3);

            return canvas.toDataURL();
        } catch (e) {
            return 'webgl_error';
        }
    }

    getAudioFingerprint() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const analyser = audioContext.createAnalyser();
            const gainNode = audioContext.createGain();

            oscillator.type = 'triangle';
            oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0, audioContext.currentTime);

            oscillator.connect(analyser);
            analyser.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.start(0);

            const frequencyData = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(frequencyData);

            oscillator.stop();
            audioContext.close();

            return Array.from(frequencyData).slice(0, 30).join(',');
        } catch (e) {
            return 'audio_error';
        }
    }

    detectVirtualEnvironment() {
        try {
            const indicators = [];
            let confidence = 0;

            // GPU-based VM detection
            const gpu = this.fingerprintData.gpu;
            if (gpu && gpu.renderer) {
                const renderer = gpu.renderer.toLowerCase();
                const vendor = (gpu.vendor || '').toLowerCase();

                const vmGpuIndicators = [
                    'microsoft basic', 'vmware', 'virtualbox', 'parallels',
                    'qemu', 'swiftshader', 'llvmpipe', 'software'
                ];

                vmGpuIndicators.forEach(indicator => {
                    if (renderer.includes(indicator) || vendor.includes(indicator)) {
                        indicators.push(`GPU: ${indicator}`);
                        confidence += 30;
                    }
                });
            }

            // Memory-based detection
            if (this.fingerprintData.deviceMemory && this.fingerprintData.deviceMemory <= 4) {
                indicators.push('Low memory (≤4GB)');
                confidence += 20;
            }

            // CPU-based detection
            if (this.fingerprintData.hardwareConcurrency && this.fingerprintData.hardwareConcurrency <= 2) {
                indicators.push('Low CPU cores (≤2)');
                confidence += 15;
            }

            // Screen resolution detection
            const screen = this.fingerprintData.screen;
            if (screen.width === 1024 && screen.height === 768) {
                indicators.push('Common VM resolution (1024x768)');
                confidence += 10;
            }

            // WebDriver detection
            if (this.fingerprintData.webdriver) {
                indicators.push('WebDriver detected');
                confidence += 50;
            }

            // Automation tools detection
            if (this.fingerprintData.phantom || this.fingerprintData.selenium) {
                indicators.push('Automation tools detected');
                confidence += 50;
            }

            this.vmDetectionResults = {
                isVM: confidence >= 50,
                confidence: confidence,
                indicators: indicators,
                vmType: this.determineVMType(indicators)
            };

            console.log('🔍 VM Detection Results:', this.vmDetectionResults);

        } catch (error) {
            console.error('❌ VM detection error:', error);
        }
    }

    determineVMType(indicators) {
        const indicatorText = indicators.join(' ').toLowerCase();

        if (indicatorText.includes('vmware')) return 'VMware';
        if (indicatorText.includes('virtualbox')) return 'VirtualBox';
        if (indicatorText.includes('parallels')) return 'Parallels';
        if (indicatorText.includes('qemu')) return 'QEMU';
        if (indicatorText.includes('hyper-v')) return 'Hyper-V';
        if (indicatorText.includes('webdriver') || indicatorText.includes('automation')) return 'Automation Tool';

        return 'Unknown';
    }

    sendFingerprintToPython() {
        try {
            const completeFingerprint = {
                fingerprint: this.fingerprintData,
                vmDetection: this.vmDetectionResults,
                timestamp: new Date().toISOString()
            };

            // Store in window for access
            window.VIP_BIG_BANG_ADVANCED_FINGERPRINT = completeFingerprint;

            // Send via Chrome extension messaging
            if (window.chrome && window.chrome.runtime) {
                chrome.runtime.sendMessage({
                    type: 'ADVANCED_FINGERPRINT',
                    data: completeFingerprint
                });
            }

            // Send via WebSocket
            try {
                const ws = new WebSocket('ws://localhost:8765');
                ws.onopen = function() {
                    ws.send(JSON.stringify({
                        type: 'browser_fingerprint',
                        data: completeFingerprint
                    }));
                    ws.close();
                };
            } catch (e) {
                console.log('WebSocket communication failed:', e);
            }

            console.log('🔍 Advanced fingerprint sent to Python application');

        } catch (error) {
            console.error('❌ Fingerprint sending error:', error);
        }
    }
}

// Initialize Advanced Fingerprinting
const vipFingerprinting = new VIPAdvancedFingerprinting();

// Enhanced content script state
let isInitialized = false;
let priceObserver = null;
let balanceObserver = null;
let tradeObserver = null;
let currentBalance = 0;
let currentPrices = {};
let websocketConnection = null;
let lastPriceUpdate = 0;

// Enhanced configuration with multiple selector fallbacks
const CONFIG = {
    SELECTORS: {
        // Balance selectors (multiple fallbacks for different Quotex versions)
        balance: [
            '.balance__value', '.user-balance', '[data-testid="balance"]',
            '.account-balance', '.header-balance', '.balance-amount',
            '.wallet-balance', '.current-balance', '.balance-display'
        ],

        // Price selectors
        price: [
            '.chart-price', '.current-rate', '[data-testid="current-price"]',
            '.asset-price', '.price-display', '.rate-value',
            '.trading-chart__price', '.chart__price', '.quote-value',
            '.current-quote', '.live-price'
        ],

        // Trade button selectors
        callButton: [
            '[data-testid="call-button"]', '.call-btn', '.higher-btn',
            '.up-btn', '[data-direction="call"]', '.trade-call',
            '.btn-call', '.button-call', '.call-trade-btn'
        ],

        putButton: [
            '[data-testid="put-button"]', '.put-btn', '.lower-btn',
            '.down-btn', '[data-direction="put"]', '.trade-put',
            '.btn-put', '.button-put', '.put-trade-btn'
        ],

        // Amount input selectors
        amountInput: [
            '[data-testid="amount-input"]', '.amount-input', '.trade-amount',
            'input[type="number"]', '.amount-field', '.investment-amount',
            '.bet-amount', '.stake-input'
        ],

        // Duration/Expiry selectors
        durationSelect: [
            '[data-testid="expiry-select"]', '.expiry-select', '.duration-select',
            '.time-select', '.expiry-time', '.trade-duration',
            '.expiration-select', '.timer-select'
        ],

        // Asset selectors
        assetSelect: [
            '[data-testid="asset-select"]', '.asset-select', '.symbol-select',
            '.currency-select', '.pair-select', '.instrument-select'
        ]
    },

    // Enhanced timing configuration
    TIMING: {
        RETRY_DELAY: 1000,
        MAX_RETRIES: 10,
        PRICE_UPDATE_INTERVAL: 500,  // Faster updates
        BALANCE_UPDATE_INTERVAL: 2000,
        RECONNECT_INTERVAL: 5000,
        HUMAN_DELAY_MIN: 100,
        HUMAN_DELAY_MAX: 300
    },

    // WebSocket configuration
    WEBSOCKET: {
        URL: 'ws://localhost:8765',
        RECONNECT_ATTEMPTS: 5,
        HEARTBEAT_INTERVAL: 30000
    }
};

/**
 * 🚀 Enhanced initialization with WebSocket connection
 */
function initialize() {
    if (isInitialized) return;

    console.log('🚀 VIP BIG BANG Ultimate Content Script initializing...');

    // Wait for page to be fully loaded
    if (document.readyState !== 'complete') {
        window.addEventListener('load', initialize);
        return;
    }

    // Setup enhanced monitoring
    setupEnhancedObservers();

    // Setup message listeners
    setupMessageListeners();

    // Setup WebSocket connection to robot application
    setupWebSocketConnection();

    // Start enhanced monitoring
    startEnhancedMonitoring();

    // Inject VIP BIG BANG integration
    injectVIPIntegration();

    // Notify background script
    chrome.runtime.sendMessage({
        type: 'VIP_ULTIMATE_READY',
        data: {
            url: window.location.href,
            timestamp: Date.now(),
            version: '3.0.0'
        }
    });

    isInitialized = true;
    console.log('🏆 VIP BIG BANG Ultimate Content Script initialized');
}

/**
 * 🔌 Setup WebSocket connection to robot application
 */
function setupWebSocketConnection() {
    try {
        console.log('🔌 Connecting to VIP BIG BANG robot application...');

        websocketConnection = new WebSocket(CONFIG.WEBSOCKET.URL);

        websocketConnection.onopen = function(event) {
            console.log('✅ Connected to VIP BIG BANG robot application');

            // Send initial status
            sendToRobot({
                type: 'CONNECTION_STATUS',
                data: {
                    connected: true,
                    url: window.location.href,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent,
                    version: '3.0.0'
                }
            });

            // Start heartbeat
            startHeartbeat();

            // Start real-time data streaming
            startRealTimeDataStreaming();
        };

        websocketConnection.onmessage = function(event) {
            try {
                const message = JSON.parse(event.data);
                handleRobotMessage(message);
            } catch (error) {
                console.error('❌ WebSocket message error:', error);
            }
        };

        websocketConnection.onclose = function(event) {
            console.log('🔌 WebSocket connection closed, attempting reconnect...');
            setTimeout(setupWebSocketConnection, CONFIG.TIMING.RECONNECT_INTERVAL);
        };

        websocketConnection.onerror = function(error) {
            console.error('❌ WebSocket error:', error);
        };

    } catch (error) {
        console.error('❌ WebSocket setup failed:', error);
        // Retry connection
        setTimeout(setupWebSocketConnection, CONFIG.TIMING.RECONNECT_INTERVAL);
    }
}

/**
 * 💓 Start heartbeat to keep connection alive
 */
function startHeartbeat() {
    setInterval(() => {
        if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
            sendToRobot({
                type: 'HEARTBEAT',
                data: { timestamp: Date.now() }
            });
        }
    }, CONFIG.WEBSOCKET.HEARTBEAT_INTERVAL);
}

/**
 * 📡 Start real-time data streaming
 */
function startRealTimeDataStreaming() {
    console.log('📡 Starting real-time data streaming...');

    // Stream price updates every 500ms
    setInterval(() => {
        if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
            const priceData = extractCurrentPriceData();
            if (priceData) {
                sendToRobot({
                    type: 'PRICE_UPDATE',
                    data: priceData
                });
            }
        }
    }, CONFIG.TIMING.PRICE_UPDATE_INTERVAL);

    // Stream balance updates every 2 seconds
    setInterval(() => {
        if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
            const balanceData = extractCurrentBalanceData();
            if (balanceData) {
                sendToRobot({
                    type: 'BALANCE_UPDATE',
                    data: balanceData
                });
            }
        }
    }, CONFIG.TIMING.BALANCE_UPDATE_INTERVAL);

    // Stream market status every 5 seconds
    setInterval(() => {
        if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
            const marketStatus = extractMarketStatus();
            if (marketStatus) {
                sendToRobot({
                    type: 'MARKET_STATUS',
                    data: marketStatus
                });
            }
        }
    }, 5000);
}

/**
 * 💰 Extract current price data from Quotex interface
 */
function extractCurrentPriceData() {
    try {
        const priceData = {};

        // Try multiple price selectors
        for (const selector of CONFIG.SELECTORS.price) {
            const priceElements = document.querySelectorAll(selector);

            priceElements.forEach(element => {
                const priceText = element.textContent || element.innerText;
                const priceMatch = priceText.match(/(\d+\.\d+)/);

                if (priceMatch) {
                    const price = parseFloat(priceMatch[1]);
                    const asset = determineAssetFromElement(element) || 'EUR/USD';

                    priceData[asset] = {
                        price: price,
                        timestamp: Date.now(),
                        element_selector: selector
                    };
                }
            });
        }

        // Get main trading pair price
        const mainPrice = getCurrentMainPrice();
        if (mainPrice) {
            priceData['MAIN'] = {
                asset: getCurrentAsset(),
                price: mainPrice,
                timestamp: Date.now()
            };
        }

        return Object.keys(priceData).length > 0 ? priceData : null;

    } catch (error) {
        console.error('❌ Price extraction error:', error);
        return null;
    }
}

/**
 * 💳 Extract current balance data
 */
function extractCurrentBalanceData() {
    try {
        for (const selector of CONFIG.SELECTORS.balance) {
            const balanceElement = document.querySelector(selector);

            if (balanceElement) {
                const balanceText = balanceElement.textContent || balanceElement.innerText;
                const balanceMatch = balanceText.match(/(\d+(?:,\d{3})*(?:\.\d{2})?)/);

                if (balanceMatch) {
                    const balance = parseFloat(balanceMatch[1].replace(/,/g, ''));

                    return {
                        balance: balance,
                        currency: extractCurrency(balanceText) || 'USD',
                        timestamp: Date.now(),
                        element_selector: selector
                    };
                }
            }
        }

        return null;

    } catch (error) {
        console.error('❌ Balance extraction error:', error);
        return null;
    }
}

/**
 * 📊 Extract market status
 */
function extractMarketStatus() {
    try {
        const status = {
            timestamp: Date.now(),
            url: window.location.href,
            trading_active: isTradingActive(),
            current_asset: getCurrentAsset(),
            demo_mode: isDemoMode(),
            connection_quality: getConnectionQuality()
        };

        return status;

    } catch (error) {
        console.error('❌ Market status extraction error:', error);
        return null;
    }
}

/**
 * 📡 Send message to robot application
 */
function sendToRobot(message) {
    try {
        if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
            websocketConnection.send(JSON.stringify(message));
        }
    } catch (error) {
        console.error('❌ Failed to send message to robot:', error);
    }
}

/**
 * 📨 Handle messages from robot application
 */
function handleRobotMessage(message) {
    try {
        console.log('📨 Message from robot:', message.type);

        switch (message.type) {
            case 'EXECUTE_TRADE':
                executeTradeFromRobot(message.data);
                break;

            case 'GET_MARKET_DATA':
                sendMarketDataToRobot();
                break;

            case 'SET_TRADE_AMOUNT':
                setTradeAmount(message.data.amount);
                break;

            case 'GET_BALANCE':
                sendBalanceToRobot();
                break;

            case 'CONNECTION_TEST':
                sendToRobot({
                    type: 'CONNECTION_TEST_RESPONSE',
                    data: {
                        timestamp: Date.now(),
                        status: 'connected',
                        version: '3.0.0'
                    }
                });
                break;

            case 'HEARTBEAT':
                // Respond to heartbeat
                sendToRobot({
                    type: 'HEARTBEAT_RESPONSE',
                    data: { timestamp: Date.now() }
                });
                break;

            case 'PING':
                sendToRobot({ type: 'PONG', data: { timestamp: Date.now() } });
                break;

            default:
                console.log('❓ Unknown message type:', message.type);
        }

    } catch (error) {
        console.error('❌ Robot message handling error:', error);
        sendToRobot({
            type: 'ERROR',
            data: {
                error: error.message,
                timestamp: Date.now()
            }
        });
    }
}

/**
 * 🎯 Execute trade from robot command
 */
async function executeTradeFromRobot(tradeData) {
    try {
        console.log('🎯 Executing trade from robot:', tradeData);

        const { asset, direction, amount, duration, order_id } = tradeData;

        // Set asset if specified
        if (asset) {
            await selectAsset(asset);
            await sleep(500);
        }

        // Set amount if specified
        if (amount) {
            await setTradeAmount(amount);
            await sleep(300);
        }

        // Set duration if specified
        if (duration) {
            await setTradeDuration(duration);
            await sleep(300);
        }

        // Execute the trade
        const success = await clickTradeButton(direction);

        if (success) {
            const result = {
                order_id: order_id,
                success: true,
                asset: asset,
                direction: direction,
                amount: amount,
                duration: duration,
                entry_price: getCurrentMainPrice(),
                timestamp: Date.now()
            };

            // Send trade result back to robot
            sendToRobot({
                type: 'TRADE_RESULT',
                data: result
            });

            console.log('✅ Trade executed successfully:', result);
        } else {
            throw new Error('Failed to click trade button');
        }

    } catch (error) {
        console.error('❌ Trade execution error:', error);

        // Send error back to robot
        sendToRobot({
            type: 'TRADE_RESULT',
            data: {
                order_id: tradeData.order_id,
                success: false,
                error: error.message,
                timestamp: Date.now()
            }
        });
    }
}

/**
 * 📊 Send market data to robot
 */
function sendMarketDataToRobot() {
    try {
        const marketData = {
            prices: extractCurrentPriceData(),
            balance: extractCurrentBalanceData(),
            status: extractMarketStatus(),
            timestamp: Date.now()
        };

        sendToRobot({
            type: 'MARKET_DATA_RESPONSE',
            data: marketData
        });

    } catch (error) {
        console.error('❌ Market data send error:', error);
    }
}

/**
 * 💰 Send balance to robot
 */
function sendBalanceToRobot() {
    try {
        const balanceData = extractCurrentBalanceData();

        sendToRobot({
            type: 'BALANCE_RESPONSE',
            data: balanceData
        });

    } catch (error) {
        console.error('❌ Balance send error:', error);
    }
}

/**
 * Setup DOM observers for dynamic content
 */
function setupObservers() {
    // Observe balance changes
    const balanceElement = findElement(CONFIG.SELECTORS.balance);
    if (balanceElement) {
        balanceObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    updateBalance();
                }
            });
        });
        
        balanceObserver.observe(balanceElement, {
            childList: true,
            subtree: true,
            characterData: true
        });
    }
    
    // Observe price changes
    const priceElement = findElement(CONFIG.SELECTORS.priceDisplay);
    if (priceElement) {
        priceObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    updatePrices();
                }
            });
        });
        
        priceObserver.observe(priceElement, {
            childList: true,
            subtree: true,
            characterData: true
        });
    }
}

/**
 * Setup message listeners
 */
function setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        handleMessage(message, sender, sendResponse);
        return true; // Keep message channel open
    });
}

/**
 * Handle messages from background script
 */
async function handleMessage(message, sender, sendResponse) {
    try {
        switch (message.type) {
            case 'EXECUTE_TRADE':
                const result = await executeTrade(message.data);
                sendResponse({ success: true, data: result });
                break;
                
            case 'GET_BALANCE':
                const balance = getCurrentBalance();
                sendResponse({ success: true, data: { balance } });
                break;
                
            case 'GET_PRICES':
                sendResponse({ success: true, data: currentPrices });
                break;
                
            case 'SET_AMOUNT':
                setTradeAmount(message.data.amount);
                sendResponse({ success: true });
                break;
                
            case 'SET_DURATION':
                setTradeDuration(message.data.duration);
                sendResponse({ success: true });
                break;
                
            default:
                sendResponse({ success: false, error: 'Unknown command' });
        }
    } catch (error) {
        console.error('❌ Message handling error:', error);
        sendResponse({ success: false, error: error.message });
    }
}

/**
 * Execute trade on Quotex interface
 */
async function executeTrade(tradeData) {
    try {
        console.log('📊 Executing trade:', tradeData);
        
        const { direction, amount, duration, asset } = tradeData;
        
        // Set trade amount
        if (amount) {
            await setTradeAmount(amount);
        }
        
        // Set trade duration
        if (duration) {
            await setTradeDuration(duration);
        }
        
        // Select asset if specified
        if (asset) {
            await selectAsset(asset);
        }
        
        // Click appropriate trade button
        const success = await clickTradeButton(direction);
        
        if (success) {
            const result = {
                tradeId: generateTradeId(),
                direction,
                amount,
                duration,
                asset,
                timestamp: new Date().toISOString(),
                entryPrice: getCurrentPrice(asset)
            };
            
            // Notify background script
            chrome.runtime.sendMessage({
                type: 'TRADE_EXECUTED',
                data: result
            });
            
            return result;
        } else {
            throw new Error('Failed to execute trade');
        }
        
    } catch (error) {
        console.error('❌ Trade execution error:', error);
        throw error;
    }
}

/**
 * Set trade amount
 */
async function setTradeAmount(amount) {
    const amountInput = findElement(CONFIG.SELECTORS.amountInput);
    if (!amountInput) {
        throw new Error('Amount input not found');
    }
    
    // Clear current value
    amountInput.value = '';
    amountInput.focus();
    
    // Type new amount
    for (const char of amount.toString()) {
        amountInput.value += char;
        amountInput.dispatchEvent(new Event('input', { bubbles: true }));
        await sleep(50);
    }
    
    amountInput.dispatchEvent(new Event('change', { bubbles: true }));
    amountInput.blur();
    
    console.log(`💰 Amount set to: ${amount}`);
}

/**
 * Set trade duration
 */
async function setTradeDuration(duration) {
    const durationSelect = findElement(CONFIG.SELECTORS.durationSelect);
    if (!durationSelect) {
        console.warn('⚠️ Duration selector not found');
        return;
    }
    
    // Find option with matching duration
    const options = durationSelect.querySelectorAll('option');
    for (const option of options) {
        if (option.value === duration.toString() || option.textContent.includes(duration.toString())) {
            durationSelect.value = option.value;
            durationSelect.dispatchEvent(new Event('change', { bubbles: true }));
            break;
        }
    }
    
    console.log(`⏱️ Duration set to: ${duration}s`);
}

/**
 * Select trading asset
 */
async function selectAsset(asset) {
    const assetSelect = findElement(CONFIG.SELECTORS.assetSelect);
    if (!assetSelect) {
        console.warn('⚠️ Asset selector not found');
        return;
    }
    
    // Find and select the asset
    const options = assetSelect.querySelectorAll('option');
    for (const option of options) {
        if (option.value === asset || option.textContent.includes(asset)) {
            assetSelect.value = option.value;
            assetSelect.dispatchEvent(new Event('change', { bubbles: true }));
            break;
        }
    }
    
    console.log(`📈 Asset selected: ${asset}`);
}

/**
 * Click trade button (CALL or PUT)
 */
async function clickTradeButton(direction) {
    const selector = direction === 'CALL' ? CONFIG.SELECTORS.callButton : CONFIG.SELECTORS.putButton;
    const button = findElement(selector);
    
    if (!button) {
        throw new Error(`${direction} button not found`);
    }
    
    // Ensure button is enabled
    if (button.disabled || button.classList.contains('disabled')) {
        throw new Error(`${direction} button is disabled`);
    }
    
    // Click the button
    button.click();
    
    // Wait for trade confirmation
    await sleep(1000);
    
    console.log(`🎯 ${direction} button clicked`);
    return true;
}

/**
 * Get current balance
 */
function getCurrentBalance() {
    const balanceElement = findElement(CONFIG.SELECTORS.balance);
    if (!balanceElement) {
        return currentBalance;
    }
    
    const balanceText = balanceElement.textContent || balanceElement.innerText;
    const balance = parseFloat(balanceText.replace(/[^0-9.]/g, ''));
    
    return isNaN(balance) ? currentBalance : balance;
}

/**
 * Get current price for asset
 */
function getCurrentPrice(asset = 'EUR/USD') {
    return currentPrices[asset] || 0;
}

/**
 * Update balance from DOM
 */
function updateBalance() {
    const newBalance = getCurrentBalance();
    if (newBalance !== currentBalance) {
        currentBalance = newBalance;
        console.log(`💰 Balance updated: ${currentBalance}`);
    }
}

/**
 * Update prices from DOM
 */
function updatePrices() {
    const priceElements = document.querySelectorAll(CONFIG.SELECTORS.priceDisplay);
    
    priceElements.forEach(element => {
        const priceText = element.textContent || element.innerText;
        const price = parseFloat(priceText.replace(/[^0-9.]/g, ''));
        
        if (!isNaN(price)) {
            // Try to determine asset from context
            const asset = determineAssetFromContext(element);
            currentPrices[asset] = price;
        }
    });
    
    // Notify background script of price updates
    chrome.runtime.sendMessage({
        type: 'PRICE_UPDATE',
        data: currentPrices
    });
}

/**
 * Determine asset from DOM context
 */
function determineAssetFromContext(element) {
    // This would need to be customized based on actual Quotex DOM structure
    const parent = element.closest('[data-asset]') || element.closest('.asset-item');
    if (parent) {
        return parent.dataset.asset || parent.textContent.match(/[A-Z]{3}\/[A-Z]{3}/)?.[0] || 'EUR/USD';
    }
    return 'EUR/USD';
}

/**
 * Start price monitoring
 */
function startPriceMonitoring() {
    setInterval(() => {
        updatePrices();
        updateBalance();
    }, CONFIG.PRICE_UPDATE_INTERVAL);
}

/**
 * Find element with multiple selector fallbacks
 */
function findElement(selectors) {
    const selectorList = selectors.split(', ');
    
    for (const selector of selectorList) {
        const element = document.querySelector(selector.trim());
        if (element) {
            return element;
        }
    }
    
    return null;
}

/**
 * Generate unique trade ID
 */
function generateTradeId() {
    return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Sleep utility
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 🎯 Get current main trading price
 */
function getCurrentMainPrice() {
    try {
        // Try multiple selectors for main price
        const priceSelectors = [
            '.chart-price .price-value',
            '.trading-chart .current-price',
            '.price-display .main-price',
            '.asset-price .current',
            '.rate-display .rate-value',
            '[data-testid="current-price"]'
        ];

        for (const selector of priceSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const priceText = element.textContent || element.innerText;
                const priceMatch = priceText.match(/(\d+\.\d+)/);
                if (priceMatch) {
                    return parseFloat(priceMatch[1]);
                }
            }
        }

        return null;

    } catch (error) {
        console.error('❌ Main price extraction error:', error);
        return null;
    }
}

/**
 * 📈 Get current trading asset
 */
function getCurrentAsset() {
    try {
        // Try multiple selectors for asset
        const assetSelectors = [
            '.asset-selector .selected-asset',
            '.trading-pair .pair-name',
            '.asset-display .asset-name',
            '.current-asset .name',
            '[data-testid="selected-asset"]'
        ];

        for (const selector of assetSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const assetText = element.textContent || element.innerText;
                const assetMatch = assetText.match(/([A-Z]{3}\/[A-Z]{3})/);
                if (assetMatch) {
                    return assetMatch[1];
                }
            }
        }

        return 'EUR/USD'; // Default

    } catch (error) {
        console.error('❌ Asset extraction error:', error);
        return 'EUR/USD';
    }
}

/**
 * 🏦 Extract currency from balance text
 */
function extractCurrency(balanceText) {
    try {
        const currencyMatch = balanceText.match(/([A-Z]{3})/);
        return currencyMatch ? currencyMatch[1] : 'USD';
    } catch (error) {
        return 'USD';
    }
}

/**
 * 📊 Check if trading is active
 */
function isTradingActive() {
    try {
        // Check for disabled trade buttons
        const callButton = document.querySelector(CONFIG.SELECTORS.callButton);
        const putButton = document.querySelector(CONFIG.SELECTORS.putButton);

        if (callButton && putButton) {
            return !callButton.disabled && !putButton.disabled;
        }

        // Check for market closed indicators
        const marketClosedIndicators = [
            '.market-closed',
            '.trading-disabled',
            '.market-offline'
        ];

        for (const selector of marketClosedIndicators) {
            if (document.querySelector(selector)) {
                return false;
            }
        }

        return true;

    } catch (error) {
        console.error('❌ Trading status check error:', error);
        return false;
    }
}

/**
 * 🎮 Check if in demo mode
 */
function isDemoMode() {
    try {
        // Look for demo mode indicators
        const demoIndicators = [
            '.demo-mode',
            '.practice-mode',
            '[data-mode="demo"]',
            '.account-type.demo'
        ];

        for (const selector of demoIndicators) {
            const element = document.querySelector(selector);
            if (element) {
                return true;
            }
        }

        // Check URL for demo indicators
        const url = window.location.href.toLowerCase();
        if (url.includes('demo') || url.includes('practice')) {
            return true;
        }

        return false;

    } catch (error) {
        console.error('❌ Demo mode check error:', error);
        return false;
    }
}

/**
 * 📶 Get connection quality
 */
function getConnectionQuality() {
    try {
        // Check WebSocket connection
        if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
            return 'excellent';
        }

        // Check for connection indicators
        const connectionIndicators = [
            '.connection-status.good',
            '.signal-strength.high',
            '.network-status.online'
        ];

        for (const selector of connectionIndicators) {
            if (document.querySelector(selector)) {
                return 'good';
            }
        }

        // Check for poor connection indicators
        const poorConnectionIndicators = [
            '.connection-status.poor',
            '.signal-strength.low',
            '.network-status.offline'
        ];

        for (const selector of poorConnectionIndicators) {
            if (document.querySelector(selector)) {
                return 'poor';
            }
        }

        return 'unknown';

    } catch (error) {
        console.error('❌ Connection quality check error:', error);
        return 'unknown';
    }
}

/**
 * 🔍 Determine asset from DOM element
 */
function determineAssetFromElement(element) {
    try {
        // Check element attributes
        if (element.dataset.asset) {
            return element.dataset.asset;
        }

        // Check parent elements
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            if (parent.dataset.asset) {
                return parent.dataset.asset;
            }

            // Check for asset in text content
            const assetMatch = parent.textContent.match(/([A-Z]{3}\/[A-Z]{3})/);
            if (assetMatch) {
                return assetMatch[1];
            }

            parent = parent.parentElement;
        }

        // Check siblings
        const siblings = element.parentElement?.children || [];
        for (const sibling of siblings) {
            const assetMatch = sibling.textContent.match(/([A-Z]{3}\/[A-Z]{3})/);
            if (assetMatch) {
                return assetMatch[1];
            }
        }

        return getCurrentAsset();

    } catch (error) {
        console.error('❌ Asset determination error:', error);
        return getCurrentAsset();
    }
}

/**
 * Error handler
 */
window.addEventListener('error', (event) => {
    console.error('❌ Content script error:', event.error);
    chrome.runtime.sendMessage({
        type: 'ERROR',
        error: event.error.message
    });
});

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}
