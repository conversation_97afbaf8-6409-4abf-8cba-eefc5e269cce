#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG Enterprise Trading Robot - Professional Launcher
Ultra-fast 15s analysis | 5s trades | Quantum-level performance
Enterprise Security & Optimization
"""

import sys
import os
import asyncio
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
    from PySide6.QtCore import Qt, QTimer, QThread, Signal
    from PySide6.QtGui import QFont, QIcon
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("PySide6 not available. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
    from PySide6.QtCore import Qt, QTimer, QThread, Signal
    from PySide6.QtGui import QFont, QIcon
    PYSIDE6_AVAILABLE = True

# VIP BIG BANG Core Systems
try:
    from core.analysis_engine import AnalysisEngine
    from core.signal_manager import SignalManager
    from core.settings import Settings
    from core.realtime_quotex_connector import RealtimeQuotexConnector
    from core.dynamic_timeframe_manager import DynamicTimeframeManager
    from trading.autotrade import AutoTrader
    from ui.vip_main_dashboard import VIPMainDashboard
    from utils.logger import setup_logger
    CORE_AVAILABLE = True
    print("VIP BIG BANG core systems loaded successfully")
except ImportError as e:
    print(f"Core systems not available: {e}")
    CORE_AVAILABLE = False

class VIPBigBangLauncher(QMainWindow):
    """
    🚀 VIP BIG BANG Professional Launcher
    
    Features:
    - Unified system initialization
    - Real-time status monitoring
    - Professional UI integration
    - Chrome extension management
    - Trading system orchestration
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VIP BIG BANG Enterprise Trading Robot")
        self.setGeometry(100, 100, 800, 600)
        
        # Initialize core systems
        self.settings = None
        self.analysis_engine = None
        self.signal_manager = None
        self.auto_trader = None
        self.quotex_connector = None
        self.timeframe_manager = None
        self.main_dashboard = None
        
        # Setup logger
        self.logger = setup_logger("vip_big_bang_launcher")
        
        # Initialize UI
        self.init_ui()
        
        # Initialize systems
        if CORE_AVAILABLE:
            self.init_core_systems()
        
    def init_ui(self):
        """Initialize the launcher UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("VIP BIG BANG Enterprise Trading Robot")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #2563eb;
                background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                padding: 20px;
                border-radius: 15px;
                border: 2px solid #3b82f6;
            }
        """)
        layout.addWidget(title)

        # Status section
        self.status_label = QLabel("Initializing systems...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 14))
        self.status_label.setStyleSheet("""
            QLabel {
                color: #059669;
                background: #f0fdf4;
                padding: 15px;
                border-radius: 10px;
                border: 1px solid #10b981;
            }
        """)
        layout.addWidget(self.status_label)
        
        # Buttons section
        buttons_layout = QHBoxLayout()
        
        # Launch Main Dashboard button
        self.launch_dashboard_btn = QPushButton("Launch Main Dashboard")
        self.launch_dashboard_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.launch_dashboard_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
            }
        """)
        self.launch_dashboard_btn.clicked.connect(self.launch_main_dashboard)
        buttons_layout.addWidget(self.launch_dashboard_btn)

        # Launch Extension Manager button
        self.launch_extension_btn = QPushButton("Extension Manager")
        self.launch_extension_btn.setFont(QFont("Arial", 12, QFont.Bold))
        self.launch_extension_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            }
        """)
        self.launch_extension_btn.clicked.connect(self.launch_extension_manager)
        buttons_layout.addWidget(self.launch_extension_btn)
        
        layout.addLayout(buttons_layout)
        
        # System info section
        self.system_info = QLabel()
        self.system_info.setAlignment(Qt.AlignLeft)
        self.system_info.setFont(QFont("Consolas", 10))
        self.system_info.setStyleSheet("""
            QLabel {
                background: #f8fafc;
                color: #475569;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #e2e8f0;
            }
        """)
        layout.addWidget(self.system_info)
        
        # Update system info
        self.update_system_info()
        
        # Setup timer for status updates
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(2000)  # Update every 2 seconds
        
    def init_core_systems(self):
        """Initialize VIP BIG BANG core systems"""
        try:
            self.logger.info("Initializing VIP BIG BANG core systems...")
            
            # Load settings
            self.settings = Settings()
            
            # Initialize managers
            self.timeframe_manager = DynamicTimeframeManager(self.settings)
            self.analysis_engine = AnalysisEngine(self.settings)
            self.signal_manager = SignalManager(self.settings)

            # Create a mock quotex client for AutoTrader
            class MockQuotexClient:
                def __init__(self, settings):
                    self.settings = settings
                def get_balance(self):
                    return 1000.0
                def get_current_price(self, asset):
                    return 1.0000
                async def place_trade(self, **kwargs):
                    return {'success': True, 'trade_id': 'mock_123'}

            mock_client = MockQuotexClient(self.settings)
            self.auto_trader = AutoTrader(mock_client, self.signal_manager)
            
            # Initialize Quotex connector
            self.quotex_connector = RealtimeQuotexConnector()
            
            # Set default timeframe (15s analysis, 5s trades)
            # Note: This will be set when the system starts trading
            
            self.status_label.setText("✅ Core systems initialized successfully")
            self.logger.info("VIP BIG BANG core systems initialized successfully")
            
        except Exception as e:
            self.status_label.setText(f"❌ Failed to initialize core systems: {e}")
            self.logger.error(f"Failed to initialize core systems: {e}")
    
    def launch_main_dashboard(self):
        """Launch the main trading dashboard"""
        try:
            if not CORE_AVAILABLE:
                self.status_label.setText("❌ Core systems not available")
                return
            
            self.status_label.setText("🚀 Launching main dashboard...")
            
            # Create and show main dashboard
            self.main_dashboard = VIPMainDashboard()
            self.main_dashboard.show()
            
            # Hide launcher
            self.hide()
            
            self.logger.info("Main dashboard launched successfully")
            
        except Exception as e:
            self.status_label.setText(f"❌ Failed to launch dashboard: {e}")
            self.logger.error(f"Failed to launch dashboard: {e}")
    
    def launch_extension_manager(self):
        """Launch Chrome extension manager"""
        try:
            self.status_label.setText("🔧 Launching extension manager...")
            
            # Import and run extension manager
            from vip_auto_extension_quotex import main as extension_main
            extension_main()
            
            self.status_label.setText("✅ Extension manager launched")
            self.logger.info("Extension manager launched successfully")
            
        except Exception as e:
            self.status_label.setText(f"❌ Failed to launch extension manager: {e}")
            self.logger.error(f"Failed to launch extension manager: {e}")
    
    def update_system_info(self):
        """Update system information display"""
        info_text = f"""
🔧 System Information:
├── Python Version: {sys.version.split()[0]}
├── Project Root: {project_root}
├── Core Systems: {'✅ Available' if CORE_AVAILABLE else '❌ Not Available'}
├── PySide6: {'✅ Available' if PYSIDE6_AVAILABLE else '❌ Not Available'}
├── Config File: {'✅ Found' if os.path.exists('config.json') else '❌ Missing'}
├── Chrome Extension: {'✅ Found' if os.path.exists('chrome_extension') else '❌ Missing'}
└── Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        self.system_info.setText(info_text.strip())
    
    def update_status(self):
        """Update status periodically"""
        if CORE_AVAILABLE and self.analysis_engine:
            # Get performance stats
            stats = self.analysis_engine.get_performance_stats()
            self.status_label.setText(f"🟢 Systems running | Cache: {stats.get('cache_size', 0)} | Analyzers: {stats.get('analyzers_count', 0)}")
        else:
            self.status_label.setText("🔄 Waiting for core systems...")

def main():
    """Main entry point"""
    print("=" * 60)
    print("VIP BIG BANG Enterprise Trading Robot")
    print("Ultra-fast 15s analysis | 5s trades")
    print("Enterprise Security & Optimization")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    app.setApplicationName("VIP BIG BANG Enterprise")
    app.setApplicationVersion("1.0.0")
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show launcher
    launcher = VIPBigBangLauncher()
    launcher.show()
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
