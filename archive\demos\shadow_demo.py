"""
💫 Shadow Effects Demo - تکنیک‌های سایه ChatGPT
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import Qt
from PySide6.QtGui import QGraphicsDropShadowEffect, QColor

class ShadowDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("💫 Shadow Effects Demo")
        self.setGeometry(100, 100, 1000, 700)
        
        # ChatGPT background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:1 #1a1a2e);
                color: white;
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QGridLayout(central_widget)
        layout.setSpacing(30)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # Title
        title = QLabel("💫 Modern Shadow Effects")
        title.setStyleSheet("""
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title, 0, 0, 1, 3)
        
        # 1. Subtle Shadow (ChatGPT Cards)
        subtle_card = self.create_shadow_demo(
            "Subtle Shadow",
            "ChatGPT-style card shadows",
            blur_radius=15,
            x_offset=0,
            y_offset=4,
            color_alpha=30
        )
        layout.addWidget(subtle_card, 1, 0)
        
        # 2. Medium Shadow (Panels)
        medium_card = self.create_shadow_demo(
            "Medium Shadow",
            "Panel and container shadows",
            blur_radius=20,
            x_offset=0,
            y_offset=6,
            color_alpha=40
        )
        layout.addWidget(medium_card, 1, 1)
        
        # 3. Strong Shadow (Modals)
        strong_card = self.create_shadow_demo(
            "Strong Shadow",
            "Modal and popup shadows",
            blur_radius=30,
            x_offset=0,
            y_offset=10,
            color_alpha=60
        )
        layout.addWidget(strong_card, 1, 2)
        
        # 4. Colored Shadow (Special Elements)
        colored_card = self.create_colored_shadow_demo(
            "Colored Shadow",
            "Special accent shadows",
            blur_radius=25,
            x_offset=0,
            y_offset=8,
            color=QColor(76, 175, 80, 50)  # Green shadow
        )
        layout.addWidget(colored_card, 2, 0)
        
        # 5. Inner Shadow Effect (Simulated)
        inner_card = self.create_inner_shadow_demo(
            "Inner Shadow",
            "Inset shadow simulation"
        )
        layout.addWidget(inner_card, 2, 1)
        
        # 6. Glow Effect
        glow_card = self.create_glow_demo(
            "Glow Effect",
            "Glowing elements"
        )
        layout.addWidget(glow_card, 2, 2)
        
        # Shadow Rules
        rules_widget = self.create_shadow_rules()
        layout.addWidget(rules_widget, 3, 0, 1, 3)
    
    def create_shadow_demo(self, title, description, blur_radius, x_offset, y_offset, color_alpha):
        """Create shadow demonstration widget"""
        container = QWidget()
        container.setFixedSize(250, 180)
        
        layout = QVBoxLayout(container)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Shadow sample
        sample = QWidget()
        sample.setFixedSize(200, 100)
        sample.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255,255,255,0.12),
                stop:1 rgba(255,255,255,0.06));
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 16px;
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setXOffset(x_offset)
        shadow.setYOffset(y_offset)
        shadow.setColor(QColor(0, 0, 0, color_alpha))
        sample.setGraphicsEffect(shadow)
        
        # Center the sample
        sample_container = QWidget()
        sample_layout = QHBoxLayout(sample_container)
        sample_layout.addWidget(sample)
        sample_layout.setAlignment(Qt.AlignCenter)
        layout.addWidget(sample_container)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 11px;
            color: rgba(255,255,255,0.7);
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Shadow values
        values_label = QLabel(f"Blur: {blur_radius}px, Offset: ({x_offset}, {y_offset}), Alpha: {color_alpha}")
        values_label.setStyleSheet("""
            font-size: 10px;
            color: rgba(255,255,255,0.5);
            font-family: monospace;
        """)
        values_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(values_label)
        
        return container
    
    def create_colored_shadow_demo(self, title, description, blur_radius, x_offset, y_offset, color):
        """Create colored shadow demonstration"""
        container = QWidget()
        container.setFixedSize(250, 180)
        
        layout = QVBoxLayout(container)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Colored shadow sample
        sample = QWidget()
        sample.setFixedSize(200, 100)
        sample.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(76, 175, 80, 0.2),
                stop:1 rgba(76, 175, 80, 0.1));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 16px;
        """)
        
        # Add colored shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setXOffset(x_offset)
        shadow.setYOffset(y_offset)
        shadow.setColor(color)
        sample.setGraphicsEffect(shadow)
        
        # Center the sample
        sample_container = QWidget()
        sample_layout = QHBoxLayout(sample_container)
        sample_layout.addWidget(sample)
        sample_layout.setAlignment(Qt.AlignCenter)
        layout.addWidget(sample_container)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 11px;
            color: rgba(255,255,255,0.7);
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)
        
        return container
    
    def create_inner_shadow_demo(self, title, description):
        """Create inner shadow simulation"""
        container = QWidget()
        container.setFixedSize(250, 180)
        
        layout = QVBoxLayout(container)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Inner shadow simulation with CSS
        sample = QWidget()
        sample.setFixedSize(200, 100)
        sample.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0,0,0,0.3),
                stop:1 rgba(255,255,255,0.05));
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 16px;
        """)
        
        # Center the sample
        sample_container = QWidget()
        sample_layout = QHBoxLayout(sample_container)
        sample_layout.addWidget(sample)
        sample_layout.setAlignment(Qt.AlignCenter)
        layout.addWidget(sample_container)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 11px;
            color: rgba(255,255,255,0.7);
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)
        
        return container
    
    def create_glow_demo(self, title, description):
        """Create glow effect demonstration"""
        container = QWidget()
        container.setFixedSize(250, 180)
        
        layout = QVBoxLayout(container)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Glow sample
        sample = QWidget()
        sample.setFixedSize(200, 100)
        sample.setStyleSheet("""
            background: #4CAF50;
            border-radius: 16px;
        """)
        
        # Add glow effect (large blur, bright color)
        glow = QGraphicsDropShadowEffect()
        glow.setBlurRadius(40)
        glow.setXOffset(0)
        glow.setYOffset(0)
        glow.setColor(QColor(76, 175, 80, 100))
        sample.setGraphicsEffect(glow)
        
        # Center the sample
        sample_container = QWidget()
        sample_layout = QHBoxLayout(sample_container)
        sample_layout.addWidget(sample)
        sample_layout.setAlignment(Qt.AlignCenter)
        layout.addWidget(sample_container)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 11px;
            color: rgba(255,255,255,0.7);
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)
        
        return container
    
    def create_shadow_rules(self):
        """Create shadow rules widget"""
        rules_widget = QWidget()
        rules_layout = QVBoxLayout(rules_widget)
        
        rules_title = QLabel("📝 Shadow Rules:")
        rules_title.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: #4CAF50;
            margin: 10px 0;
        """)
        rules_layout.addWidget(rules_title)
        
        rules_text = QLabel("""
💫 Shadow Best Practices:

• Blur Radius: 15-30px برای modern look
• Offset: Y offset بیشتر از X (مثل نور از بالا)
• Color: معمولاً black با alpha 20-60
• Subtle is Better: سایه‌های ملایم بهتر از تیز
• Consistency: همه shadows هم‌خانواده باشند
• Performance: زیاد استفاده نکنید (سنگین است)

🎨 Common Values:
• Cards: blur=15, offset=(0,4), alpha=30
• Panels: blur=20, offset=(0,6), alpha=40  
• Modals: blur=30, offset=(0,10), alpha=60
• Buttons: blur=10, offset=(0,2), alpha=25
        """)
        rules_text.setStyleSheet("""
            font-size: 13px;
            color: rgba(255,255,255,0.8);
            line-height: 1.6;
            padding: 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 16px;
            border: 1px solid rgba(255,255,255,0.1);
        """)
        rules_text.setWordWrap(True)
        rules_layout.addWidget(rules_text)
        
        return rules_widget

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ShadowDemo()
    window.show()
    sys.exit(app.exec())
