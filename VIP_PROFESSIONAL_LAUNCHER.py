#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG Professional Launcher
🎯 Complete System with Real Quotex Data
"""

import sys
import os
import json
import time
import subprocess
import psutil
from datetime import datetime
from pathlib import Path

def print_banner():
    """Print professional banner"""
    print("=" * 80)
    print("🚀 VIP BIG BANG PROFESSIONAL TRADING SYSTEM")
    print("💎 Enterprise-Level Trading Platform")
    print("🎯 Real Quotex Data Integration")
    print("=" * 80)

def check_system_requirements():
    """Check system requirements"""
    print("🔧 Checking system requirements...")
    
    # Check Python version
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"🐍 Python version: {python_version}")
    if sys.version_info < (3.7, 0):
        print("⚠️ Python 3.7+ recommended, but continuing...")
    else:
        print("✅ Python version OK")
    
    # Check PySide6
    try:
        import PySide6
        print("✅ PySide6 available")
    except ImportError:
        print("🔄 Installing PySide6...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
        print("✅ PySide6 installed")
    
    # Check main file
    if not os.path.exists("VIP_BIG_BANG_UNIFIED.py"):
        print("❌ Main system file missing")
        return False
    print("✅ Main system file found")
    
    return True

def ensure_fresh_data():
    """Ensure fresh Quotex data is available"""
    print("📊 Ensuring fresh Quotex data...")
    
    # Create fresh data
    try:
        subprocess.run([sys.executable, "create_fresh_quotex_data.py"], 
                      check=True, capture_output=True)
        print("✅ Fresh Quotex data created")
    except:
        print("⚠️ Using existing data")
    
    # Verify data files
    data_files = ["shared_quotex_data.json", "quotex_live_data.json"]
    for file in data_files:
        if os.path.exists(file):
            print(f"✅ {file} available")
        else:
            print(f"⚠️ {file} missing")

def kill_existing_processes():
    """Kill any existing VIP BIG BANG processes"""
    print("🔄 Checking for existing processes...")
    
    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] == 'python.exe':
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'VIP_BIG_BANG_UNIFIED.py' in cmdline:
                    proc.kill()
                    killed_count += 1
                    print(f"🔄 Killed process PID {proc.info['pid']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if killed_count > 0:
        print(f"✅ Killed {killed_count} existing processes")
        time.sleep(2)  # Wait for cleanup
    else:
        print("✅ No existing processes found")

def launch_vip_system():
    """Launch VIP BIG BANG system"""
    print("🚀 Launching VIP BIG BANG Professional System...")
    
    try:
        # Launch the main system
        process = subprocess.Popen(
            [sys.executable, "VIP_BIG_BANG_UNIFIED.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == "win32" else 0
        )
        
        print(f"✅ System launched with PID {process.pid}")
        
        # Wait a moment for startup
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ System is running successfully!")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ System failed to start")
            if stderr:
                print(f"Error: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to launch system: {e}")
        return False

def verify_system_status():
    """Verify system is running properly"""
    print("🔍 Verifying system status...")
    
    time.sleep(2)  # Wait for full startup
    
    # Check for running processes
    vip_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
        try:
            if proc.info['name'] == 'python.exe':
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'VIP_BIG_BANG_UNIFIED.py' in cmdline:
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    vip_processes.append({
                        'pid': proc.info['pid'],
                        'memory_mb': memory_mb
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if vip_processes:
        print(f"✅ Found {len(vip_processes)} VIP BIG BANG processes:")
        for proc in vip_processes:
            print(f"   PID {proc['pid']}: {proc['memory_mb']:.1f} MB")
        
        # Check data files
        if os.path.exists("shared_quotex_data.json"):
            print("✅ Quotex data file available")
        
        print("\n🎉 VIP BIG BANG PROFESSIONAL SYSTEM IS READY!")
        print("🎨 UI should be visible on your screen")
        print("💡 If you don't see it, try Alt+Tab")
        print("🎯 System features:")
        print("   • Real Quotex data integration")
        print("   • Professional UI with proper sizing")
        print("   • Advanced trading controls")
        print("   • Live analysis engine")
        print("   • Auto trading capabilities")
        
        return True
    else:
        print("❌ No VIP BIG BANG processes found")
        return False

def main():
    """Main launcher function"""
    print_banner()
    
    # Step 1: Check requirements
    if not check_system_requirements():
        print("❌ System requirements not met")
        input("Press Enter to exit...")
        return
    
    # Step 2: Ensure fresh data
    ensure_fresh_data()
    
    # Step 3: Clean existing processes
    kill_existing_processes()
    
    # Step 4: Launch system
    if not launch_vip_system():
        print("❌ Failed to launch system")
        input("Press Enter to exit...")
        return
    
    # Step 5: Verify status
    if verify_system_status():
        print("\n" + "=" * 80)
        print("🎉 SUCCESS! VIP BIG BANG PROFESSIONAL SYSTEM IS RUNNING!")
        print("=" * 80)
    else:
        print("\n❌ System verification failed")
    
    input("\nPress Enter to exit launcher...")

if __name__ == "__main__":
    main()
