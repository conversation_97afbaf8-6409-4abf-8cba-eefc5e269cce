import sys
from PySide6.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                               QFrame, QProgressBar, QGraphicsDropShadowEffect)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QPainter, QColor, QPen, QBrush, QLinearGradient

class ModernButton(QPushButton):
    """Modern button with ChatGPT-style design"""
    
    def __init__(self, text="", icon="", button_type="default"):
        super().__init__()
        self.button_text = text
        self.button_icon = icon
        self.button_type = button_type
        self.setup_button()
        
    def setup_button(self):
        """Setup modern button styling"""
        self.setFixedSize(90, 90)
        
        # Create layout for icon and text
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)
        
        # Icon label
        if self.button_icon:
            icon_label = QLabel(self.button_icon)
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    color: white;
                    background: transparent;
                    border: none;
                    margin: 0;
                    padding: 0;
                }
            """)
            layout.addWidget(icon_label)
        
        # Text label
        if self.button_text:
            text_label = QLabel(self.button_text)
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setWordWrap(True)
            text_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    font-weight: 600;
                    color: white;
                    background: transparent;
                    border: none;
                    margin: 0;
                    padding: 0;
                    line-height: 1.2;
                }
            """)
            layout.addWidget(text_label)
        
        # Apply button styling based on type
        self.apply_button_style()
        
        # Add shadow effect
        self.add_shadow_effect()
    
    def apply_button_style(self):
        """Apply modern button styling"""
        base_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.15),
                    stop:1 rgba(255,255,255,0.05));
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 18px;
                color: white;
                font-weight: 600;
                text-align: center;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.25),
                    stop:1 rgba(255,255,255,0.15));
                border: 1px solid rgba(255,255,255,0.5);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.1),
                    stop:1 rgba(255,255,255,0.3));
            }
        """
        
        if self.button_type == "buy":
            base_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #45a049);
                    border: 1px solid #4CAF50;
                    border-radius: 25px;
                    color: white;
                    font-weight: 700;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #45a049, stop:1 #4CAF50);
                }
            """
        elif self.button_type == "sell":
            base_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #F44336, stop:1 #da190b);
                    border: 1px solid #F44336;
                    border-radius: 25px;
                    color: white;
                    font-weight: 700;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #da190b, stop:1 #F44336);
                }
            """
        elif self.button_type == "active":
            base_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(76, 175, 80, 0.4),
                        stop:1 rgba(76, 175, 80, 0.2));
                    border: 2px solid #4CAF50;
                    border-radius: 18px;
                    color: #4CAF50;
                    font-weight: 700;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(76, 175, 80, 0.6),
                        stop:1 rgba(76, 175, 80, 0.3));
                }
            """
        
        self.setStyleSheet(base_style)
    
    def add_shadow_effect(self):
        """Add modern shadow effect"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.setGraphicsEffect(shadow)

class ModernPanel(QFrame):
    """Modern panel with ChatGPT-style design"""
    
    def __init__(self, title="", icon=""):
        super().__init__()
        self.panel_title = title
        self.panel_icon = icon
        self.setup_panel()
    
    def setup_panel(self):
        """Setup modern panel styling"""
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.12),
                    stop:1 rgba(255,255,255,0.08));
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 20px;
                padding: 20px;
            }
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(6)
        shadow.setColor(QColor(0, 0, 0, 40))
        self.setGraphicsEffect(shadow)

class VIPBigBangChatGPTUI(QMainWindow):
    """ChatGPT-style VIP BIG BANG UI"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 VIP BIG BANG - ChatGPT Style")
        self.setGeometry(100, 100, 1300, 850)
        
        # Modern font setup
        self.setup_fonts()
        
        # Enhanced gradient background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a2e, stop:0.3 #16213e, stop:0.7 #0f3460, stop:1 #533483);
                color: white;
            }
            QLabel {
                color: white;
                font-family: 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }
        """)
        
        self.setup_ui()
    
    def setup_fonts(self):
        """Setup modern typography"""
        # Load modern fonts
        font_db = QApplication.instance().font()
        
        # Set application font
        modern_font = QFont("Segoe UI", 10)
        modern_font.setWeight(QFont.Weight.Medium)
        QApplication.instance().setFont(modern_font)
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Enhanced header
        self.create_modern_header(main_layout)
        
        # Main content with better spacing
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        self.create_modern_left_panel(content_layout)
        self.create_modern_center_panel(content_layout)
        self.create_modern_right_panel(content_layout)
        
        main_layout.addLayout(content_layout)
    
    def create_modern_header(self, main_layout):
        """Modern header with ChatGPT-style design"""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(15)
        
        # Left - Currency pairs with modern design
        currency_layout = QHBoxLayout()
        
        # BUG/USD active button
        bug_btn = ModernButton("BUG/USD", "✓", "active")
        bug_btn.setFixedSize(120, 45)
        currency_layout.addWidget(bug_btn)
        
        # Other currency pairs
        for pair in ["GBP/USD", "EUR/JPY", "LIVE"]:
            btn = ModernButton(pair)
            btn.setFixedSize(100, 45)
            currency_layout.addWidget(btn)
        
        header_layout.addLayout(currency_layout)
        header_layout.addStretch()
        
        # Center - Enhanced title
        title = QLabel("VIP BIG BANG")
        title.setStyleSheet("""
            font-size: 36px; 
            font-weight: 700; 
            color: white;
            letter-spacing: 3px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ffffff, stop:0.5 #e0e0e0, stop:1 #ffffff);
            -webkit-background-clip: text;
            margin: 10px 0;
        """)
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Right - Modern controls
        right_layout = QHBoxLayout()
        
        # Mode buttons
        modes = [("OTC", False), ("LIVE", True), ("DEMO", False)]
        for mode, active in modes:
            btn = ModernButton(mode, "", "active" if active else "default")
            btn.setFixedSize(80, 45)
            right_layout.addWidget(btn)
        
        # BUY/SELL buttons
        buy_label = QLabel("BUY")
        buy_label.setStyleSheet("""
            color: white; 
            font-size: 16px; 
            font-weight: 600;
            margin: 0 15px;
        """)
        right_layout.addWidget(buy_label)
        
        buy_btn = ModernButton("BUY", "", "buy")
        buy_btn.setFixedSize(100, 45)
        right_layout.addWidget(buy_btn)
        
        sell_btn = ModernButton("SELL", "", "sell")
        sell_btn.setFixedSize(100, 45)
        right_layout.addWidget(sell_btn)
        
        # Menu button
        menu_btn = ModernButton("≡")
        menu_btn.setFixedSize(50, 45)
        right_layout.addWidget(menu_btn)
        
        header_layout.addLayout(right_layout)
        main_layout.addLayout(header_layout)

    def create_modern_left_panel(self, content_layout):
        """Modern left panel with enhanced typography"""
        left_widget = QWidget()
        left_widget.setFixedWidth(240)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(20)

        # Manual Trading panel
        manual_panel = ModernPanel("Manual Trading", "🖱️")
        manual_layout = QVBoxLayout(manual_panel)

        # Header with icon and title
        header_layout = QHBoxLayout()
        icon_label = QLabel("🖱️")
        icon_label.setStyleSheet("font-size: 24px; margin-right: 10px;")
        header_layout.addWidget(icon_label)

        title_label = QLabel("Manual Trading")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin: 0;
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        manual_layout.addLayout(header_layout)

        # Modern toggle switch
        toggle_container = QWidget()
        toggle_container.setFixedHeight(50)
        toggle_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 25px;
                margin: 10px 30px;
            }
        """)
        manual_layout.addWidget(toggle_container)

        left_layout.addWidget(manual_panel)

        # Account Summary panel
        account_panel = ModernPanel("Account Summary", "💰")
        account_layout = QVBoxLayout(account_panel)

        account_title = QLabel("Account Summary")
        account_title.setStyleSheet("""
            font-size: 14px;
            font-weight: 500;
            color: rgba(255,255,255,0.8);
            margin-bottom: 8px;
        """)
        account_layout.addWidget(account_title)

        balance_label = QLabel("$1,251.76")
        balance_label.setStyleSheet("""
            font-size: 32px;
            font-weight: 700;
            color: #4CAF50;
            margin: 10px 0;
            letter-spacing: 1px;
        """)
        account_layout.addWidget(balance_label)

        # Profit indicator with modern styling
        profit_container = QWidget()
        profit_layout = QHBoxLayout(profit_container)
        profit_layout.setContentsMargins(0, 0, 0, 0)

        profit_icon = QLabel("↗")
        profit_icon.setStyleSheet("font-size: 16px; color: #4CAF50; font-weight: bold;")
        profit_layout.addWidget(profit_icon)

        profit_text = QLabel("+$251.76 (25.17%)")
        profit_text.setStyleSheet("""
            font-size: 13px;
            font-weight: 600;
            color: #4CAF50;
            margin-left: 5px;
        """)
        profit_layout.addWidget(profit_text)
        profit_layout.addStretch()

        account_layout.addWidget(profit_container)
        left_layout.addWidget(account_panel)

        # AutoTrade panel
        autotrade_panel = ModernPanel("AutoTrade", "🤖")
        autotrade_layout = QVBoxLayout(autotrade_panel)

        # Header
        autotrade_header = QHBoxLayout()
        autotrade_icon = QLabel("🤖")
        autotrade_icon.setStyleSheet("font-size: 20px; margin-right: 8px;")
        autotrade_header.addWidget(autotrade_icon)

        autotrade_title = QLabel("AutoTrade")
        autotrade_title.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
        """)
        autotrade_header.addWidget(autotrade_title)

        status_badge = QLabel("ON")
        status_badge.setStyleSheet("""
            background: #4CAF50;
            color: white;
            font-size: 10px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 10px;
            margin-left: 10px;
        """)
        autotrade_header.addWidget(status_badge)
        autotrade_header.addStretch()
        autotrade_layout.addLayout(autotrade_header)

        # Stats with modern typography
        stats_data = [
            ("Trade Amount", "$5.00", "rgba(255,255,255,0.8)"),
            ("Session P/L", "+$10.00", "#4CAF50"),
            ("Win Rate", "87%", "#4CAF50"),
            ("Total Trades", "15", "rgba(255,255,255,0.8)")
        ]

        for label, value, color in stats_data:
            stat_container = QWidget()
            stat_layout = QHBoxLayout(stat_container)
            stat_layout.setContentsMargins(0, 2, 0, 2)

            stat_label = QLabel(label + ":")
            stat_label.setStyleSheet(f"""
                font-size: 12px;
                color: rgba(255,255,255,0.7);
                font-weight: 500;
            """)
            stat_layout.addWidget(stat_label)

            stat_value = QLabel(value)
            stat_value.setStyleSheet(f"""
                font-size: 12px;
                color: {color};
                font-weight: 600;
            """)
            stat_layout.addStretch()
            stat_layout.addWidget(stat_value)

            autotrade_layout.addWidget(stat_container)

        left_layout.addWidget(autotrade_panel)

        # PulseBar panel
        pulsebar_panel = ModernPanel("Market Pulse", "📊")
        pulsebar_layout = QVBoxLayout(pulsebar_panel)

        pulsebar_header = QHBoxLayout()
        pulsebar_icon = QLabel("📊")
        pulsebar_icon.setStyleSheet("font-size: 18px; margin-right: 8px;")
        pulsebar_header.addWidget(pulsebar_icon)

        pulsebar_title = QLabel("Market Pulse")
        pulsebar_title.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: white;
        """)
        pulsebar_header.addWidget(pulsebar_title)
        pulsebar_header.addStretch()
        pulsebar_layout.addLayout(pulsebar_header)

        # Modern color bars with labels
        pulse_colors = [
            ("#FF4444", "Extreme Sell"),
            ("#FF6644", "Strong Sell"),
            ("#FF8844", "Sell"),
            ("#FFAA44", "Weak Sell"),
            ("#FFDD44", "Neutral"),
            ("#DDFF44", "Weak Buy"),
            ("#88FF44", "Buy"),
            ("#44FF88", "Strong Buy")
        ]

        for i, (color, label) in enumerate(pulse_colors):
            bar_container = QWidget()
            bar_layout = QHBoxLayout(bar_container)
            bar_layout.setContentsMargins(0, 1, 0, 1)

            bar = QWidget()
            bar.setFixedHeight(8)
            bar.setStyleSheet(f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color}, stop:1 rgba(255,255,255,0.1));
                border-radius: 4px;
            """)
            bar_layout.addWidget(bar, 3)

            if i == 6:  # Highlight current state
                bar.setStyleSheet(f"""
                    background: {color};
                    border: 2px solid white;
                    border-radius: 4px;
                """)

            pulsebar_layout.addWidget(bar_container)

        # Market strength indicator
        strength_container = QWidget()
        strength_layout = QHBoxLayout(strength_container)
        strength_layout.setContentsMargins(0, 8, 0, 0)

        strength_label = QLabel("Market Strength:")
        strength_label.setStyleSheet("""
            font-size: 11px;
            color: rgba(255,255,255,0.7);
            font-weight: 500;
        """)
        strength_layout.addWidget(strength_label)

        strength_value = QLabel("STRONG 💪")
        strength_value.setStyleSheet("""
            font-size: 11px;
            color: #4CAF50;
            font-weight: 700;
        """)
        strength_layout.addStretch()
        strength_layout.addWidget(strength_value)

        pulsebar_layout.addWidget(strength_container)
        left_layout.addWidget(pulsebar_panel)

        left_layout.addStretch()
        content_layout.addWidget(left_widget)

    def create_modern_center_panel(self, content_layout):
        """Modern center panel with enhanced chart"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(15)

        # Main chart panel
        chart_panel = ModernPanel("Live Chart", "📈")
        chart_panel.setMinimumHeight(400)
        chart_layout = QVBoxLayout(chart_panel)

        # Price header with modern design
        price_header = QHBoxLayout()

        # Alert with glow effect
        alert_container = QWidget()
        alert_layout = QHBoxLayout(alert_container)
        alert_layout.setContentsMargins(0, 0, 0, 0)

        alert_bell = QLabel("🔔")
        alert_bell.setStyleSheet("""
            font-size: 32px;
            color: #FFD700;
            margin-right: 15px;
        """)
        alert_layout.addWidget(alert_bell)

        alert_text = QLabel("Price Alert")
        alert_text.setStyleSheet("""
            font-size: 12px;
            color: #FFD700;
            font-weight: 600;
        """)
        alert_layout.addWidget(alert_text)
        alert_layout.addStretch()

        price_header.addWidget(alert_container)
        price_header.addStretch()

        # Modern price display
        price_container = QWidget()
        price_layout = QVBoxLayout(price_container)
        price_layout.setContentsMargins(0, 0, 0, 0)

        current_price = QLabel("1.07329")
        current_price.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4CAF50, stop:1 #45a049);
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 24px;
            font-weight: 700;
            color: white;
            letter-spacing: 1px;
        """)
        price_layout.addWidget(current_price)

        price_header.addWidget(price_container)

        # Price details with modern typography
        price_details = QVBoxLayout()
        price_data = [
            ("H", "1.07325", "#FF9800"),
            ("L", "1.07320", "#2196F3"),
            ("O", "1.07320", "#9E9E9E"),
            ("T", "1.07330", "#4CAF50")
        ]

        for label, value, color in price_data:
            detail_container = QWidget()
            detail_layout = QHBoxLayout(detail_container)
            detail_layout.setContentsMargins(0, 1, 0, 1)

            label_widget = QLabel(f"{label}:")
            label_widget.setStyleSheet(f"""
                font-size: 11px;
                color: {color};
                font-weight: 600;
                min-width: 15px;
            """)
            detail_layout.addWidget(label_widget)

            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"""
                font-size: 11px;
                color: {color};
                font-weight: 700;
            """)
            detail_layout.addWidget(value_widget)

            price_details.addWidget(detail_container)

        price_header.addLayout(price_details)
        chart_layout.addLayout(price_header)

        # Chart area with modern styling
        chart_area = QWidget()
        chart_area.setMinimumHeight(280)
        chart_area.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0,0,0,0.4),
                    stop:1 rgba(0,0,0,0.2));
                border: 1px solid rgba(255,255,255,0.1);
                border-radius: 15px;
            }
        """)

        chart_content = QVBoxLayout(chart_area)
        chart_placeholder = QLabel("📊 LIVE CANDLESTICK CHART")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("""
            color: rgba(255,255,255,0.6);
            font-size: 20px;
            font-weight: 600;
            letter-spacing: 2px;
        """)
        chart_content.addWidget(chart_placeholder)

        chart_layout.addWidget(chart_area)
        center_layout.addWidget(chart_panel)

        # Bottom indicators with modern design
        indicators_layout = QHBoxLayout()
        indicators_layout.setSpacing(15)

        # Modern VORTEX indicator
        vortex_panel = ModernPanel("VORTEX", "🌀")
        vortex_panel.setFixedWidth(140)
        vortex_layout = QVBoxLayout(vortex_panel)

        vortex_header = QHBoxLayout()
        vortex_icon = QLabel("🌀")
        vortex_icon.setStyleSheet("font-size: 16px; margin-right: 5px;")
        vortex_header.addWidget(vortex_icon)

        vortex_title = QLabel("VORTEX")
        vortex_title.setStyleSheet("""
            font-size: 12px;
            font-weight: 600;
            color: white;
        """)
        vortex_header.addWidget(vortex_title)
        vortex_layout.addLayout(vortex_header)

        vortex_wave = QLabel("〰️〰️〰️〰️")
        vortex_wave.setStyleSheet("""
            color: #4CAF50;
            font-size: 16px;
            margin: 5px 0;
        """)
        vortex_layout.addWidget(vortex_wave)

        vortex_value = QLabel("0.0436 ↗")
        vortex_value.setStyleSheet("""
            font-size: 13px;
            color: #4CAF50;
            font-weight: 700;
        """)
        vortex_layout.addWidget(vortex_value)

        indicators_layout.addWidget(vortex_panel)

        # Other indicators...
        center_layout.addLayout(indicators_layout)
        content_layout.addWidget(center_widget, 2)

    def create_modern_right_panel(self, content_layout):
        """Modern right panel with enhanced buttons"""
        right_widget = QWidget()
        right_widget.setFixedWidth(240)
        right_layout = QGridLayout(right_widget)
        right_layout.setSpacing(15)

        # Modern control buttons with better typography
        buttons_data = [
            (0, 0, "🚀", "AutoTrade"),
            (0, 1, "✓", "Confirm Mode"),
            (1, 0, "🎯", "Signal Mode"),
            (1, 1, "🔥", "Heatmap"),
            (2, 0, "📊", "Economic News"),
            (2, 1, "😊", "Brothers Can"),
            (3, 0, "⚙️", "Settings"),
            (3, 1, "🔒", "Security")
        ]

        for row, col, icon, text in buttons_data:
            btn = ModernButton(text, icon)
            right_layout.addWidget(btn, row, col)

        content_layout.addWidget(right_widget)

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set modern application style
    app.setStyle('Fusion')

    # Enable high DPI scaling
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
    app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)

    window = VIPBigBangChatGPTUI()
    window.show()
    sys.exit(app.exec())
