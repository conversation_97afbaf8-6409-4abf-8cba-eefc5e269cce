"""
📈 VIP BIG BANG - Chart Widget Component
Real-time candlestick chart with indicators and professional styling
"""

import random
import math
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class CandleData:
    """Candlestick data structure"""
    def __init__(self, timestamp: datetime, open_price: float, high: float, low: float, close: float, volume: float = 0):
        self.timestamp = timestamp
        self.open = open_price
        self.high = high
        self.low = low
        self.close = close
        self.volume = volume
    
    @property
    def is_bullish(self) -> bool:
        return self.close > self.open

class VIPChartWidget(QWidget):
    """
    Professional real-time chart widget with candlesticks and indicators
    
    Features:
    - Real-time candlestick chart
    - MA6 overlay
    - Vortex indicator
    - Volume bars
    - Interactive crosshair
    - Zoom and pan functionality
    """
    
    # Signals
    price_clicked = Signal(float, datetime)
    timeframe_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        
        # Chart data
        self.candles: List[CandleData] = []
        self.ma6_data: List[float] = []
        self.vortex_data: List[Tuple[float, float]] = []  # (VI+, VI-)
        
        # Chart settings
        self.timeframe = "15s"
        self.max_candles = 100
        self.base_price = 1.07320
        
        # Visual settings
        self.candle_width = 8
        self.candle_spacing = 2
        self.margin = 40
        
        # Colors
        self.bullish_color = QColor("#10B981")  # Green
        self.bearish_color = QColor("#EF4444")  # Red
        self.ma6_color = QColor("#8B5CF6")      # Purple
        self.vortex_plus_color = QColor("#60A5FA")  # Blue
        self.vortex_minus_color = QColor("#EC4899") # Pink
        self.grid_color = QColor("#374151")     # Gray
        self.text_color = QColor("#F3F4F6")     # Light gray
        
        # Mouse interaction
        self.mouse_pos = QPoint()
        self.crosshair_enabled = True
        
        # Setup widget
        self.setMinimumSize(400, 300)
        self.setMouseTracking(True)
        
        # Generate initial data
        self._generate_initial_data()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._add_new_candle)
        self.update_timer.start(1000)  # Update every second
    
    def _generate_initial_data(self):
        """Generate initial chart data"""
        current_time = datetime.now()
        
        # Generate 50 initial candles
        for i in range(50):
            timestamp = current_time - timedelta(seconds=(50-i) * 15)  # 15 second intervals
            
            # Generate realistic price movement
            if i == 0:
                open_price = self.base_price
            else:
                open_price = self.candles[-1].close
            
            # Random price movement
            change = random.uniform(-0.0005, 0.0005)
            close_price = open_price + change
            
            # High and low
            high = max(open_price, close_price) + random.uniform(0, 0.0002)
            low = min(open_price, close_price) - random.uniform(0, 0.0002)
            
            # Volume
            volume = random.uniform(100, 1000)
            
            candle = CandleData(timestamp, open_price, high, low, close_price, volume)
            self.candles.append(candle)
        
        # Calculate indicators
        self._calculate_ma6()
        self._calculate_vortex()
    
    def _calculate_ma6(self):
        """Calculate 6-period moving average"""
        self.ma6_data.clear()
        
        for i in range(len(self.candles)):
            if i < 5:  # Not enough data for MA6
                self.ma6_data.append(self.candles[i].close)
            else:
                # Calculate average of last 6 closes
                sum_close = sum(candle.close for candle in self.candles[i-5:i+1])
                ma6 = sum_close / 6
                self.ma6_data.append(ma6)
    
    def _calculate_vortex(self):
        """Calculate Vortex Indicator"""
        self.vortex_data.clear()
        
        if len(self.candles) < 14:
            return
        
        for i in range(14, len(self.candles)):
            # Calculate True Range and Vortex Movement
            tr_sum = 0
            vm_plus = 0
            vm_minus = 0
            
            for j in range(i-13, i+1):
                if j > 0:
                    # True Range
                    tr = max(
                        self.candles[j].high - self.candles[j].low,
                        abs(self.candles[j].high - self.candles[j-1].close),
                        abs(self.candles[j].low - self.candles[j-1].close)
                    )
                    tr_sum += tr
                    
                    # Vortex Movement
                    vm_plus += abs(self.candles[j].high - self.candles[j-1].low)
                    vm_minus += abs(self.candles[j].low - self.candles[j-1].high)
            
            # Calculate VI+ and VI-
            vi_plus = vm_plus / tr_sum if tr_sum > 0 else 1.0
            vi_minus = vm_minus / tr_sum if tr_sum > 0 else 1.0
            
            self.vortex_data.append((vi_plus, vi_minus))
    
    def _add_new_candle(self):
        """Add new candle (simulated real-time data)"""
        if not self.candles:
            return
        
        # Get last candle
        last_candle = self.candles[-1]
        current_time = datetime.now()
        
        # Check if we need a new candle (based on timeframe)
        time_diff = (current_time - last_candle.timestamp).total_seconds()
        timeframe_seconds = self._get_timeframe_seconds()
        
        if time_diff >= timeframe_seconds:
            # Create new candle
            open_price = last_candle.close
            change = random.uniform(-0.0003, 0.0003)
            close_price = open_price + change
            
            high = max(open_price, close_price) + random.uniform(0, 0.0001)
            low = min(open_price, close_price) - random.uniform(0, 0.0001)
            volume = random.uniform(100, 1000)
            
            new_candle = CandleData(current_time, open_price, high, low, close_price, volume)
            self.candles.append(new_candle)
            
            # Remove old candles if too many
            if len(self.candles) > self.max_candles:
                self.candles.pop(0)
            
            # Recalculate indicators
            self._calculate_ma6()
            self._calculate_vortex()
        else:
            # Update current candle
            change = random.uniform(-0.0001, 0.0001)
            new_close = last_candle.close + change
            
            # Update high/low if necessary
            last_candle.close = new_close
            last_candle.high = max(last_candle.high, new_close)
            last_candle.low = min(last_candle.low, new_close)
            
            # Update MA6 for current candle
            if len(self.ma6_data) > 0:
                if len(self.candles) >= 6:
                    sum_close = sum(candle.close for candle in self.candles[-6:])
                    self.ma6_data[-1] = sum_close / 6
        
        # Trigger repaint
        self.update()
    
    def _get_timeframe_seconds(self) -> int:
        """Get timeframe in seconds"""
        timeframe_map = {
            "5s": 5,
            "15s": 15,
            "30s": 30,
            "1m": 60,
            "5m": 300
        }
        return timeframe_map.get(self.timeframe, 15)
    
    def update_price(self, new_price: float):
        """Update current price (external call)"""
        if self.candles:
            last_candle = self.candles[-1]
            last_candle.close = new_price
            last_candle.high = max(last_candle.high, new_price)
            last_candle.low = min(last_candle.low, new_price)
            
            # Update MA6
            if len(self.ma6_data) > 0 and len(self.candles) >= 6:
                sum_close = sum(candle.close for candle in self.candles[-6:])
                self.ma6_data[-1] = sum_close / 6
            
            self.update()
    
    def update_chart(self):
        """Force chart update"""
        self.update()
    
    def set_timeframe(self, timeframe: str):
        """Set chart timeframe"""
        if timeframe != self.timeframe:
            self.timeframe = timeframe
            self.timeframe_changed.emit(timeframe)
            
            # Restart timer with new interval
            self.update_timer.stop()
            self.update_timer.start(1000)  # Keep 1 second for smooth updates
    
    def paintEvent(self, event):
        """Paint the chart"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Fill background
        painter.fillRect(self.rect(), QColor("#1F2937"))
        
        if not self.candles:
            return
        
        # Calculate chart area
        chart_rect = self.rect().adjusted(self.margin, self.margin, -self.margin, -self.margin)
        
        # Calculate price range
        all_prices = []
        for candle in self.candles:
            all_prices.extend([candle.high, candle.low])
        
        if not all_prices:
            return
        
        min_price = min(all_prices)
        max_price = max(all_prices)
        price_range = max_price - min_price
        
        if price_range == 0:
            price_range = 0.001  # Prevent division by zero
        
        # Draw grid
        self._draw_grid(painter, chart_rect, min_price, max_price)
        
        # Draw candlesticks
        self._draw_candlesticks(painter, chart_rect, min_price, price_range)
        
        # Draw MA6
        self._draw_ma6(painter, chart_rect, min_price, price_range)
        
        # Draw crosshair
        if self.crosshair_enabled:
            self._draw_crosshair(painter, chart_rect, min_price, price_range)
        
        # Draw price labels
        self._draw_price_labels(painter, chart_rect, min_price, max_price)
    
    def _draw_grid(self, painter: QPainter, rect: QRect, min_price: float, max_price: float):
        """Draw chart grid"""
        painter.setPen(QPen(self.grid_color, 1, Qt.PenStyle.DotLine))
        
        # Horizontal lines (price levels)
        for i in range(5):
            y = rect.top() + (rect.height() * i / 4)
            painter.drawLine(rect.left(), y, rect.right(), y)
        
        # Vertical lines (time)
        candle_count = min(len(self.candles), 20)
        for i in range(0, candle_count, 5):
            x = rect.left() + (rect.width() * i / (candle_count - 1)) if candle_count > 1 else rect.left()
            painter.drawLine(x, rect.top(), x, rect.bottom())
    
    def _draw_candlesticks(self, painter: QPainter, rect: QRect, min_price: float, price_range: float):
        """Draw candlestick chart"""
        if not self.candles:
            return
        
        candle_count = len(self.candles)
        total_width = rect.width()
        candle_total_width = self.candle_width + self.candle_spacing
        
        for i, candle in enumerate(self.candles):
            # Calculate x position
            x = rect.left() + (total_width * i / max(candle_count - 1, 1))
            
            # Calculate y positions
            open_y = rect.bottom() - ((candle.open - min_price) / price_range) * rect.height()
            close_y = rect.bottom() - ((candle.close - min_price) / price_range) * rect.height()
            high_y = rect.bottom() - ((candle.high - min_price) / price_range) * rect.height()
            low_y = rect.bottom() - ((candle.low - min_price) / price_range) * rect.height()
            
            # Choose color
            color = self.bullish_color if candle.is_bullish else self.bearish_color
            painter.setPen(QPen(color, 1))
            painter.setBrush(QBrush(color))
            
            # Draw high-low line
            painter.drawLine(x, high_y, x, low_y)
            
            # Draw candle body
            body_top = min(open_y, close_y)
            body_height = abs(close_y - open_y)
            body_rect = QRect(x - self.candle_width//2, body_top, self.candle_width, max(body_height, 1))
            
            if candle.is_bullish:
                painter.fillRect(body_rect, color)
            else:
                painter.drawRect(body_rect)
    
    def _draw_ma6(self, painter: QPainter, rect: QRect, min_price: float, price_range: float):
        """Draw MA6 line"""
        if len(self.ma6_data) < 2:
            return
        
        painter.setPen(QPen(self.ma6_color, 2))
        
        points = []
        for i, ma_value in enumerate(self.ma6_data):
            x = rect.left() + (rect.width() * i / max(len(self.ma6_data) - 1, 1))
            y = rect.bottom() - ((ma_value - min_price) / price_range) * rect.height()
            points.append(QPoint(x, y))
        
        # Draw line
        for i in range(len(points) - 1):
            painter.drawLine(points[i], points[i + 1])
    
    def _draw_crosshair(self, painter: QPainter, rect: QRect, min_price: float, price_range: float):
        """Draw crosshair at mouse position"""
        if not rect.contains(self.mouse_pos):
            return
        
        painter.setPen(QPen(self.text_color, 1, Qt.PenStyle.DashLine))
        
        # Vertical line
        painter.drawLine(self.mouse_pos.x(), rect.top(), self.mouse_pos.x(), rect.bottom())
        
        # Horizontal line
        painter.drawLine(rect.left(), self.mouse_pos.y(), rect.right(), self.mouse_pos.y())
        
        # Price label
        price = min_price + ((rect.bottom() - self.mouse_pos.y()) / rect.height()) * price_range
        price_text = f"{price:.5f}"
        
        painter.setPen(QPen(self.text_color))
        painter.drawText(rect.right() + 5, self.mouse_pos.y() + 5, price_text)
    
    def _draw_price_labels(self, painter: QPainter, rect: QRect, min_price: float, max_price: float):
        """Draw price labels on the right side"""
        painter.setPen(QPen(self.text_color))
        font = painter.font()
        font.setPointSize(8)
        painter.setFont(font)
        
        # Draw 5 price levels
        for i in range(5):
            price = min_price + (max_price - min_price) * (4 - i) / 4
            y = rect.top() + (rect.height() * i / 4)
            price_text = f"{price:.5f}"
            painter.drawText(rect.right() + 5, y + 5, price_text)
    
    def mouseMoveEvent(self, event):
        """Handle mouse move for crosshair"""
        self.mouse_pos = event.pos()
        self.update()
        super().mouseMoveEvent(event)
    
    def mousePressEvent(self, event):
        """Handle mouse click"""
        if event.button() == Qt.MouseButton.LeftButton:
            # Calculate clicked price and time
            chart_rect = self.rect().adjusted(self.margin, self.margin, -self.margin, -self.margin)
            
            if chart_rect.contains(event.pos()) and self.candles:
                # Calculate price
                all_prices = []
                for candle in self.candles:
                    all_prices.extend([candle.high, candle.low])
                
                min_price = min(all_prices)
                max_price = max(all_prices)
                price_range = max_price - min_price
                
                clicked_price = min_price + ((chart_rect.bottom() - event.pos().y()) / chart_rect.height()) * price_range
                
                # Calculate time (approximate)
                candle_index = int((event.pos().x() - chart_rect.left()) / chart_rect.width() * len(self.candles))
                candle_index = max(0, min(candle_index, len(self.candles) - 1))
                clicked_time = self.candles[candle_index].timestamp
                
                self.price_clicked.emit(clicked_price, clicked_time)
        
        super().mousePressEvent(event)
