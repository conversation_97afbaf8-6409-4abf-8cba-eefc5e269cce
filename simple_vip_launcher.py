#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Simple System Launcher
💎 Easy Launch Options
"""

import sys
import os
import subprocess
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")
    except:
        pass

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("Installing PySide6...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *

class SimpleVIPLauncher(QMainWindow):
    """Simple VIP BIG BANG System Launcher"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VIP BIG BANG - System Launcher")
        self.setFixedSize(500, 450)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup simple launcher UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Simple dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: #1a1a2e;
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background: #3b82f6;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: #2563eb;
            }
            QPushButton:pressed {
                background: #1d4ed8;
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
            #title {
                font-size: 20px;
                color: #60a5fa;
                margin: 15px;
            }
            #subtitle {
                font-size: 14px;
                color: #9ca3af;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(15)
        
        # Title
        title = QLabel("VIP BIG BANG System Launcher")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("Choose your launch option:")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        layout.addSpacing(20)
        
        # Launch buttons
        btn1 = QPushButton("1. Launch Beautiful UI Only")
        btn1.clicked.connect(self.launch_beautiful_ui)
        layout.addWidget(btn1)
        
        btn2 = QPushButton("2. Launch Main System Only")
        btn2.clicked.connect(self.launch_main_system)
        layout.addWidget(btn2)
        
        btn3 = QPushButton("3. Launch Dashboard Only")
        btn3.clicked.connect(self.launch_dashboard)
        layout.addWidget(btn3)
        
        btn4 = QPushButton("4. Launch Main + Dashboard")
        btn4.clicked.connect(self.launch_main_and_dashboard)
        layout.addWidget(btn4)
        
        btn5 = QPushButton("5. Launch All Systems")
        btn5.clicked.connect(self.launch_all_systems)
        layout.addWidget(btn5)
        
        layout.addStretch()
        
        # Exit button
        exit_btn = QPushButton("Exit")
        exit_btn.setStyleSheet("""
            QPushButton {
                background: #ef4444;
                min-height: 35px;
            }
            QPushButton:hover {
                background: #dc2626;
            }
        """)
        exit_btn.clicked.connect(self.close)
        layout.addWidget(exit_btn)
        
    def launch_beautiful_ui(self):
        """Launch beautiful UI only"""
        print("Launching Beautiful UI...")
        self.close()
        subprocess.Popen([sys.executable, "vip_beautiful_main_ui.py"])
        
    def launch_main_system(self):
        """Launch main system only"""
        print("Launching Main System...")
        self.close()
        subprocess.Popen([sys.executable, "vip_real_quotex_main.py"])
        
    def launch_dashboard(self):
        """Launch dashboard only"""
        print("Launching Dashboard...")
        self.close()
        subprocess.Popen([sys.executable, "vip_comprehensive_dashboard.py"])
        
    def launch_main_and_dashboard(self):
        """Launch main system and dashboard"""
        print("Launching Main System + Dashboard...")
        self.close()
        subprocess.Popen([sys.executable, "vip_real_quotex_main.py"])
        subprocess.Popen([sys.executable, "vip_comprehensive_dashboard.py"])
        
    def launch_all_systems(self):
        """Launch all systems separately"""
        print("Launching All Systems...")
        self.close()
        # Launch with delays to avoid conflicts
        subprocess.Popen([sys.executable, "vip_real_quotex_main.py"])
        import time
        time.sleep(2)
        subprocess.Popen([sys.executable, "vip_comprehensive_dashboard.py"])
        time.sleep(1)
        subprocess.Popen([sys.executable, "vip_beautiful_main_ui.py"])

def main():
    """Main entry point"""
    print("=" * 50)
    print("VIP BIG BANG - Simple System Launcher")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    launcher = SimpleVIPLauncher()
    launcher.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
