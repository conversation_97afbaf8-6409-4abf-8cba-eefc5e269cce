#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Smart Python Launcher
لانچر هوشمند Python برای VIP BIG BANG
"""

import sys
import os
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime

class VIPSmartLauncher:
    """🎯 Smart Launcher for VIP BIG BANG"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.requirements_file = self.project_root / "requirements.txt"
        
        # Color codes for terminal output
        self.colors = {
            'red': '\033[91m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'magenta': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }
        
        # Emojis for better UX
        self.emojis = {
            'rocket': '🚀',
            'check': '✅',
            'cross': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'gear': '⚙️',
            'fire': '🔥',
            'target': '🎯',
            'robot': '🤖',
            'lightning': '⚡',
            'diamond': '💎'
        }
    
    def print_colored(self, text, color='white', emoji=None, bold=False):
        """Print colored text with optional emoji"""
        color_code = self.colors.get(color, self.colors['white'])
        bold_code = self.colors['bold'] if bold else ''
        reset_code = self.colors['reset']
        
        emoji_text = f"{emoji} " if emoji else ""
        print(f"{bold_code}{color_code}{emoji_text}{text}{reset_code}")
    
    def show_banner(self):
        """Display the VIP BIG BANG banner"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
        banner = f"""
{self.colors['magenta']}{self.colors['bold']}
████████████████████████████████████████████████████████████████████
██                                                                ██
██    🚀 VIP BIG BANG - SMART PYTHON LAUNCHER 🚀                 ██
██                                                                ██
██    ⚡ Intelligent System Detection ⚡                          ██
██    🎯 Automated Setup & Launch 🎯                             ██
██    🤖 AI-Powered Diagnostics 🤖                               ██
██                                                                ██
████████████████████████████████████████████████████████████████████
{self.colors['reset']}
"""
        print(banner)
    
    def check_python_version(self):
        """Check if Python version is compatible"""
        self.print_colored("Checking Python version...", 'cyan', self.emojis['info'])
        
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            self.print_colored(f"Python {version.major}.{version.minor}.{version.micro} detected", 'green', self.emojis['check'])
            return True
        else:
            self.print_colored(f"Python {version.major}.{version.minor}.{version.micro} is too old", 'red', self.emojis['cross'])
            self.print_colored("Please install Python 3.8 or newer", 'yellow', self.emojis['warning'])
            return False
    
    def check_virtual_environment(self):
        """Check and create virtual environment if needed"""
        self.print_colored("Checking virtual environment...", 'cyan', self.emojis['gear'])
        
        if self.venv_path.exists():
            self.print_colored("Virtual environment found", 'green', self.emojis['check'])
            return True
        else:
            self.print_colored("Creating virtual environment...", 'yellow', self.emojis['gear'])
            try:
                subprocess.run([sys.executable, '-m', 'venv', str(self.venv_path)], check=True)
                self.print_colored("Virtual environment created successfully", 'green', self.emojis['check'])
                return True
            except subprocess.CalledProcessError:
                self.print_colored("Failed to create virtual environment", 'red', self.emojis['cross'])
                return False
    
    def get_python_executable(self):
        """Get the correct Python executable path"""
        if os.name == 'nt':  # Windows
            return self.venv_path / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            return self.venv_path / "bin" / "python"
    
    def get_pip_executable(self):
        """Get the correct pip executable path"""
        if os.name == 'nt':  # Windows
            return self.venv_path / "Scripts" / "pip.exe"
        else:  # Unix/Linux/macOS
            return self.venv_path / "bin" / "pip"
    
    def install_dependencies(self):
        """Install required dependencies"""
        self.print_colored("Installing dependencies...", 'cyan', self.emojis['gear'])
        
        pip_exe = self.get_pip_executable()
        
        # Core dependencies
        core_deps = ['playwright', 'pandas', 'cryptography', 'PySide6']
        
        for dep in core_deps:
            try:
                self.print_colored(f"Installing {dep}...", 'blue', self.emojis['gear'])
                subprocess.run([str(pip_exe), 'install', dep], 
                             check=True, capture_output=True, text=True)
                self.print_colored(f"{dep} installed successfully", 'green', self.emojis['check'])
            except subprocess.CalledProcessError as e:
                self.print_colored(f"Failed to install {dep}: {e}", 'red', self.emojis['cross'])
                return False
        
        # Install all requirements if file exists
        if self.requirements_file.exists():
            try:
                self.print_colored("Installing all requirements...", 'blue', self.emojis['gear'])
                subprocess.run([str(pip_exe), 'install', '-r', str(self.requirements_file)], 
                             check=True, capture_output=True, text=True)
                self.print_colored("All requirements installed", 'green', self.emojis['check'])
            except subprocess.CalledProcessError as e:
                self.print_colored(f"Warning: Some requirements failed to install", 'yellow', self.emojis['warning'])
        
        # Install Playwright browsers
        try:
            python_exe = self.get_python_executable()
            self.print_colored("Installing Playwright browsers...", 'blue', self.emojis['gear'])
            subprocess.run([str(python_exe), '-m', 'playwright', 'install'], 
                         check=True, capture_output=True, text=True)
            self.print_colored("Playwright browsers installed", 'green', self.emojis['check'])
        except subprocess.CalledProcessError:
            self.print_colored("Warning: Playwright browsers installation failed", 'yellow', self.emojis['warning'])
        
        return True
    
    def run_system_test(self):
        """Run system tests"""
        self.print_colored("Running system tests...", 'cyan', self.emojis['gear'])
        
        python_exe = self.get_python_executable()
        test_file = self.project_root / "test_real_data_system.py"
        
        if test_file.exists():
            try:
                result = subprocess.run([str(python_exe), str(test_file)], 
                                      capture_output=True, text=True, timeout=120)
                if result.returncode == 0:
                    self.print_colored("All system tests passed!", 'green', self.emojis['check'])
                    return True
                else:
                    self.print_colored("Some system tests failed", 'yellow', self.emojis['warning'])
                    print(result.stdout)
                    return False
            except subprocess.TimeoutExpired:
                self.print_colored("System tests timed out", 'yellow', self.emojis['warning'])
                return False
            except Exception as e:
                self.print_colored(f"System test error: {e}", 'red', self.emojis['cross'])
                return False
        else:
            self.print_colored("Test file not found, skipping tests", 'yellow', self.emojis['warning'])
            return True
    
    def launch_vip_big_bang(self):
        """Launch the main VIP BIG BANG system"""
        self.print_colored("Launching VIP BIG BANG...", 'cyan', self.emojis['rocket'], bold=True)
        
        python_exe = self.get_python_executable()
        
        # Try main.py first
        main_file = self.project_root / "main.py"
        if main_file.exists():
            try:
                self.print_colored("Starting main system...", 'blue', self.emojis['lightning'])
                subprocess.run([str(python_exe), str(main_file)], check=True)
                return True
            except subprocess.CalledProcessError:
                self.print_colored("Main launcher failed, trying alternative...", 'yellow', self.emojis['warning'])
        
        # Try alternative launcher
        alt_file = self.project_root / "vip_real_quotex_main.py"
        if alt_file.exists():
            try:
                self.print_colored("Starting alternative system...", 'blue', self.emojis['lightning'])
                subprocess.run([str(python_exe), str(alt_file)], check=True)
                return True
            except subprocess.CalledProcessError:
                self.print_colored("Alternative launcher also failed", 'red', self.emojis['cross'])
        
        self.print_colored("All launch methods failed", 'red', self.emojis['cross'])
        return False
    
    def show_menu(self):
        """Show interactive menu"""
        while True:
            print("\n" + "="*60)
            self.print_colored("VIP BIG BANG Smart Launcher Menu", 'magenta', self.emojis['target'], bold=True)
            print("="*60)
            
            options = [
                ("1", "🚀 Quick Launch", "Launch VIP BIG BANG immediately"),
                ("2", "🔧 Full Setup", "Complete setup with tests"),
                ("3", "🧪 Run Tests Only", "Run system tests"),
                ("4", "📦 Install Dependencies", "Install/update dependencies"),
                ("5", "📋 System Status", "Show system information"),
                ("6", "🌐 Chrome Extension Guide", "Show extension setup"),
                ("0", "❌ Exit", "Exit launcher")
            ]
            
            for code, title, desc in options:
                self.print_colored(f"[{code}] {title}", 'cyan')
                self.print_colored(f"    {desc}", 'white')
            
            choice = input(f"\n{self.emojis['target']} Select option: ").strip()
            
            if choice == "1":
                self.quick_launch()
            elif choice == "2":
                self.full_setup()
            elif choice == "3":
                self.run_system_test()
                input("Press Enter to continue...")
            elif choice == "4":
                self.install_dependencies()
                input("Press Enter to continue...")
            elif choice == "5":
                self.show_system_status()
                input("Press Enter to continue...")
            elif choice == "6":
                self.show_chrome_extension_guide()
                input("Press Enter to continue...")
            elif choice == "0":
                self.print_colored("Goodbye!", 'green', self.emojis['check'])
                break
            else:
                self.print_colored("Invalid choice", 'red', self.emojis['cross'])
    
    def quick_launch(self):
        """Quick launch without full setup"""
        self.print_colored("Quick Launch Mode", 'magenta', self.emojis['lightning'], bold=True)
        
        if not self.check_python_version():
            return
        
        if self.check_virtual_environment():
            self.launch_vip_big_bang()
    
    def full_setup(self):
        """Full setup with all checks"""
        self.print_colored("Full Setup Mode", 'magenta', self.emojis['gear'], bold=True)
        
        steps = [
            ("Python Version", self.check_python_version),
            ("Virtual Environment", self.check_virtual_environment),
            ("Dependencies", self.install_dependencies),
            ("System Tests", self.run_system_test),
            ("Launch System", self.launch_vip_big_bang)
        ]
        
        for step_name, step_func in steps:
            self.print_colored(f"Step: {step_name}", 'blue', self.emojis['gear'])
            if not step_func():
                self.print_colored(f"Setup failed at: {step_name}", 'red', self.emojis['cross'])
                return
            time.sleep(0.5)  # Brief pause between steps
        
        self.print_colored("Full setup completed successfully!", 'green', self.emojis['check'], bold=True)
    
    def show_system_status(self):
        """Show detailed system status"""
        self.print_colored("System Status Report", 'magenta', self.emojis['info'], bold=True)
        print("-" * 50)
        
        # Python info
        version = sys.version_info
        self.print_colored(f"Python: {version.major}.{version.minor}.{version.micro}", 'green', self.emojis['check'])
        
        # Virtual environment
        if self.venv_path.exists():
            self.print_colored("Virtual Environment: Present", 'green', self.emojis['check'])
        else:
            self.print_colored("Virtual Environment: Missing", 'red', self.emojis['cross'])
        
        # Project files
        important_files = ['main.py', 'vip_real_quotex_main.py', 'requirements.txt']
        for file in important_files:
            if (self.project_root / file).exists():
                self.print_colored(f"{file}: Present", 'green', self.emojis['check'])
            else:
                self.print_colored(f"{file}: Missing", 'red', self.emojis['cross'])
        
        # Chrome extension
        if (self.project_root / "chrome_extension").exists():
            self.print_colored("Chrome Extension: Present", 'green', self.emojis['check'])
        else:
            self.print_colored("Chrome Extension: Missing", 'red', self.emojis['cross'])
    
    def show_chrome_extension_guide(self):
        """Show Chrome extension installation guide"""
        self.print_colored("Chrome Extension Setup Guide", 'magenta', self.emojis['info'], bold=True)
        print("-" * 50)
        
        steps = [
            "1. Open Chrome browser",
            "2. Go to chrome://extensions/",
            "3. Enable 'Developer mode' (top right)",
            "4. Click 'Load unpacked'",
            "5. Select the 'chrome_extension' folder",
            "6. Extension will be installed"
        ]
        
        for step in steps:
            self.print_colored(step, 'cyan', self.emojis['gear'])
        
        self.print_colored("\nExtension folder location:", 'yellow', self.emojis['info'])
        self.print_colored(str(self.project_root / "chrome_extension"), 'white')

def main():
    """Main entry point"""
    launcher = VIPSmartLauncher()
    launcher.show_banner()
    
    # Check for command line arguments
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--quick', '-q']:
            launcher.quick_launch()
        elif arg in ['--full', '-f']:
            launcher.full_setup()
        elif arg in ['--test', '-t']:
            launcher.run_system_test()
        else:
            launcher.print_colored(f"Unknown argument: {arg}", 'red', launcher.emojis['cross'])
            launcher.show_menu()
    else:
        launcher.show_menu()

if __name__ == "__main__":
    main()
