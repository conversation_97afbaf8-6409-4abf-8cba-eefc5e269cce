// 🚀 VIP BIG BANG - Background Service Worker
console.log('🚀 VIP BIG BANG Background Service Worker Started');

class VIPBigBangBackground {
    constructor() {
        this.activeConnections = new Set();
        this.lastStatus = {};
        this.init();
    }

    init() {
        // Listen for messages from content scripts and popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Listen for tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        console.log('✅ VIP BIG BANG Background initialized');
    }

    handleMessage(message, sender, sendResponse) {
        console.log('📨 Background received message:', message);

        switch (message.type) {
            case 'status_update':
                this.handleStatusUpdate(message, sender);
                sendResponse({ received: true });
                break;

            case 'get_status':
                sendResponse({
                    status: this.lastStatus,
                    activeConnections: this.activeConnections.size
                });
                break;

            case 'ping':
                sendResponse({ pong: true, timestamp: Date.now() });
                break;

            default:
                console.log('Unknown message type:', message.type);
                sendResponse({ error: 'Unknown message type' });
        }
    }

    handleStatusUpdate(message, sender) {
        // Store status from content script
        if (sender.tab) {
            this.lastStatus[sender.tab.id] = {
                ...message,
                tabId: sender.tab.id,
                url: sender.tab.url,
                timestamp: Date.now()
            };

            // Notify popup if open
            this.notifyPopup(message);
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Check if tab is Quotex page
        if (changeInfo.status === 'complete' && tab.url) {
            const isQuotexPage = tab.url.includes('qxbroker.com') ||
                               tab.url.includes('quotex.io') ||
                               tab.url.includes('quotex.com');

            if (isQuotexPage) {
                console.log('✅ Quotex page loaded:', tab.url);

                // Inject content script if needed
                this.ensureContentScriptInjected(tabId);
            }
        }
    }

    async ensureContentScriptInjected(tabId) {
        try {
            // Try to ping content script
            const response = await chrome.tabs.sendMessage(tabId, { type: 'ping' });

            if (!response) {
                console.log('🔧 Injecting content script...');

                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });

                console.log('✅ Content script injected');
            }
        } catch (error) {
            console.log('Content script injection not needed or failed:', error.message);
        }
    }

    notifyPopup(message) {
        // Try to send message to popup (if open)
        try {
            chrome.runtime.sendMessage({
                type: 'background_update',
                data: message
            }).catch(() => {
                // Popup not open, ignore
            });
        } catch (error) {
            // Popup not open, ignore
        }
    }
}

// Initialize background service
const vipBigBangBackground = new VIPBigBangBackground();

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
    console.log('🚀 VIP BIG BANG Extension started');
});

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
    console.log('🚀 VIP BIG BANG Extension installed:', details.reason);

    if (details.reason === 'install') {
        // Open welcome page or instructions
        chrome.tabs.create({
            url: 'https://qxbroker.com/en/trade'
        });
    }
});
