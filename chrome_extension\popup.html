<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP BIG BANG Extension</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 350px;
            min-height: 400px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            padding: 0;
        }

        .header {
            background: linear-gradient(135deg, #0f3460, #16537e);
            padding: 15px;
            text-align: center;
            border-bottom: 2px solid #00d4ff;
        }

        .header h1 {
            font-size: 18px;
            margin-bottom: 5px;
            color: #00d4ff;
        }

        .header .subtitle {
            font-size: 12px;
            color: #a0a0a0;
        }

        .content {
            padding: 15px;
        }

        .status-section {
            margin-bottom: 20px;
        }

        .status-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #00d4ff;
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #2a2a3e;
        }

        .status-label {
            font-size: 13px;
            color: #cccccc;
        }

        .status-value {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: bold;
        }

        .status-value.online {
            background: #4ade80;
            color: #000;
        }

        .status-value.offline {
            background: #ef4444;
            color: #fff;
        }

        .status-value.warning {
            background: #f59e0b;
            color: #000;
        }

        .controls {
            margin-top: 20px;
        }

        .btn {
            width: 100%;
            padding: 10px;
            margin-bottom: 8px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0099cc, #007399);
            color: #fff;
        }

        .btn-secondary {
            background: #374151;
            color: #fff;
            border: 1px solid #4b5563;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #ef4444;
            color: #fff;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .data-section {
            background: #1f2937;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
        }

        .data-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .data-label {
            font-size: 12px;
            color: #9ca3af;
        }

        .data-value {
            font-size: 12px;
            font-weight: bold;
            color: #00d4ff;
        }

        .footer {
            text-align: center;
            padding: 10px;
            font-size: 10px;
            color: #6b7280;
            border-top: 1px solid #333;
        }

        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 VIP BIG BANG</h1>
        <div class="subtitle">Professional Trading Extension</div>
    </div>

    <div class="content">
        <!-- Connection Status -->
        <div class="status-section">
            <div class="status-title">📡 Connection Status</div>
            <div class="status-item">
                <span class="status-label">VIP BIG BANG Desktop</span>
                <span id="desktop-value" class="status-value offline">Offline</span>
            </div>
            <div class="status-item">
                <span class="status-label">Quotex Platform</span>
                <span id="quotex-value" class="status-value offline">Offline</span>
            </div>
            <div class="status-item">
                <span class="status-label">WebSocket Monitor</span>
                <span id="websocket-value" class="status-value offline">Offline</span>
            </div>
            <div class="status-item">
                <span class="status-label">Extension Status</span>
                <span id="extension-value" class="status-value online">Online</span>
            </div>
        </div>

        <!-- Trading Data -->
        <div class="data-section">
            <div class="status-title">💰 Trading Data</div>
            <div class="data-row">
                <span class="data-label">Balance:</span>
                <span id="balance-value" class="data-value">$0.00</span>
            </div>
            <div class="data-row">
                <span class="data-label">Current Asset:</span>
                <span id="asset-value" class="data-value">None</span>
            </div>
            <div class="data-row">
                <span class="data-label">Current Price:</span>
                <span id="price-value" class="data-value">-</span>
            </div>
            <div class="data-row">
                <span class="data-label">Extractions:</span>
                <span id="extractions-value" class="data-value">0</span>
            </div>
        </div>

        <!-- Controls -->
        <div class="controls">
            <button id="start-btn" class="btn btn-primary">🚀 Start Extraction</button>
            <button id="stop-btn" class="btn btn-secondary hidden">⏹️ Stop Extraction</button>
            <button id="refresh-btn" class="btn btn-secondary">🔄 Refresh Status</button>
            <button id="open-quotex-btn" class="btn btn-secondary">🌐 Open Quotex</button>
        </div>
    </div>

    <div class="footer">
        <div id="last-update">Last update: Never</div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
