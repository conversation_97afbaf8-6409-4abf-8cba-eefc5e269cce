#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 Test VIP BIG BANG Unified System
Quick test to verify system functionality
"""

import sys
import os
import time
from datetime import datetime

def test_system_status():
    """Test if the unified system is working"""
    print("=" * 50)
    print("🧪 VIP BIG BANG UNIFIED SYSTEM TEST")
    print("=" * 50)
    
    # Test 1: Check if main file exists
    main_file = "VIP_BIG_BANG_UNIFIED.py"
    if os.path.exists(main_file):
        print("✅ Main file exists")
        file_size = os.path.getsize(main_file)
        print(f"📁 File size: {file_size:,} bytes")
    else:
        print("❌ Main file missing")
        return False
    
    # Test 2: Check data files
    data_files = [
        "shared_quotex_data.json",
        "quotex_live_data.json", 
        "trading_data.json"
    ]
    
    print("\n📊 Data Sources:")
    real_data_available = False
    for file in data_files:
        if os.path.exists(file):
            age = time.time() - os.path.getmtime(file)
            print(f"✅ {file} (age: {age:.1f}s)")
            real_data_available = True
        else:
            print(f"❌ {file} (not found)")
    
    if real_data_available:
        print("🔌 Real data sources available")
    else:
        print("🎮 Will use simulation mode")
    
    # Test 3: Try to import main components
    print("\n🔧 Component Tests:")
    try:
        # Test PySide6 import
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6 available")
        
        # Test if we can create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ QApplication created")
        
        # Test import of main system
        sys.path.insert(0, os.getcwd())
        from VIP_BIG_BANG_UNIFIED import VIPUnifiedSystem, VIPDataConnector, VIPAnalysisEngine
        print("✅ Main components imported")
        
        # Test data connector
        connector = VIPDataConnector()
        print("✅ Data connector initialized")
        
        # Test analysis engine
        engine = VIPAnalysisEngine()
        signal = engine.get_trading_signal()
        print(f"✅ Analysis engine working: {signal['signal']} ({signal['confidence']:.1f}%)")
        
        print("\n🎯 System Status: FULLY OPERATIONAL")
        print("🚀 Ready to launch VIP BIG BANG Unified System!")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False

def main():
    """Run system test"""
    success = test_system_status()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALL TESTS PASSED")
        print("🎉 VIP BIG BANG Unified System is ready!")
        print("💡 Run: python VIP_BIG_BANG_UNIFIED.py")
    else:
        print("❌ SOME TESTS FAILED")
        print("🔧 Please check the issues above")
    print("=" * 50)

if __name__ == "__main__":
    main()
