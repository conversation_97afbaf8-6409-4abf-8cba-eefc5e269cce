"""
🔌 VIP BIG BANG - Extension Data Manager
Manages real-time data flow from Chrome Extension to UI
"""

import sys
import asyncio
import websockets
import json
from pathlib import Path
from PySide6.QtCore import *
from PySide6.QtWidgets import *
from datetime import datetime
import threading

# Add paths
sys.path.append(str(Path(__file__).parent.parent))

class ExtensionDataManager(QObject):
    """
    🔌 Extension Data Manager
    
    Manages real-time data flow from Chrome Extension to UI components
    
    Features:
    - WebSocket connection to desktop robot
    - Real-time data processing
    - UI component updates
    - Connection monitoring
    - Data validation
    """
    
    # Signals
    data_received = Signal(dict)
    connection_status_changed = Signal(bool)
    balance_updated = Signal(float)
    asset_updated = Signal(str)
    extraction_count_updated = Signal(int)
    
    def __init__(self):
        super().__init__()
        
        # Connection settings
        self.websocket_url = "ws://localhost:8765"
        self.is_connected = False
        self.websocket = None
        
        # Data storage
        self.current_data = {}
        self.last_balance = 0.0
        self.last_asset = ""
        self.extraction_count = 0
        
        # UI components to update
        self.ui_components = []
        
        # Start connection monitoring
        self.connection_timer = QTimer()
        self.connection_timer.timeout.connect(self._monitor_connection)
        self.connection_timer.start(5000)  # Check every 5 seconds
        
        print("🔌 Extension Data Manager initialized")
        
    def register_ui_component(self, component):
        """Register UI component for data updates"""
        if hasattr(component, 'update_from_extension_data'):
            self.ui_components.append(component)
            print(f"✅ UI component registered: {component.__class__.__name__}")
        else:
            print(f"⚠️ UI component missing update_from_extension_data method: {component.__class__.__name__}")
            
    def start_listening(self):
        """Start listening for extension data"""
        try:
            # Start WebSocket listener in separate thread
            self.listener_thread = threading.Thread(target=self._run_websocket_listener, daemon=True)
            self.listener_thread.start()
            print("🚀 Extension data listener started")
        except Exception as e:
            print(f"❌ Error starting extension data listener: {e}")
            
    def _run_websocket_listener(self):
        """Run WebSocket listener in separate thread"""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run WebSocket listener
            loop.run_until_complete(self._websocket_listener())
        except Exception as e:
            print(f"❌ WebSocket listener error: {e}")
            
    async def _websocket_listener(self):
        """WebSocket listener for extension data"""
        while True:
            try:
                print(f"🔌 Attempting to connect to {self.websocket_url}")
                
                async with websockets.connect(self.websocket_url) as websocket:
                    self.websocket = websocket
                    self.is_connected = True
                    self.connection_status_changed.emit(True)
                    print("✅ Connected to VIP BIG BANG desktop robot")
                    
                    # Listen for messages
                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            self._process_extension_data(data)
                        except json.JSONDecodeError:
                            print(f"⚠️ Invalid JSON received: {message}")
                        except Exception as e:
                            print(f"❌ Error processing message: {e}")
                            
            except websockets.exceptions.ConnectionClosed:
                print("🔌 WebSocket connection closed")
                self.is_connected = False
                self.connection_status_changed.emit(False)
                
            except Exception as e:
                print(f"❌ WebSocket connection error: {e}")
                self.is_connected = False
                self.connection_status_changed.emit(False)
                
            # Wait before reconnecting
            await asyncio.sleep(5)
            
    def _process_extension_data(self, data):
        """Process data received from extension"""
        try:
            # Check if this is extension data
            source = data.get('source', '')
            if source not in ['REAL_CHROME_EXTENSION', 'ADVANCED_SCANNER']:
                return
                
            # Store current data
            self.current_data = data
            
            # Process balance
            if 'balance' in data and data['balance']:
                balance_str = data['balance']
                if balance_str and balance_str != 'N/A' and '$' in balance_str:
                    try:
                        balance_clean = balance_str.replace('$', '').replace(',', '')
                        balance_value = float(balance_clean)
                        if balance_value != self.last_balance:
                            self.last_balance = balance_value
                            self.balance_updated.emit(balance_value)
                    except ValueError:
                        pass
                        
            # Process asset
            if 'currentAsset' in data and data['currentAsset']:
                asset = data['currentAsset']
                if asset and asset != 'Market' and asset != 'None' and asset != self.last_asset:
                    self.last_asset = asset
                    self.asset_updated.emit(asset)
                    
            # Process extraction count
            if 'extractionCount' in data:
                count = data['extractionCount']
                if count != self.extraction_count:
                    self.extraction_count = count
                    self.extraction_count_updated.emit(count)
                    
            # Update UI components
            self._update_ui_components(data)
            
            # Emit general data signal
            self.data_received.emit(data)
            
            print(f"📊 Extension data processed: Balance={data.get('balance', 'N/A')}, Asset={data.get('currentAsset', 'N/A')}")
            
        except Exception as e:
            print(f"❌ Error processing extension data: {e}")
            
    def _update_ui_components(self, data):
        """Update all registered UI components"""
        for component in self.ui_components:
            try:
                component.update_from_extension_data(data)
            except Exception as e:
                print(f"❌ Error updating UI component {component.__class__.__name__}: {e}")
                
    def _monitor_connection(self):
        """Monitor connection status"""
        if not self.is_connected:
            print("🔍 Checking extension connection...")
            # Try to start listener if not running
            if not hasattr(self, 'listener_thread') or not self.listener_thread.is_alive():
                self.start_listening()
                
    def get_current_data(self):
        """Get current extension data"""
        return self.current_data.copy()
        
    def get_connection_status(self):
        """Get connection status"""
        return self.is_connected
        
    def get_balance(self):
        """Get current balance"""
        return self.last_balance
        
    def get_asset(self):
        """Get current asset"""
        return self.last_asset
        
    def get_extraction_count(self):
        """Get extraction count"""
        return self.extraction_count


class ExtensionDataConnector(QObject):
    """
    🔗 Extension Data Connector
    
    Connects Extension Data Manager to UI components
    """
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.data_manager = ExtensionDataManager()
        
        # Connect signals
        self._connect_signals()
        
        # Start data manager
        self.data_manager.start_listening()
        
    def _connect_signals(self):
        """Connect data manager signals to UI updates"""
        self.data_manager.data_received.connect(self._on_data_received)
        self.data_manager.connection_status_changed.connect(self._on_connection_status_changed)
        self.data_manager.balance_updated.connect(self._on_balance_updated)
        self.data_manager.asset_updated.connect(self._on_asset_updated)
        self.data_manager.extraction_count_updated.connect(self._on_extraction_count_updated)
        
    def register_ui_component(self, component):
        """Register UI component for updates"""
        self.data_manager.register_ui_component(component)
        
    def _on_data_received(self, data):
        """Handle data received from extension"""
        print(f"🔌 Data received in connector: {data.get('source', 'Unknown')}")
        
    def _on_connection_status_changed(self, connected):
        """Handle connection status change"""
        status = "🟢 Connected" if connected else "🔴 Disconnected"
        print(f"🔌 Extension connection status: {status}")
        
    def _on_balance_updated(self, balance):
        """Handle balance update"""
        print(f"💰 Balance updated: ${balance}")
        
    def _on_asset_updated(self, asset):
        """Handle asset update"""
        print(f"📊 Asset updated: {asset}")
        
    def _on_extraction_count_updated(self, count):
        """Handle extraction count update"""
        print(f"🔍 Extraction count updated: {count}")
