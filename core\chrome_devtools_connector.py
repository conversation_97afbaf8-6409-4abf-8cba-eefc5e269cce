"""
🔗 VIP BIG BANG CHROME DEVTOOLS CONNECTOR
🚀 DIRECT CONNECTION TO EXISTING CHROME INSTANCE
📡 REAL-TIME DATA EXTRACTION FROM QUOTEX
"""

import json
import logging
import requests
import time
import threading
from typing import Dict, Any, List, Optional

class ChromeDevToolsConnector:
    """
    🔗 CHROME DEVTOOLS CONNECTOR
    🚀 Connects directly to existing Chrome instance
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ChromeDevToolsConnector")
        
        # DevTools settings
        self.devtools_port = 9222
        self.devtools_url = f"http://localhost:{self.devtools_port}"
        self.quotex_tab = None
        self.websocket_url = None
        
        # Connection status
        self.is_connected = False
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Data storage
        self.latest_price_data = {}
        self.latest_balance = 0.0
        self.connection_callbacks = []
        
        self.logger.info("🔗 Chrome DevTools Connector initialized")
    
    def find_chrome_tabs(self) -> List[Dict]:
        """🔍 Find all Chrome tabs"""
        try:
            response = requests.get(f"{self.devtools_url}/json", timeout=5)
            if response.status_code == 200:
                tabs = response.json()
                self.logger.info(f"✅ Found {len(tabs)} Chrome tabs")
                return tabs
            else:
                self.logger.error(f"❌ Failed to get tabs: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"❌ Error finding Chrome tabs: {e}")
            return []
    
    def find_quotex_tab(self) -> Optional[Dict]:
        """🎯 Find Quotex tab"""
        try:
            tabs = self.find_chrome_tabs()
            
            for tab in tabs:
                url = tab.get('url', '').lower()
                title = tab.get('title', '').lower()
                
                if any(domain in url for domain in ['quotex.io', 'qxbroker.com', 'quotex.com']):
                    self.logger.info(f"✅ Found Quotex tab: {tab.get('title', 'Unknown')}")
                    return tab
                    
                if 'quotex' in title:
                    self.logger.info(f"✅ Found Quotex tab by title: {title}")
                    return tab
            
            self.logger.warning("⚠️ No Quotex tab found")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error finding Quotex tab: {e}")
            return None
    
    def connect_to_quotex_tab(self) -> bool:
        """🔗 Connect to Quotex tab"""
        try:
            self.quotex_tab = self.find_quotex_tab()
            
            if not self.quotex_tab:
                self.logger.error("❌ No Quotex tab found to connect")
                return False
            
            # Get WebSocket URL for the tab
            self.websocket_url = self.quotex_tab.get('webSocketDebuggerUrl')
            
            if not self.websocket_url:
                self.logger.error("❌ No WebSocket URL found for Quotex tab")
                return False
            
            self.is_connected = True
            self.logger.info("✅ Connected to Quotex tab")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error connecting to Quotex tab: {e}")
            return False
    
    def inject_data_extractor(self) -> bool:
        """💉 Inject data extraction script into Quotex tab"""
        try:
            if not self.quotex_tab:
                self.logger.error("❌ No Quotex tab connected")
                return False
            
            tab_id = self.quotex_tab.get('id')
            
            # JavaScript code to extract data
            extraction_script = """
            // 🚀 VIP BIG BANG Data Extractor
            (function() {
                console.log('🚀 VIP BIG BANG Data Extractor Loading...');
                
                // Create data extraction object
                window.VIP_BIG_BANG_EXTRACTOR = {
                    isActive: true,
                    lastUpdate: Date.now(),
                    
                    // Extract current price
                    extractPrice: function() {
                        const selectors = [
                            '.chart-price',
                            '.current-rate', 
                            '[data-testid="current-price"]',
                            '.asset-price',
                            '.price-display',
                            '.rate-value',
                            '.trading-chart__price',
                            '.chart__price',
                            '.quote-value',
                            '.price-current',
                            '.current-price'
                        ];
                        
                        for (const selector of selectors) {
                            try {
                                const element = document.querySelector(selector);
                                if (element) {
                                    const text = element.textContent || element.innerText || '';
                                    const priceMatch = text.match(/\\d+\\.\\d{3,5}/);
                                    
                                    if (priceMatch) {
                                        const price = parseFloat(priceMatch[0]);
                                        if (price > 0 && price < 1000) {
                                            return {
                                                price: price,
                                                timestamp: Date.now(),
                                                source: selector
                                            };
                                        }
                                    }
                                }
                            } catch (e) {}
                        }
                        
                        return null;
                    },
                    
                    // Extract balance
                    extractBalance: function() {
                        const selectors = [
                            '.balance__value',
                            '.user-balance',
                            '[data-testid="balance"]',
                            '.account-balance',
                            '.header-balance',
                            '.balance-amount',
                            '.wallet-balance',
                            '.current-balance'
                        ];
                        
                        for (const selector of selectors) {
                            try {
                                const element = document.querySelector(selector);
                                if (element) {
                                    const text = element.textContent || element.innerText || '';
                                    const balanceMatch = text.match(/\\d+(?:\\.\\d+)?/);
                                    
                                    if (balanceMatch) {
                                        const balance = parseFloat(balanceMatch[0]);
                                        if (balance > 0) {
                                            return balance;
                                        }
                                    }
                                }
                            } catch (e) {}
                        }
                        
                        return 0;
                    },
                    
                    // Extract asset name
                    extractAsset: function() {
                        const selectors = [
                            '.asset-name',
                            '.trading-pair',
                            '.current-asset',
                            '.selected-asset',
                            '[data-testid="asset-name"]'
                        ];
                        
                        for (const selector of selectors) {
                            try {
                                const element = document.querySelector(selector);
                                if (element) {
                                    const text = element.textContent || element.innerText || '';
                                    const assetMatch = text.match(/(EUR\\/USD|GBP\\/USD|USD\\/JPY|AUD\\/USD|USD\\/CAD|USD\\/CHF|NZD\\/USD)/i);
                                    if (assetMatch) {
                                        return assetMatch[1].toUpperCase();
                                    }
                                }
                            } catch (e) {}
                        }
                        
                        return 'EUR/USD';
                    },
                    
                    // Get all data
                    getAllData: function() {
                        const price = this.extractPrice();
                        const balance = this.extractBalance();
                        const asset = this.extractAsset();
                        
                        return {
                            price: price,
                            balance: balance,
                            asset: asset,
                            timestamp: Date.now(),
                            status: 'active'
                        };
                    }
                };
                
                // Auto-update data every second
                setInterval(() => {
                    if (window.VIP_BIG_BANG_EXTRACTOR.isActive) {
                        const data = window.VIP_BIG_BANG_EXTRACTOR.getAllData();
                        
                        // Store in global variable for external access
                        window.VIP_BIG_BANG_DATA = data;
                        
                        // Update timestamp
                        window.VIP_BIG_BANG_EXTRACTOR.lastUpdate = Date.now();
                    }
                }, 1000);
                
                console.log('✅ VIP BIG BANG Data Extractor Active!');
                
            })();
            """
            
            # Send script via DevTools API
            payload = {
                "id": 1,
                "method": "Runtime.evaluate",
                "params": {
                    "expression": extraction_script,
                    "returnByValue": True
                }
            }
            
            response = requests.post(
                f"{self.devtools_url}/json/runtime/evaluate",
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                self.logger.info("✅ Data extractor injected successfully")
                return True
            else:
                self.logger.error(f"❌ Failed to inject extractor: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error injecting data extractor: {e}")
            return False
    
    def get_data_from_chrome(self) -> Dict[str, Any]:
        """📡 Get data from Chrome tab"""
        try:
            if not self.quotex_tab:
                return {}
            
            # JavaScript to get extracted data
            get_data_script = """
            if (window.VIP_BIG_BANG_DATA) {
                window.VIP_BIG_BANG_DATA;
            } else {
                null;
            }
            """
            
            payload = {
                "id": 2,
                "method": "Runtime.evaluate",
                "params": {
                    "expression": get_data_script,
                    "returnByValue": True
                }
            }
            
            response = requests.post(
                f"{self.devtools_url}/json/runtime/evaluate",
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('result', {}).get('value'):
                    return result['result']['value']
            
            return {}
            
        except Exception as e:
            self.logger.error(f"❌ Error getting data from Chrome: {e}")
            return {}
    
    def start_monitoring(self) -> bool:
        """🔄 Start monitoring Chrome data"""
        try:
            if self.monitoring_active:
                self.logger.warning("⚠️ Monitoring already active")
                return True
            
            # Connect to Quotex tab
            if not self.connect_to_quotex_tab():
                return False
            
            # Inject data extractor
            if not self.inject_data_extractor():
                return False
            
            # Start monitoring thread
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            self.logger.info("✅ Chrome monitoring started")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error starting monitoring: {e}")
            return False
    
    def _monitor_loop(self):
        """🔄 Monitoring loop"""
        while self.monitoring_active:
            try:
                # Get data from Chrome
                data = self.get_data_from_chrome()
                
                if data:
                    # Update stored data
                    if data.get('price'):
                        self.latest_price_data = {
                            data.get('asset', 'EUR/USD'): data['price']
                        }
                        self.notify_callbacks('price_update', self.latest_price_data)
                    
                    if data.get('balance', 0) > 0:
                        self.latest_balance = data['balance']
                        self.notify_callbacks('balance_update', self.latest_balance)
                    
                    # Update connection status
                    if not self.is_connected:
                        self.is_connected = True
                        self.notify_callbacks('connection_status', True)
                
                # Wait before next check
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"❌ Monitor loop error: {e}")
                time.sleep(2)
    
    def stop_monitoring(self):
        """🛑 Stop monitoring"""
        self.monitoring_active = False
        self.is_connected = False
        self.logger.info("🛑 Chrome monitoring stopped")
    
    def add_callback(self, callback):
        """➕ Add callback for events"""
        self.connection_callbacks.append(callback)
    
    def notify_callbacks(self, event_type: str, data: Any):
        """📢 Notify registered callbacks"""
        for callback in self.connection_callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                self.logger.error(f"❌ Callback error: {e}")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """📊 Get connection status"""
        return {
            'is_connected': self.is_connected,
            'monitoring_active': self.monitoring_active,
            'quotex_tab_found': self.quotex_tab is not None,
            'latest_price_data': self.latest_price_data,
            'latest_balance': self.latest_balance,
            'devtools_port': self.devtools_port
        }
