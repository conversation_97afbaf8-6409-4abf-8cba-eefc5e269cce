"""
🎨 VIP BIG BANG - Professional Modern UI
رابط کاربری حرفه‌ای و مدرن
"""

import sys
import random
from datetime import datetime
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class ModernCard(QFrame):
    """کارت مدرن با سایه و انیمیشن"""
    
    def __init__(self, title="", value="", icon="", color="#4A90E2"):
        super().__init__()
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """تنظیم UI کارت"""
        self.setFixedSize(200, 120)
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.95),
                    stop:1 rgba(255,255,255,0.85));
                border: none;
                border-radius: 15px;
                border-left: 4px solid {self.color};
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,1.0),
                    stop:1 rgba(255,255,255,0.95));
            }}
        """)
        
        # Shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(8)
        
        # Header
        header_layout = QHBoxLayout()
        
        # Icon
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet(f"""
            font-size: 24px;
            color: {self.color};
        """)
        header_layout.addWidget(icon_label)
        
        header_layout.addStretch()
        
        # Title
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            font-size: 12px;
            font-weight: 600;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        """)
        header_layout.addWidget(title_label)
        
        layout.addLayout(header_layout)
        
        # Value
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 700;
            color: {self.color};
            margin-top: 5px;
        """)
        layout.addWidget(self.value_label)
        
        # Progress indicator
        self.progress = QProgressBar()
        self.progress.setRange(0, 100)
        self.progress.setValue(random.randint(60, 95))
        self.progress.setFixedHeight(4)
        self.progress.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 2px;
                background: rgba(0,0,0,0.1);
            }}
            QProgressBar::chunk {{
                background: {self.color};
                border-radius: 2px;
            }}
        """)
        layout.addWidget(self.progress)
    
    def setup_animations(self):
        """تنظیم انیمیشن‌ها"""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def update_value(self, new_value):
        """به‌روزرسانی مقدار"""
        self.value_label.setText(str(new_value))
        
        # Animate progress
        new_progress = random.randint(60, 95)
        self.animate_progress(new_progress)
    
    def animate_progress(self, target_value):
        """انیمیشن پروگرس بار"""
        self.progress_animation = QPropertyAnimation(self.progress, b"value")
        self.progress_animation.setDuration(1000)
        self.progress_animation.setStartValue(self.progress.value())
        self.progress_animation.setEndValue(target_value)
        self.progress_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.progress_animation.start()

class ModernButton(QPushButton):
    """دکمه مدرن با انیمیشن"""
    
    def __init__(self, text="", icon="", style="primary"):
        super().__init__(text)
        self.icon_text = icon
        self.button_style = style
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """تنظیم UI دکمه"""
        colors = {
            "primary": "#4A90E2",
            "success": "#28A745", 
            "warning": "#FFC107",
            "danger": "#DC3545",
            "info": "#17A2B8"
        }
        
        color = colors.get(self.button_style, "#4A90E2")
        
        self.setFixedHeight(50)
        self.setMinimumWidth(120)
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 25px;
                color: white;
                font-size: 14px;
                font-weight: 600;
                padding: 0 25px;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color},
                    stop:1 rgba(255,255,255,0.2));
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: rgba(0,0,0,0.1);
                transform: translateY(0px);
            }}
        """)
        
        # Shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)
        
        # Set text with icon
        if self.icon_text:
            self.setText(f"{self.icon_text} {self.text()}")
    
    def setup_animations(self):
        """تنظیم انیمیشن‌ها"""
        self.click_animation = QPropertyAnimation(self, b"geometry")
        self.click_animation.setDuration(100)

class ModernChart(QFrame):
    """چارت مدرن"""
    
    def __init__(self):
        super().__init__()
        self.data_points = []
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """تنظیم UI چارت"""
        self.setMinimumHeight(300)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.95),
                    stop:1 rgba(255,255,255,0.85));
                border: none;
                border-radius: 15px;
            }
        """)
        
        # Shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Header
        header = QLabel("📈 EUR/USD Live Chart")
        header.setStyleSheet("""
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        """)
        layout.addWidget(header)
        
        # Chart area
        self.chart_widget = QWidget()
        self.chart_widget.setMinimumHeight(250)
        layout.addWidget(self.chart_widget)
        
        # Generate initial data
        for i in range(50):
            self.data_points.append(1.0850 + random.uniform(-0.01, 0.01))
    
    def setup_timer(self):
        """تنظیم تایمر به‌روزرسانی"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_chart)
        self.update_timer.start(2000)  # Every 2 seconds
    
    def update_chart(self):
        """به‌روزرسانی چارت"""
        # Add new data point
        last_price = self.data_points[-1] if self.data_points else 1.0850
        new_price = last_price + random.uniform(-0.005, 0.005)
        self.data_points.append(new_price)
        
        # Keep only last 50 points
        if len(self.data_points) > 50:
            self.data_points.pop(0)
        
        # Trigger repaint
        self.chart_widget.update()
    
    def paintEvent(self, event):
        """رسم چارت"""
        super().paintEvent(event)
        
        painter = QPainter(self.chart_widget)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        if len(self.data_points) < 2:
            return
        
        # Chart area
        rect = self.chart_widget.rect()
        margin = 20
        chart_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        # Calculate scaling
        min_price = min(self.data_points)
        max_price = max(self.data_points)
        price_range = max_price - min_price if max_price != min_price else 0.01
        
        # Draw grid
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        for i in range(5):
            y = chart_rect.top() + (chart_rect.height() * i / 4)
            painter.drawLine(chart_rect.left(), y, chart_rect.right(), y)
        
        # Draw price line
        painter.setPen(QPen(QColor(74, 144, 226), 3))
        
        points = []
        for i, price in enumerate(self.data_points):
            x = chart_rect.left() + (chart_rect.width() * i / (len(self.data_points) - 1))
            y = chart_rect.bottom() - ((price - min_price) / price_range * chart_rect.height())
            points.append(QPointF(x, y))
        
        if len(points) > 1:
            for i in range(len(points) - 1):
                painter.drawLine(points[i], points[i + 1])

class VIPProfessionalUI(QMainWindow):
    """رابط کاربری حرفه‌ای VIP BIG BANG"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎯 VIP BIG BANG - Professional Trading Interface")
        self.setGeometry(100, 100, 1400, 900)
        
        # Data
        self.balance = 3250.89
        self.winrate = 94.7
        self.trades = 127
        self.profit = 2150.89
        
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """تنظیم رابط کاربری"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Modern gradient background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa,
                    stop:0.3 #e9ecef,
                    stop:0.7 #dee2e6,
                    stop:1 #f8f9fa);
            }
            QLabel {
                color: #333;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        
        # Header
        self.create_header(layout)
        
        # Main content
        main_layout = QHBoxLayout()
        main_layout.setSpacing(25)
        
        # Left panel - Stats
        self.create_stats_panel(main_layout)
        
        # Center panel - Chart
        self.create_chart_panel(main_layout)
        
        # Right panel - Controls
        self.create_controls_panel(main_layout)
        
        layout.addLayout(main_layout)
        
        # Footer
        self.create_footer(layout)
    
    def create_header(self, layout):
        """ایجاد هدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.9),
                    stop:1 rgba(255,255,255,0.7));
                border: none;
                border-radius: 15px;
            }
        """)
        
        # Shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 3)
        header_frame.setGraphicsEffect(shadow)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # Logo and title
        logo_layout = QHBoxLayout()
        
        logo = QLabel("🎯")
        logo.setStyleSheet("font-size: 36px;")
        logo_layout.addWidget(logo)
        
        title_layout = QVBoxLayout()
        title_layout.setSpacing(0)
        
        title = QLabel("VIP BIG BANG")
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        """)
        title_layout.addWidget(title)
        
        subtitle = QLabel("Professional Trading System")
        subtitle.setStyleSheet("""
            font-size: 14px;
            color: #7f8c8d;
            margin: 0;
        """)
        title_layout.addWidget(subtitle)
        
        logo_layout.addLayout(title_layout)
        header_layout.addLayout(logo_layout)
        
        header_layout.addStretch()
        
        # Status
        status_layout = QVBoxLayout()
        status_layout.setSpacing(0)
        
        status = QLabel("🟢 LIVE")
        status.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #27ae60;
        """)
        status_layout.addWidget(status)
        
        time_label = QLabel(datetime.now().strftime("%H:%M:%S"))
        time_label.setStyleSheet("""
            font-size: 12px;
            color: #7f8c8d;
        """)
        status_layout.addWidget(time_label)
        
        header_layout.addLayout(status_layout)
        
        layout.addWidget(header_frame)
    
    def create_stats_panel(self, layout):
        """پنل آمار"""
        stats_widget = QWidget()
        stats_widget.setFixedWidth(250)
        
        stats_layout = QVBoxLayout(stats_widget)
        stats_layout.setSpacing(15)
        
        # Title
        title = QLabel("📊 Trading Statistics")
        title.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        stats_layout.addWidget(title)
        
        # Stats cards
        self.balance_card = ModernCard("Balance", f"${self.balance:,.2f}", "💰", "#27ae60")
        stats_layout.addWidget(self.balance_card)
        
        self.winrate_card = ModernCard("Win Rate", f"{self.winrate}%", "🏆", "#3498db")
        stats_layout.addWidget(self.winrate_card)
        
        self.trades_card = ModernCard("Trades", str(self.trades), "📊", "#9b59b6")
        stats_layout.addWidget(self.trades_card)
        
        self.profit_card = ModernCard("Profit", f"${self.profit:,.2f}", "💎", "#f39c12")
        stats_layout.addWidget(self.profit_card)
        
        stats_layout.addStretch()
        
        layout.addWidget(stats_widget)
    
    def create_chart_panel(self, layout):
        """پنل چارت"""
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)
        chart_layout.setSpacing(15)
        
        # Chart
        self.chart = ModernChart()
        chart_layout.addWidget(self.chart)
        
        layout.addWidget(chart_widget, 2)
    
    def create_controls_panel(self, layout):
        """پنل کنترل‌ها"""
        controls_widget = QWidget()
        controls_widget.setFixedWidth(250)
        
        controls_layout = QVBoxLayout(controls_widget)
        controls_layout.setSpacing(15)
        
        # Title
        title = QLabel("🎮 Trading Controls")
        title.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        controls_layout.addWidget(title)
        
        # Control buttons
        start_btn = ModernButton("Start Trading", "🚀", "success")
        start_btn.clicked.connect(self.start_trading)
        controls_layout.addWidget(start_btn)
        
        stop_btn = ModernButton("Stop Trading", "🛑", "danger")
        stop_btn.clicked.connect(self.stop_trading)
        controls_layout.addWidget(stop_btn)
        
        settings_btn = ModernButton("Settings", "⚙️", "info")
        settings_btn.clicked.connect(self.open_settings)
        controls_layout.addWidget(settings_btn)
        
        # Spacer
        controls_layout.addSpacing(20)
        
        # Quick actions
        quick_title = QLabel("⚡ Quick Actions")
        quick_title.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        controls_layout.addWidget(quick_title)
        
        call_btn = ModernButton("CALL", "📈", "success")
        call_btn.clicked.connect(lambda: self.place_trade("CALL"))
        controls_layout.addWidget(call_btn)
        
        put_btn = ModernButton("PUT", "📉", "danger")
        put_btn.clicked.connect(lambda: self.place_trade("PUT"))
        controls_layout.addWidget(put_btn)
        
        controls_layout.addStretch()
        
        layout.addWidget(controls_widget)
    
    def create_footer(self, layout):
        """ایجاد فوتر"""
        footer = QFrame()
        footer.setFixedHeight(50)
        footer.setStyleSheet("""
            QFrame {
                background: rgba(255,255,255,0.8);
                border: none;
                border-radius: 10px;
            }
        """)
        
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(20, 10, 20, 10)
        
        status = QLabel("🔗 Connected to VIP BIG BANG Core")
        status.setStyleSheet("""
            color: #27ae60;
            font-weight: 600;
            font-size: 12px;
        """)
        footer_layout.addWidget(status)
        
        footer_layout.addStretch()
        
        self.time_label = QLabel()
        self.time_label.setStyleSheet("""
            color: #7f8c8d;
            font-size: 12px;
        """)
        footer_layout.addWidget(self.time_label)
        
        layout.addWidget(footer)
    
    def setup_timer(self):
        """تنظیم تایمر"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.start(3000)  # Every 3 seconds
        
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # Every second
    
    def update_data(self):
        """به‌روزرسانی داده‌ها"""
        # Update balance
        self.balance += random.uniform(-50, 100)
        self.balance_card.update_value(f"${self.balance:,.2f}")
        
        # Update winrate
        self.winrate += random.uniform(-0.5, 0.5)
        self.winrate = max(85, min(98, self.winrate))
        self.winrate_card.update_value(f"{self.winrate:.1f}%")
        
        # Update trades
        if random.random() < 0.3:  # 30% chance
            self.trades += 1
            self.trades_card.update_value(str(self.trades))
        
        # Update profit
        self.profit += random.uniform(-20, 50)
        self.profit_card.update_value(f"${self.profit:,.2f}")
    
    def update_time(self):
        """به‌روزرسانی زمان"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"Last Update: {current_time}")
    
    def start_trading(self):
        """شروع تریدینگ"""
        QMessageBox.information(self, "🚀 Trading Started", "VIP BIG BANG trading system activated!")
    
    def stop_trading(self):
        """توقف تریدینگ"""
        QMessageBox.information(self, "🛑 Trading Stopped", "VIP BIG BANG trading system deactivated!")
    
    def open_settings(self):
        """باز کردن تنظیمات"""
        QMessageBox.information(self, "⚙️ Settings", "Settings panel will open here!")
    
    def place_trade(self, direction):
        """انجام معامله"""
        QMessageBox.information(self, f"📊 Trade Placed", f"{direction} trade executed successfully!")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    window = VIPProfessionalUI()
    window.show()
    
    sys.exit(app.exec())
