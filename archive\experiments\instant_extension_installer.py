#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
⚡ VIP BIG BANG - Instant Extension Installer
نصب فوری و اجباری اکستنشن Chrome
"""

import os
import sys
import subprocess
import time
import shutil
from pathlib import Path

class InstantExtensionInstaller:
    """
    ⚡ نصب‌کننده فوری اکستنشن
    """
    
    def __init__(self):
        self.extension_dir = Path("chrome_extension")
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
    
    def find_chrome(self):
        """پیدا کردن Chrome"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                return path
        return None
    
    def kill_chrome(self):
        """بستن تمام پروسه‌های Chrome"""
        try:
            subprocess.run(["taskkill", "/f", "/im", "chrome.exe"], 
                         capture_output=True, check=False)
            time.sleep(2)
            print("✅ Chrome processes killed")
        except:
            pass
    
    def create_extension_loader_script(self):
        """ایجاد اسکریپت بارگذار اکستنشن"""
        chrome_exe = self.find_chrome()
        if not chrome_exe:
            return False
        
        extension_path = os.path.abspath(self.extension_dir)
        
        # Create PowerShell script for instant loading
        ps_script = f'''
# VIP BIG BANG Extension Instant Loader
Write-Host "🚀 VIP BIG BANG Extension Instant Loader" -ForegroundColor Green
Write-Host ""

$ChromePath = "{chrome_exe}"
$ExtensionPath = "{extension_path}"

Write-Host "Chrome Path: $ChromePath" -ForegroundColor Yellow
Write-Host "Extension Path: $ExtensionPath" -ForegroundColor Yellow
Write-Host ""

# Kill existing Chrome processes
Write-Host "Terminating existing Chrome processes..." -ForegroundColor Cyan
Get-Process -Name "chrome" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# Start Chrome with extension
Write-Host "Starting Chrome with VIP BIG BANG extension..." -ForegroundColor Green
$Arguments = @(
    "--load-extension=$ExtensionPath",
    "--disable-extensions-file-access-check",
    "--disable-extensions-http-throttling",
    "--enable-experimental-extension-apis",
    "--disable-web-security",
    "--allow-running-insecure-content",
    "--no-first-run",
    "--no-default-browser-check",
    "--disable-background-mode",
    "--force-dev-mode-highlighting",
    "https://qxbroker.com/en/trade"
)

Start-Process -FilePath $ChromePath -ArgumentList $Arguments

Write-Host ""
Write-Host "✅ Chrome started with VIP BIG BANG extension!" -ForegroundColor Green
Write-Host "🎯 Extension should be automatically loaded" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Check chrome://extensions/" -ForegroundColor White
Write-Host "2. Verify VIP BIG BANG is enabled" -ForegroundColor White
Write-Host "3. Test on Quotex page" -ForegroundColor White
Write-Host ""
'''
        
        with open("instant_extension_loader.ps1", "w", encoding="utf-8") as f:
            f.write(ps_script)
        
        # Create batch file to run PowerShell script
        batch_script = f'''@echo off
echo 🚀 VIP BIG BANG Extension Instant Loader
echo.
echo Starting PowerShell script...
powershell -ExecutionPolicy Bypass -File "instant_extension_loader.ps1"
echo.
echo ✅ Extension loading completed!
pause
'''
        
        with open("instant_extension_loader.bat", "w", encoding="utf-8") as f:
            f.write(batch_script)
        
        print("✅ Extension loader scripts created")
        return True
    
    def instant_install(self):
        """نصب فوری اکستنشن"""
        print("="*60)
        print("⚡ VIP BIG BANG Instant Extension Installer")
        print("="*60)
        
        # Check extension files
        if not self.extension_dir.exists():
            print("❌ Extension directory not found!")
            return False
        
        print("✅ Extension files found")
        
        # Find Chrome
        chrome_exe = self.find_chrome()
        if not chrome_exe:
            print("❌ Chrome not found!")
            return False
        
        print(f"✅ Chrome found: {chrome_exe}")
        
        # Kill existing Chrome
        print("🔄 Terminating existing Chrome processes...")
        self.kill_chrome()
        
        # Create loader scripts
        self.create_extension_loader_script()
        
        # Launch Chrome with extension
        extension_path = os.path.abspath(self.extension_dir)
        
        chrome_args = [
            chrome_exe,
            f"--load-extension={extension_path}",
            "--disable-extensions-file-access-check",
            "--disable-extensions-http-throttling",
            "--enable-experimental-extension-apis",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-background-mode",
            "--force-dev-mode-highlighting",
            "--enable-logging",
            "--log-level=0",
            "https://qxbroker.com/en/trade"
        ]
        
        print("🚀 Launching Chrome with VIP BIG BANG extension...")
        
        try:
            subprocess.Popen(chrome_args, shell=False)
            time.sleep(3)
            
            print("✅ Chrome launched successfully!")
            print("🎯 VIP BIG BANG extension should be loaded automatically")
            
            print("\n" + "="*60)
            print("✅ INSTANT INSTALLATION COMPLETED!")
            print("\n📋 What happened:")
            print("1. ✅ Chrome processes terminated")
            print("2. ✅ Chrome launched with extension")
            print("3. ✅ Quotex page opened")
            print("4. ✅ Extension should be active")
            
            print("\n🎯 Verification steps:")
            print("1. Go to chrome://extensions/")
            print("2. Check if VIP BIG BANG is enabled")
            print("3. Test on Quotex page")
            print("4. Look for extension icon in toolbar")
            
            print("\n🔧 If extension is not working:")
            print("1. Run: instant_extension_loader.bat")
            print("2. Enable Developer mode in chrome://extensions/")
            print("3. Refresh Quotex page")
            
            print("="*60)
            return True
            
        except Exception as e:
            print(f"❌ Failed to launch Chrome: {e}")
            return False

def main():
    """اجرای نصب فوری"""
    installer = InstantExtensionInstaller()
    success = installer.instant_install()
    
    if not success:
        print("\n🔧 Manual installation required:")
        print("1. Open Chrome")
        print("2. Go to chrome://extensions/")
        print("3. Enable Developer mode")
        print("4. Click 'Load unpacked'")
        print("5. Select chrome_extension folder")

if __name__ == "__main__":
    main()
