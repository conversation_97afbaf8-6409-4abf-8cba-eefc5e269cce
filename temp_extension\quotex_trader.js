/**
 * 🚀 VIP BIG BANG - Quotex Auto Trader
 * 💎 JavaScript برای کنترل مستقیم صفحه Quotex
 * 🎯 اتوماسیون کامل معاملات
 */

class QuotexTrader {
    constructor() {
        this.isConnected = false;
        this.currentAmount = 10;
        this.currentDuration = 5;
        this.tradingEnabled = false;
        
        console.log('🚀 VIP BIG BANG Quotex Trader initialized');
        this.init();
    }
    
    init() {
        // Wait for page to load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupTrader());
        } else {
            this.setupTrader();
        }
    }
    
    setupTrader() {
        console.log('🎯 Setting up Quotex trader...');
        
        // Check if we're on Quotex
        if (!window.location.href.includes('quotex.io')) {
            console.log('❌ Not on Quotex page');
            return;
        }
        
        // Wait for Quotex interface to load
        this.waitForQuotexInterface();
        
        // Setup message listener for commands from dashboard
        this.setupMessageListener();
        
        // Create floating control panel
        this.createFloatingPanel();
        
        this.isConnected = true;
        console.log('✅ Quotex trader connected');
    }
    
    waitForQuotexInterface() {
        const checkInterval = setInterval(() => {
            // Look for Quotex trading interface elements
            const callButton = this.findCallButton();
            const putButton = this.findPutButton();
            const amountInput = this.findAmountInput();
            
            if (callButton && putButton) {
                console.log('✅ Quotex interface detected');
                clearInterval(checkInterval);
                this.tradingEnabled = true;
                this.updateFloatingPanel();
            }
        }, 1000);
        
        // Stop checking after 30 seconds
        setTimeout(() => clearInterval(checkInterval), 30000);
    }
    
    setupMessageListener() {
        // Listen for messages from the dashboard
        window.addEventListener('message', (event) => {
            if (event.data.type === 'VIP_TRADE_COMMAND') {
                this.handleTradeCommand(event.data);
            }
        });
        
        // Also listen for extension messages
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
                if (request.type === 'EXECUTE_TRADE') {
                    this.executeTrade(request.direction, request.amount, request.duration);
                    sendResponse({success: true});
                }
            });
        }
    }
    
    createFloatingPanel() {
        // Create floating control panel
        const panel = document.createElement('div');
        panel.id = 'vip-floating-panel';
        panel.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #8B5CF6, #EC4899);
                color: white;
                padding: 15px;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
                min-width: 200px;
            ">
                <div style="font-weight: bold; margin-bottom: 10px;">
                    🚀 VIP BIG BANG Trader
                </div>
                <div id="vip-status">🔄 در حال اتصال...</div>
                <div style="margin-top: 10px;">
                    <button id="vip-test-call" style="
                        background: #10B981;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 5px;
                        margin-right: 5px;
                        cursor: pointer;
                    ">📈 Test CALL</button>
                    <button id="vip-test-put" style="
                        background: #EF4444;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">📉 Test PUT</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        
        // Add event listeners
        document.getElementById('vip-test-call').addEventListener('click', () => {
            this.executeTrade('CALL', this.currentAmount, this.currentDuration);
        });
        
        document.getElementById('vip-test-put').addEventListener('click', () => {
            this.executeTrade('PUT', this.currentAmount, this.currentDuration);
        });
    }
    
    updateFloatingPanel() {
        const statusElement = document.getElementById('vip-status');
        if (statusElement) {
            if (this.tradingEnabled) {
                statusElement.textContent = '✅ آماده معاملات';
                statusElement.style.color = '#10B981';
            } else {
                statusElement.textContent = '❌ رابط Quotex یافت نشد';
                statusElement.style.color = '#EF4444';
            }
        }
    }
    
    handleTradeCommand(data) {
        console.log('📊 Trade command received:', data);
        this.executeTrade(data.direction, data.amount, data.duration);
    }
    
    executeTrade(direction, amount, duration) {
        if (!this.tradingEnabled) {
            console.log('❌ Trading not enabled - Quotex interface not ready');
            this.showNotification('❌ رابط Quotex آماده نیست', 'error');
            return false;
        }
        
        console.log(`🎯 Executing ${direction} trade: $${amount} for ${duration}s`);
        
        try {
            // Set amount
            this.setTradeAmount(amount);
            
            // Set duration
            this.setTradeDuration(duration);
            
            // Wait a bit for settings to apply
            setTimeout(() => {
                // Execute trade
                if (direction === 'CALL') {
                    this.clickCallButton();
                } else if (direction === 'PUT') {
                    this.clickPutButton();
                }
                
                this.showNotification(`🎯 ${direction} معامله اجرا شد: $${amount}`, 'success');
            }, 500);
            
            return true;
            
        } catch (error) {
            console.error('❌ Trade execution failed:', error);
            this.showNotification('❌ خطا در اجرای معامله', 'error');
            return false;
        }
    }
    
    setTradeAmount(amount) {
        const amountInput = this.findAmountInput();
        if (amountInput) {
            // Clear current value
            amountInput.value = '';
            amountInput.focus();
            
            // Type new amount
            amountInput.value = amount.toString();
            
            // Trigger change events
            amountInput.dispatchEvent(new Event('input', { bubbles: true }));
            amountInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            console.log(`💰 Amount set to: $${amount}`);
        } else {
            console.log('❌ Amount input not found');
        }
    }
    
    setTradeDuration(duration) {
        // Try to find duration selector
        const durationSelectors = [
            'button[data-value="' + duration + '"]',
            '.duration-button[data-duration="' + duration + '"]',
            '.time-button[data-time="' + duration + '"]'
        ];
        
        for (const selector of durationSelectors) {
            const durationButton = document.querySelector(selector);
            if (durationButton) {
                durationButton.click();
                console.log(`⏰ Duration set to: ${duration}s`);
                return;
            }
        }
        
        console.log('❌ Duration selector not found');
    }
    
    clickCallButton() {
        const callButton = this.findCallButton();
        if (callButton) {
            callButton.click();
            console.log('📈 CALL button clicked');
        } else {
            console.log('❌ CALL button not found');
        }
    }
    
    clickPutButton() {
        const putButton = this.findPutButton();
        if (putButton) {
            putButton.click();
            console.log('📉 PUT button clicked');
        } else {
            console.log('❌ PUT button not found');
        }
    }
    
    findCallButton() {
        // Multiple selectors to find CALL button
        const selectors = [
            'button[data-direction="call"]',
            'button[data-type="call"]',
            '.call-button',
            '.trade-button.call',
            'button:contains("CALL")',
            'button[class*="call"]',
            '.trading-panel button:first-child'
        ];
        
        for (const selector of selectors) {
            const button = document.querySelector(selector);
            if (button) return button;
        }
        
        // Try to find by text content
        const buttons = document.querySelectorAll('button');
        for (const button of buttons) {
            if (button.textContent.toLowerCase().includes('call') || 
                button.textContent.toLowerCase().includes('higher') ||
                button.textContent.toLowerCase().includes('up')) {
                return button;
            }
        }
        
        return null;
    }
    
    findPutButton() {
        // Multiple selectors to find PUT button
        const selectors = [
            'button[data-direction="put"]',
            'button[data-type="put"]',
            '.put-button',
            '.trade-button.put',
            'button:contains("PUT")',
            'button[class*="put"]',
            '.trading-panel button:last-child'
        ];
        
        for (const selector of selectors) {
            const button = document.querySelector(selector);
            if (button) return button;
        }
        
        // Try to find by text content
        const buttons = document.querySelectorAll('button');
        for (const button of buttons) {
            if (button.textContent.toLowerCase().includes('put') || 
                button.textContent.toLowerCase().includes('lower') ||
                button.textContent.toLowerCase().includes('down')) {
                return button;
            }
        }
        
        return null;
    }
    
    findAmountInput() {
        // Multiple selectors to find amount input
        const selectors = [
            'input[data-field="amount"]',
            'input[name="amount"]',
            'input[placeholder*="amount"]',
            '.amount-input input',
            '.trade-amount input',
            'input[type="number"]'
        ];
        
        for (const selector of selectors) {
            const input = document.querySelector(selector);
            if (input) return input;
        }
        
        return null;
    }
    
    showNotification(message, type = 'info') {
        // Create notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#6366F1'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10001;
            font-family: Arial, sans-serif;
            font-size: 14px;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize trader when page loads
const vipTrader = new QuotexTrader();

// Make it globally accessible
window.vipTrader = vipTrader;
