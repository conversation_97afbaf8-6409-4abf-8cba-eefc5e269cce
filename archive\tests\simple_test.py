"""
VIP BIG BANG Enterprise - Simple System Test
Simple test without complex type annotations
"""

import pandas as pd  # type: ignore
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def create_simple_data():
    """Create simple test data"""
    data = []
    base_price = 1.2000
    
    for i in range(30):
        price = base_price + (i * 0.0001)
        data.append({
            'timestamp': datetime.now() - timedelta(minutes=30-i),
            'open': price,
            'high': price + 0.0002,
            'low': price - 0.0002,
            'close': price,
            'volume': 500 + i * 10,
            'price': price
        })
    
    return pd.DataFrame(data)

def test_primary_engine():
    """Test primary analysis engine"""
    print("🔍 Testing Primary Analysis Engine...")
    
    try:
        from core.analysis_engine import AnalysisEngine
        from core.settings import Settings
        
        # Initialize
        settings = Settings()
        engine = AnalysisEngine(settings)
        
        # Create test data
        data = create_simple_data()
        
        # Update engine with data
        for _, row in data.iterrows():
            engine.update_market_data(row.to_dict())
        
        # Run analysis
        results = engine.analyze()
        
        if 'error' in results:
            print(f"❌ Analysis Error: {results['error']}")
            return False
        
        print(f"✅ Primary Analysis Success!")
        print(f"   - Processing Time: {results.get('processing_time', 0):.3f}s")
        print(f"   - Overall Score: {results.get('overall_score', 0):.3f}")
        print(f"   - Direction: {results.get('direction', 'UNKNOWN')}")
        print(f"   - Confidence: {results.get('confidence', 0):.3f}")
        print(f"   - Signals: {len(results.get('signals', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Primary Engine Error: {e}")
        return False

def test_individual_analyzers():
    """Test individual analyzers"""
    print("\n🔍 Testing Individual Analyzers...")
    
    data = create_simple_data()
    
    analyzers_to_test = [
        ('MA6', 'core.ma6_analyzer', 'MA6Analyzer'),
        ('Volume', 'core.volume_analyzer', 'VolumeAnalyzer'),
        ('Vortex', 'core.vortex_analysis', 'VortexAnalyzer'),
        ('Momentum', 'core.momentum', 'MomentumAnalyzer'),
        ('Trap Candle', 'core.trap_candle', 'TrapCandleAnalyzer'),
    ]
    
    success_count = 0
    
    for name, module_name, class_name in analyzers_to_test:
        try:
            from core.settings import Settings
            settings = Settings()
            
            # Dynamic import
            module = __import__(module_name, fromlist=[class_name])
            analyzer_class = getattr(module, class_name)
            analyzer = analyzer_class(settings)
            
            # Run analysis
            result = analyzer.analyze(data)
            
            if isinstance(result, dict) and 'score' in result:
                direction = result.get('direction', 'UNKNOWN')
                confidence = result.get('confidence', 0)
                score = result.get('score', 0)
                print(f"✅ {name}: {direction} (Score: {score:.2f}, Conf: {confidence:.2f})")
                success_count += 1
            else:
                print(f"❌ {name}: Invalid result format")
                
        except Exception as e:
            print(f"❌ {name}: {e}")
    
    print(f"\n📊 Individual Tests: {success_count}/{len(analyzers_to_test)} passed")
    return success_count == len(analyzers_to_test)

def test_complementary_engine():
    """Test complementary engine"""
    print("\n🔍 Testing Complementary Engine...")
    
    try:
        from core.complementary_engine import ComplementaryEngine
        from core.settings import Settings
        
        # Initialize
        settings = Settings()
        engine = ComplementaryEngine(settings)
        
        # Create test data
        data = create_simple_data()
        
        # Mock primary results
        primary_results = {
            'ma6': {'direction': 'UP', 'confidence': 0.7, 'score': 0.6},
            'momentum': {'direction': 'UP', 'confidence': 0.8, 'score': 0.7},
            'vortex': {'direction': 'NEUTRAL', 'confidence': 0.5, 'score': 0.5}
        }
        
        # Mock account data
        account_data = {
            'balance': 100.0,
            'daily_trades': 5,
            'daily_pnl': 2.5,
            'performance': {'consecutive_losses': 1, 'win_rate': 0.75}
        }
        
        # Run complementary analysis
        comp_results = engine.run_all_complementary_analyses(
            data, primary_results, account_data, None
        )
        
        print(f"✅ Complementary Analysis Success!")
        print(f"   - Filters Analyzed: {len(comp_results)}")
        
        # Test final decision
        final_decision = engine.calculate_final_trading_decision(primary_results, comp_results)
        
        print(f"✅ Final Decision: {final_decision['final_decision']}")
        print(f"   - Allow Trading: {final_decision['allow_trading']}")
        print(f"   - Final Score: {final_decision['final_score']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Complementary Engine Error: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print("\n🔍 Testing Configuration Loading...")
    
    try:
        from core.settings import Settings
        
        settings = Settings()
        
        print(f"✅ Settings loaded successfully!")
        print(f"   - Config file exists: {settings.config_file.exists()}")
        print(f"   - Analysis indicators: {len(getattr(settings.analysis, 'enabled_indicators', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings Error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 VIP BIG BANG Enterprise - Simple System Test")
    print("=" * 60)
    
    tests = [
        ("Configuration Loading", test_config_loading),
        ("Primary Analysis Engine", test_primary_engine),
        ("Individual Analyzers", test_individual_analyzers),
        ("Complementary Engine", test_complementary_engine),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System is working correctly! 🚀")
        print("✅ VIP BIG BANG Enterprise is ready for production!")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("🔧 System needs fixes before production use.")
    
    return passed == total

if __name__ == "__main__":
    main()
