@echo off
chcp 65001 >nul
title 🚀 VIP BIG BANG - Start Real Trading

REM ===================================================================
REM 🚀 VIP BIG BANG - Start Real Trading
REM راه‌اندازی تریدینگ واقعی با Quotex
REM ===================================================================

color 0E
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 VIP BIG BANG - REAL TRADING LAUNCHER 🚀             ██
echo ██                                                            ██
echo ██    💰 Real Money Trading 💰                               ██
echo ██    📊 Live Quotex Data 📊                                 ██
echo ██    ⚡ Professional System ⚡                              ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🎯 REAL TRADING MODE - LIVE MONEY
echo ═══════════════════════════════════════
echo.

REM Warning for real trading
echo ⚠️  WARNING: This is REAL TRADING mode
echo 💰 You will be trading with REAL MONEY
echo 📊 Make sure you understand the risks
echo.
set /p confirm="Are you sure you want to continue? (y/n): "
if /i not "%confirm%"=="y" (
    echo ❌ Trading cancelled by user
    pause
    exit /b 0
)

echo.
echo 🚀 Starting VIP BIG BANG for Real Trading...
echo ═══════════════════════════════════════

REM Activate virtual environment
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
    echo ✅ Virtual environment activated
) else (
    echo ⚠️  No virtual environment found, using system Python
)

REM Quick dependency check
echo 📦 Checking dependencies...
python -c "import playwright, pandas, cryptography" 2>nul
if %errorLevel% neq 0 (
    echo 🔧 Installing missing dependencies...
    pip install playwright pandas cryptography --quiet
    python -m playwright install chromium --quiet
)
echo ✅ Dependencies ready

echo.
echo 📋 SETUP CHECKLIST:
echo ═══════════════════════════════════════
echo.
echo ✅ 1. VIP BIG BANG System: Ready
echo ⚠️  2. Chrome Extension: Please install manually
echo ⚠️  3. Quotex Account: Please login manually
echo ⚠️  4. Trading Page: Please navigate manually
echo.

echo 🌐 CHROME EXTENSION SETUP:
echo ────────────────────────────────────────
echo 1. Open Chrome
echo 2. Go to: chrome://extensions/
echo 3. Enable "Developer mode"
echo 4. Click "Load unpacked"
echo 5. Select: chrome_extension folder
echo.

echo 📊 QUOTEX CONNECTION SETUP:
echo ────────────────────────────────────────
echo 1. Open Chrome
echo 2. Go to: https://qxbroker.com/en/trade
echo 3. Login to your account
echo 4. Select an asset (EUR/USD, etc.)
echo 5. Click extension icon and "Start Extraction"
echo.

set /p ready="Have you completed the setup? (y/n): "
if /i not "%ready%"=="y" (
    echo 💡 Please complete the setup first
    echo 📋 Read REAL_CONNECTION_GUIDE.md for detailed instructions
    pause
    exit /b 0
)

echo.
echo 🚀 Launching VIP BIG BANG Real Trading System...
echo ⚡ Starting Quantum Trading Engine...
echo 📊 Connecting to Real Data Sources...
echo.

REM Launch VIP BIG BANG
python main.py
if %errorLevel% neq 0 (
    echo.
    echo ⚠️  Main launcher failed, trying alternative...
    python vip_real_quotex_main.py
    if %errorLevel% neq 0 (
        echo.
        echo ❌ All launch methods failed
        echo 🔧 Please check your setup and try again
        echo 📋 Read REAL_CONNECTION_GUIDE.md for help
        pause
        exit /b 1
    )
)

echo.
echo ✅ VIP BIG BANG Real Trading System launched!
echo.
echo 📊 NEXT STEPS:
echo ═══════════════════════════════════════
echo 1. Check that real data is showing in VIP BIG BANG
echo 2. Verify your Quotex account balance
echo 3. Set your trading parameters
echo 4. Start trading with caution
echo.
echo 💡 IMPORTANT REMINDERS:
echo ────────────────────────────────────────
echo • This is REAL MONEY trading
echo • Start with small amounts
echo • Monitor your trades carefully
echo • Use stop-loss and take-profit
echo • Never risk more than you can afford to lose
echo.

pause
