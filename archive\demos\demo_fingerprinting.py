"""
🧪 DEMO - VIP BIG BANG Fingerprinting System
🔍 نمایش سیستم تشخیص سخت‌افزار و مرورگر
"""

import platform
import json
from datetime import datetime

def show_system_info():
    """نمایش اطلاعات سیستم"""
    print("🖥️ === SYSTEM INFORMATION ===")
    print(f"System: {platform.system()}")
    print(f"Release: {platform.release()}")
    print(f"Platform: {platform.platform()}")
    print(f"Architecture: {platform.architecture()}")
    print(f"Machine: {platform.machine()}")
    print(f"Processor: {platform.processor()}")
    print(f"Python: {platform.python_version()}")

def detect_vm():
    """تشخیص محیط مجازی"""
    print("\n🔍 === VM DETECTION ===")
    
    vm_indicators = []
    confidence = 0
    
    # بررسی پردازنده
    processor = platform.processor().lower()
    if "virtual" in processor:
        vm_indicators.append("Virtual processor detected")
        confidence += 30
    if "vmware" in processor:
        vm_indicators.append("VMware detected in processor")
        confidence += 40
    if "virtualbox" in processor:
        vm_indicators.append("VirtualBox detected in processor")
        confidence += 40
    
    # بررسی پلتفرم
    platform_info = platform.platform().lower()
    if "vmware" in platform_info:
        vm_indicators.append("VMware detected in platform")
        confidence += 35
    if "virtualbox" in platform_info:
        vm_indicators.append("VirtualBox detected in platform")
        confidence += 35
    
    is_vm = confidence >= 50
    
    print(f"VM Detected: {'Yes' if is_vm else 'No'}")
    print(f"Confidence: {confidence}%")
    
    if vm_indicators:
        print("Indicators:")
        for indicator in vm_indicators:
            print(f"  - {indicator}")
    else:
        print("✅ No VM indicators found")
    
    return {"is_vm": is_vm, "confidence": confidence, "indicators": vm_indicators}

def generate_browser_script():
    """تولید اسکریپت مرورگر"""
    print("\n🌐 === BROWSER FINGERPRINT SCRIPT ===")
    
    script = """
// VIP BIG BANG Browser Fingerprinting
(function() {
    const fingerprint = {
        // Hardware
        deviceMemory: navigator.deviceMemory || 'unknown',
        hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
        
        // Screen
        screenWidth: screen.width,
        screenHeight: screen.height,
        colorDepth: screen.colorDepth,
        
        // Browser
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        
        // Timezone
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        
        // Automation Detection
        webdriver: navigator.webdriver,
        
        // GPU (WebGL)
        gpu: (function() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl');
                if (!gl) return 'WebGL not supported';
                
                const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                if (debugInfo) {
                    return {
                        vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
                        renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
                    };
                }
                return 'GPU info not available';
            } catch (e) {
                return 'GPU detection error';
            }
        })()
    };
    
    console.log('🔍 VIP BIG BANG Fingerprint:', fingerprint);
    window.VIP_FINGERPRINT = fingerprint;
    return fingerprint;
})();
"""
    
    # ذخیره اسکریپت
    with open("vip_fingerprint.js", "w", encoding="utf-8") as f:
        f.write(script)
    
    print("✅ Browser fingerprint script generated")
    print("📁 Saved to: vip_fingerprint.js")
    print(f"📊 Script size: {len(script)} characters")

def create_report():
    """ایجاد گزارش"""
    print("\n📄 === GENERATING REPORT ===")
    
    # جمع‌آوری اطلاعات
    system_info = {
        "system": platform.system(),
        "release": platform.release(),
        "platform": platform.platform(),
        "processor": platform.processor(),
        "python_version": platform.python_version()
    }
    
    vm_detection = detect_vm()
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "system": system_info,
        "vm_detection": vm_detection,
        "security_level": "HIGH_RISK" if vm_detection["is_vm"] else "SECURE",
        "recommendations": [
            "✅ System fingerprinting completed",
            "🌐 Use browser script for web detection",
            "🔄 Monitor system regularly"
        ]
    }
    
    if vm_detection["is_vm"]:
        report["recommendations"].insert(0, "⚠️ Virtual Machine detected - Enhanced security needed")
    
    # ذخیره گزارش
    with open("vip_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("✅ Report generated")
    print("📁 Saved to: vip_report.json")
    
    return report

def main():
    """تابع اصلی"""
    print("🚀 VIP BIG BANG - Fingerprinting System Demo")
    print("=" * 60)
    
    try:
        # نمایش اطلاعات سیستم
        show_system_info()
        
        # تشخیص VM
        vm_result = detect_vm()
        
        # تولید اسکریپت مرورگر
        generate_browser_script()
        
        # ایجاد گزارش
        report = create_report()
        
        # خلاصه نهایی
        print("\n🏆 === DEMO COMPLETED ===")
        print(f"Security Level: {report['security_level']}")
        print(f"VM Detected: {'Yes' if vm_result['is_vm'] else 'No'}")
        
        print("\n📁 Generated Files:")
        print("  📜 vip_fingerprint.js - Browser fingerprinting script")
        print("  📄 vip_report.json - Complete system report")
        
        print("\n🎯 Next Steps:")
        print("1. Use vip_fingerprint.js in web pages")
        print("2. Review vip_report.json for security analysis")
        print("3. Install full system: pip install psutil wmi cpuinfo")
        print("4. Run complete test: python quick_test_fingerprinting.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Demo completed successfully!")
    else:
        print("\n❌ Demo failed!")
