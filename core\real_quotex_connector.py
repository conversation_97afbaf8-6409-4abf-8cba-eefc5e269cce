"""
🔗 VIP BIG BANG - Real Quotex Connection
💎 Direct connection to your actual Quotex account
🚀 Live trading with real balance and trades
"""

import asyncio
import websockets
import json
import time
import threading
import requests
from datetime import datetime
from typing import Dict, List, Callable, Optional
import logging

class RealQuotexConnector:
    """
    🔗 Real Quotex Connection System
    
    Features:
    - Direct connection to Quotex platform
    - Real account balance
    - Live trading execution
    - Real-time price data
    - Account synchronization
    """
    
    def __init__(self):
        self.logger = logging.getLogger("RealQuotexConnector")
        
        # Connection settings
        self.quotex_urls = [
            "https://quotex.io",
            "https://qxbroker.com",
            "https://quotex.com"
        ]
        
        self.websocket_urls = [
            "wss://ws.quotex.io/socket.io/?EIO=4&transport=websocket",
            "wss://quotex.io/socket.io/?EIO=4&transport=websocket"
        ]
        
        # Connection state
        self.is_connected = False
        self.websocket = None
        self.session = None
        self.user_token = None
        self.account_id = None
        
        # Account data
        self.real_balance = 0.0
        self.demo_balance = 0.0
        self.is_demo_mode = True
        self.open_trades = []
        self.trade_history = []
        
        # Real-time data
        self.current_prices = {}
        self.market_data_callbacks = []
        self.trade_result_callbacks = []
        self.connection_callbacks = []
        
        # Setup session
        self._setup_session()
    
    def _setup_session(self):
        """Setup HTTP session with proper headers"""
        self.session = requests.Session()
        
        # Headers to mimic real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
    
    async def connect_to_quotex(self, email: str = None, password: str = None) -> bool:
        """
        Connect to real Quotex account
        
        Args:
            email: Your Quotex email (optional if using browser session)
            password: Your Quotex password (optional if using browser session)
        
        Returns:
            bool: True if connected successfully
        """
        try:
            self.logger.info("🔗 Connecting to Real Quotex Account...")
            
            # Method 1: Try to use existing browser session
            if await self._connect_via_browser_session():
                return True
            
            # Method 2: Try direct login if credentials provided
            if email and password:
                if await self._connect_via_login(email, password):
                    return True
            
            # Method 3: Try Chrome extension bridge
            if await self._connect_via_extension():
                return True
            
            self.logger.error("❌ Failed to connect to Quotex")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Connection error: {e}")
            return False
    
    async def _connect_via_browser_session(self) -> bool:
        """Try to connect using existing browser session"""
        try:
            self.logger.info("🌐 Attempting browser session connection...")
            
            # Try to access Quotex main page
            for url in self.quotex_urls:
                try:
                    response = self.session.get(f"{url}/api/v1/cabinets/digest", timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if 'user' in data and data['user']:
                            # Successfully connected with existing session
                            self._process_account_data(data)
                            
                            # Connect to WebSocket
                            if await self._connect_websocket():
                                self.is_connected = True
                                self.logger.info("✅ Connected via browser session")
                                self._notify_connection_status(True)
                                return True
                    
                except Exception as e:
                    self.logger.debug(f"Browser session attempt failed for {url}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Browser session connection error: {e}")
            return False
    
    async def _connect_via_login(self, email: str, password: str) -> bool:
        """Connect via direct login"""
        try:
            self.logger.info("🔐 Attempting direct login...")
            
            # Get login page first
            login_url = f"{self.quotex_urls[0]}/api/v1/auth/login"
            
            login_data = {
                'email': email,
                'password': password,
                'remember': True
            }
            
            response = self.session.post(login_url, json=login_data, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    self.user_token = data.get('token')
                    
                    # Update session headers
                    self.session.headers.update({
                        'Authorization': f'Bearer {self.user_token}'
                    })
                    
                    # Get account data
                    if await self._fetch_account_data():
                        # Connect to WebSocket
                        if await self._connect_websocket():
                            self.is_connected = True
                            self.logger.info("✅ Connected via direct login")
                            self._notify_connection_status(True)
                            return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Direct login error: {e}")
            return False
    
    async def _connect_via_extension(self) -> bool:
        """Connect via Chrome extension bridge"""
        try:
            self.logger.info("🌐 Attempting Chrome extension connection...")
            
            # Try to connect to local extension bridge
            try:
                # Check if extension is running
                response = requests.get("http://localhost:8765/status", timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get('quotex_connected'):
                        # Extension is connected to Quotex
                        self.account_id = data.get('account_id')
                        self.real_balance = data.get('real_balance', 0)
                        self.demo_balance = data.get('demo_balance', 0)
                        self.is_demo_mode = data.get('is_demo_mode', True)
                        
                        # Start extension data bridge
                        self._start_extension_bridge()
                        
                        self.is_connected = True
                        self.logger.info("✅ Connected via Chrome extension")
                        self._notify_connection_status(True)
                        return True
                
            except requests.exceptions.ConnectionError:
                self.logger.debug("Extension bridge not available")
            
            return False
            
        except Exception as e:
            self.logger.error(f"Extension connection error: {e}")
            return False
    
    async def _connect_websocket(self) -> bool:
        """Connect to Quotex WebSocket"""
        try:
            for ws_url in self.websocket_urls:
                try:
                    self.websocket = await websockets.connect(
                        ws_url,
                        extra_headers=self.session.headers,
                        timeout=10
                    )
                    
                    # Start WebSocket listener
                    asyncio.create_task(self._websocket_listener())
                    
                    self.logger.info(f"✅ WebSocket connected: {ws_url}")
                    return True
                    
                except Exception as e:
                    self.logger.debug(f"WebSocket connection failed for {ws_url}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"WebSocket connection error: {e}")
            return False
    
    async def _websocket_listener(self):
        """Listen to WebSocket messages"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self._process_websocket_message(data)
                    
                except json.JSONDecodeError:
                    # Handle non-JSON messages
                    if message.startswith('42'):
                        # Socket.IO message format
                        try:
                            json_part = message[2:]
                            data = json.loads(json_part)
                            await self._process_websocket_message(data)
                        except:
                            pass
                    
                except Exception as e:
                    self.logger.error(f"WebSocket message processing error: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("⚠️ WebSocket connection closed")
            self.is_connected = False
            self._notify_connection_status(False)
            
        except Exception as e:
            self.logger.error(f"WebSocket listener error: {e}")
    
    async def _process_websocket_message(self, data):
        """Process WebSocket message"""
        try:
            if isinstance(data, list) and len(data) > 1:
                message_type = data[0]
                message_data = data[1] if len(data) > 1 else {}
                
                if message_type == "price":
                    # Price update
                    await self._handle_price_update(message_data)
                    
                elif message_type == "trade_result":
                    # Trade result
                    await self._handle_trade_result(message_data)
                    
                elif message_type == "balance":
                    # Balance update
                    await self._handle_balance_update(message_data)
                    
        except Exception as e:
            self.logger.error(f"WebSocket message processing error: {e}")
    
    async def _handle_price_update(self, data):
        """Handle price update"""
        try:
            asset = data.get('asset')
            price = data.get('price')
            
            if asset and price:
                self.current_prices[asset] = {
                    'price': price,
                    'timestamp': datetime.now(),
                    'bid': data.get('bid'),
                    'ask': data.get('ask')
                }
                
                # Notify callbacks
                for callback in self.market_data_callbacks:
                    try:
                        callback(self.current_prices[asset])
                    except Exception as e:
                        self.logger.error(f"Market data callback error: {e}")
                        
        except Exception as e:
            self.logger.error(f"Price update handling error: {e}")
    
    async def _handle_trade_result(self, data):
        """Handle trade result"""
        try:
            trade_id = data.get('trade_id')
            result = data.get('result')  # 'win', 'loss'
            profit = data.get('profit', 0)
            
            # Update balance
            if result == 'win':
                if self.is_demo_mode:
                    self.demo_balance += profit
                else:
                    self.real_balance += profit
            else:
                # Loss is already deducted when trade was placed
                pass
            
            # Notify callbacks
            for callback in self.trade_result_callbacks:
                try:
                    callback({
                        'trade_id': trade_id,
                        'result': result,
                        'profit': profit,
                        'balance': self.demo_balance if self.is_demo_mode else self.real_balance
                    })
                except Exception as e:
                    self.logger.error(f"Trade result callback error: {e}")
                    
        except Exception as e:
            self.logger.error(f"Trade result handling error: {e}")
    
    async def _handle_balance_update(self, data):
        """Handle balance update"""
        try:
            self.real_balance = data.get('real_balance', self.real_balance)
            self.demo_balance = data.get('demo_balance', self.demo_balance)
            self.is_demo_mode = data.get('is_demo_mode', self.is_demo_mode)
            
        except Exception as e:
            self.logger.error(f"Balance update handling error: {e}")
    
    async def _fetch_account_data(self) -> bool:
        """Fetch account data"""
        try:
            response = self.session.get(f"{self.quotex_urls[0]}/api/v1/cabinets/digest", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                self._process_account_data(data)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Account data fetch error: {e}")
            return False
    
    def _process_account_data(self, data):
        """Process account data"""
        try:
            user_data = data.get('user', {})
            self.account_id = user_data.get('id')
            
            # Get balances
            balances = data.get('balances', {})
            self.real_balance = balances.get('real', 0)
            self.demo_balance = balances.get('demo', 0)
            
            # Get current mode
            self.is_demo_mode = data.get('demo_mode', True)
            
            self.logger.info(f"💰 Account loaded - Demo: ${self.demo_balance:.2f}, Real: ${self.real_balance:.2f}")
            
        except Exception as e:
            self.logger.error(f"Account data processing error: {e}")
    
    def _start_extension_bridge(self):
        """Start Chrome extension data bridge"""
        def bridge_loop():
            while self.is_connected:
                try:
                    # Get data from extension
                    response = requests.get("http://localhost:8765/data", timeout=5)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        # Update prices
                        if 'prices' in data:
                            for asset, price_data in data['prices'].items():
                                self.current_prices[asset] = price_data
                                
                                # Notify callbacks
                                for callback in self.market_data_callbacks:
                                    try:
                                        callback(price_data)
                                    except:
                                        pass
                        
                        # Update balance
                        if 'balance' in data:
                            self.real_balance = data['balance'].get('real', self.real_balance)
                            self.demo_balance = data['balance'].get('demo', self.demo_balance)
                            self.is_demo_mode = data['balance'].get('is_demo', self.is_demo_mode)
                    
                    time.sleep(1)  # Update every second
                    
                except Exception as e:
                    self.logger.debug(f"Extension bridge error: {e}")
                    time.sleep(5)
        
        bridge_thread = threading.Thread(target=bridge_loop, daemon=True)
        bridge_thread.start()
    
    async def place_trade(self, asset: str, direction: str, amount: float, duration: int) -> Dict:
        """
        Place real trade on Quotex
        
        Args:
            asset: Trading asset (e.g., "EUR/USD OTC")
            direction: "CALL" or "PUT"
            amount: Trade amount in USD
            duration: Trade duration in seconds
        
        Returns:
            Dict: Trade result
        """
        try:
            if not self.is_connected:
                return {"success": False, "error": "Not connected to Quotex"}
            
            # Prepare trade data
            trade_data = {
                "asset": asset,
                "direction": direction.upper(),
                "amount": amount,
                "duration": duration,
                "timestamp": int(time.time())
            }
            
            # Method 1: Try WebSocket trade
            if self.websocket:
                try:
                    await self.websocket.send(json.dumps([
                        "trade",
                        trade_data
                    ]))
                    
                    self.logger.info(f"🎯 Trade placed via WebSocket: {asset} {direction} ${amount}")
                    return {"success": True, "method": "websocket", "trade_data": trade_data}
                    
                except Exception as e:
                    self.logger.error(f"WebSocket trade error: {e}")
            
            # Method 2: Try HTTP API
            try:
                trade_url = f"{self.quotex_urls[0]}/api/v1/trades"
                response = self.session.post(trade_url, json=trade_data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if result.get('success'):
                        self.logger.info(f"🎯 Trade placed via API: {asset} {direction} ${amount}")
                        return {"success": True, "method": "api", "trade_data": trade_data, "trade_id": result.get('trade_id')}
                
            except Exception as e:
                self.logger.error(f"API trade error: {e}")
            
            # Method 3: Try extension bridge
            try:
                response = requests.post("http://localhost:8765/trade", json=trade_data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if result.get('success'):
                        self.logger.info(f"🎯 Trade placed via Extension: {asset} {direction} ${amount}")
                        return {"success": True, "method": "extension", "trade_data": trade_data}
                
            except Exception as e:
                self.logger.debug(f"Extension trade error: {e}")
            
            return {"success": False, "error": "All trade methods failed"}
            
        except Exception as e:
            self.logger.error(f"Trade placement error: {e}")
            return {"success": False, "error": str(e)}
    
    def get_current_balance(self) -> Dict:
        """Get current account balance"""
        return {
            "real_balance": self.real_balance,
            "demo_balance": self.demo_balance,
            "is_demo_mode": self.is_demo_mode,
            "current_balance": self.demo_balance if self.is_demo_mode else self.real_balance
        }
    
    def get_current_prices(self) -> Dict:
        """Get current market prices"""
        return self.current_prices.copy()
    
    def switch_to_demo(self):
        """Switch to demo mode"""
        self.is_demo_mode = True
        self.logger.info("🎮 Switched to DEMO mode")
    
    def switch_to_live(self):
        """Switch to live mode"""
        self.is_demo_mode = False
        self.logger.info("💰 Switched to LIVE mode")
    
    def add_market_data_callback(self, callback: Callable):
        """Add market data callback"""
        self.market_data_callbacks.append(callback)
    
    def add_trade_result_callback(self, callback: Callable):
        """Add trade result callback"""
        self.trade_result_callbacks.append(callback)
    
    def add_connection_callback(self, callback: Callable):
        """Add connection status callback"""
        self.connection_callbacks.append(callback)
    
    def _notify_connection_status(self, connected: bool):
        """Notify connection status change"""
        for callback in self.connection_callbacks:
            try:
                callback(connected)
            except Exception as e:
                self.logger.error(f"Connection callback error: {e}")
    
    async def disconnect(self):
        """Disconnect from Quotex"""
        try:
            self.is_connected = False
            
            if self.websocket:
                await self.websocket.close()
            
            self.logger.info("🔌 Disconnected from Quotex")
            self._notify_connection_status(False)
            
        except Exception as e:
            self.logger.error(f"Disconnect error: {e}")
    
    def get_connection_status(self) -> Dict:
        """Get connection status"""
        return {
            "connected": self.is_connected,
            "account_id": self.account_id,
            "demo_mode": self.is_demo_mode,
            "real_balance": self.real_balance,
            "demo_balance": self.demo_balance
        }
