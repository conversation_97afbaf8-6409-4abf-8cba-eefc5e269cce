#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 VIP BIG BANG - Perfect UI Recreation
بازسازی کامل و دقیق UI با Copilot
"""

import sys
import os
import random
import math
from datetime import datetime
from typing import Dict, List, Optional

# Fix encoding
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

os.environ['QT_QPA_PLATFORM'] = 'windows'

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class PerfectCard(QFrame):
    """کارت کامل مطابق تصویر"""
    
    clicked = Signal()
    
    def __init__(self, width=200, height=150):
        super().__init__()
        self.setFixedSize(width, height)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # استایل دقیق مطابق تصویر
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 0.95),
                    stop:1 rgba(124, 58, 237, 0.85));
                border: 2px solid rgba(147, 51, 234, 0.7);
                border-radius: 20px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 1.0),
                    stop:1 rgba(124, 58, 237, 0.95));
                border: 2px solid rgba(147, 51, 234, 0.9);
            }
        """)
        
        # Shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(124, 58, 237, 80))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

class PerfectButton(QPushButton):
    """دکمه کامل مطابق تصویر"""
    
    def __init__(self, text="", button_type="default"):
        super().__init__(text)
        self.button_type = button_type
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        if button_type == "buy":
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: none;
                    border-radius: 20px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: bold;
                    font-family: 'Segoe UI';
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #34d399, stop:1 #10b981);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #059669, stop:1 #047857);
                }
            """)
        elif button_type == "sell":
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #ef4444, stop:1 #dc2626);
                    color: white;
                    border: none;
                    border-radius: 20px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: bold;
                    font-family: 'Segoe UI';
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #f87171, stop:1 #ef4444);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #dc2626, stop:1 #b91c1c);
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background: rgba(55, 65, 81, 0.8);
                    color: white;
                    border: 1px solid rgba(107, 114, 128, 0.5);
                    border-radius: 15px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Segoe UI';
                }
                QPushButton:hover {
                    background: rgba(124, 58, 237, 0.8);
                    border: 1px solid rgba(147, 51, 234, 0.8);
                }
            """)

class PerfectToggle(QWidget):
    """Toggle کامل مطابق تصویر"""
    
    toggled = Signal(bool)
    
    def __init__(self, checked=True):
        super().__init__()
        self.checked = checked
        self.setFixedSize(60, 30)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Background
        if self.checked:
            painter.setBrush(QBrush(QColor(16, 185, 129)))  # Green
        else:
            painter.setBrush(QBrush(QColor(107, 114, 128)))  # Gray
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(0, 0, 60, 30, 15, 15)
        
        # Circle
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        if self.checked:
            painter.drawEllipse(32, 3, 24, 24)
        else:
            painter.drawEllipse(4, 3, 24, 24)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.checked = not self.checked
            self.toggled.emit(self.checked)
            self.update()
        super().mousePressEvent(event)

class VIPPerfectUI(QMainWindow):
    """
    🎯 VIP BIG BANG - Perfect UI
    UI کامل و دقیق مطابق تصویر
    """
    
    def __init__(self):
        super().__init__()
        
        # تنظیمات پنجره دقیق مطابق تصویر
        self.setWindowTitle("🎮 VIP BIG BANG")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        
        # اعمال تم دقیق
        self.apply_perfect_theme()
        
        # ساخت UI دقیق
        self.setup_perfect_ui()
        
        # سیستم‌های زنده
        self.setup_live_systems()
        
        print("🎯 VIP Perfect UI launched!")
    
    def apply_perfect_theme(self):
        """اعمال تم کامل مطابق تصویر"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4c1d95, stop:0.2 #5b21b6, 
                    stop:0.5 #6d28d9, stop:0.8 #7c3aed, stop:1 #8b5cf6);
            }
            QWidget {
                background: transparent;
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel {
                color: white;
                background: transparent;
            }
        """)
    
    def setup_perfect_ui(self):
        """ساخت UI کامل مطابق تصویر"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # هدر کامل مطابق تصویر
        self.create_perfect_header(main_layout)
        
        # محتوای اصلی کامل مطابق تصویر
        self.create_perfect_content(main_layout)
    
    def create_perfect_header(self, layout):
        """ساخت هدر کامل مطابق تصویر"""
        header_layout = QHBoxLayout()
        
        # بخش چپ: آواتار + عنوان
        left_section = QHBoxLayout()
        
        # آواتار دقیق مطابق تصویر
        avatar_frame = QFrame()
        avatar_frame.setFixedSize(60, 60)
        avatar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(88, 28, 135, 0.95));
                border: 2px solid rgba(147, 51, 234, 0.7);
                border-radius: 30px;
            }
        """)
        
        avatar_layout = QVBoxLayout(avatar_frame)
        avatar_layout.setContentsMargins(0, 0, 0, 0)
        
        avatar_emoji = QLabel("😊")
        avatar_emoji.setFont(QFont("Segoe UI Emoji", 24))
        avatar_emoji.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar_emoji.setStyleSheet("background: transparent;")
        avatar_layout.addWidget(avatar_emoji)
        
        left_section.addWidget(avatar_frame)
        
        # عنوان دقیق مطابق تصویر
        title_section = QVBoxLayout()
        
        manual_trad = QLabel("Manual Trad")
        manual_trad.setFont(QFont("Segoe UI", 10))
        manual_trad.setStyleSheet("color: rgba(255,255,255,0.7);")
        title_section.addWidget(manual_trad)
        
        vip_title = QLabel("VIP BIG BANG")
        vip_title.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        vip_title.setStyleSheet("color: white;")
        title_section.addWidget(vip_title)
        
        left_section.addLayout(title_section)
        header_layout.addLayout(left_section)
        
        # بخش وسط: جفت ارزها دقیق مطابق تصویر
        center_section = QHBoxLayout()
        
        # BUG/USD با تیک سبز
        bug_usd = PerfectButton("✅ BUG/USD", "default")
        bug_usd.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #10b981, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 15px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        center_section.addWidget(bug_usd)
        
        # سایر جفت ارزها
        for pair in ["GBP/USD", "EUR/JPY", "LIVE"]:
            pair_btn = PerfectButton(pair, "default")
            center_section.addWidget(pair_btn)
        
        header_layout.addLayout(center_section)
        
        # بخش راست: دکمه‌های حالت + خرید/فروش
        right_section = QHBoxLayout()
        
        # دکمه‌های حالت
        mode_layout = QHBoxLayout()
        for mode in ["OTC", "LIVE", "DEMO"]:
            mode_btn = PerfectButton(mode, "default")
            mode_layout.addWidget(mode_btn)
        
        right_section.addLayout(mode_layout)
        
        # بخش خرید/فروش دقیق مطابق تصویر
        buy_sell_section = QVBoxLayout()
        
        buy_label = QLabel("BUY")
        buy_label.setFont(QFont("Segoe UI", 10))
        buy_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        buy_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        buy_sell_section.addWidget(buy_label)
        
        buttons_row = QHBoxLayout()
        
        self.buy_btn = PerfectButton("BUY", "buy")
        self.sell_btn = PerfectButton("SELL", "sell")
        
        buttons_row.addWidget(self.buy_btn)
        buttons_row.addWidget(self.sell_btn)
        
        buy_sell_section.addLayout(buttons_row)
        right_section.addLayout(buy_sell_section)
        
        header_layout.addLayout(right_section)
        layout.addLayout(header_layout)
    
    def create_perfect_content(self, layout):
        """ساخت محتوای کامل مطابق تصویر"""
        content_layout = QHBoxLayout()
        
        # پنل چپ کامل
        left_panel = self.create_perfect_left_panel()
        content_layout.addWidget(left_panel)
        
        # پنل مرکزی (چارت) کامل
        center_panel = self.create_perfect_center_panel()
        content_layout.addWidget(center_panel)
        
        # پنل راست کامل
        right_panel = self.create_perfect_right_panel()
        content_layout.addWidget(right_panel)
        
        # نسبت‌های دقیق مطابق تصویر
        content_layout.setStretch(0, 1)  # چپ: 25%
        content_layout.setStretch(1, 2)  # وسط: 50%
        content_layout.setStretch(2, 1)  # راست: 25%
        
        layout.addLayout(content_layout)

    def create_perfect_left_panel(self):
        """ساخت پنل چپ کامل مطابق تصویر"""
        panel = QWidget()
        panel.setFixedWidth(250)
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # کارت Manual Trading دقیق مطابق تصویر
        manual_card = PerfectCard(220, 120)
        manual_layout = QVBoxLayout(manual_card)
        manual_layout.setContentsMargins(15, 15, 15, 15)

        # هدر با آیکون
        header_layout = QHBoxLayout()
        icon = QLabel("🖱️")
        icon.setFont(QFont("Segoe UI Emoji", 16))
        header_layout.addWidget(icon)

        title = QLabel("Manual Trading")
        title.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        header_layout.addWidget(title)
        header_layout.addStretch()

        manual_layout.addLayout(header_layout)

        # Toggle switch
        self.manual_toggle = PerfectToggle(True)
        manual_layout.addWidget(self.manual_toggle)
        manual_layout.addStretch()

        layout.addWidget(manual_card)

        # کارت Account Summary دقیق مطابق تصویر
        account_card = PerfectCard(220, 100)
        account_layout = QVBoxLayout(account_card)
        account_layout.setContentsMargins(15, 15, 15, 15)

        account_title = QLabel("Account Summary")
        account_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        account_layout.addWidget(account_title)

        self.balance_label = QLabel("$1251,76")
        self.balance_label.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        self.balance_label.setStyleSheet("color: #10b981;")
        account_layout.addWidget(self.balance_label)
        account_layout.addStretch()

        layout.addWidget(account_card)

        # کارت AutoTrade دقیق مطابق تصویر
        autotrade_card = PerfectCard(220, 120)
        autotrade_layout = QVBoxLayout(autotrade_card)
        autotrade_layout.setContentsMargins(15, 15, 15, 15)

        autotrade_title = QLabel("AutoTrade ON")
        autotrade_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        autotrade_title.setStyleSheet("color: #10b981;")
        autotrade_layout.addWidget(autotrade_title)

        # آمار معاملات
        trade_layout = QHBoxLayout()
        trade_label = QLabel("Trade $")
        trade_label.setFont(QFont("Segoe UI", 10))
        trade_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        trade_layout.addWidget(trade_label)

        trade_value = QLabel("+5")
        trade_value.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        trade_value.setStyleSheet("color: #10b981;")
        trade_layout.addWidget(trade_value)

        autotrade_layout.addLayout(trade_layout)

        profit_layout = QHBoxLayout()
        profit_label = QLabel("Profit / Loss")
        profit_label.setFont(QFont("Segoe UI", 10))
        profit_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        profit_layout.addWidget(profit_label)

        profit_value = QLabel("+10")
        profit_value.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        profit_value.setStyleSheet("color: #10b981;")
        profit_layout.addWidget(profit_value)

        autotrade_layout.addLayout(profit_layout)
        autotrade_layout.addStretch()

        layout.addWidget(autotrade_card)

        # کارت PulseBar دقیق مطابق تصویر
        pulsebar_card = PerfectCard(220, 140)
        pulsebar_layout = QVBoxLayout(pulsebar_card)
        pulsebar_layout.setContentsMargins(15, 15, 15, 15)

        pulsebar_title = QLabel("PulseBar")
        pulsebar_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        pulsebar_layout.addWidget(pulsebar_title)

        # نوارهای رنگی دقیق مطابق تصویر
        colors = ["#ef4444", "#f97316", "#eab308", "#22c55e"]
        for color in colors:
            bar = QWidget()
            bar.setFixedHeight(8)
            bar.setStyleSheet(f"""
                QWidget {{
                    background: {color};
                    border-radius: 4px;
                    margin: 2px 0;
                }}
            """)
            pulsebar_layout.addWidget(bar)

        pulsebar_layout.addStretch()
        layout.addWidget(pulsebar_card)

        # کارت Economic News دقیق مطابق تصویر
        news_card = PerfectCard(220, 100)
        news_layout = QVBoxLayout(news_card)
        news_layout.setContentsMargins(15, 15, 15, 15)

        news_title = QLabel("Economic News")
        news_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        news_layout.addWidget(news_title)

        news_icon = QLabel("📊")
        news_icon.setFont(QFont("Segoe UI Emoji", 24))
        news_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        news_layout.addWidget(news_icon)
        news_layout.addStretch()

        layout.addWidget(news_card)
        layout.addStretch()

        return panel

    def create_perfect_center_panel(self):
        """ساخت پنل مرکزی کامل مطابق تصویر"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # ناحیه چارت دقیق مطابق تصویر
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 0.95),
                    stop:1 rgba(124, 58, 237, 0.85));
                border: 2px solid rgba(147, 51, 234, 0.7);
                border-radius: 20px;
                padding: 20px;
            }
        """)

        chart_layout = QVBoxLayout(chart_frame)

        # بخش قیمت دقیق مطابق تصویر
        price_layout = QHBoxLayout()

        # آیکون زنگ
        bell = QLabel("🔔")
        bell.setFont(QFont("Segoe UI Emoji", 20))
        price_layout.addWidget(bell)

        price_layout.addStretch()

        # قیمت فعلی دقیق مطابق تصویر
        self.current_price = QLabel("1.07329")
        self.current_price.setFont(QFont("Segoe UI", 24, QFont.Weight.Bold))
        self.current_price.setStyleSheet("""
            QLabel {
                color: white;
                background: rgba(59, 130, 246, 0.8);
                padding: 8px 16px;
                border-radius: 10px;
            }
        """)
        price_layout.addWidget(self.current_price)

        price_layout.addStretch()

        # سطوح قیمت دقیق مطابق تصویر
        levels_layout = QVBoxLayout()
        levels = ["1.07325", "1.07320", "1.07320", "1.07330"]
        for level in levels:
            level_label = QLabel(level)
            level_label.setFont(QFont("Segoe UI", 10))
            level_label.setStyleSheet("color: rgba(255,255,255,0.7);")
            level_label.setAlignment(Qt.AlignmentFlag.AlignRight)
            levels_layout.addWidget(level_label)

        price_layout.addLayout(levels_layout)
        chart_layout.addLayout(price_layout)

        # شبیه‌سازی چارت
        chart_sim = QLabel("📈 LIVE CHART SIMULATION")
        chart_sim.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        chart_sim.setStyleSheet("color: rgba(255,255,255,0.8);")
        chart_sim.setAlignment(Qt.AlignmentFlag.AlignCenter)
        chart_sim.setFixedHeight(200)
        chart_layout.addWidget(chart_sim)

        # بخش VORTEX دقیق مطابق تصویر
        vortex_layout = QHBoxLayout()

        vortex_label = QLabel("VORTEX")
        vortex_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        vortex_layout.addWidget(vortex_label)

        vortex_layout.addStretch()

        vortex_value = QLabel("0.0436")
        vortex_value.setFont(QFont("Segoe UI", 12))
        vortex_value.setStyleSheet("color: rgba(255,255,255,0.7);")
        vortex_layout.addWidget(vortex_value)

        chart_layout.addLayout(vortex_layout)

        # موج VORTEX دقیق مطابق تصویر
        vortex_wave = QLabel("〰️〰️〰️〰️〰️")
        vortex_wave.setFont(QFont("Segoe UI Emoji", 16))
        vortex_wave.setAlignment(Qt.AlignmentFlag.AlignCenter)
        vortex_wave.setStyleSheet("color: #3b82f6;")
        chart_layout.addWidget(vortex_wave)

        layout.addWidget(chart_frame)

        # ناحیه سیگنال‌ها دقیق مطابق تصویر
        signals_frame = QFrame()
        signals_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(88, 28, 135, 0.95),
                    stop:1 rgba(124, 58, 237, 0.85));
                border: 2px solid rgba(147, 51, 234, 0.7);
                border-radius: 20px;
                padding: 15px;
            }
        """)

        signals_layout = QHBoxLayout(signals_frame)

        # آیکون اخبار اقتصادی
        econ_icon = QLabel("🎯")
        econ_icon.setFont(QFont("Segoe UI Emoji", 24))
        signals_layout.addWidget(econ_icon)

        # سیگنال‌های زنده دقیق مطابق تصویر
        live_signals_layout = QVBoxLayout()

        live_signals_title = QLabel("LIVE SIGNALS")
        live_signals_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        live_signals_layout.addWidget(live_signals_title)

        # سیگنال خرید
        buy_signal_layout = QHBoxLayout()
        buy_signal_label = QLabel("BUY")
        buy_signal_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        buy_signal_label.setStyleSheet("color: #10b981;")
        buy_signal_layout.addWidget(buy_signal_label)

        buy_percent = QLabel("71%")
        buy_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        buy_percent.setStyleSheet("color: #10b981;")
        buy_signal_layout.addWidget(buy_percent)

        live_signals_layout.addLayout(buy_signal_layout)

        # سیگنال فروش
        sell_signal_layout = QHBoxLayout()
        sell_signal_label = QLabel("71%")
        sell_signal_label.setFont(QFont("Segoe UI", 10))
        sell_signal_label.setStyleSheet("color: rgba(255,255,255,0.7);")
        sell_signal_layout.addWidget(sell_signal_label)

        sell_percent = QLabel("29%")
        sell_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        sell_percent.setStyleSheet("color: #f97316;")
        sell_signal_layout.addWidget(sell_percent)

        live_signals_layout.addLayout(sell_signal_layout)
        signals_layout.addLayout(live_signals_layout)

        # قدرت خریدار/فروشنده دقیق مطابق تصویر
        power_layout = QVBoxLayout()

        power_title = QLabel("Buyer/Seller Power")
        power_title.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        power_layout.addWidget(power_title)

        # نوار قدرت
        power_bar = QWidget()
        power_bar.setFixedHeight(20)
        power_bar.setStyleSheet("""
            QWidget {
                background: #374151;
                border-radius: 10px;
            }
        """)
        power_layout.addWidget(power_bar)

        # درصدهای قدرت
        power_percent_layout = QHBoxLayout()

        buyer_percent = QLabel("34%")
        buyer_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        buyer_percent.setStyleSheet("color: #10b981;")
        power_percent_layout.addWidget(buyer_percent)

        power_percent_layout.addStretch()

        seller_percent = QLabel("66%")
        seller_percent.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        seller_percent.setStyleSheet("color: #ef4444;")
        power_percent_layout.addWidget(seller_percent)

        power_layout.addLayout(power_percent_layout)
        signals_layout.addLayout(power_layout)

        # آیکون‌های عملکرد دقیق مطابق تصویر
        icons_layout = QHBoxLayout()
        action_icons = ["✅", "〰️", "📊", "⚡"]
        for icon in action_icons:
            icon_label = QLabel(icon)
            icon_label.setFont(QFont("Segoe UI Emoji", 20))
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setFixedSize(40, 40)
            icon_label.setStyleSheet("""
                QLabel {
                    background: rgba(255,255,255,0.1);
                    border-radius: 20px;
                    padding: 5px;
                }
            """)
            icons_layout.addWidget(icon_label)

        signals_layout.addLayout(icons_layout)
        layout.addWidget(signals_frame)

        return panel

    def create_perfect_right_panel(self):
        """ساخت پنل راست کامل مطابق تصویر"""
        panel = QWidget()
        panel.setFixedWidth(250)
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)

        # ردیف 1: AutoTrade و Confirm Mode
        row1_layout = QHBoxLayout()

        autotrade_card = self.create_perfect_right_card("🚀", "AutoTrade")
        confirm_card = self.create_perfect_right_card("✅", "Confirm Mode")

        row1_layout.addWidget(autotrade_card)
        row1_layout.addWidget(confirm_card)
        layout.addLayout(row1_layout)

        # ردیف 2: Confirm Mode و Heatmap
        row2_layout = QHBoxLayout()

        confirm2_card = self.create_perfect_right_card("🚀", "Confirm Mode")
        heatmap_card = self.create_perfect_right_card("🔥", "Heatmap")

        row2_layout.addWidget(confirm2_card)
        row2_layout.addWidget(heatmap_card)
        layout.addLayout(row2_layout)

        # ردیف 3: Economic News و Can
        row3_layout = QHBoxLayout()

        econ_news_card = self.create_perfect_right_card("📊", "Economic News")
        can_card = self.create_perfect_right_card("😊", "Can")

        row3_layout.addWidget(econ_news_card)
        row3_layout.addWidget(can_card)
        layout.addLayout(row3_layout)

        # ردیف 4: Settings و Secures
        row4_layout = QHBoxLayout()

        settings_card = self.create_perfect_right_card("⚙️", "Settings")
        secures_card = self.create_perfect_right_card("🔒", "Secures")

        row4_layout.addWidget(settings_card)
        row4_layout.addWidget(secures_card)
        layout.addLayout(row4_layout)

        layout.addStretch()
        return panel

    def create_perfect_right_card(self, icon, title):
        """ساخت کارت پنل راست دقیق مطابق تصویر"""
        card = PerfectCard(110, 100)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # آیکون دقیق مطابق تصویر
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI Emoji", 20))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)

        # عنوان دقیق مطابق تصویر
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)

        return card

    def setup_live_systems(self):
        """راه‌اندازی سیستم‌های زنده"""
        # تایمر برای آپدیت‌های زمان واقعی
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_live_data)
        self.update_timer.start(1000)  # هر ثانیه آپدیت

        # اتصال سیگنال‌ها
        self.buy_btn.clicked.connect(self.execute_buy_trade)
        self.sell_btn.clicked.connect(self.execute_sell_trade)
        self.manual_toggle.toggled.connect(self.toggle_manual_mode)

        print("✅ Live systems initialized")

    def update_live_data(self):
        """آپدیت داده‌های زنده دقیق مطابق تصویر"""
        # آپدیت قیمت با تغییرات واقعی
        current = float(self.current_price.text())
        change = random.uniform(-0.0001, 0.0001)
        new_price = current + change
        self.current_price.setText(f"{new_price:.5f}")

        # آپدیت موجودی با تغییرات واقعی
        current_balance = float(self.balance_label.text().replace('$', '').replace(',', ''))
        balance_change = random.uniform(-0.5, 1.0)
        new_balance = current_balance + balance_change
        self.balance_label.setText(f"${new_balance:,.2f}")

        # تغییر رنگ بر اساس سود/زیان
        if balance_change > 0:
            self.balance_label.setStyleSheet("color: #10b981;")
        else:
            self.balance_label.setStyleSheet("color: #ef4444;")

    def execute_buy_trade(self):
        """اجرای معامله خرید"""
        print("🟢 BUY trade executed!")
        # منطق معامله خرید

    def execute_sell_trade(self):
        """اجرای معامله فروش"""
        print("🔴 SELL trade executed!")
        # منطق معامله فروش

    def toggle_manual_mode(self, enabled):
        """تغییر حالت دستی"""
        if enabled:
            print("🖱️ Manual mode enabled")
        else:
            print("🤖 Auto mode enabled")

def main():
    """تابع اصلی"""
    app = QApplication(sys.argv)

    # تنظیمات برنامه دقیق مطابق تصویر
    app.setApplicationName("🎮 VIP BIG BANG")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("VIP Trading")

    # فونت سراسری دقیق مطابق تصویر
    font = QFont("Segoe UI", 10)
    app.setFont(font)

    # ساخت و نمایش پنجره اصلی
    window = VIPPerfectUI()
    window.show()

    # مرکز کردن پنجره روی صفحه
    screen = app.primaryScreen().geometry()
    window.move(
        (screen.width() - window.width()) // 2,
        (screen.height() - window.height()) // 2
    )

    print("🎯 VIP Perfect UI launched successfully!")

    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
