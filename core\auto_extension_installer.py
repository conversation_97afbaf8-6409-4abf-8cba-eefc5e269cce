"""
🔌 VIP BIG BANG AUTO EXTENSION INSTALLER
🚀 AUTOMATIC CHROME EXTENSION INSTALLATION
🕵️‍♂️ STEALTH INSTALLATION WITHOUT USER INTERVENTION
"""

import os
import json
import shutil
import subprocess
import winreg
import logging
from pathlib import Path
from typing import Dict, List, Optional

class AutoExtensionInstaller:
    """
    🔌 AUTO EXTENSION INSTALLER
    🚀 Automatically installs Chrome extension when app starts
    """
    
    def __init__(self):
        self.logger = logging.getLogger("AutoExtensionInstaller")
        
        # Chrome paths
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        # Extension paths
        self.extension_source = os.path.join(os.getcwd(), "chrome_extension")
        self.temp_extension_path = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Temp", "vip_big_bang_extension")
        
        # Chrome user data paths
        self.chrome_user_data_paths = [
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data"),
            os.path.expanduser(r"~\AppData\Local\Google\Chrome Beta\User Data"),
            os.path.expanduser(r"~\AppData\Local\Google\Chrome Dev\User Data")
        ]
        
        self.logger.info("🔌 Auto Extension Installer initialized")
    
    def find_chrome_executable(self) -> Optional[str]:
        """🔍 Find Chrome executable"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                self.logger.info(f"✅ Chrome found: {path}")
                return path
        
        self.logger.warning("⚠️ Chrome not found in standard locations")
        return None
    
    def find_chrome_user_data(self) -> Optional[str]:
        """📁 Find Chrome user data directory"""
        for path in self.chrome_user_data_paths:
            if os.path.exists(path):
                self.logger.info(f"✅ Chrome user data found: {path}")
                return path
        
        self.logger.warning("⚠️ Chrome user data not found")
        return None
    
    def prepare_extension_package(self) -> bool:
        """📦 Prepare extension package for installation"""
        try:
            if not os.path.exists(self.extension_source):
                self.logger.error(f"❌ Extension source not found: {self.extension_source}")
                return False
            
            # Create temp directory
            os.makedirs(self.temp_extension_path, exist_ok=True)
            
            # Copy extension files
            for item in os.listdir(self.extension_source):
                source_item = os.path.join(self.extension_source, item)
                dest_item = os.path.join(self.temp_extension_path, item)
                
                if os.path.isfile(source_item):
                    shutil.copy2(source_item, dest_item)
                elif os.path.isdir(source_item):
                    shutil.copytree(source_item, dest_item, dirs_exist_ok=True)
            
            self.logger.info(f"✅ Extension package prepared: {self.temp_extension_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to prepare extension package: {e}")
            return False
    
    def install_extension_via_registry(self) -> bool:
        """📝 Install extension via Windows Registry (Enterprise method)"""
        try:
            # Extension ID (generate consistent ID)
            extension_id = "vipbigbangultimate"
            
            # Registry path for Chrome extensions
            reg_path = r"SOFTWARE\Policies\Google\Chrome\ExtensionInstallForcelist"
            
            try:
                # Open/create registry key
                key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
                
                # Set extension to force install
                extension_entry = f"{extension_id};file:///{self.temp_extension_path.replace(os.sep, '/')}/manifest.json"
                winreg.SetValueEx(key, "1", 0, winreg.REG_SZ, extension_entry)
                
                winreg.CloseKey(key)
                
                self.logger.info("✅ Extension registered via Windows Registry")
                return True
                
            except PermissionError:
                self.logger.warning("⚠️ No admin rights for registry installation")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Registry installation failed: {e}")
            return False
    
    def install_extension_via_chrome_flags(self) -> bool:
        """🚀 Install extension via Chrome command line flags"""
        try:
            chrome_exe = self.find_chrome_executable()
            if not chrome_exe:
                return False
            
            # Chrome flags for extension loading (Fully Security-Safe)
            chrome_flags = [
                chrome_exe,
                f"--load-extension={self.temp_extension_path}",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
                "--enable-experimental-extension-apis",
                "--disable-features=VizDisplayCompositor,TranslateUI",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--no-first-run",
                "--no-default-browser-check"
            ]
            
            # Start Chrome with extension
            subprocess.Popen(chrome_flags, shell=False)
            
            self.logger.info("✅ Chrome started with extension loaded")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Chrome flags installation failed: {e}")
            return False
    
    def create_extension_shortcut(self) -> bool:
        """🔗 Create desktop shortcut with extension pre-loaded"""
        try:
            chrome_exe = self.find_chrome_executable()
            if not chrome_exe:
                return False
            
            # Desktop path
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            shortcut_path = os.path.join(desktop, "VIP BIG BANG Chrome.lnk")
            
            # Create batch file for launching Chrome with extension
            batch_content = f'''@echo off
"{chrome_exe}" --load-extension="{self.temp_extension_path}" --disable-web-security --allow-running-insecure-content --disable-features=VizDisplayCompositor
'''
            
            batch_path = os.path.join(self.temp_extension_path, "launch_chrome.bat")
            with open(batch_path, 'w') as f:
                f.write(batch_content)
            
            self.logger.info(f"✅ Chrome launcher created: {batch_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Shortcut creation failed: {e}")
            return False
    
    def install_extension_developer_mode(self) -> bool:
        """🛠️ Install extension in developer mode"""
        try:
            # Create instructions file
            instructions = f"""
🔌 VIP BIG BANG EXTENSION INSTALLATION

To manually install the extension:

1. Open Chrome
2. Go to: chrome://extensions/
3. Enable "Developer mode" (top right)
4. Click "Load unpacked"
5. Select folder: {self.temp_extension_path}
6. Extension will be installed

Or run this batch file: {os.path.join(self.temp_extension_path, "launch_chrome.bat")}
"""
            
            instructions_path = os.path.join(self.temp_extension_path, "INSTALLATION_INSTRUCTIONS.txt")
            with open(instructions_path, 'w', encoding='utf-8') as f:
                f.write(instructions)
            
            self.logger.info(f"✅ Installation instructions created: {instructions_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Instructions creation failed: {e}")
            return False
    
    def auto_install_extension(self) -> Dict[str, bool]:
        """🚀 Attempt all installation methods"""
        results = {}
        
        self.logger.info("🚀 Starting auto extension installation...")
        
        # Step 1: Prepare extension package
        results['package_prepared'] = self.prepare_extension_package()
        
        if not results['package_prepared']:
            return results
        
        # Step 2: Try registry installation (requires admin)
        results['registry_install'] = self.install_extension_via_registry()
        
        # Step 3: Try Chrome flags method
        results['chrome_flags'] = self.install_extension_via_chrome_flags()
        
        # Step 4: Create shortcut
        results['shortcut_created'] = self.create_extension_shortcut()
        
        # Step 5: Create manual instructions
        results['instructions_created'] = self.install_extension_developer_mode()
        
        # Summary
        success_count = sum(results.values())
        self.logger.info(f"🏆 Extension installation: {success_count}/{len(results)} methods successful")
        
        return results
    
    def launch_chrome_with_extension(self) -> bool:
        """🚀 Launch Chrome with extension loaded"""
        try:
            chrome_exe = self.find_chrome_executable()
            if not chrome_exe:
                return False
            
            # Enhanced Chrome flags for better compatibility
            chrome_flags = [
                chrome_exe,
                f"--load-extension={self.temp_extension_path}",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--disable-features=VizDisplayCompositor",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
                "--enable-experimental-extension-apis",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-ipc-flooding-protection",
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            
            # Launch Chrome
            process = subprocess.Popen(chrome_flags, shell=False)
            
            self.logger.info("🚀 Chrome launched with VIP BIG BANG extension")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to launch Chrome with extension: {e}")
            return False
    
    def check_extension_installed(self) -> bool:
        """✅ Check if extension is installed and active"""
        try:
            # This would require Chrome DevTools Protocol or similar
            # For now, we'll assume success if files are in place
            return os.path.exists(self.temp_extension_path)
            
        except Exception as e:
            self.logger.error(f"❌ Extension check failed: {e}")
            return False
    
    def get_installation_status(self) -> Dict[str, any]:
        """📊 Get installation status"""
        return {
            'chrome_found': self.find_chrome_executable() is not None,
            'extension_source_exists': os.path.exists(self.extension_source),
            'extension_prepared': os.path.exists(self.temp_extension_path),
            'temp_path': self.temp_extension_path,
            'source_path': self.extension_source
        }
