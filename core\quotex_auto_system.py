#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Quotex Auto System
🔐 اتو لوگین + خواندن کامل داده‌ها
⚡ تحلیل زیر 1 ثانیه
💎 سیستم کامل تریدینگ
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import subprocess
import os

class QuotexAutoSystem:
    """
    🚀 Quotex Auto System
    🔐 اتو لوگین کامل
    ⚡ خواندن داده‌ها زیر 1 ثانیه
    💎 تحلیل OTC و ستاره‌دار
    """

    def __init__(self, parent_frame):
        self.parent_frame = parent_frame
        self.driver = None
        self.is_logged_in = False
        self.is_reading_data = False
        
        # Login credentials
        self.email = ""
        self.password = ""
        
        # Trading data
        self.quotex_data = {}
        self.starred_assets = []
        self.otc_assets = []
        
        print("🚀 Quotex Auto System initialized")

    def create_main_interface(self):
        """🎯 Create Main Interface"""
        try:
            # Clear parent
            for widget in self.parent_frame.winfo_children():
                widget.destroy()

            # Main container
            main_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            main_container.pack(fill=tk.BOTH, expand=True)

            # Create login page first
            self.create_login_page(main_container)

            return True

        except Exception as e:
            print(f"❌ Interface creation error: {e}")
            return False

    def create_login_page(self, parent):
        """🔐 Create Auto Login Page"""
        try:
            # Login container
            self.login_container = tk.Frame(parent, bg='#0A0A0F')
            self.login_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(self.login_container, bg='#1E88E5', height=100)
            header.pack(fill=tk.X, pady=(0, 30))
            header.pack_propagate(False)

            tk.Label(header, text="🔐 QUOTEX AUTO LOGIN", 
                    font=("Arial", 24, "bold"), fg="#FFFFFF", bg="#1E88E5").pack(pady=30)

            # Login form
            form_frame = tk.Frame(self.login_container, bg='#1A1A2E', relief=tk.RAISED, bd=3)
            form_frame.pack(padx=100, pady=50)

            tk.Label(form_frame, text="📧 EMAIL", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.email_entry = tk.Entry(form_frame, font=("Arial", 14), width=40, 
                                      bg="#FFFFFF", fg="#000000")
            self.email_entry.pack(pady=10)

            tk.Label(form_frame, text="🔒 PASSWORD", 
                    font=("Arial", 14, "bold"), fg="#FFD700", bg="#1A1A2E").pack(pady=15)

            self.password_entry = tk.Entry(form_frame, font=("Arial", 14), width=40, 
                                         bg="#FFFFFF", fg="#000000", show="*")
            self.password_entry.pack(pady=10)

            # Login button
            self.login_btn = tk.Button(form_frame, text="🚀 AUTO LOGIN TO QUOTEX", 
                                     font=("Arial", 16, "bold"), bg="#00FF88", fg="#000000",
                                     padx=50, pady=20, command=self.start_auto_login)
            self.login_btn.pack(pady=30)

            # Status
            self.login_status = tk.Label(form_frame, text="🔴 Ready to login", 
                                       font=("Arial", 12), fg="#FF4444", bg="#1A1A2E")
            self.login_status.pack(pady=10)

            # Load saved credentials
            self.load_credentials()

        except Exception as e:
            print(f"❌ Login page error: {e}")

    def start_auto_login(self):
        """🚀 Start Auto Login Process"""
        try:
            self.email = self.email_entry.get().strip()
            self.password = self.password_entry.get().strip()

            if not self.email or not self.password:
                messagebox.showwarning("Warning", "Please enter email and password!")
                return

            # Save credentials
            self.save_credentials()

            # Update status
            self.login_status.config(text="🔄 Starting auto login...", fg="#FFD700")
            self.login_btn.config(state=tk.DISABLED, text="🔄 LOGGING IN...")

            # Start login thread
            def login_thread():
                try:
                    if self.perform_auto_login():
                        # Login successful, switch to main interface
                        self.login_container.destroy()
                        self.create_trading_interface()
                    else:
                        # Login failed
                        self.login_status.config(text="❌ Login failed", fg="#FF4444")
                        self.login_btn.config(state=tk.NORMAL, text="🚀 AUTO LOGIN TO QUOTEX")

                except Exception as e:
                    print(f"❌ Login thread error: {e}")
                    self.login_status.config(text="❌ Login error", fg="#FF4444")
                    self.login_btn.config(state=tk.NORMAL, text="🚀 AUTO LOGIN TO QUOTEX")

            thread = threading.Thread(target=login_thread, daemon=True)
            thread.start()

        except Exception as e:
            print(f"❌ Start login error: {e}")

    def perform_auto_login(self):
        """🔐 Perform Automatic Login in Main Chrome"""
        try:
            print("🔐 Starting automatic login in main Chrome...")

            # Setup Chrome to use existing profile
            chrome_options = Options()

            # Use existing Chrome profile
            user_data_dir = rf"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
            chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
            chrome_options.add_argument("--profile-directory=Default")

            # Anti-detection
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--no-sandbox")

            # Create driver with existing profile
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Navigate to Quotex
            self.driver.get("https://qxbroker.com/en/sign-in")
            
            # Wait for page load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            time.sleep(2)

            # Find and fill email
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[placeholder*='email']",
                "#email",
                ".email-input"
            ]

            email_field = None
            for selector in email_selectors:
                try:
                    email_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if email_field.is_displayed():
                        break
                except:
                    continue

            if email_field:
                email_field.clear()
                email_field.send_keys(self.email)
                time.sleep(1)
            else:
                print("❌ Email field not found")
                return False

            # Find and fill password
            password_selectors = [
                "input[type='password']",
                "input[name='password']",
                "#password",
                ".password-input"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_field.is_displayed():
                        break
                except:
                    continue

            if password_field:
                password_field.clear()
                password_field.send_keys(self.password)
                time.sleep(1)
            else:
                print("❌ Password field not found")
                return False

            # Find and click login button
            login_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button[class*='login']",
                "button[class*='sign-in']",
                ".login-btn",
                ".submit-btn"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if login_button.is_displayed() and login_button.is_enabled():
                        break
                except:
                    continue

            if login_button:
                login_button.click()
                time.sleep(5)

                # Check if login successful
                current_url = self.driver.current_url
                if "trade" in current_url.lower() or "dashboard" in current_url.lower():
                    print("✅ Login successful!")
                    self.is_logged_in = True
                    return True
                else:
                    print("❌ Login failed - wrong credentials or captcha")
                    return False
            else:
                print("❌ Login button not found")
                return False

        except Exception as e:
            print(f"❌ Auto login error: {e}")
            return False

    def create_trading_interface(self):
        """📊 Create Main Trading Interface"""
        try:
            # Main trading container
            self.trading_container = tk.Frame(self.parent_frame, bg='#0A0A0F')
            self.trading_container.pack(fill=tk.BOTH, expand=True)

            # Header
            header = tk.Frame(self.trading_container, bg='#00C851', height=80)
            header.pack(fill=tk.X, pady=(0, 10))
            header.pack_propagate(False)

            tk.Label(header, text="🚀 QUOTEX LIVE DATA READER", 
                    font=("Arial", 20, "bold"), fg="#FFFFFF", bg="#00C851").pack(pady=25)

            # Status panel
            status_panel = tk.Frame(self.trading_container, bg='#1A1A2E', relief=tk.RAISED, bd=2)
            status_panel.pack(fill=tk.X, padx=10, pady=(0, 10))

            status_frame = tk.Frame(status_panel, bg='#1A1A2E')
            status_frame.pack(pady=15)

            tk.Label(status_frame, text="📊 STATUS:", font=("Arial", 14, "bold"), 
                    fg="#FFD700", bg="#1A1A2E").pack(side=tk.LEFT)

            self.trading_status = tk.Label(status_frame, text="🟢 LOGGED IN & READY", 
                                         font=("Arial", 14, "bold"), fg="#00FF88", bg="#1A1A2E")
            self.trading_status.pack(side=tk.LEFT, padx=(10, 0))

            # Control buttons
            control_frame = tk.Frame(self.trading_container, bg='#2D3748', relief=tk.RAISED, bd=2)
            control_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

            button_frame = tk.Frame(control_frame, bg='#2D3748')
            button_frame.pack(pady=15)

            # Start reading button
            self.start_reading_btn = tk.Button(button_frame, text="📊 START READING DATA", 
                                             font=("Arial", 14, "bold"), bg="#00FF88", fg="#000000",
                                             padx=30, pady=15, command=self.start_reading_data)
            self.start_reading_btn.pack(side=tk.LEFT, padx=10)

            # Stop reading button
            self.stop_reading_btn = tk.Button(button_frame, text="⏹️ STOP READING", 
                                            font=("Arial", 14, "bold"), bg="#FF4444", fg="#FFFFFF",
                                            padx=30, pady=15, command=self.stop_reading_data, state=tk.DISABLED)
            self.stop_reading_btn.pack(side=tk.LEFT, padx=10)

            # Refresh button
            tk.Button(button_frame, text="🔄 REFRESH", 
                     font=("Arial", 14, "bold"), bg="#FFD700", fg="#000000",
                     padx=30, pady=15, command=self.refresh_data).pack(side=tk.LEFT, padx=10)

            # Data display area
            data_frame = tk.Frame(self.trading_container, bg='#1A202C', relief=tk.SUNKEN, bd=3)
            data_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

            tk.Label(data_frame, text="📈 LIVE QUOTEX DATA (Under 1 Second)", 
                    font=("Arial", 16, "bold"), fg="#00FFFF", bg="#1A202C").pack(pady=15)

            # Data text area
            self.data_display = tk.Text(data_frame, bg="#0D1117", fg="#00FFFF", 
                                      font=("Consolas", 10), wrap=tk.WORD)
            self.data_display.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

            # Add scrollbar
            scrollbar = tk.Scrollbar(self.data_display)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            self.data_display.config(yscrollcommand=scrollbar.set)
            scrollbar.config(command=self.data_display.yview)

            # Initial message
            self.add_data_log("🚀 Quotex Auto System Ready")
            self.add_data_log("✅ Successfully logged in to Quotex")
            self.add_data_log("📊 Click 'START READING DATA' to begin live analysis")

        except Exception as e:
            print(f"❌ Trading interface error: {e}")

    def start_reading_data(self):
        """📊 Start Reading Live Data"""
        try:
            if not self.is_logged_in or not self.driver:
                messagebox.showwarning("Warning", "Please login first!")
                return

            self.is_reading_data = True
            self.start_reading_btn.config(state=tk.DISABLED)
            self.stop_reading_btn.config(state=tk.NORMAL)
            self.trading_status.config(text="🟢 READING LIVE DATA", fg="#00FF88")

            self.add_data_log("📊 Starting live data reading...")

            def reading_thread():
                try:
                    while self.is_reading_data:
                        start_time = time.time()
                        
                        # Read all Quotex data
                        data = self.read_quotex_data()
                        
                        # Calculate read time
                        read_time = time.time() - start_time
                        
                        # Display data
                        if data:
                            self.display_quotex_data(data, read_time)
                        
                        # Wait before next read
                        time.sleep(1)

                except Exception as e:
                    self.add_data_log(f"❌ Reading thread error: {e}")

            thread = threading.Thread(target=reading_thread, daemon=True)
            thread.start()

        except Exception as e:
            self.add_data_log(f"❌ Start reading error: {e}")

    def read_quotex_data(self):
        """📈 Read All Quotex Data Under 1 Second"""
        try:
            # Execute comprehensive JavaScript to get all data
            data = self.driver.execute_script("""
                return {
                    // Basic info
                    timestamp: new Date().toLocaleTimeString(),
                    balance: document.querySelector('.balance, [class*="balance"], [data-test*="balance"]')?.innerText || 'N/A',
                    
                    // Current asset
                    currentAsset: document.querySelector('.asset-name, [class*="asset"], [class*="symbol"]')?.innerText || 'N/A',
                    currentPrice: document.querySelector('.price, [class*="price"], [class*="rate"]')?.innerText || 'N/A',
                    currentProfit: document.querySelector('.profit, [class*="profit"], [class*="payout"]')?.innerText || 'N/A',
                    
                    // All assets list
                    allAssets: Array.from(document.querySelectorAll('.asset-item, [class*="asset"], .symbol-item')).map(el => ({
                        name: el.querySelector('.name, .symbol')?.innerText || el.innerText,
                        price: el.querySelector('.price, .rate')?.innerText || 'N/A',
                        profit: el.querySelector('.profit, .payout')?.innerText || 'N/A',
                        isOTC: el.querySelector('.otc, [class*="otc"]') !== null,
                        isStarred: el.querySelector('.star, [class*="star"], .favorite') !== null || el.classList.contains('starred'),
                        trend: el.querySelector('.trend, [class*="trend"]')?.innerText || 'N/A'
                    })),
                    
                    // Starred assets specifically
                    starredAssets: Array.from(document.querySelectorAll('.starred, [class*="starred"], .favorite, [class*="favorite"]')).map(el => ({
                        name: el.querySelector('.name, .symbol')?.innerText || el.innerText,
                        price: el.querySelector('.price, .rate')?.innerText || 'N/A',
                        profit: el.querySelector('.profit, .payout')?.innerText || 'N/A',
                        isOTC: el.querySelector('.otc, [class*="otc"]') !== null
                    })),
                    
                    // OTC assets
                    otcAssets: Array.from(document.querySelectorAll('.otc, [class*="otc"]')).map(el => {
                        const parent = el.closest('.asset-item, [class*="asset"], .symbol-item') || el.parentElement;
                        return {
                            name: parent.querySelector('.name, .symbol')?.innerText || parent.innerText,
                            price: parent.querySelector('.price, .rate')?.innerText || 'N/A',
                            profit: parent.querySelector('.profit, .payout')?.innerText || 'N/A',
                            isStarred: parent.querySelector('.star, [class*="star"], .favorite') !== null
                        };
                    }),
                    
                    // Market status
                    marketStatus: document.querySelector('.market-status, [class*="market"]')?.innerText || 'OPEN',
                    
                    // Trading buttons status
                    callButtonEnabled: document.querySelector('.call-btn, [class*="call"], .higher-btn')?.disabled === false,
                    putButtonEnabled: document.querySelector('.put-btn, [class*="put"], .lower-btn')?.disabled === false,
                    
                    // Chart data
                    chartVisible: document.querySelector('.chart, [class*="chart"], canvas') !== null,
                    
                    // Account type
                    accountType: document.querySelector('.account-type, [class*="account"]')?.innerText || 'REAL',
                    
                    // Current URL and title
                    url: window.location.href,
                    title: document.title
                };
            """)
            
            return data

        except Exception as e:
            self.add_data_log(f"❌ Data read error: {e}")
            return None

    def display_quotex_data(self, data, read_time):
        """📊 Display All Quotex Data"""
        try:
            # Format comprehensive data display
            display_text = f"""
{'='*80}
⏰ TIME: {data.get('timestamp', 'N/A')} | ⚡ READ TIME: {read_time:.3f}s
💳 BALANCE: {data.get('balance', 'N/A')} | 🏦 ACCOUNT: {data.get('accountType', 'N/A')}
📊 CURRENT ASSET: {data.get('currentAsset', 'N/A')} | 💰 PRICE: {data.get('currentPrice', 'N/A')}
💎 PROFIT: {data.get('currentProfit', 'N/A')} | 🎯 MARKET: {data.get('marketStatus', 'N/A')}
🔴 CALL: {'✅' if data.get('callButtonEnabled') else '❌'} | 🔵 PUT: {'✅' if data.get('putButtonEnabled') else '❌'}

⭐ STARRED ASSETS ({len(data.get('starredAssets', []))}):{self.format_assets_list(data.get('starredAssets', []))}

🏷️ OTC ASSETS ({len(data.get('otcAssets', []))}):{self.format_assets_list(data.get('otcAssets', []))}

📈 ALL ASSETS ({len(data.get('allAssets', []))}):{self.format_assets_list(data.get('allAssets', [])[:10])}  # Show first 10

📊 CHART: {'✅ VISIBLE' if data.get('chartVisible') else '❌ NOT VISIBLE'}
🌐 URL: {data.get('url', 'N/A')}
{'='*80}
"""

            self.add_data_log(display_text)

        except Exception as e:
            self.add_data_log(f"❌ Display error: {e}")

    def format_assets_list(self, assets):
        """📋 Format Assets List"""
        try:
            if not assets:
                return "\n   No assets found"
            
            formatted = ""
            for asset in assets[:5]:  # Show first 5
                name = asset.get('name', 'N/A')
                price = asset.get('price', 'N/A')
                profit = asset.get('profit', 'N/A')
                otc = "🏷️" if asset.get('isOTC') else "📊"
                star = "⭐" if asset.get('isStarred') else ""
                
                formatted += f"\n   {otc} {star} {name} | 💰 {price} | 💎 {profit}"
            
            if len(assets) > 5:
                formatted += f"\n   ... and {len(assets) - 5} more"
            
            return formatted

        except Exception as e:
            return f"\n   Error formatting: {e}"

    def stop_reading_data(self):
        """⏹️ Stop Reading Data"""
        try:
            self.is_reading_data = False
            self.start_reading_btn.config(state=tk.NORMAL)
            self.stop_reading_btn.config(state=tk.DISABLED)
            self.trading_status.config(text="🟡 STOPPED", fg="#FFD700")
            self.add_data_log("⏹️ Data reading stopped")

        except Exception as e:
            self.add_data_log(f"❌ Stop reading error: {e}")

    def refresh_data(self):
        """🔄 Refresh Data"""
        try:
            if self.driver:
                self.driver.refresh()
                time.sleep(3)
                self.add_data_log("🔄 Page refreshed")
            else:
                self.add_data_log("❌ No browser connection")

        except Exception as e:
            self.add_data_log(f"❌ Refresh error: {e}")

    def save_credentials(self):
        """💾 Save Credentials"""
        try:
            credentials = {"email": self.email, "password": self.password}
            with open("quotex_credentials.json", "w") as f:
                json.dump(credentials, f)
        except Exception as e:
            print(f"❌ Save credentials error: {e}")

    def load_credentials(self):
        """📂 Load Credentials"""
        try:
            if os.path.exists("quotex_credentials.json"):
                with open("quotex_credentials.json", "r") as f:
                    credentials = json.load(f)
                self.email_entry.insert(0, credentials.get("email", ""))
                self.password_entry.insert(0, credentials.get("password", ""))
        except Exception as e:
            print(f"❌ Load credentials error: {e}")

    def add_data_log(self, message):
        """📝 Add Data Log"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.data_display.insert(tk.END, f"[{timestamp}] {message}\n")
            self.data_display.see(tk.END)
        except:
            pass

# Test function
def test_quotex_auto_system():
    """🧪 Test Quotex Auto System"""
    print("🧪 Testing Quotex Auto System...")
    
    root = tk.Tk()
    root.title("🚀 Quotex Auto System Test")
    root.geometry("1400x900")
    root.configure(bg='#0A0A0F')
    
    system = QuotexAutoSystem(root)
    system.create_main_interface()
    
    root.mainloop()

if __name__ == "__main__":
    test_quotex_auto_system()
