"""
🚀 INTEGRATED SYSTEM MANAGER
🔗 ادغام کامل سیستم تشخیص سخت‌افزار، مرورگر و برنامه اصلی
🛡️ مدیریت یکپارچه anti-detection و fingerprinting
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QApplication

from .advanced_hardware_detector import AdvancedHardwareDetector
from .browser_fingerprint_manager import BrowserFingerprintManager
from .ultimate_system_detector import UltimateSystemDetector

class IntegratedSystemManager(QObject):
    """
    🚀 مدیر سیستم یکپارچه
    ادغام تمام سیستم‌های تشخیص و fingerprinting
    """
    
    # Signals
    system_ready = Signal(dict)
    security_alert = Signal(dict)
    fingerprint_updated = Signal(dict)
    vm_detected = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("IntegratedSystemManager")
        
        # System components
        self.hardware_detector = None
        self.browser_manager = None
        self.system_detector = None
        
        # Combined data
        self.complete_system_info = {}
        self.security_status = {
            "vm_detected": False,
            "bot_detected": False,
            "risk_level": "LOW",
            "confidence": 0
        }
        
        # Monitoring timer (will be initialized when QApplication is available)
        self.monitoring_timer = None
        
        # Initialize all components
        self.initialize_components()
        
        self.logger.info("🚀 Integrated System Manager initialized")
    
    def initialize_components(self):
        """🔧 راه‌اندازی تمام اجزای سیستم"""
        try:
            self.logger.info("🔧 Initializing system components...")
            
            # Initialize hardware detector
            self.hardware_detector = AdvancedHardwareDetector()
            self.hardware_detector.hardware_detected.connect(self.on_hardware_detected)
            self.hardware_detector.vm_detection_complete.connect(self.on_vm_detected)
            
            # Initialize browser fingerprint manager
            self.browser_manager = BrowserFingerprintManager()
            self.browser_manager.fingerprint_received.connect(self.on_browser_fingerprint)
            self.browser_manager.suspicious_activity_detected.connect(self.on_suspicious_activity)
            
            # Initialize ultimate system detector
            self.system_detector = UltimateSystemDetector()
            self.system_detector.system_detected.connect(self.on_system_detected)
            self.system_detector.performance_updated.connect(self.on_performance_updated)
            
            # Start performance monitoring
            self.system_detector.start_performance_monitoring(5000)  # Every 5 seconds
            
            # Start integrated monitoring (skip timer for now)
            # self.monitoring_timer.start(10000)  # Every 10 seconds
            
            self.logger.info("✅ All system components initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Component initialization failed: {e}")
    
    def on_hardware_detected(self, hardware_info):
        """📡 پردازش اطلاعات سخت‌افزاری"""
        try:
            self.complete_system_info["advanced_hardware"] = hardware_info
            self.logger.info("📡 Advanced hardware information received")
            
            # Update security status
            self.update_security_status()
            
        except Exception as e:
            self.logger.error(f"❌ Hardware detection processing error: {e}")
    
    def on_browser_fingerprint(self, fingerprint_info):
        """🌐 پردازش fingerprint مرورگر"""
        try:
            self.complete_system_info["browser_fingerprint"] = fingerprint_info
            self.logger.info("🌐 Browser fingerprint received")
            
            # Update security status
            self.update_security_status()
            
            # Emit signal
            self.fingerprint_updated.emit(fingerprint_info)
            
        except Exception as e:
            self.logger.error(f"❌ Browser fingerprint processing error: {e}")
    
    def on_system_detected(self, system_info):
        """🖥️ پردازش اطلاعات سیستم"""
        try:
            self.complete_system_info["basic_system"] = system_info
            self.logger.info("🖥️ Basic system information received")
            
        except Exception as e:
            self.logger.error(f"❌ System detection processing error: {e}")
    
    def on_performance_updated(self, performance_info):
        """⚡ پردازش اطلاعات عملکرد"""
        try:
            self.complete_system_info["performance"] = performance_info
            
        except Exception as e:
            self.logger.error(f"❌ Performance update processing error: {e}")
    
    def on_vm_detected(self, vm_info):
        """🚨 پردازش تشخیص VM"""
        try:
            self.complete_system_info["vm_detection"] = vm_info
            
            if vm_info.get("is_vm", False):
                self.logger.warning(f"🚨 Virtual Machine detected: {vm_info}")
                self.vm_detected.emit(vm_info)
            
            # Update security status
            self.update_security_status()
            
        except Exception as e:
            self.logger.error(f"❌ VM detection processing error: {e}")
    
    def on_suspicious_activity(self, activity_info):
        """🚨 پردازش فعالیت مشکوک"""
        try:
            self.logger.warning(f"🚨 Suspicious activity detected: {activity_info}")
            
            # Add to security alerts
            if "security_alerts" not in self.complete_system_info:
                self.complete_system_info["security_alerts"] = []
            
            self.complete_system_info["security_alerts"].append({
                "activity": activity_info,
                "timestamp": datetime.now().isoformat()
            })
            
            # Emit security alert
            self.security_alert.emit(activity_info)
            
        except Exception as e:
            self.logger.error(f"❌ Suspicious activity processing error: {e}")
    
    def update_security_status(self):
        """🛡️ بروزرسانی وضعیت امنیتی"""
        try:
            # Reset status
            self.security_status = {
                "vm_detected": False,
                "bot_detected": False,
                "risk_level": "LOW",
                "confidence": 0,
                "indicators": []
            }
            
            # Check VM detection from hardware
            vm_detection = self.complete_system_info.get("vm_detection", {})
            if vm_detection.get("is_vm", False):
                self.security_status["vm_detected"] = True
                self.security_status["confidence"] += vm_detection.get("confidence", 0)
                self.security_status["indicators"].extend(vm_detection.get("indicators", []))
            
            # Check browser fingerprint
            browser_fp = self.complete_system_info.get("browser_fingerprint", {})
            if browser_fp:
                analysis = browser_fp.get("analysis", {})
                risk_score = analysis.get("risk_score", 0)
                
                if risk_score >= 50:
                    self.security_status["bot_detected"] = True
                    self.security_status["confidence"] += risk_score
                    self.security_status["indicators"].extend(analysis.get("bot_indicators", []))
                    self.security_status["indicators"].extend(analysis.get("vm_indicators", []))
            
            # Determine risk level
            total_confidence = self.security_status["confidence"]
            if total_confidence >= 80:
                self.security_status["risk_level"] = "CRITICAL"
            elif total_confidence >= 60:
                self.security_status["risk_level"] = "HIGH"
            elif total_confidence >= 40:
                self.security_status["risk_level"] = "MEDIUM"
            else:
                self.security_status["risk_level"] = "LOW"
            
            # Log security status
            if self.security_status["risk_level"] in ["HIGH", "CRITICAL"]:
                self.logger.warning(f"🚨 Security Risk: {self.security_status['risk_level']} (Confidence: {total_confidence}%)")
                for indicator in self.security_status["indicators"]:
                    self.logger.warning(f"   - {indicator}")
            
        except Exception as e:
            self.logger.error(f"❌ Security status update error: {e}")
    
    def update_system_status(self):
        """🔄 بروزرسانی وضعیت کلی سیستم"""
        try:
            # Update timestamp
            self.complete_system_info["last_update"] = datetime.now().isoformat()
            
            # Check if system is ready
            required_components = ["advanced_hardware", "basic_system"]
            system_ready = all(comp in self.complete_system_info for comp in required_components)
            
            if system_ready:
                self.system_ready.emit(self.complete_system_info)
            
        except Exception as e:
            self.logger.error(f"❌ System status update error: {e}")
    
    def get_complete_system_info(self):
        """📊 دریافت اطلاعات کامل سیستم"""
        return {
            "system_info": self.complete_system_info,
            "security_status": self.security_status,
            "timestamp": datetime.now().isoformat()
        }
    
    def is_safe_environment(self):
        """✅ بررسی امنیت محیط"""
        return self.security_status["risk_level"] in ["LOW", "MEDIUM"]
    
    def get_risk_level(self):
        """📊 دریافت سطح ریسک"""
        return self.security_status["risk_level"]
    
    def get_security_indicators(self):
        """🔍 دریافت نشانگرهای امنیتی"""
        return self.security_status["indicators"]
    
    def export_system_report(self, file_path: str = None):
        """📄 صادرات گزارش کامل سیستم"""
        try:
            report = {
                "report_info": {
                    "generated_at": datetime.now().isoformat(),
                    "version": "1.0.0",
                    "generator": "VIP BIG BANG Integrated System Manager"
                },
                "system_data": self.complete_system_info,
                "security_analysis": self.security_status,
                "recommendations": self.generate_recommendations()
            }
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                self.logger.info(f"📄 System report exported to: {file_path}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"❌ Report export error: {e}")
            return None
    
    def generate_recommendations(self):
        """💡 تولید توصیه‌های امنیتی"""
        recommendations = []
        
        if self.security_status["vm_detected"]:
            recommendations.append("⚠️ Virtual Machine detected - Consider using physical hardware for better security")
        
        if self.security_status["bot_detected"]:
            recommendations.append("🚨 Bot/Automation detected - Review browser settings and extensions")
        
        if self.security_status["risk_level"] == "CRITICAL":
            recommendations.append("🔴 CRITICAL: Immediate security review required")
        elif self.security_status["risk_level"] == "HIGH":
            recommendations.append("🟡 HIGH: Enhanced security measures recommended")
        
        if not recommendations:
            recommendations.append("✅ System appears secure - Continue monitoring")
        
        return recommendations
    
    def log_complete_summary(self):
        """📊 نمایش خلاصه کامل سیستم"""
        try:
            self.logger.info("🚀 === INTEGRATED SYSTEM SUMMARY ===")
            
            # Hardware summary
            if self.hardware_detector:
                self.hardware_detector.log_detection_summary()
            
            # Browser fingerprint summary
            if self.browser_manager:
                self.browser_manager.log_fingerprint_summary()
            
            # System summary
            if self.system_detector:
                self.system_detector.log_system_summary()
            
            # Security summary
            self.logger.info(f"🛡️ Security Status: {self.security_status['risk_level']}")
            self.logger.info(f"🔍 Risk Confidence: {self.security_status['confidence']}%")
            
            if self.security_status["indicators"]:
                self.logger.info("🚨 Security Indicators:")
                for indicator in self.security_status["indicators"]:
                    self.logger.info(f"   - {indicator}")
            
            # Recommendations
            recommendations = self.generate_recommendations()
            self.logger.info("💡 Recommendations:")
            for rec in recommendations:
                self.logger.info(f"   {rec}")
            
            self.logger.info("🏆 === INTEGRATED SUMMARY COMPLETE ===")
            
        except Exception as e:
            self.logger.error(f"❌ Complete summary error: {e}")
    
    def stop_monitoring(self):
        """🛑 توقف نظارت"""
        try:
            if self.monitoring_timer:
                self.monitoring_timer.stop()
            
            if self.system_detector:
                self.system_detector.stop_performance_monitoring()
            
            self.logger.info("🛑 System monitoring stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Stop monitoring error: {e}")
