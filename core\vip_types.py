"""
VIP BIG BANG Enterprise - Type Definitions
Complete type definitions for all components
"""

from typing import Dict, List, Optional, Any, Union, Protocol, TypedDict
from datetime import datetime
import pandas as pd

# Basic Types
Price = float
Volume = int
Score = float
Confidence = float
Direction = str  # 'UP', 'DOWN', 'NEUTRAL', 'CALL', 'PUT'

# Market Data Types
class MarketData(TypedDict):
    timestamp: datetime
    open: Price
    high: Price
    low: Price
    close: Price
    volume: Volume
    price: Price

class CandleData(TypedDict):
    open: Price
    high: Price
    low: Price
    close: Price
    volume: Volume
    timestamp: datetime

# Analysis Result Types
class AnalysisResult(TypedDict):
    score: Score
    direction: Direction
    confidence: Confidence
    details: str

class ExtendedAnalysisResult(AnalysisResult):
    timestamp: Optional[str]
    processing_time: Optional[float]
    additional_data: Optional[Dict[str, Any]]

# Signal Types
class SignalData(TypedDict):
    direction: Direction
    strength: float
    timestamp: datetime
    source: str

class TradingSignal(TypedDict):
    direction: Direction
    confidence: Confidence
    entry_price: Price
    timestamp: datetime
    expiry_time: int
    analysis_results: Dict[str, AnalysisResult]

# Settings Types
class AnalysisSettings(TypedDict):
    enabled_indicators: List[str]
    timeframes: List[str]
    primary_timeframe: str
    lookback_periods: int
    sensitivity: str

class SignalProcessingSettings(TypedDict):
    min_confidence: float
    confirmation_required: int
    signal_timeout: int
    filter_weak_signals: bool
    combine_signals: bool
    weight_distribution: Dict[str, float]

class TradingSettings(TypedDict):
    platform: str
    analysis_interval: int
    trade_duration: int
    auto_trade_enabled: bool
    demo_mode: bool
    max_trades_per_hour: int
    max_daily_trades: int
    min_signal_strength: float
    confirm_mode_enabled: bool

class RiskManagementSettings(TypedDict):
    max_trade_amount: float
    min_trade_amount: float
    daily_loss_limit: float
    max_consecutive_losses: int
    stop_loss_percentage: float
    take_profit_percentage: float
    risk_per_trade: float

# Account Types
class AccountData(TypedDict):
    balance: float
    daily_trades: int
    daily_pnl: float
    initial_balance: float
    max_balance: float
    performance: Dict[str, Any]

class PerformanceData(TypedDict):
    consecutive_losses: int
    win_rate: float
    total_trades: int
    profitable_trades: int
    total_profit: float
    total_loss: float

# Filter Types
class FilterResult(TypedDict):
    allow_trading: bool
    risk_level: str
    reason: str
    score: Score
    confidence: Confidence

class NewsFilterResult(FilterResult):
    upcoming_news: List[Dict[str, Any]]
    blocking_news: List[Dict[str, Any]]
    next_clear_time: datetime

class OTCModeResult(FilterResult):
    is_otc_mode: bool
    risk_multiplier: float
    adjusted_settings: Dict[str, Any]

# Complementary Analysis Types
class ComplementaryResults(TypedDict):
    heatmap_pulsebar: AnalysisResult
    economic_news_filter: NewsFilterResult
    otc_mode_detector: OTCModeResult
    live_signal_scanner: AnalysisResult
    confirm_mode: FilterResult
    brothers_can_pattern: AnalysisResult
    active_analyses_panel: AnalysisResult
    autotrade_conditions_check: FilterResult
    account_safety: FilterResult
    manual_confirm: FilterResult

class FinalDecision(TypedDict):
    final_score: Score
    primary_score: Score
    filter_score: Score
    direction: Direction
    confidence: Confidence
    final_decision: str
    allow_trading: bool
    blocking_factors: List[str]
    warning_factors: List[str]
    signal_quality: Dict[str, Any]
    details: str

# Visual Types
class HeatmapData(TypedDict):
    primary_color: str
    intensity_level: float
    color_intensity: int
    rgb: Dict[str, int]
    hex_color: str

class PulseBarData(TypedDict):
    pulse_intensity: float
    pulse_color: str
    pulse_size: int
    volume_level: str
    volume_ratio: float

class VisualBox(TypedDict):
    combined_intensity: float
    visual_confidence: float
    signal_agreement: bool
    box_size: int
    box_opacity: float
    primary_color: str
    secondary_color: str
    animation_speed: str
    animation_type: str
    display_text: str

# Pattern Types
class CandleProperties(TypedDict):
    open: Price
    high: Price
    low: Price
    close: Price
    volume: Volume
    total_range: float
    body_size: float
    body_ratio: float
    upper_shadow: float
    lower_shadow: float
    upper_shadow_ratio: float
    lower_shadow_ratio: float
    direction: Direction
    price_movement: float
    price_movement_percent: float

class PatternResult(TypedDict):
    pattern_detected: bool
    pattern_type: str
    pattern_strength: float
    candle1_properties: Optional[CandleProperties]
    candle2_properties: Optional[CandleProperties]
    strength_factors: Dict[str, float]
    reason: str

# Level Analysis Types
class SupportResistanceLevel(TypedDict):
    price: Price
    touches: int
    index: int
    strength: float

class LevelAnalysis(TypedDict):
    support_levels: List[SupportResistanceLevel]
    resistance_levels: List[SupportResistanceLevel]

class BreakoutInfo(TypedDict):
    breakout_type: str
    breakout_level: Price
    breakout_strength: float
    level_strength: Optional[float]

# Volume Analysis Types
class VolumeMetrics(TypedDict):
    current_volume: Volume
    avg_volume: float
    volume_ratio: float
    volume_trend: str

class VolumePriceRelationship(TypedDict):
    relationship: str
    confirmation: float
    price_change: float
    volume_ratio: float

# Trend Analysis Types
class TrendData(TypedDict):
    price_trend: str
    trend_strength: float
    trend_slope: float
    ma_trend: str
    ma_strength: float
    ma_distance: float
    momentum_trend: str
    momentum_strength: float
    momentum_value: float
    volatility_trend: str
    volatility_level: str

# Protocol Definitions
class AnalyzerProtocol(Protocol):
    """Protocol for all analyzers"""
    def __init__(self, settings: Any) -> None: ...
    def analyze(self, data: pd.DataFrame) -> AnalysisResult: ...

class FilterProtocol(Protocol):
    """Protocol for all filters"""
    def __init__(self, settings: Any) -> None: ...
    def analyze(self, *args: Any, **kwargs: Any) -> FilterResult: ...

# Engine Types
class EngineResult(TypedDict):
    timestamp: str
    processing_time: float
    signals: Dict[str, AnalysisResult]
    overall_score: Score
    direction: Direction
    confidence: Confidence

class TradingSummary(TypedDict):
    timestamp: datetime
    primary_analyses: Dict[str, Any]
    complementary_filters: Dict[str, Any]
    final_recommendation: Dict[str, Any]
    visual_indicators: Dict[str, Any]
    risk_assessment: Dict[str, Any]

# Error Types
class AnalysisError(TypedDict):
    error: str
    timestamp: Optional[str]
    component: Optional[str]

# Union Types
AnalysisOutput = Union[AnalysisResult, ExtendedAnalysisResult, AnalysisError]
FilterOutput = Union[FilterResult, NewsFilterResult, OTCModeResult]
EngineOutput = Union[EngineResult, AnalysisError]

# Type Aliases
SettingsType = Union[
    AnalysisSettings,
    SignalProcessingSettings, 
    TradingSettings,
    RiskManagementSettings
]

DataType = Union[MarketData, CandleData, pd.DataFrame]
ResultType = Union[AnalysisResult, FilterResult, EngineResult]
