#!/usr/bin/env python3
"""
🚀 VIP BIG BANG - Professional Real Quotex Data Extractor
استخراج حرفه‌ای اطلاعات واقعی از Quotex با Playwright
"""

import asyncio
import time
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rows<PERSON>, BrowserContext
from playwright.sync_api import sync_playwright

class ProfessionalQuotexRealExtractor:
    """🎯 Professional Real Quotex Data Extractor"""
    
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        self.is_connected = False
        self.quotex_url = "https://qxbroker.com/en/trade"
        self.last_extraction_time = 0
        self.extraction_cache = {}
        
        # Advanced selectors for Quotex elements
        self.selectors = {
            # Account information
            'balance': [
                '[data-testid="balance"]',
                '.balance',
                '[class*="balance"]',
                '.header-balance',
                '[class*="header-balance"]',
                '.account-balance',
                '[class*="account-balance"]'
            ],
            
            # Current asset
            'current_asset': [
                '[data-testid="asset-name"]',
                '.asset-name',
                '[class*="asset-name"]',
                '.current-asset',
                '[class*="current-asset"]',
                '.trading-asset',
                '[class*="trading-asset"]'
            ],
            
            # Current price
            'current_price': [
                '[data-testid="current-price"]',
                '.current-price',
                '[class*="current-price"]',
                '.price',
                '[class*="price"]',
                '.rate',
                '[class*="rate"]'
            ],
            
            # Payout percentage
            'payout': [
                '[data-testid="payout"]',
                '.payout',
                '[class*="payout"]',
                '.profit',
                '[class*="profit"]',
                '.percentage',
                '[class*="percentage"]'
            ],
            
            # Trade buttons
            'call_button': [
                '[data-testid="call-button"]',
                '.call-button',
                '[class*="call-button"]',
                '.buy-button',
                '[class*="buy-button"]',
                '.higher-button',
                '[class*="higher-button"]'
            ],
            
            'put_button': [
                '[data-testid="put-button"]',
                '.put-button',
                '[class*="put-button"]',
                '.sell-button',
                '[class*="sell-button"]',
                '.lower-button',
                '[class*="lower-button"]'
            ],
            
            # Trade amount
            'trade_amount': [
                '[data-testid="trade-amount"]',
                '.trade-amount',
                '[class*="trade-amount"]',
                '.amount',
                '[class*="amount"]',
                '.investment',
                '[class*="investment"]'
            ]
        }
    
    async def initialize_browser(self) -> bool:
        """🚀 Initialize Playwright browser with stealth settings"""
        try:
            print("🚀 Initializing Professional Playwright Browser...")
            
            playwright = await async_playwright().start()
            
            # Launch browser with stealth settings
            self.browser = await playwright.chromium.launch(
                headless=False,  # Show browser for debugging
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            
            # Create context with realistic settings
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # Create page
            self.page = await self.context.new_page()
            
            # Add stealth scripts
            await self.add_stealth_scripts()
            
            print("✅ Professional Playwright Browser initialized")
            return True
            
        except Exception as e:
            print(f"❌ Browser initialization error: {e}")
            return False
    
    async def add_stealth_scripts(self):
        """🥷 Add stealth scripts to avoid detection"""
        try:
            # Override webdriver property
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # Override plugins
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
            
            # Override languages
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            print("✅ Stealth scripts added")
            
        except Exception as e:
            print(f"❌ Stealth scripts error: {e}")
    
    async def connect_to_quotex(self) -> bool:
        """🌐 Connect to Quotex platform"""
        try:
            print("🌐 Connecting to Quotex...")
            
            if not self.page:
                await self.initialize_browser()
            
            # Navigate to Quotex
            await self.page.goto(self.quotex_url, wait_until='networkidle')
            
            # Wait for page to load
            await self.page.wait_for_timeout(3000)
            
            # Check if page loaded successfully
            title = await self.page.title()
            print(f"📄 Page title: {title}")
            
            if 'quotex' in title.lower() or 'qx' in title.lower():
                self.is_connected = True
                print("✅ Successfully connected to Quotex")
                return True
            else:
                print("❌ Failed to connect to Quotex")
                return False
                
        except Exception as e:
            print(f"❌ Quotex connection error: {e}")
            return False
    
    async def extract_element_text(self, selectors: List[str]) -> Optional[str]:
        """📊 Extract text from element using multiple selectors"""
        try:
            for selector in selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        text = await element.text_content()
                        if text and text.strip():
                            return text.strip()
                except:
                    continue
            return None
            
        except Exception as e:
            print(f"❌ Element extraction error: {e}")
            return None
    
    async def extract_complete_quotex_data(self) -> Dict[str, Any]:
        """🎯 Extract complete real data from Quotex"""
        try:
            start_time = time.time()
            
            if not self.is_connected:
                await self.connect_to_quotex()
            
            if not self.is_connected:
                return {'success': False, 'error': 'Not connected to Quotex'}
            
            print("📊 Extracting real Quotex data...")
            
            # Extract all data elements
            data = {
                'success': True,
                'timestamp': datetime.now().isoformat(),
                'extraction_time': 0,
                'data_quality': 'Unknown'
            }
            
            # Extract balance
            balance = await self.extract_element_text(self.selectors['balance'])
            data['balance'] = balance if balance else '❌ Not found'
            
            # Extract current asset
            current_asset = await self.extract_element_text(self.selectors['current_asset'])
            data['current_asset'] = current_asset if current_asset else '❌ Not found'
            
            # Extract current price
            current_price = await self.extract_element_text(self.selectors['current_price'])
            data['current_price'] = current_price if current_price else '❌ Not found'
            
            # Extract payout
            payout = await self.extract_element_text(self.selectors['payout'])
            data['payout'] = payout if payout else '❌ Not found'
            
            # Extract trade amount
            trade_amount = await self.extract_element_text(self.selectors['trade_amount'])
            data['trade_amount'] = trade_amount if trade_amount else '❌ Not found'
            
            # Check trade buttons
            call_button = await self.page.query_selector(self.selectors['call_button'][0])
            put_button = await self.page.query_selector(self.selectors['put_button'][0])
            
            data['call_enabled'] = call_button is not None
            data['put_enabled'] = put_button is not None
            
            # Calculate extraction time
            extraction_time = time.time() - start_time
            data['extraction_time'] = extraction_time
            
            # Determine data quality
            found_elements = sum(1 for key in ['balance', 'current_asset', 'current_price', 'payout'] 
                               if data.get(key) and data[key] != '❌ Not found')
            
            if found_elements >= 3:
                data['data_quality'] = 'High'
            elif found_elements >= 2:
                data['data_quality'] = 'Medium'
            elif found_elements >= 1:
                data['data_quality'] = 'Low'
            else:
                data['data_quality'] = 'None'
                data['success'] = False
            
            print(f"✅ Data extraction completed in {extraction_time:.3f}s")
            print(f"📊 Data quality: {data['data_quality']} ({found_elements}/4 elements found)")
            
            return data
            
        except Exception as e:
            print(f"❌ Data extraction error: {e}")
            return {'success': False, 'error': str(e)}
    
    def extract_complete_quotex_data_sync(self) -> Dict[str, Any]:
        """🎯 Synchronous wrapper for data extraction"""
        try:
            return asyncio.run(self.extract_complete_quotex_data())
        except Exception as e:
            print(f"❌ Sync extraction error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def close(self):
        """🔒 Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            
            self.is_connected = False
            print("✅ Professional Quotex Extractor closed")
            
        except Exception as e:
            print(f"❌ Cleanup error: {e}")

# Singleton instance
_quotex_extractor = None

def get_professional_quotex_extractor() -> ProfessionalQuotexRealExtractor:
    """🎯 Get singleton instance of Professional Quotex Extractor"""
    global _quotex_extractor
    if _quotex_extractor is None:
        _quotex_extractor = ProfessionalQuotexRealExtractor()
    return _quotex_extractor

if __name__ == "__main__":
    # Test the extractor
    async def test_extractor():
        extractor = ProfessionalQuotexRealExtractor()
        await extractor.initialize_browser()
        await extractor.connect_to_quotex()
        
        data = await extractor.extract_complete_quotex_data()
        print(f"📊 Extracted data: {json.dumps(data, indent=2)}")
        
        await extractor.close()
    
    asyncio.run(test_extractor())
