"""
🔧 CONSOLE PASTE ENABLER
🚀 ENABLES PASTING IN CHROME CONSOLE
🛡️ BYPASSES CHROME CONSOLE SECURITY
"""

import time
import logging

class ConsolePasteEnabler:
    """
    🔧 CONSOLE PASTE ENABLER
    🚀 Enables pasting in Chrome console
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ConsolePasteEnabler")
        self.logger.info("🔧 Console Paste Enabler initialized")
    
    def get_console_paste_instructions(self):
        """📋 Get step-by-step console paste instructions"""
        return """
🔧 CHROME CONSOLE PASTE INSTRUCTIONS

📋 مراحل فعال‌سازی Paste در Console:

1️⃣ Chrome رو باز کن و به https://quotex.io برو
2️⃣ F12 بزن تا Developer Tools باز بشه
3️⃣ Console تب رو انتخاب کن
4️⃣ در Console این کد رو تایپ کن (نه Copy/Paste):

allow pasting

5️⃣ Enter بزن
6️⃣ حالا می‌تونی Script رو Paste کنی!

💡 اگر باز هم نشد، این مراحل رو انجام بده:

🔧 روش دوم:
1️⃣ در Console تایپ کن: allow pasting
2️⃣ Enter بزن
3️⃣ دوباره تایپ کن: allow pasting
4️⃣ Enter بزن
5️⃣ حالا Paste کار می‌کنه!

🔧 روش سوم (اگر باز نشد):
1️⃣ Chrome Settings برو
2️⃣ Privacy and security
3️⃣ Site Settings
4️⃣ Additional content settings
5️⃣ Clipboard
6️⃣ Allow sites to see text and images copied to the clipboard رو فعال کن

🔧 روش چهارم (Manual Typing):
اگر هیچ‌کدام کار نکرد، Script رو دستی تایپ کن!
        """
    
    def get_simplified_script(self):
        """📝 Get simplified script for manual typing"""
        return """
// 🚀 VIP BIG BANG Simple Script
(function(){
delete navigator.webdriver;
Object.defineProperty(navigator,'webdriver',{get:()=>undefined});
window.chrome=window.chrome||{runtime:{}};
console.log('✅ VIP BIG BANG Active!');
})();
        """
    
    def get_step_by_step_typing_instructions(self):
        """⌨️ Get step-by-step typing instructions"""
        return """
⌨️ MANUAL TYPING INSTRUCTIONS

اگر Paste کار نمی‌کنه، این Script کوتاه رو دستی تایپ کن:

📝 Script کوتاه (فقط 4 خط):

(function(){
delete navigator.webdriver;
Object.defineProperty(navigator,'webdriver',{get:()=>undefined});
console.log('✅ VIP BIG BANG Active!');
})();

🔤 مراحل تایپ:
1️⃣ ( تایپ کن
2️⃣ function تایپ کن
3️⃣ ( تایپ کن
4️⃣ ) تایپ کن
5️⃣ { تایپ کن
6️⃣ Enter بزن
7️⃣ delete navigator.webdriver; تایپ کن
8️⃣ Enter بزن
9️⃣ Object.defineProperty(navigator,'webdriver',{get:()=>undefined}); تایپ کن
🔟 Enter بزن
1️⃣1️⃣ console.log('✅ VIP BIG BANG Active!'); تایپ کن
1️⃣2️⃣ Enter بزن
1️⃣3️⃣ } تایپ کن
1️⃣4️⃣ ) تایپ کن
1️⃣5️⃣ ( تایپ کن
1️⃣6️⃣ ) تایپ کن
1️⃣7️⃣ ; تایپ کن
1️⃣8️⃣ Enter بزن

✅ باید "✅ VIP BIG BANG Active!" رو ببینی
        """
    
    def get_alternative_injection_methods(self):
        """🔄 Get alternative injection methods"""
        return """
🔄 ALTERNATIVE INJECTION METHODS

🔧 روش 1: URL Bar Injection
1️⃣ در Address Bar این رو تایپ کن:
javascript:(function(){delete navigator.webdriver;Object.defineProperty(navigator,'webdriver',{get:()=>undefined});console.log('✅ VIP BIG BANG Active!');})();

2️⃣ Enter بزن

🔧 روش 2: Bookmarklet
1️⃣ یک Bookmark جدید بساز
2️⃣ Name: VIP BIG BANG
3️⃣ URL: javascript:(function(){delete navigator.webdriver;Object.defineProperty(navigator,'webdriver',{get:()=>undefined});console.log('✅ VIP BIG BANG Active!');})();
4️⃣ Bookmark رو کلیک کن

🔧 روش 3: Sources Tab
1️⃣ Developer Tools → Sources
2️⃣ Snippets
3️⃣ New snippet
4️⃣ Script رو Paste کن
5️⃣ Run کن

🔧 روش 4: Extension Injection
1️⃣ Chrome Extension بساز
2️⃣ Script رو inject کن
3️⃣ Extension رو فعال کن
        """
    
    def create_bookmarklet_file(self):
        """🔖 Create bookmarklet file"""
        bookmarklet_code = """
javascript:(function(){
delete navigator.webdriver;
delete navigator.__webdriver_evaluate;
delete navigator.__selenium_evaluate;
delete navigator.__webdriver_script_fn;
delete navigator.__driver_evaluate;
delete navigator.__driver_unwrapped;
delete navigator.__selenium_unwrapped;
delete navigator.__fxdriver_evaluate;
delete navigator.__fxdriver_unwrapped;
Object.defineProperty(navigator,'webdriver',{get:()=>undefined,configurable:false});
window.chrome=window.chrome||{runtime:{onConnect:undefined,onMessage:undefined}};
console.log('🚀 VIP BIG BANG Bookmarklet Active!');
alert('✅ VIP BIG BANG Active!');
})();
        """
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>VIP BIG BANG Bookmarklet</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #1e1e1e; color: white; }}
        .container {{ max-width: 800px; margin: 0 auto; }}
        .bookmarklet {{ background: #333; padding: 20px; border-radius: 10px; margin: 20px 0; }}
        .code {{ background: #000; color: #0f0; padding: 10px; border-radius: 5px; font-family: monospace; word-break: break-all; }}
        .button {{ background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 VIP BIG BANG Bookmarklet</h1>
        
        <div class="bookmarklet">
            <h2>📋 Instructions:</h2>
            <ol>
                <li>Right-click on the button below</li>
                <li>Select "Add to bookmarks" or "Bookmark this link"</li>
                <li>Go to Quotex.io</li>
                <li>Click the bookmark</li>
                <li>VIP BIG BANG will be activated!</li>
            </ol>
            
            <a href="{bookmarklet_code}" class="button">🚀 VIP BIG BANG Activator</a>
        </div>
        
        <div class="bookmarklet">
            <h2>🔧 Manual Code:</h2>
            <div class="code">{bookmarklet_code}</div>
        </div>
        
        <div class="bookmarklet">
            <h2>💡 Alternative Methods:</h2>
            <ul>
                <li>Copy the code above and paste in address bar</li>
                <li>Create a bookmark manually with the code as URL</li>
                <li>Use Developer Tools → Sources → Snippets</li>
            </ul>
        </div>
    </div>
</body>
</html>
        """
        
        with open("vip_big_bang_bookmarklet.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        return "vip_big_bang_bookmarklet.html"
    
    def create_extension_files(self):
        """🔌 Create Chrome extension files"""
        try:
            import os
            
            # Create extension directory
            ext_dir = "vip_big_bang_extension"
            os.makedirs(ext_dir, exist_ok=True)
            
            # Manifest file
            manifest = {
                "manifest_version": 3,
                "name": "VIP BIG BANG",
                "version": "1.0",
                "description": "VIP BIG BANG Anti-Detection System",
                "permissions": ["activeTab", "scripting"],
                "action": {
                    "default_popup": "popup.html",
                    "default_title": "VIP BIG BANG"
                },
                "content_scripts": [{
                    "matches": ["*://quotex.io/*", "*://*.quotex.io/*"],
                    "js": ["content.js"],
                    "run_at": "document_start"
                }]
            }
            
            with open(f"{ext_dir}/manifest.json", "w") as f:
                import json
                json.dump(manifest, f, indent=2)
            
            # Content script
            content_script = """
// VIP BIG BANG Content Script
(function() {
    'use strict';
    
    console.log('🚀 VIP BIG BANG Extension Loading...');
    
    // Remove webdriver traces
    delete navigator.webdriver;
    delete navigator.__webdriver_evaluate;
    delete navigator.__selenium_evaluate;
    delete navigator.__webdriver_script_fn;
    
    // Override webdriver property
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
        configurable: false
    });
    
    // Spoof chrome object
    if (!window.chrome || !window.chrome.runtime) {
        Object.defineProperty(window, 'chrome', {
            get: () => ({
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined
                }
            }),
            configurable: false
        });
    }
    
    console.log('✅ VIP BIG BANG Extension Active!');
    
})();
            """
            
            with open(f"{ext_dir}/content.js", "w") as f:
                f.write(content_script)
            
            # Popup HTML
            popup_html = """
<!DOCTYPE html>
<html>
<head>
    <style>
        body { width: 300px; padding: 20px; font-family: Arial, sans-serif; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .active { background: #4CAF50; color: white; }
        .button { background: #2196F3; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer; width: 100%; }
    </style>
</head>
<body>
    <h2>🚀 VIP BIG BANG</h2>
    <div class="status active">✅ Extension Active</div>
    <button class="button" onclick="activate()">🔄 Re-activate</button>
    
    <script>
        function activate() {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.scripting.executeScript({
                    target: {tabId: tabs[0].id},
                    function: function() {
                        delete navigator.webdriver;
                        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                        console.log('🔄 VIP BIG BANG Re-activated!');
                        alert('✅ VIP BIG BANG Re-activated!');
                    }
                });
            });
        }
    </script>
</body>
</html>
            """
            
            with open(f"{ext_dir}/popup.html", "w") as f:
                f.write(popup_html)
            
            return ext_dir
            
        except Exception as e:
            self.logger.error(f"❌ Extension creation error: {e}")
            return None

def main():
    """🚀 Main function"""
    print("🔧 CONSOLE PASTE ENABLER")
    print("=" * 50)
    
    enabler = ConsolePasteEnabler()
    
    print(enabler.get_console_paste_instructions())
    print("\n" + "="*50 + "\n")
    print(enabler.get_step_by_step_typing_instructions())
    print("\n" + "="*50 + "\n")
    print(enabler.get_alternative_injection_methods())
    
    # Create bookmarklet file
    bookmarklet_file = enabler.create_bookmarklet_file()
    print(f"\n✅ Bookmarklet file created: {bookmarklet_file}")
    
    # Create extension
    ext_dir = enabler.create_extension_files()
    if ext_dir:
        print(f"✅ Chrome extension created: {ext_dir}")
    
    print("\n🏆 All alternative methods ready!")

if __name__ == "__main__":
    main()
