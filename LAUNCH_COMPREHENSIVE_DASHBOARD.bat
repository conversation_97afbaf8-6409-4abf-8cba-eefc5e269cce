@echo off
chcp 65001 >nul
title VIP BIG BANG - Comprehensive Dashboard Launcher
color 0B

echo.
echo ================================================================
echo                VIP BIG BANG COMPREHENSIVE DASHBOARD
echo ================================================================
echo.
echo                    Unified Trading Interface
echo                Real-time System Status Monitor
echo                 Live Data Integration Center
echo                  Complete Control Dashboard
echo.
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo [INFO] Please install Python from https://python.org
    pause
    exit /b 1
)

echo [OK] Python found
echo.

REM Check if PySide6 is installed
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing PySide6...
    python -m pip install PySide6
    if errorlevel 1 (
        echo [ERROR] Failed to install PySide6
        pause
        exit /b 1
    )
    echo [OK] PySide6 installed successfully
)

echo [OK] PySide6 ready
echo.

REM Check if required packages are installed
echo [INFO] Checking required packages...
python -c "import websocket, requests, psutil" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing required packages...
    python -m pip install websocket-client requests psutil
    if errorlevel 1 (
        echo [WARNING] Some packages may not be installed
    )
)

echo [OK] Required packages ready
echo.

echo ================================================================
echo                    LAUNCHING DASHBOARD
echo ================================================================
echo.
echo [INFO] Starting VIP BIG BANG Comprehensive Dashboard...
echo [INFO] This dashboard will show:
echo        - Real-time system status
echo        - Live Quotex data integration
echo        - Chrome extension status
echo        - Trading controls
echo        - Performance metrics
echo        - System logs
echo        - Quick actions
echo.
echo [INFO] The dashboard will automatically connect to:
echo        - Running VIP BIG BANG systems
echo        - WebSocket server on port 8765
echo        - Chrome extension data
echo        - Shared data files
echo.

REM Launch the comprehensive dashboard
python vip_comprehensive_dashboard.py

REM Check if launch was successful
if errorlevel 1 (
    echo.
    echo [ERROR] Dashboard launch failed
    echo [INFO] Troubleshooting steps:
    echo        1. Make sure VIP BIG BANG system is running
    echo        2. Check if port 8765 is available
    echo        3. Verify all dependencies are installed
    echo        4. Try running: python main.py first
    echo.
    pause
    exit /b 1
)

echo.
echo [INFO] Dashboard closed successfully
pause
