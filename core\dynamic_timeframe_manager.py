"""
🎯 VIP BIG BANG DYNAMIC TIMEFRAME MANAGER
⚡ AUTOMATIC ADJUSTMENT OF ALL INDICATORS & ANALYSIS
🔧 SMART PARAMETER OPTIMIZATION BASED ON TIMEFRAME & TRADE DURATION
"""

import asyncio
import time
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging

@dataclass
class TimeframeConfig:
    """📊 Configuration for specific timeframe"""
    analysis_interval: int  # seconds
    trade_duration: int     # seconds
    data_points_needed: int
    ma_periods: Dict[str, int]
    rsi_period: int
    vortex_period: int
    volume_lookback: int
    pattern_lookback: int
    trend_lookback: int
    support_resistance_lookback: int
    breakout_lookback: int
    candle_lookback: int
    power_lookback: int
    confidence_threshold: float
    signal_strength_multiplier: float

class DynamicTimeframeManager:
    """
    🎯 DYNAMIC TIMEFRAME MANAGER
    ⚡ Automatically adjusts all indicators based on timeframe & trade duration
    🧠 Smart optimization for maximum accuracy
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("DynamicTimeframe")
        
        # Current configuration
        self.current_config: Optional[TimeframeConfig] = None
        self.analysis_interval = 15  # Default 15 seconds
        self.trade_duration = 5      # Default 5 seconds
        
        # Predefined optimal configurations
        self.timeframe_configs = self._initialize_timeframe_configs()
        
        # Performance tracking
        self.performance_history = {}
        
        self.logger.info("🎯 Dynamic Timeframe Manager initialized")
    
    def _initialize_timeframe_configs(self) -> Dict[Tuple[int, int], TimeframeConfig]:
        """🔧 Initialize optimal configurations for different timeframe combinations"""
        configs = {}
        
        # 🚀 ULTRA FAST: 5-second analysis, 5-second trades
        configs[(5, 5)] = TimeframeConfig(
            analysis_interval=5,
            trade_duration=5,
            data_points_needed=30,
            ma_periods={'ma3': 3, 'ma6': 6, 'ma9': 9},
            rsi_period=7,
            vortex_period=3,
            volume_lookback=5,
            pattern_lookback=3,
            trend_lookback=5,
            support_resistance_lookback=10,
            breakout_lookback=8,
            candle_lookback=3,
            power_lookback=5,
            confidence_threshold=0.85,
            signal_strength_multiplier=1.3
        )
        
        # ⚡ FAST: 15-second analysis, 5-second trades (Default VIP BIG BANG)
        configs[(15, 5)] = TimeframeConfig(
            analysis_interval=15,
            trade_duration=5,
            data_points_needed=50,
            ma_periods={'ma6': 6, 'ma12': 12, 'ma18': 18},
            rsi_period=14,
            vortex_period=6,
            volume_lookback=10,
            pattern_lookback=5,
            trend_lookback=10,
            support_resistance_lookback=20,
            breakout_lookback=15,
            candle_lookback=5,
            power_lookback=10,
            confidence_threshold=0.80,
            signal_strength_multiplier=1.0
        )
        
        # 🎯 STANDARD: 1-minute analysis, 5-second trades
        configs[(60, 5)] = TimeframeConfig(
            analysis_interval=60,
            trade_duration=5,
            data_points_needed=100,
            ma_periods={'ma6': 6, 'ma12': 12, 'ma24': 24},
            rsi_period=14,
            vortex_period=8,
            volume_lookback=20,
            pattern_lookback=10,
            trend_lookback=20,
            support_resistance_lookback=50,
            breakout_lookback=30,
            candle_lookback=10,
            power_lookback=20,
            confidence_threshold=0.75,
            signal_strength_multiplier=0.9
        )
        
        # 📊 MEDIUM: 1-minute analysis, 1-minute trades
        configs[(60, 60)] = TimeframeConfig(
            analysis_interval=60,
            trade_duration=60,
            data_points_needed=120,
            ma_periods={'ma12': 12, 'ma24': 24, 'ma48': 48},
            rsi_period=21,
            vortex_period=12,
            volume_lookback=30,
            pattern_lookback=15,
            trend_lookback=30,
            support_resistance_lookback=100,
            breakout_lookback=50,
            candle_lookback=15,
            power_lookback=30,
            confidence_threshold=0.70,
            signal_strength_multiplier=0.8
        )
        
        # 🕐 LONG: 5-minute analysis, 1-minute trades
        configs[(300, 60)] = TimeframeConfig(
            analysis_interval=300,
            trade_duration=60,
            data_points_needed=200,
            ma_periods={'ma20': 20, 'ma50': 50, 'ma100': 100},
            rsi_period=28,
            vortex_period=20,
            volume_lookback=50,
            pattern_lookback=25,
            trend_lookback=50,
            support_resistance_lookback=200,
            breakout_lookback=100,
            candle_lookback=25,
            power_lookback=50,
            confidence_threshold=0.65,
            signal_strength_multiplier=0.7
        )
        
        # 📈 EXTENDED: 5-minute analysis, 5-minute trades
        configs[(300, 300)] = TimeframeConfig(
            analysis_interval=300,
            trade_duration=300,
            data_points_needed=300,
            ma_periods={'ma50': 50, 'ma100': 100, 'ma200': 200},
            rsi_period=35,
            vortex_period=25,
            volume_lookback=100,
            pattern_lookback=50,
            trend_lookback=100,
            support_resistance_lookback=500,
            breakout_lookback=200,
            candle_lookback=50,
            power_lookback=100,
            confidence_threshold=0.60,
            signal_strength_multiplier=0.6
        )
        
        return configs
    
    async def set_timeframe_and_duration(self, analysis_interval: int, trade_duration: int) -> bool:
        """🎯 Set new timeframe and trade duration with automatic optimization"""
        try:
            self.logger.info(f"🔧 Setting timeframe: {analysis_interval}s analysis, {trade_duration}s trades")
            
            # Find exact match or closest configuration
            config_key = (analysis_interval, trade_duration)
            
            if config_key in self.timeframe_configs:
                # Exact match found
                self.current_config = self.timeframe_configs[config_key]
                self.logger.info(f"✅ Using predefined optimal configuration")
            else:
                # Create custom configuration
                self.current_config = await self._create_custom_config(analysis_interval, trade_duration)
                self.logger.info(f"🔧 Created custom configuration")
            
            # Update current settings
            self.analysis_interval = analysis_interval
            self.trade_duration = trade_duration
            
            # Log configuration details
            self._log_configuration_details()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to set timeframe: {e}")
            return False
    
    async def _create_custom_config(self, analysis_interval: int, trade_duration: int) -> TimeframeConfig:
        """🔧 Create custom configuration for non-standard timeframes"""
        
        # Find closest predefined configuration
        closest_config = self._find_closest_config(analysis_interval, trade_duration)
        
        # Calculate scaling factors
        analysis_scale = analysis_interval / closest_config.analysis_interval
        trade_scale = trade_duration / closest_config.trade_duration
        
        # Scale parameters intelligently
        scaled_config = TimeframeConfig(
            analysis_interval=analysis_interval,
            trade_duration=trade_duration,
            data_points_needed=max(30, int(closest_config.data_points_needed * analysis_scale)),
            ma_periods=self._scale_ma_periods(closest_config.ma_periods, analysis_scale),
            rsi_period=max(7, int(closest_config.rsi_period * analysis_scale)),
            vortex_period=max(3, int(closest_config.vortex_period * analysis_scale)),
            volume_lookback=max(5, int(closest_config.volume_lookback * analysis_scale)),
            pattern_lookback=max(3, int(closest_config.pattern_lookback * analysis_scale)),
            trend_lookback=max(5, int(closest_config.trend_lookback * analysis_scale)),
            support_resistance_lookback=max(10, int(closest_config.support_resistance_lookback * analysis_scale)),
            breakout_lookback=max(8, int(closest_config.breakout_lookback * analysis_scale)),
            candle_lookback=max(3, int(closest_config.candle_lookback * analysis_scale)),
            power_lookback=max(5, int(closest_config.power_lookback * analysis_scale)),
            confidence_threshold=max(0.6, min(0.9, closest_config.confidence_threshold * (1 + (trade_scale - 1) * 0.1))),
            signal_strength_multiplier=max(0.5, min(1.5, closest_config.signal_strength_multiplier * (2 - analysis_scale * 0.1)))
        )
        
        return scaled_config
    
    def _find_closest_config(self, analysis_interval: int, trade_duration: int) -> TimeframeConfig:
        """🔍 Find closest predefined configuration"""
        min_distance = float('inf')
        closest_config = None
        
        for (a_interval, t_duration), config in self.timeframe_configs.items():
            # Calculate distance (weighted by analysis interval more heavily)
            distance = abs(analysis_interval - a_interval) * 2 + abs(trade_duration - t_duration)
            
            if distance < min_distance:
                min_distance = distance
                closest_config = config
        
        return closest_config or self.timeframe_configs[(15, 5)]  # Fallback to default
    
    def _scale_ma_periods(self, original_periods: Dict[str, int], scale_factor: float) -> Dict[str, int]:
        """📊 Scale MA periods intelligently"""
        scaled_periods = {}
        
        for name, period in original_periods.items():
            # Scale period but keep it reasonable
            scaled_period = max(3, int(period * scale_factor))
            
            # Ensure we don't have duplicate periods
            while scaled_period in scaled_periods.values():
                scaled_period += 1
            
            scaled_periods[name] = scaled_period
        
        return scaled_periods
    
    def get_current_config(self) -> Optional[TimeframeConfig]:
        """📊 Get current timeframe configuration"""
        return self.current_config
    
    def get_indicator_parameters(self, indicator_name: str) -> Dict[str, Any]:
        """🔧 Get specific indicator parameters for current timeframe"""
        if not self.current_config:
            return {}
        
        config = self.current_config
        
        parameters = {
            'ma6': {
                'periods': config.ma_periods,
                'data_points': max(config.ma_periods.values()) + 5
            },
            'vortex': {
                'period': config.vortex_period,
                'data_points': config.vortex_period + 5
            },
            'momentum': {
                'rsi_period': config.rsi_period,
                'data_points': config.rsi_period + 5
            },
            'volume': {
                'lookback': config.volume_lookback,
                'data_points': config.volume_lookback + 5
            },
            'pattern': {
                'lookback': config.pattern_lookback,
                'data_points': config.pattern_lookback + 2
            },
            'trend': {
                'lookback': config.trend_lookback,
                'data_points': config.trend_lookback + 5
            },
            'support_resistance': {
                'lookback': config.support_resistance_lookback,
                'data_points': config.support_resistance_lookback + 10
            },
            'breakout': {
                'lookback': config.breakout_lookback,
                'data_points': config.breakout_lookback + 5
            },
            'candle': {
                'lookback': config.candle_lookback,
                'data_points': config.candle_lookback + 2
            },
            'power': {
                'lookback': config.power_lookback,
                'data_points': config.power_lookback + 5
            }
        }
        
        return parameters.get(indicator_name, {})
    
    def _log_configuration_details(self):
        """📋 Log detailed configuration information"""
        if not self.current_config:
            return
        
        config = self.current_config
        
        self.logger.info(f"🎯 TIMEFRAME CONFIGURATION:")
        self.logger.info(f"   ⏱️ Analysis Interval: {config.analysis_interval}s")
        self.logger.info(f"   💰 Trade Duration: {config.trade_duration}s")
        self.logger.info(f"   📊 Data Points Needed: {config.data_points_needed}")
        self.logger.info(f"   📈 MA Periods: {config.ma_periods}")
        self.logger.info(f"   🚀 RSI Period: {config.rsi_period}")
        self.logger.info(f"   🌪️ Vortex Period: {config.vortex_period}")
        self.logger.info(f"   🎯 Confidence Threshold: {config.confidence_threshold}")
        self.logger.info(f"   💪 Signal Multiplier: {config.signal_strength_multiplier}")
    
    def get_available_timeframes(self) -> List[Dict[str, Any]]:
        """📋 Get list of available predefined timeframes"""
        timeframes = []
        
        for (analysis_interval, trade_duration), config in self.timeframe_configs.items():
            timeframes.append({
                'analysis_interval': analysis_interval,
                'trade_duration': trade_duration,
                'name': self._get_timeframe_name(analysis_interval, trade_duration),
                'description': self._get_timeframe_description(analysis_interval, trade_duration),
                'confidence_threshold': config.confidence_threshold,
                'recommended_for': self._get_timeframe_recommendation(analysis_interval, trade_duration)
            })
        
        return sorted(timeframes, key=lambda x: (x['analysis_interval'], x['trade_duration']))
    
    def _get_timeframe_name(self, analysis_interval: int, trade_duration: int) -> str:
        """🏷️ Get human-readable name for timeframe"""
        names = {
            (5, 5): "🚀 Ultra Fast",
            (15, 5): "⚡ VIP BIG BANG Default",
            (60, 5): "🎯 Standard Fast",
            (60, 60): "📊 Medium",
            (300, 60): "🕐 Long Analysis",
            (300, 300): "📈 Extended"
        }
        
        return names.get((analysis_interval, trade_duration), f"Custom {analysis_interval}s/{trade_duration}s")
    
    def _get_timeframe_description(self, analysis_interval: int, trade_duration: int) -> str:
        """📝 Get description for timeframe"""
        return f"{analysis_interval}s analysis, {trade_duration}s trades"
    
    def _get_timeframe_recommendation(self, analysis_interval: int, trade_duration: int) -> str:
        """💡 Get recommendation for when to use this timeframe"""
        recommendations = {
            (5, 5): "Maximum speed, high-frequency trading",
            (15, 5): "Optimal balance of speed and accuracy",
            (60, 5): "More stable signals, fast execution",
            (60, 60): "Balanced approach, medium-term trades",
            (300, 60): "Detailed analysis, quick execution",
            (300, 300): "Long-term analysis, patient trading"
        }
        
        return recommendations.get((analysis_interval, trade_duration), "Custom trading strategy")
