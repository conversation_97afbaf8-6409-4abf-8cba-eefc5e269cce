"""
🧪 QUICK TEST - Hardware & Browser Fingerprinting
🔍 تست سریع سیستم تشخیص سخت‌افزار و مرورگر
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.advanced_hardware_detector import AdvancedHardwareDetector
from core.browser_fingerprint_manager import BrowserFingerprintManager
from core.integrated_system_manager import IntegratedSystemManager
from utils.logger import setup_logger

def test_hardware_detection():
    """🔍 تست تشخیص سخت‌افزار"""
    print("🔍 Testing Hardware Detection...")
    print("=" * 50)
    
    try:
        # Initialize detector
        detector = AdvancedHardwareDetector()
        
        # Get complete info
        complete_info = detector.get_complete_info()
        
        # Display results
        hardware = complete_info.get("hardware", {})
        
        # CPU Info
        cpu = hardware.get("cpu", {})
        print(f"💻 CPU: {cpu.get('brand', cpu.get('name', 'Unknown'))}")
        print(f"   Cores: {cpu.get('cores_physical', 0)}P/{cpu.get('cores_logical', 0)}L")
        print(f"   Vendor: {cpu.get('vendor', 'Unknown')}")
        
        # RAM Info
        ram = hardware.get("ram", {})
        print(f"🧠 RAM: {ram.get('total_gb', 0)}GB total")
        print(f"   Modules: {ram.get('used_slots', 0)}/{ram.get('total_slots', 0)} slots")
        
        # GPU Info
        gpu = hardware.get("gpu", {})
        if gpu.get("detected", False):
            primary = gpu.get("primary_gpu", {})
            print(f"🎮 GPU: {primary.get('name', 'Unknown')}")
            print(f"   Cards: {len(gpu.get('cards', []))}")
        else:
            print("🎮 GPU: Not detected")
        
        # Motherboard Info
        mb = hardware.get("motherboard", {})
        print(f"🔧 Motherboard: {mb.get('manufacturer', 'Unknown')} {mb.get('product', 'Unknown')}")
        
        # VM Detection
        vm_detection = complete_info.get("vm_detection", {})
        if vm_detection.get("is_vm", False):
            print(f"🚨 VM Detected: {vm_detection.get('vm_type', 'Unknown')} ({vm_detection.get('confidence', 0)}%)")
            for indicator in vm_detection.get("indicators", []):
                print(f"   - {indicator}")
        else:
            print("✅ Physical machine confirmed")
        
        # Fingerprint
        fingerprint = complete_info.get("fingerprint", {})
        if fingerprint:
            print(f"🔐 Fingerprint: {fingerprint.get('short_hash', 'Unknown')}")
        
        print("✅ Hardware detection test completed")
        return True
        
    except Exception as e:
        print(f"❌ Hardware detection test failed: {e}")
        return False

def test_browser_fingerprinting():
    """🌐 تست fingerprinting مرورگر"""
    print("\n🌐 Testing Browser Fingerprinting...")
    print("=" * 50)
    
    try:
        # Initialize manager
        manager = BrowserFingerprintManager()
        
        # Get fingerprinting script
        script = manager.get_browser_fingerprint_script()
        print(f"📜 Fingerprint script generated ({len(script)} characters)")
        
        # Get Chrome extension script
        extension_script = manager.get_chrome_extension_script()
        print(f"🔧 Extension script generated ({len(extension_script)} characters)")
        
        # Show script preview
        print("\n📋 Script Preview (first 200 chars):")
        print(script[:200] + "...")
        
        print("✅ Browser fingerprinting test completed")
        return True
        
    except Exception as e:
        print(f"❌ Browser fingerprinting test failed: {e}")
        return False

def test_integrated_system():
    """🚀 تست سیستم یکپارچه"""
    print("\n🚀 Testing Integrated System...")
    print("=" * 50)
    
    try:
        # Initialize integrated system
        system_manager = IntegratedSystemManager()
        
        # Wait a moment for initialization
        import time
        time.sleep(2)
        
        # Get complete system info
        complete_info = system_manager.get_complete_system_info()
        
        # Security status
        security_status = complete_info.get("security_status", {})
        print(f"🛡️ Security Risk Level: {security_status.get('risk_level', 'UNKNOWN')}")
        print(f"🔍 VM Detected: {'Yes' if security_status.get('vm_detected', False) else 'No'}")
        print(f"🤖 Bot Detected: {'Yes' if security_status.get('bot_detected', False) else 'No'}")
        print(f"📊 Confidence: {security_status.get('confidence', 0)}%")
        
        # Security indicators
        indicators = security_status.get("indicators", [])
        if indicators:
            print("🚨 Security Indicators:")
            for indicator in indicators:
                print(f"   - {indicator}")
        
        # Environment safety
        is_safe = system_manager.is_safe_environment()
        print(f"✅ Environment Safe: {'Yes' if is_safe else 'No'}")
        
        # Generate recommendations
        recommendations = system_manager.generate_recommendations()
        print("💡 Recommendations:")
        for rec in recommendations:
            print(f"   {rec}")
        
        # Export report
        report = system_manager.export_system_report("test_system_report.json")
        if report:
            print("📄 System report exported to: test_system_report.json")
        
        print("✅ Integrated system test completed")
        return True
        
    except Exception as e:
        print(f"❌ Integrated system test failed: {e}")
        return False

def show_fingerprint_script():
    """📜 نمایش اسکریپت fingerprinting"""
    print("\n📜 Browser Fingerprinting Script:")
    print("=" * 50)
    
    try:
        detector = AdvancedHardwareDetector()
        script = detector.get_browser_fingerprint_script()
        
        # Show key parts of the script
        lines = script.split('\n')
        important_lines = []
        
        for line in lines:
            if any(keyword in line.lower() for keyword in [
                'devicememory', 'hardwareconcurrency', 'gpu', 'webgl', 
                'canvas', 'audio', 'webdriver', 'phantom'
            ]):
                important_lines.append(line.strip())
        
        print("🔍 Key Detection Features:")
        for line in important_lines[:10]:  # Show first 10 important lines
            if line:
                print(f"   {line}")
        
        print(f"\n📊 Total script size: {len(script)} characters")
        print(f"📊 Total lines: {len(lines)}")
        
    except Exception as e:
        print(f"❌ Script display failed: {e}")

def main():
    """تابع اصلی تست"""
    print("🧪 VIP BIG BANG - Quick Fingerprinting Test")
    print("=" * 60)
    print("🔍 Testing advanced hardware and browser detection system")
    print("=" * 60)
    
    # Setup logging
    logger = setup_logger("QuickTest")
    
    # Test results
    results = {
        "hardware": False,
        "browser": False,
        "integrated": False
    }
    
    try:
        # Test 1: Hardware Detection
        results["hardware"] = test_hardware_detection()
        
        # Test 2: Browser Fingerprinting
        results["browser"] = test_browser_fingerprinting()
        
        # Test 3: Integrated System
        results["integrated"] = test_integrated_system()
        
        # Show fingerprinting script
        show_fingerprint_script()
        
        # Final results
        print("\n🏆 Test Results Summary:")
        print("=" * 50)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name.upper()}: {status}")
        
        all_passed = all(results.values())
        if all_passed:
            print("\n🎉 All tests passed! System is ready.")
        else:
            print("\n⚠️ Some tests failed. Check the logs above.")
        
        # Additional info
        print("\n📋 Next Steps:")
        print("1. Run 'python test_integrated_system.py' for full UI test")
        print("2. Check 'test_system_report.json' for detailed report")
        print("3. Integrate with main application using IntegratedSystemManager")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
