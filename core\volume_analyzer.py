"""
VIP BIG BANG Enterprise - Volume Per Candle Analyzer
Volume analysis for each candle with PulseBar visualization
"""

import numpy as np
import pandas as pd
from typing import Dict
import logging
from datetime import datetime

class VolumeAnalyzer:
    """
    Volume Per Candle - Original VIP BIG BANG indicator
    Analyzes volume for each candle to confirm market movements
    Displays as PulseBar in UI
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("VolumeAnalyzer")
        
        # Volume analysis parameters
        self.volume_period = 20
        self.volume_spike_threshold = 2.0  # 2x average volume
        self.high_volume_threshold = 1.5   # 1.5x average volume
        
        # PulseBar parameters
        self.pulse_sensitivity = 0.8
        self.pulse_decay = 0.95
        
        self.logger.debug("Volume Analyzer initialized")
    
    def calculate_volume_metrics(self, data: pd.DataFrame) -> Dict:
        """Calculate comprehensive volume metrics"""
        if 'volume' not in data.columns:
            return {
                'current_volume': 0,
                'avg_volume': 0,
                'volume_ratio': 1.0,
                'volume_trend': 'NEUTRAL'
            }
        
        volume = data['volume'].fillna(0)
        
        if len(volume) < self.volume_period:
            return {
                'current_volume': volume.iloc[-1] if not volume.empty else 0,
                'avg_volume': volume.mean() if not volume.empty else 0,
                'volume_ratio': 1.0,
                'volume_trend': 'NEUTRAL'
            }
        
        # Current and average volume
        current_volume = volume.iloc[-1]
        avg_volume = volume.tail(self.volume_period).mean()
        
        # Volume ratio
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Volume trend analysis
        recent_avg = volume.tail(5).mean()
        older_avg = volume.tail(self.volume_period).head(5).mean()
        
        if recent_avg > older_avg * 1.2:
            volume_trend = 'INCREASING'
        elif recent_avg < older_avg * 0.8:
            volume_trend = 'DECREASING'
        else:
            volume_trend = 'STABLE'
        
        return {
            'current_volume': current_volume,
            'avg_volume': avg_volume,
            'volume_ratio': volume_ratio,
            'volume_trend': volume_trend
        }
    
    def detect_volume_spikes(self, data: pd.DataFrame) -> Dict:
        """Detect volume spikes and their significance"""
        volume_metrics = self.calculate_volume_metrics(data)
        
        volume_ratio = volume_metrics['volume_ratio']
        spike_type = 'NORMAL'
        spike_strength = 0.0
        
        if volume_ratio >= self.volume_spike_threshold:
            spike_type = 'SPIKE'
            spike_strength = min((volume_ratio - 1.0) / 2.0, 1.0)
        elif volume_ratio >= self.high_volume_threshold:
            spike_type = 'HIGH'
            spike_strength = min((volume_ratio - 1.0) / 1.5, 0.8)
        elif volume_ratio <= 0.5:
            spike_type = 'LOW'
            spike_strength = min((1.0 - volume_ratio) / 0.5, 0.6)
        
        return {
            'spike_type': spike_type,
            'spike_strength': spike_strength,
            'volume_ratio': volume_ratio
        }
    
    def analyze_volume_price_relationship(self, data: pd.DataFrame) -> Dict:
        """Analyze relationship between volume and price movement"""
        if len(data) < 2:
            return {
                'relationship': 'NEUTRAL',
                'confirmation': 0.0
            }
        
        # Price change
        prices = data['close'] if 'close' in data.columns else data['price']
        price_change = prices.pct_change().iloc[-1]
        
        # Volume metrics
        volume_metrics = self.calculate_volume_metrics(data)
        volume_ratio = volume_metrics['volume_ratio']
        
        # Analyze relationship
        relationship = 'NEUTRAL'
        confirmation = 0.0
        
        if abs(price_change) > 0.001:  # Significant price movement
            if volume_ratio > self.high_volume_threshold:
                # High volume confirms price movement
                if price_change > 0:
                    relationship = 'BULLISH_CONFIRMED'
                else:
                    relationship = 'BEARISH_CONFIRMED'
                confirmation = min(volume_ratio / 2.0, 1.0)
            elif volume_ratio < 0.7:
                # Low volume weakens price movement
                relationship = 'WEAK_MOVEMENT'
                confirmation = 0.3
            else:
                # Normal volume
                relationship = 'NORMAL_MOVEMENT'
                confirmation = 0.5
        
        return {
            'relationship': relationship,
            'confirmation': confirmation,
            'price_change': price_change,
            'volume_ratio': volume_ratio
        }
    
    def calculate_pulse_bar_intensity(self, data: pd.DataFrame) -> Dict:
        """Calculate PulseBar intensity for UI visualization"""
        volume_metrics = self.calculate_volume_metrics(data)
        spike_analysis = self.detect_volume_spikes(data)
        
        # Base intensity from volume ratio
        base_intensity = min(volume_metrics['volume_ratio'] / 2.0, 1.0)
        
        # Boost intensity for spikes
        spike_boost = 0.0
        if spike_analysis['spike_type'] == 'SPIKE':
            spike_boost = 0.3
        elif spike_analysis['spike_type'] == 'HIGH':
            spike_boost = 0.2
        
        # Final intensity
        pulse_intensity = min(base_intensity + spike_boost, 1.0)
        
        # Pulse color based on price movement
        prices = data['close'] if 'close' in data.columns else data['price']
        if len(prices) >= 2:
            price_change = prices.iloc[-1] - prices.iloc[-2]
            if price_change > 0:
                pulse_color = 'GREEN'
            elif price_change < 0:
                pulse_color = 'RED'
            else:
                pulse_color = 'YELLOW'
        else:
            pulse_color = 'YELLOW'
        
        return {
            'intensity': pulse_intensity,
            'color': pulse_color,
            'volume_level': spike_analysis['spike_type']
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main volume analysis function
        Returns comprehensive volume analysis with PulseBar data
        """
        try:
            if len(data) < 2:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for volume analysis'
                }
            
            # Volume metrics
            volume_metrics = self.calculate_volume_metrics(data)
            
            # Volume spikes
            spike_analysis = self.detect_volume_spikes(data)
            
            # Volume-price relationship
            vp_relationship = self.analyze_volume_price_relationship(data)
            
            # PulseBar data
            pulse_data = self.calculate_pulse_bar_intensity(data)
            
            # Calculate overall score
            score = 0.5  # Neutral base
            confidence = 0.0
            
            # Adjust score based on volume confirmation
            if vp_relationship['relationship'] == 'BULLISH_CONFIRMED':
                score += 0.3 * vp_relationship['confirmation']
            elif vp_relationship['relationship'] == 'BEARISH_CONFIRMED':
                score -= 0.3 * vp_relationship['confirmation']
            elif vp_relationship['relationship'] == 'WEAK_MOVEMENT':
                # Reduce confidence for weak movements
                score = 0.5  # Stay neutral
            
            # Adjust for volume spikes
            if spike_analysis['spike_type'] == 'SPIKE':
                confidence += 0.3
            elif spike_analysis['spike_type'] == 'HIGH':
                confidence += 0.2
            elif spike_analysis['spike_type'] == 'LOW':
                confidence -= 0.1
            
            # Base confidence from volume confirmation
            confidence += vp_relationship['confirmation'] * 0.5
            
            # Ensure bounds
            score = max(0, min(1, score))
            confidence = max(0, min(1, confidence))
            
            # Determine direction
            if score > 0.6:
                direction = 'UP'
            elif score < 0.4:
                direction = 'DOWN'
            else:
                direction = 'NEUTRAL'
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'volume_metrics': volume_metrics,
                'spike_analysis': spike_analysis,
                'vp_relationship': vp_relationship,
                'pulse_data': pulse_data,
                'details': f'Volume: {spike_analysis["spike_type"]}, Confirmation: {vp_relationship["relationship"]}'
            }
            
        except Exception as e:
            self.logger.error(f"Volume analysis error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'details': f'Volume analysis failed: {str(e)}'
            }
