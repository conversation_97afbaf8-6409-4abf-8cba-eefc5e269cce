#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 TEST QUOTEX CONNECTION
🔍 بررسی تمام روش‌های اتصال به Quotex
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_chrome_devtools():
    """🔍 Test Chrome DevTools connection"""
    try:
        print("🔍 Testing Chrome DevTools...")
        response = requests.get("http://localhost:9222/json", timeout=5)
        
        if response.status_code == 200:
            tabs = response.json()
            print(f"✅ Chrome DevTools active - {len(tabs)} tabs open")
            
            # Check for Quotex tabs
            quotex_tabs = []
            for tab in tabs:
                url = tab.get('url', '')
                if 'quotex' in url.lower():
                    quotex_tabs.append(tab)
            
            if quotex_tabs:
                print(f"🎯 Found {len(quotex_tabs)} Quotex tabs:")
                for tab in quotex_tabs:
                    print(f"   📱 {tab.get('title', 'Unknown')} - {tab.get('url', '')}")
                return True
            else:
                print("⚠️ No Quotex tabs found")
                return False
        else:
            print(f"❌ Chrome DevTools not responding - Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chrome DevTools test failed: {e}")
        return False

def test_quotex_access():
    """🌐 Test direct Quotex access"""
    try:
        print("🌐 Testing direct Quotex access...")
        
        quotex_urls = [
            "https://quotex.io",
            "https://quotex.io/en/sign-in",
            "https://qxbroker.com"
        ]
        
        working_urls = []
        for url in quotex_urls:
            try:
                response = requests.get(url, timeout=10, allow_redirects=True)
                if response.status_code == 200:
                    working_urls.append(url)
                    print(f"✅ {url} - Working")
                else:
                    print(f"⚠️ {url} - Status: {response.status_code}")
            except Exception as e:
                print(f"❌ {url} - Error: {str(e)[:50]}")
        
        if working_urls:
            print(f"🎯 {len(working_urls)} Quotex URLs are accessible")
            return True
        else:
            print("❌ No Quotex URLs accessible")
            return False
            
    except Exception as e:
        print(f"❌ Quotex access test failed: {e}")
        return False

def test_chrome_installation():
    """🔍 Test Chrome installation"""
    try:
        print("🔍 Testing Chrome installation...")
        
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                print(f"✅ Chrome found: {path}")
                return True
        
        print("❌ Chrome not found")
        return False
        
    except Exception as e:
        print(f"❌ Chrome test failed: {e}")
        return False

def test_extension_files():
    """🔧 Test extension files"""
    try:
        print("🔧 Testing extension files...")
        
        extension_files = [
            "chrome_extension/manifest.json",
            "chrome_extension/content.js",
            "chrome_extension/background.js"
        ]
        
        missing_files = []
        for file_path in extension_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path} - Found")
            else:
                print(f"❌ {file_path} - Missing")
                missing_files.append(file_path)
        
        if not missing_files:
            print("✅ All extension files present")
            return True
        else:
            print(f"❌ {len(missing_files)} extension files missing")
            return False
            
    except Exception as e:
        print(f"❌ Extension test failed: {e}")
        return False

def test_profile_directory():
    """📁 Test Chrome profile directory"""
    try:
        print("📁 Testing Chrome profile directory...")
        
        profile_path = os.path.join(os.path.expanduser("~"), "VIP_BIG_BANG_Chrome", "VIP_BIG_BANG_Quotex")
        
        if os.path.exists(profile_path):
            print(f"✅ Profile directory exists: {profile_path}")
            
            # Check profile contents
            profile_files = os.listdir(profile_path)
            print(f"📊 Profile contains {len(profile_files)} items")
            
            # Check for important files
            important_files = ["Preferences", "Local State", "History"]
            found_files = [f for f in important_files if f in profile_files]
            
            if found_files:
                print(f"✅ Found {len(found_files)} important profile files")
                return True
            else:
                print("⚠️ Profile directory empty or incomplete")
                return False
        else:
            print(f"❌ Profile directory not found: {profile_path}")
            return False
            
    except Exception as e:
        print(f"❌ Profile test failed: {e}")
        return False

def create_new_quotex_tab():
    """🆕 Create new Quotex tab"""
    try:
        print("🆕 Creating new Quotex tab...")
        
        # Try to create new tab via DevTools
        new_tab_url = "http://localhost:9222/json/new?https://quotex.io/en/sign-in"
        response = requests.get(new_tab_url, timeout=5)
        
        if response.status_code == 200:
            tab_info = response.json()
            print(f"✅ New Quotex tab created: {tab_info.get('id', 'Unknown ID')}")
            return True
        else:
            print(f"❌ Failed to create tab - Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Tab creation failed: {e}")
        return False

def main():
    """🧪 Main test function"""
    print("🧪" + "=" * 60 + "🧪")
    print("🔍" + " " * 15 + "QUOTEX CONNECTION TEST" + " " * 15 + "🔍")
    print("🎯" + " " * 12 + "Testing All Connection Methods" + " " * 12 + "🎯")
    print("🧪" + "=" * 60 + "🧪")
    
    tests = [
        ("🔍 Chrome Installation", test_chrome_installation),
        ("📁 Profile Directory", test_profile_directory),
        ("🔧 Extension Files", test_extension_files),
        ("🌐 Quotex Access", test_quotex_access),
        ("🔍 Chrome DevTools", test_chrome_devtools)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            failed += 1
            print(f"💥 {test_name} - CRASHED: {e}")
    
    print(f"\n🏆" + "=" * 50 + "🏆")
    print(f"📊 TEST RESULTS:")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Quotex connection system is ready!")
        
        # Try to create new tab if DevTools is available
        print(f"\n🚀 Attempting to open Quotex...")
        if create_new_quotex_tab():
            print(f"🏆 SUCCESS! Quotex should be opening now!")
        
    else:
        print(f"\n⚠️ {failed} tests failed.")
        print(f"🔧 Recommendations:")
        
        if not test_chrome_installation():
            print(f"   📥 Install Google Chrome")
        
        if not test_quotex_access():
            print(f"   🌐 Check internet connection")
            print(f"   🔧 Try VPN if Quotex is blocked")
        
        if not test_chrome_devtools():
            print(f"   🚀 Run: python persistent_chrome_manager.py")
    
    print(f"\n🔥 Test completed!")

if __name__ == "__main__":
    main()
