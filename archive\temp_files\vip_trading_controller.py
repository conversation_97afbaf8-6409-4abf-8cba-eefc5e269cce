#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎮 VIP BIG BANG Trading Controller
کنترلر پیشرفته ترید با قابلیت‌های کامل
"""

import sys
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

# Add project root to path
from pathlib import Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import setup_logger

class VIPTradingController:
    """
    🎮 VIP Trading Controller
    کنترلر پیشرفته ترید
    """
    
    def __init__(self):
        self.logger = setup_logger("TradingController")
        
        # Trading parameters
        self.trading_config = {
            'timeframe': 15,  # 15 seconds analysis
            'trade_duration': 5,  # 5 seconds trades
            'min_confidence': 0.8,  # 80% minimum confidence
            'min_confirmations': 8,  # 8 signal confirmations
            'risk_per_trade': 0.02,  # 2% of balance per trade
            'max_daily_trades': 50,  # Maximum trades per day
            'max_consecutive_losses': 3,  # Stop after 3 consecutive losses
            'target_win_rate': 95,  # Target 95% win rate
            'daily_profit_target': 1000,  # $1000 daily target
            'stop_loss_amount': 100  # Stop if daily loss exceeds $100
        }
        
        # Trading state
        self.trading_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'consecutive_losses': 0,
            'daily_profit': 0.0,
            'daily_trades': 0,
            'session_start': datetime.now(),
            'last_trade_time': None,
            'current_streak': 0,
            'best_streak': 0
        }
        
        # Risk management
        self.risk_manager = {
            'is_trading_allowed': True,
            'risk_level': 'LOW',  # LOW, MEDIUM, HIGH
            'last_risk_check': datetime.now(),
            'daily_loss': 0.0,
            'account_protection': True
        }
        
        self.logger.info("🎮 VIP Trading Controller initialized")
    
    def update_trading_config(self, config: Dict):
        """📝 به‌روزرسانی تنظیمات ترید"""
        try:
            self.trading_config.update(config)
            self.logger.info(f"📝 Trading config updated: {config}")
        except Exception as e:
            self.logger.error(f"❌ Error updating config: {e}")
    
    def should_allow_trade(self, analysis: Dict, account_info: Dict) -> Dict:
        """🛡️ بررسی مجوز ترید"""
        try:
            result = {
                'allowed': False,
                'reason': '',
                'risk_level': self.risk_manager['risk_level'],
                'checks': {}
            }
            
            # Check 1: Basic trading permission
            if not self.risk_manager['is_trading_allowed']:
                result['reason'] = 'Trading is disabled'
                return result
            
            result['checks']['trading_enabled'] = True
            
            # Check 2: Daily trade limit
            if self.trading_stats['daily_trades'] >= self.trading_config['max_daily_trades']:
                result['reason'] = f"Daily trade limit reached ({self.trading_config['max_daily_trades']})"
                return result
            
            result['checks']['daily_limit'] = True
            
            # Check 3: Consecutive losses
            if self.trading_stats['consecutive_losses'] >= self.trading_config['max_consecutive_losses']:
                result['reason'] = f"Too many consecutive losses ({self.trading_stats['consecutive_losses']})"
                return result
            
            result['checks']['consecutive_losses'] = True
            
            # Check 4: Daily loss limit
            if self.risk_manager['daily_loss'] >= self.trading_config['stop_loss_amount']:
                result['reason'] = f"Daily loss limit reached (${self.risk_manager['daily_loss']:.2f})"
                return result
            
            result['checks']['daily_loss'] = True
            
            # Check 5: Account balance
            balance = account_info.get('balance', 0)
            min_balance = 1.0  # Minimum $1
            if balance < min_balance:
                result['reason'] = f"Insufficient balance (${balance:.2f} < ${min_balance})"
                return result
            
            result['checks']['balance'] = True
            
            # Check 6: Analysis confidence
            confidence = analysis.get('confidence', 0)
            if confidence < self.trading_config['min_confidence']:
                result['reason'] = f"Low confidence ({confidence:.3f} < {self.trading_config['min_confidence']})"
                return result
            
            result['checks']['confidence'] = True
            
            # Check 7: Signal confirmations
            signals = analysis.get('signals', {})
            confirmations = sum(1 for signal in signals.values() if signal.get('strength', 0) > 0.6)
            if confirmations < self.trading_config['min_confirmations']:
                result['reason'] = f"Insufficient confirmations ({confirmations} < {self.trading_config['min_confirmations']})"
                return result
            
            result['checks']['confirmations'] = True
            
            # Check 8: Market direction
            direction = analysis.get('direction', 'NEUTRAL')
            if direction == 'NEUTRAL':
                result['reason'] = 'No clear market direction'
                return result
            
            result['checks']['direction'] = True
            
            # Check 9: Time-based restrictions
            now = datetime.now()
            if self.trading_stats['last_trade_time']:
                time_since_last = (now - self.trading_stats['last_trade_time']).total_seconds()
                if time_since_last < 30:  # Minimum 30 seconds between trades
                    result['reason'] = f"Too soon since last trade ({time_since_last:.1f}s < 30s)"
                    return result
            
            result['checks']['timing'] = True
            
            # Check 10: Profit target reached
            if self.trading_stats['daily_profit'] >= self.trading_config['daily_profit_target']:
                result['reason'] = f"Daily profit target reached (${self.trading_stats['daily_profit']:.2f})"
                return result
            
            result['checks']['profit_target'] = True
            
            # All checks passed
            result['allowed'] = True
            result['reason'] = 'All checks passed'
            
            self.logger.info(f"🛡️ Trade approved: {result['reason']}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in trade permission check: {e}")
            return {'allowed': False, 'reason': f'Error: {e}', 'checks': {}}
    
    def calculate_trade_amount(self, account_info: Dict, analysis: Dict) -> float:
        """💰 محاسبه مقدار ترید"""
        try:
            balance = account_info.get('balance', 0)
            confidence = analysis.get('confidence', 0)
            
            # Base amount (2% of balance)
            base_amount = balance * self.trading_config['risk_per_trade']
            
            # Adjust based on confidence
            confidence_multiplier = min(confidence / 0.8, 1.5)  # Max 1.5x for high confidence
            
            # Adjust based on consecutive wins
            streak_multiplier = 1.0
            if self.trading_stats['current_streak'] > 0:
                streak_multiplier = min(1.0 + (self.trading_stats['current_streak'] * 0.1), 2.0)
            
            # Adjust based on risk level
            risk_multipliers = {'LOW': 0.8, 'MEDIUM': 1.0, 'HIGH': 1.2}
            risk_multiplier = risk_multipliers.get(self.risk_manager['risk_level'], 1.0)
            
            # Calculate final amount
            final_amount = base_amount * confidence_multiplier * streak_multiplier * risk_multiplier
            
            # Apply limits
            min_amount = 1.0
            max_amount = balance * 0.1  # Maximum 10% of balance
            
            final_amount = max(min_amount, min(final_amount, max_amount))
            
            self.logger.info(f"💰 Trade amount calculated: ${final_amount:.2f} (Base: ${base_amount:.2f}, Confidence: {confidence_multiplier:.2f}x, Streak: {streak_multiplier:.2f}x)")
            
            return final_amount
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating trade amount: {e}")
            return 1.0  # Default minimum amount
    
    def update_trade_result(self, trade_result: Dict):
        """📊 به‌روزرسانی نتیجه ترید"""
        try:
            success = trade_result.get('success', False)
            result = trade_result.get('result', 'unknown')
            amount = trade_result.get('amount', 0)
            profit = trade_result.get('profit', 0)
            
            # Update basic stats
            self.trading_stats['total_trades'] += 1
            self.trading_stats['daily_trades'] += 1
            self.trading_stats['last_trade_time'] = datetime.now()
            
            if success and result == 'win':
                # Winning trade
                self.trading_stats['winning_trades'] += 1
                self.trading_stats['consecutive_losses'] = 0
                self.trading_stats['current_streak'] += 1
                self.trading_stats['daily_profit'] += profit
                
                if self.trading_stats['current_streak'] > self.trading_stats['best_streak']:
                    self.trading_stats['best_streak'] = self.trading_stats['current_streak']
                
                self.logger.info(f"🏆 Winning trade: +${profit:.2f}, Streak: {self.trading_stats['current_streak']}")
                
            else:
                # Losing trade
                self.trading_stats['losing_trades'] += 1
                self.trading_stats['consecutive_losses'] += 1
                self.trading_stats['current_streak'] = 0
                self.trading_stats['daily_profit'] -= amount
                self.risk_manager['daily_loss'] += amount
                
                self.logger.warning(f"❌ Losing trade: -${amount:.2f}, Consecutive losses: {self.trading_stats['consecutive_losses']}")
            
            # Update risk level
            self.update_risk_level()
            
            # Check if trading should be stopped
            self.check_trading_limits()
            
        except Exception as e:
            self.logger.error(f"❌ Error updating trade result: {e}")
    
    def update_risk_level(self):
        """⚠️ به‌روزرسانی سطح ریسک"""
        try:
            # Calculate win rate
            total_trades = self.trading_stats['total_trades']
            if total_trades == 0:
                win_rate = 0
            else:
                win_rate = (self.trading_stats['winning_trades'] / total_trades) * 100
            
            # Determine risk level
            if win_rate >= 90 and self.trading_stats['consecutive_losses'] == 0:
                new_risk_level = 'LOW'
            elif win_rate >= 80 and self.trading_stats['consecutive_losses'] <= 1:
                new_risk_level = 'MEDIUM'
            else:
                new_risk_level = 'HIGH'
            
            if new_risk_level != self.risk_manager['risk_level']:
                self.risk_manager['risk_level'] = new_risk_level
                self.logger.info(f"⚠️ Risk level updated to: {new_risk_level}")
            
        except Exception as e:
            self.logger.error(f"❌ Error updating risk level: {e}")
    
    def check_trading_limits(self):
        """🚨 بررسی محدودیت‌های ترید"""
        try:
            # Check daily profit target
            if self.trading_stats['daily_profit'] >= self.trading_config['daily_profit_target']:
                self.risk_manager['is_trading_allowed'] = False
                self.logger.info(f"🎯 Daily profit target reached: ${self.trading_stats['daily_profit']:.2f}")
            
            # Check daily loss limit
            if self.risk_manager['daily_loss'] >= self.trading_config['stop_loss_amount']:
                self.risk_manager['is_trading_allowed'] = False
                self.logger.warning(f"🚨 Daily loss limit reached: ${self.risk_manager['daily_loss']:.2f}")
            
            # Check consecutive losses
            if self.trading_stats['consecutive_losses'] >= self.trading_config['max_consecutive_losses']:
                self.risk_manager['is_trading_allowed'] = False
                self.logger.warning(f"🚨 Too many consecutive losses: {self.trading_stats['consecutive_losses']}")
            
            # Check daily trade limit
            if self.trading_stats['daily_trades'] >= self.trading_config['max_daily_trades']:
                self.risk_manager['is_trading_allowed'] = False
                self.logger.info(f"📊 Daily trade limit reached: {self.trading_stats['daily_trades']}")
            
        except Exception as e:
            self.logger.error(f"❌ Error checking trading limits: {e}")
    
    def get_trading_status(self) -> Dict:
        """📊 دریافت وضعیت ترید"""
        try:
            total_trades = self.trading_stats['total_trades']
            win_rate = (self.trading_stats['winning_trades'] / total_trades * 100) if total_trades > 0 else 0
            
            session_duration = datetime.now() - self.trading_stats['session_start']
            
            return {
                'trading_allowed': self.risk_manager['is_trading_allowed'],
                'risk_level': self.risk_manager['risk_level'],
                'total_trades': total_trades,
                'winning_trades': self.trading_stats['winning_trades'],
                'losing_trades': self.trading_stats['losing_trades'],
                'win_rate': win_rate,
                'consecutive_losses': self.trading_stats['consecutive_losses'],
                'current_streak': self.trading_stats['current_streak'],
                'best_streak': self.trading_stats['best_streak'],
                'daily_profit': self.trading_stats['daily_profit'],
                'daily_loss': self.risk_manager['daily_loss'],
                'daily_trades': self.trading_stats['daily_trades'],
                'session_duration': str(session_duration).split('.')[0],
                'profit_target': self.trading_config['daily_profit_target'],
                'loss_limit': self.trading_config['stop_loss_amount'],
                'trade_limit': self.trading_config['max_daily_trades']
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting trading status: {e}")
            return {}
    
    def reset_daily_stats(self):
        """🔄 ریست آمار روزانه"""
        try:
            self.trading_stats.update({
                'daily_profit': 0.0,
                'daily_trades': 0,
                'consecutive_losses': 0,
                'current_streak': 0,
                'session_start': datetime.now(),
                'last_trade_time': None
            })
            
            self.risk_manager.update({
                'is_trading_allowed': True,
                'risk_level': 'LOW',
                'daily_loss': 0.0
            })
            
            self.logger.info("🔄 Daily stats reset")
            
        except Exception as e:
            self.logger.error(f"❌ Error resetting daily stats: {e}")
    
    def enable_trading(self):
        """✅ فعال‌سازی ترید"""
        self.risk_manager['is_trading_allowed'] = True
        self.logger.info("✅ Trading enabled")
    
    def disable_trading(self):
        """❌ غیرفعال‌سازی ترید"""
        self.risk_manager['is_trading_allowed'] = False
        self.logger.info("❌ Trading disabled")
    
    def save_session_data(self, filename: str = None):
        """💾 ذخیره داده‌های جلسه"""
        try:
            if not filename:
                filename = f"trading_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            session_data = {
                'trading_config': self.trading_config,
                'trading_stats': self.trading_stats,
                'risk_manager': self.risk_manager,
                'session_end': datetime.now().isoformat()
            }
            
            # Convert datetime objects to strings
            if self.trading_stats['last_trade_time']:
                session_data['trading_stats']['last_trade_time'] = self.trading_stats['last_trade_time'].isoformat()
            session_data['trading_stats']['session_start'] = self.trading_stats['session_start'].isoformat()
            session_data['risk_manager']['last_risk_check'] = self.risk_manager['last_risk_check'].isoformat()
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"💾 Session data saved to: {filename}")
            
        except Exception as e:
            self.logger.error(f"❌ Error saving session data: {e}")

def main():
    """تست کنترلر ترید"""
    print("🎮 VIP Trading Controller Test")
    print("-" * 30)
    
    controller = VIPTradingController()
    
    # Test trade permission
    test_analysis = {
        'confidence': 0.85,
        'direction': 'CALL',
        'overall_score': 0.75,
        'signals': {
            'ma6': {'strength': 0.8},
            'vortex': {'strength': 0.7},
            'volume': {'strength': 0.9},
            'trap_candle': {'strength': 0.6},
            'shadow_candle': {'strength': 0.8},
            'strong_level': {'strength': 0.7},
            'fake_breakout': {'strength': 0.9},
            'momentum': {'strength': 0.8},
            'trend': {'strength': 0.7},
            'buyer_seller': {'strength': 0.6}
        }
    }
    
    test_account = {
        'balance': 1000.0,
        'account_type': 'demo'
    }
    
    # Check trade permission
    permission = controller.should_allow_trade(test_analysis, test_account)
    print(f"Trade Permission: {permission}")
    
    # Calculate trade amount
    amount = controller.calculate_trade_amount(test_account, test_analysis)
    print(f"Trade Amount: ${amount:.2f}")
    
    # Simulate some trades
    for i in range(5):
        trade_result = {
            'success': True,
            'result': 'win' if i < 4 else 'loss',
            'amount': amount,
            'profit': amount * 0.8 if i < 4 else 0
        }
        controller.update_trade_result(trade_result)
    
    # Get status
    status = controller.get_trading_status()
    print(f"Trading Status: {json.dumps(status, indent=2)}")

if __name__ == "__main__":
    main()
