"""
VIP BIG BANG Enterprise - Professional WebSocket Monitor
Real-time Data Streaming from Quotex WebSocket Connections
"""

import asyncio
import json
import logging
import websockets
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from urllib.parse import urlparse
import ssl

class ProfessionalWebSocketMonitor:
    """
    🌐 Professional WebSocket Monitor
    
    Features:
    - Real-time WebSocket Connection Monitoring
    - Automatic Message Parsing and Filtering
    - Trading Data Extraction
    - Connection Health Monitoring
    - Automatic Reconnection
    - Message Queue Management
    - Data Callbacks System
    """
    
    def __init__(self, settings: Optional[Dict] = None):
        self.settings = settings or {}
        self.logger = logging.getLogger(__name__)
        
        # WebSocket connections
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.connection_status: Dict[str, bool] = {}
        
        # Message handling
        self.message_queue: List[Dict] = []
        self.message_callbacks: List[Callable] = []
        self.filtered_messages: Dict[str, List] = {}
        
        # Data extraction
        self.trading_data: Dict[str, Any] = {}
        self.price_updates: List[Dict] = []
        self.account_updates: List[Dict] = []
        
        # Monitoring settings
        self.monitor_config = {
            'max_queue_size': 1000,
            'reconnect_interval': 5,
            'ping_interval': 30,
            'message_timeout': 10,
            'auto_reconnect': True,
            'filter_patterns': [
                'price', 'asset', 'balance', 'trade', 'order',
                'candle', 'tick', 'quote', 'market', 'account'
            ]
        }
        
        # Connection health
        self.health_metrics = {
            'total_messages': 0,
            'filtered_messages': 0,
            'connection_errors': 0,
            'reconnections': 0,
            'last_message_time': None,
            'average_latency': 0
        }
        
        # Running state
        self.is_monitoring = False
        self.monitor_tasks: List[asyncio.Task] = []
        
        self.logger.info("🌐 Professional WebSocket Monitor initialized")
    
    async def start_monitoring(self, page) -> bool:
        """🚀 Start WebSocket Monitoring"""
        try:
            if self.is_monitoring:
                self.logger.warning("⚠️ WebSocket monitoring already running")
                return True
            
            self.logger.info("🚀 Starting professional WebSocket monitoring...")
            
            # Inject WebSocket monitoring script into page
            await self.inject_websocket_monitor(page)
            
            # Start monitoring tasks
            self.monitor_tasks = [
                asyncio.create_task(self.message_processor()),
                asyncio.create_task(self.health_monitor()),
                asyncio.create_task(self.data_analyzer())
            ]
            
            self.is_monitoring = True
            self.logger.info("✅ Professional WebSocket monitoring started")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket monitoring start error: {e}")
            return False
    
    async def inject_websocket_monitor(self, page):
        """💉 Inject WebSocket Monitoring Script"""
        try:
            websocket_monitor_script = """
            // Professional WebSocket Monitor Injection
            (function() {
                console.log('🌐 VIP BIG BANG WebSocket Monitor injected');
                
                // Store original WebSocket
                const OriginalWebSocket = window.WebSocket;
                
                // WebSocket connection tracking
                window.vipWebSockets = [];
                window.vipWebSocketMessages = [];
                
                // Override WebSocket constructor
                window.WebSocket = function(url, protocols) {
                    console.log('🔌 WebSocket connection detected:', url);
                    
                    const ws = new OriginalWebSocket(url, protocols);
                    const wsId = 'ws_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    
                    // Track connection
                    window.vipWebSockets.push({
                        id: wsId,
                        url: url,
                        protocols: protocols,
                        readyState: ws.readyState,
                        created: new Date().toISOString()
                    });
                    
                    // Monitor messages
                    const originalSend = ws.send;
                    ws.send = function(data) {
                        console.log('📤 WebSocket send:', data);
                        
                        // Store outgoing message
                        window.vipWebSocketMessages.push({
                            id: wsId,
                            type: 'outgoing',
                            data: data,
                            timestamp: new Date().toISOString()
                        });
                        
                        return originalSend.call(this, data);
                    };
                    
                    // Monitor incoming messages
                    const originalOnMessage = ws.onmessage;
                    ws.onmessage = function(event) {
                        console.log('📥 WebSocket message:', event.data);
                        
                        // Store incoming message
                        window.vipWebSocketMessages.push({
                            id: wsId,
                            type: 'incoming',
                            data: event.data,
                            timestamp: new Date().toISOString()
                        });
                        
                        // Try to parse JSON data
                        try {
                            const jsonData = JSON.parse(event.data);
                            
                            // Check for trading-related data
                            if (jsonData && typeof jsonData === 'object') {
                                // Asset/Symbol updates
                                if (jsonData.symbol || jsonData.asset || jsonData.pair) {
                                    window.vipTradingData = window.vipTradingData || {};
                                    window.vipTradingData.currentAsset = jsonData.symbol || jsonData.asset || jsonData.pair;
                                    window.vipTradingData.lastUpdate = new Date().toISOString();
                                }
                                
                                // Price updates
                                if (jsonData.price || jsonData.rate || jsonData.quote || jsonData.bid || jsonData.ask) {
                                    window.vipTradingData = window.vipTradingData || {};
                                    window.vipTradingData.currentPrice = jsonData.price || jsonData.rate || jsonData.quote || jsonData.bid;
                                    window.vipTradingData.lastPriceUpdate = new Date().toISOString();
                                }
                                
                                // Balance updates
                                if (jsonData.balance || jsonData.wallet || jsonData.account) {
                                    window.vipTradingData = window.vipTradingData || {};
                                    window.vipTradingData.balance = jsonData.balance || jsonData.wallet || jsonData.account;
                                    window.vipTradingData.lastBalanceUpdate = new Date().toISOString();
                                }
                                
                                // Candle/Chart updates
                                if (jsonData.candle || jsonData.ohlc || jsonData.chart) {
                                    window.vipTradingData = window.vipTradingData || {};
                                    window.vipTradingData.latestCandle = jsonData.candle || jsonData.ohlc || jsonData.chart;
                                    window.vipTradingData.lastCandleUpdate = new Date().toISOString();
                                }
                                
                                // Trade updates
                                if (jsonData.trade || jsonData.order || jsonData.position) {
                                    window.vipTradingData = window.vipTradingData || {};
                                    window.vipTradingData.latestTrade = jsonData.trade || jsonData.order || jsonData.position;
                                    window.vipTradingData.lastTradeUpdate = new Date().toISOString();
                                }
                            }
                        } catch (e) {
                            // Not JSON data, ignore
                        }
                        
                        // Call original handler
                        if (originalOnMessage) {
                            return originalOnMessage.call(this, event);
                        }
                    };
                    
                    // Monitor connection state changes
                    const originalOnOpen = ws.onopen;
                    ws.onopen = function(event) {
                        console.log('✅ WebSocket connected:', wsId);
                        
                        // Update connection status
                        const wsIndex = window.vipWebSockets.findIndex(w => w.id === wsId);
                        if (wsIndex !== -1) {
                            window.vipWebSockets[wsIndex].readyState = ws.readyState;
                            window.vipWebSockets[wsIndex].connected = new Date().toISOString();
                        }
                        
                        if (originalOnOpen) {
                            return originalOnOpen.call(this, event);
                        }
                    };
                    
                    const originalOnClose = ws.onclose;
                    ws.onclose = function(event) {
                        console.log('❌ WebSocket closed:', wsId, event.code, event.reason);
                        
                        // Update connection status
                        const wsIndex = window.vipWebSockets.findIndex(w => w.id === wsId);
                        if (wsIndex !== -1) {
                            window.vipWebSockets[wsIndex].readyState = ws.readyState;
                            window.vipWebSockets[wsIndex].closed = new Date().toISOString();
                            window.vipWebSockets[wsIndex].closeCode = event.code;
                            window.vipWebSockets[wsIndex].closeReason = event.reason;
                        }
                        
                        if (originalOnClose) {
                            return originalOnClose.call(this, event);
                        }
                    };
                    
                    const originalOnError = ws.onerror;
                    ws.onerror = function(event) {
                        console.error('❌ WebSocket error:', wsId, event);
                        
                        // Update connection status
                        const wsIndex = window.vipWebSockets.findIndex(w => w.id === wsId);
                        if (wsIndex !== -1) {
                            window.vipWebSockets[wsIndex].error = new Date().toISOString();
                            window.vipWebSockets[wsIndex].lastError = event.toString();
                        }
                        
                        if (originalOnError) {
                            return originalOnError.call(this, event);
                        }
                    };
                    
                    return ws;
                };
                
                // Copy static properties
                Object.setPrototypeOf(window.WebSocket, OriginalWebSocket);
                Object.defineProperty(window.WebSocket, 'CONNECTING', { value: 0 });
                Object.defineProperty(window.WebSocket, 'OPEN', { value: 1 });
                Object.defineProperty(window.WebSocket, 'CLOSING', { value: 2 });
                Object.defineProperty(window.WebSocket, 'CLOSED', { value: 3 });
                
                // Helper functions for data extraction
                window.getVipWebSocketData = function() {
                    return {
                        connections: window.vipWebSockets || [],
                        messages: window.vipWebSocketMessages || [],
                        tradingData: window.vipTradingData || {},
                        timestamp: new Date().toISOString()
                    };
                };
                
                window.getVipTradingData = function() {
                    return window.vipTradingData || {};
                };
                
                window.clearVipWebSocketMessages = function() {
                    window.vipWebSocketMessages = [];
                    console.log('🧹 WebSocket messages cleared');
                };
                
                console.log('✅ VIP BIG BANG WebSocket Monitor ready');
            })();
            """
            
            await page.add_init_script(websocket_monitor_script)
            self.logger.info("💉 WebSocket monitoring script injected successfully")
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket script injection error: {e}")
    
    async def extract_websocket_data(self, page) -> Dict[str, Any]:
        """📊 Extract WebSocket Data from Page"""
        try:
            # Get WebSocket data from injected script
            websocket_data = await page.evaluate("() => window.getVipWebSocketData()")
            
            if websocket_data:
                # Process and filter messages
                await self.process_websocket_messages(websocket_data.get('messages', []))
                
                # Update trading data
                trading_data = websocket_data.get('tradingData', {})
                if trading_data:
                    self.trading_data.update(trading_data)
                
                # Update health metrics
                self.health_metrics['total_messages'] += len(websocket_data.get('messages', []))
                self.health_metrics['last_message_time'] = datetime.now().isoformat()
                
                return {
                    'connections': websocket_data.get('connections', []),
                    'recent_messages': websocket_data.get('messages', [])[-10:],  # Last 10 messages
                    'trading_data': trading_data,
                    'extraction_time': datetime.now().isoformat()
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket data extraction error: {e}")
            return {}
    
    async def process_websocket_messages(self, messages: List[Dict]):
        """📨 Process WebSocket Messages"""
        try:
            for message in messages:
                # Add to queue
                self.message_queue.append(message)
                
                # Filter by patterns
                message_data = message.get('data', '')
                for pattern in self.monitor_config['filter_patterns']:
                    if pattern.lower() in message_data.lower():
                        if pattern not in self.filtered_messages:
                            self.filtered_messages[pattern] = []
                        self.filtered_messages[pattern].append(message)
                        self.health_metrics['filtered_messages'] += 1
                        break
                
                # Notify callbacks
                for callback in self.message_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(message)
                        else:
                            callback(message)
                    except Exception as e:
                        self.logger.error(f"❌ Message callback error: {e}")
            
            # Manage queue size
            if len(self.message_queue) > self.monitor_config['max_queue_size']:
                self.message_queue = self.message_queue[-self.monitor_config['max_queue_size']:]
            
        except Exception as e:
            self.logger.error(f"❌ Message processing error: {e}")
    
    async def message_processor(self):
        """📨 Background Message Processor"""
        while self.is_monitoring:
            try:
                await asyncio.sleep(1)
                
                # Process queued messages
                if self.message_queue:
                    # Extract trading signals
                    await self.extract_trading_signals()
                    
                    # Update price data
                    await self.update_price_data()
                    
                    # Analyze patterns
                    await self.analyze_message_patterns()
                
            except Exception as e:
                self.logger.error(f"❌ Message processor error: {e}")
                await asyncio.sleep(5)
    
    async def extract_trading_signals(self):
        """📊 Extract Trading Signals from Messages"""
        try:
            # Look for signal-related messages
            signal_keywords = ['signal', 'recommendation', 'alert', 'buy', 'sell', 'call', 'put']
            
            for message in self.message_queue[-50:]:  # Check last 50 messages
                message_data = message.get('data', '').lower()
                
                for keyword in signal_keywords:
                    if keyword in message_data:
                        # Try to parse as JSON
                        try:
                            data = json.loads(message.get('data', '{}'))
                            if isinstance(data, dict):
                                signal = {
                                    'timestamp': message.get('timestamp'),
                                    'type': keyword,
                                    'data': data,
                                    'source': 'websocket'
                                }
                                
                                # Store signal
                                if 'signals' not in self.trading_data:
                                    self.trading_data['signals'] = []
                                self.trading_data['signals'].append(signal)
                                
                                # Keep only last 20 signals
                                if len(self.trading_data['signals']) > 20:
                                    self.trading_data['signals'] = self.trading_data['signals'][-20:]
                                
                                break
                        except json.JSONDecodeError:
                            continue
            
        except Exception as e:
            self.logger.error(f"❌ Trading signal extraction error: {e}")
    
    async def update_price_data(self):
        """💰 Update Price Data from Messages"""
        try:
            # Look for price updates
            for message in self.message_queue[-10:]:  # Check last 10 messages
                try:
                    data = json.loads(message.get('data', '{}'))
                    if isinstance(data, dict):
                        # Extract price information
                        price_fields = ['price', 'rate', 'quote', 'bid', 'ask', 'close']
                        for field in price_fields:
                            if field in data:
                                price_update = {
                                    'timestamp': message.get('timestamp'),
                                    'price': data[field],
                                    'field': field,
                                    'source': 'websocket'
                                }
                                self.price_updates.append(price_update)
                                
                                # Keep only last 100 price updates
                                if len(self.price_updates) > 100:
                                    self.price_updates = self.price_updates[-100:]
                                break
                except json.JSONDecodeError:
                    continue
            
        except Exception as e:
            self.logger.error(f"❌ Price data update error: {e}")
    
    async def analyze_message_patterns(self):
        """📊 Analyze Message Patterns"""
        try:
            if len(self.message_queue) < 10:
                return
            
            # Analyze message frequency
            recent_messages = self.message_queue[-60:]  # Last minute of messages
            message_frequency = len(recent_messages) / 60  # Messages per second
            
            # Analyze message types
            message_types = {}
            for message in recent_messages:
                msg_type = message.get('type', 'unknown')
                message_types[msg_type] = message_types.get(msg_type, 0) + 1
            
            # Store analysis
            self.trading_data['message_analysis'] = {
                'frequency': message_frequency,
                'types': message_types,
                'total_messages': len(self.message_queue),
                'last_analysis': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Message pattern analysis error: {e}")
    
    async def health_monitor(self):
        """🏥 Monitor Connection Health"""
        while self.is_monitoring:
            try:
                await asyncio.sleep(self.monitor_config['ping_interval'])
                
                # Check connection health
                current_time = time.time()
                
                # Check for stale connections
                if self.health_metrics['last_message_time']:
                    last_message_time = datetime.fromisoformat(self.health_metrics['last_message_time']).timestamp()
                    if current_time - last_message_time > 60:  # No messages for 1 minute
                        self.logger.warning("⚠️ No WebSocket messages received for 1 minute")
                
                # Log health status
                self.logger.info(f"🏥 WebSocket Health: {self.health_metrics['total_messages']} total messages, "
                               f"{self.health_metrics['filtered_messages']} filtered, "
                               f"{self.health_metrics['connection_errors']} errors")
                
            except Exception as e:
                self.logger.error(f"❌ Health monitor error: {e}")
                await asyncio.sleep(30)
    
    async def data_analyzer(self):
        """📊 Background Data Analyzer"""
        while self.is_monitoring:
            try:
                await asyncio.sleep(5)
                
                # Analyze trading data trends
                if self.price_updates:
                    await self.analyze_price_trends()
                
                # Detect anomalies
                await self.detect_anomalies()
                
            except Exception as e:
                self.logger.error(f"❌ Data analyzer error: {e}")
                await asyncio.sleep(10)
    
    async def analyze_price_trends(self):
        """📈 Analyze Price Trends"""
        try:
            if len(self.price_updates) < 10:
                return
            
            # Get recent prices
            recent_prices = [update['price'] for update in self.price_updates[-20:] if isinstance(update['price'], (int, float))]
            
            if len(recent_prices) < 5:
                return
            
            # Calculate trend
            import numpy as np
            x = np.arange(len(recent_prices))
            slope = np.polyfit(x, recent_prices, 1)[0]
            
            # Determine trend direction
            if slope > 0.0001:
                trend = 'UPTREND'
            elif slope < -0.0001:
                trend = 'DOWNTREND'
            else:
                trend = 'SIDEWAYS'
            
            # Store trend analysis
            self.trading_data['price_trend'] = {
                'trend': trend,
                'slope': slope,
                'recent_prices': recent_prices[-5:],
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Price trend analysis error: {e}")
    
    async def detect_anomalies(self):
        """🚨 Detect Data Anomalies"""
        try:
            # Check for unusual message frequency
            if len(self.message_queue) > 0:
                recent_messages = self.message_queue[-60:]  # Last minute
                frequency = len(recent_messages)
                
                if frequency > 100:  # More than 100 messages per minute
                    self.logger.warning(f"🚨 High message frequency detected: {frequency} messages/minute")
                elif frequency == 0:
                    self.logger.warning("🚨 No messages received in the last minute")
            
            # Check for price anomalies
            if len(self.price_updates) >= 10:
                recent_prices = [update['price'] for update in self.price_updates[-10:] if isinstance(update['price'], (int, float))]
                
                if recent_prices:
                    import numpy as np
                    price_std = np.std(recent_prices)
                    price_mean = np.mean(recent_prices)
                    
                    # Check for unusual price movements
                    latest_price = recent_prices[-1]
                    if abs(latest_price - price_mean) > 3 * price_std:
                        self.logger.warning(f"🚨 Price anomaly detected: {latest_price} (mean: {price_mean:.5f}, std: {price_std:.5f})")
            
        except Exception as e:
            self.logger.error(f"❌ Anomaly detection error: {e}")
    
    def add_message_callback(self, callback: Callable):
        """📡 Add Message Callback"""
        self.message_callbacks.append(callback)
        self.logger.info("📡 Message callback added")
    
    def remove_message_callback(self, callback: Callable):
        """📡 Remove Message Callback"""
        if callback in self.message_callbacks:
            self.message_callbacks.remove(callback)
            self.logger.info("📡 Message callback removed")
    
    def get_trading_data(self) -> Dict[str, Any]:
        """📊 Get Current Trading Data"""
        return {
            **self.trading_data,
            'health_metrics': self.health_metrics,
            'message_queue_size': len(self.message_queue),
            'price_updates_count': len(self.price_updates),
            'last_update': datetime.now().isoformat()
        }
    
    def get_filtered_messages(self, pattern: str) -> List[Dict]:
        """📨 Get Filtered Messages by Pattern"""
        return self.filtered_messages.get(pattern, [])
    
    async def stop_monitoring(self):
        """⏹️ Stop WebSocket Monitoring"""
        try:
            self.logger.info("⏹️ Stopping WebSocket monitoring...")
            
            self.is_monitoring = False
            
            # Cancel monitoring tasks
            for task in self.monitor_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            self.monitor_tasks.clear()
            
            self.logger.info("✅ WebSocket monitoring stopped")
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket monitoring stop error: {e}")
    
    def __del__(self):
        """🗑️ Destructor"""
        if self.is_monitoring:
            try:
                asyncio.create_task(self.stop_monitoring())
            except:
                pass
