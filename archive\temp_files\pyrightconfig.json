{"include": ["core", "ui", "quotex", "chrome_extension", "*.py"], "exclude": ["**/node_modules", "**/__pycache__", "**/.*"], "stubPath": "./stubs", "typeCheckingMode": "basic", "reportMissingImports": false, "reportMissingTypeStubs": false, "reportUnknownParameterType": false, "reportUnknownArgumentType": false, "reportUnknownLambdaType": false, "reportUnknownVariableType": false, "reportUnknownMemberType": false, "reportMissingParameterType": false, "reportMissingTypeArgument": false, "reportUnnecessaryTypeIgnoreComment": false, "reportGeneralTypeIssues": false, "reportOptionalSubscript": false, "reportOptionalMemberAccess": false, "reportOptionalCall": false, "reportOptionalIterable": false, "reportOptionalContextManager": false, "reportOptionalOperand": false, "reportTypedDictNotRequiredAccess": false, "reportPrivateImportUsage": false, "reportConstantRedefinition": false, "reportIncompatibleMethodOverride": false, "reportIncompatibleVariableOverride": false, "reportOverlappingOverloads": false, "reportUninitializedInstanceVariable": false, "reportCallInDefaultInitializer": false, "reportUnnecessaryIsInstance": false, "reportUnnecessaryCast": false, "reportUnnecessaryComparison": false, "reportUnnecessaryContains": false, "reportAssertAlwaysTrue": false, "reportSelfClsParameterName": false, "reportImplicitStringConcatenation": false, "reportInvalidStringEscapeSequence": false, "reportMatchNotExhaustive": false, "reportShadowedImports": false, "pythonVersion": "3.8", "pythonPlatform": "Windows"}