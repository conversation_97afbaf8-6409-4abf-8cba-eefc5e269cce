
# VIP BIG BANG Extension Instant Loader
Write-Host "🚀 VIP BIG BANG Extension Instant Loader" -ForegroundColor Green
Write-Host ""

$ChromePath = "C:\Program Files\Google\Chrome\Application\chrome.exe"
$ExtensionPath = "C:\Users\<USER>\VIP_BIG_BANG\chrome_extension"

Write-Host "Chrome Path: $ChromePath" -ForegroundColor Yellow
Write-Host "Extension Path: $ExtensionPath" -ForegroundColor Yellow
Write-Host ""

# Kill existing Chrome processes
Write-Host "Terminating existing Chrome processes..." -ForegroundColor Cyan
Get-Process -Name "chrome" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# Start Chrome with extension
Write-Host "Starting Chrome with VIP BIG BANG extension..." -ForegroundColor Green
$Arguments = @(
    "--load-extension=$ExtensionPath",
    "--disable-extensions-file-access-check",
    "--disable-extensions-http-throttling",
    "--enable-experimental-extension-apis",
    "--disable-web-security",
    "--allow-running-insecure-content",
    "--no-first-run",
    "--no-default-browser-check",
    "--disable-background-mode",
    "--force-dev-mode-highlighting",
    "https://qxbroker.com/en/trade"
)

Start-Process -FilePath $ChromePath -ArgumentList $Arguments

Write-Host ""
Write-Host "✅ Chrome started with VIP BIG BANG extension!" -ForegroundColor Green
Write-Host "🎯 Extension should be automatically loaded" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Check chrome://extensions/" -ForegroundColor White
Write-Host "2. Verify VIP BIG BANG is enabled" -ForegroundColor White
Write-Host "3. Test on Quotex page" -ForegroundColor White
Write-Host ""
