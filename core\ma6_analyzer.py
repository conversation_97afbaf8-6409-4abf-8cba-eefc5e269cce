"""
VIP BIG BANG Enterprise - MA6 Analyzer
Moving Average 6 period analysis for short-term trend detection
"""

import numpy as np
import pandas as pd
from typing import Dict
import logging
from datetime import datetime

class MA6Analyzer:
    """
    MA6 (Moving Average 6) - Original VIP BIG BANG indicator
    Analyzes short-term trend using 6-period moving average
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("MA6Analyzer")
        
        # MA6 parameters
        self.period = 6
        self.trend_threshold = 0.001  # 0.1% threshold for trend detection
        
        # Signal strength thresholds
        self.strong_trend_threshold = 0.005  # 0.5%
        self.weak_trend_threshold = 0.002    # 0.2%
        
        self.logger.debug("MA6 Analyzer initialized")
    
    def calculate_ma6(self, data: pd.DataFrame) -> pd.Series:
        """Calculate 6-period moving average"""
        close_prices = data['close'] if 'close' in data.columns else data['price']
        return close_prices.rolling(window=self.period).mean()
    
    def detect_trend_direction(self, prices: pd.Series, ma6: pd.Series) -> Dict:
        """Detect trend direction based on price vs MA6"""
        if len(prices) < 2 or len(ma6) < 2:
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'distance': 0.0
            }
        
        current_price = prices.iloc[-1]
        current_ma6 = ma6.iloc[-1]
        
        if pd.isna(current_ma6) or current_ma6 == 0:
            return {
                'direction': 'NEUTRAL',
                'strength': 0.0,
                'distance': 0.0
            }
        
        # Calculate distance from MA6
        distance = (current_price - current_ma6) / current_ma6
        
        # Determine direction
        if distance > self.trend_threshold:
            direction = 'UP'
        elif distance < -self.trend_threshold:
            direction = 'DOWN'
        else:
            direction = 'NEUTRAL'
        
        # Calculate strength based on distance
        abs_distance = abs(distance)
        if abs_distance >= self.strong_trend_threshold:
            strength = 0.8
        elif abs_distance >= self.weak_trend_threshold:
            strength = 0.6
        else:
            strength = 0.4
        
        return {
            'direction': direction,
            'strength': strength,
            'distance': distance,
            'distance_percent': distance * 100
        }
    
    def analyze_ma6_slope(self, ma6: pd.Series) -> Dict:
        """Analyze MA6 slope for trend confirmation"""
        if len(ma6) < 3:
            return {
                'slope': 0.0,
                'slope_direction': 'NEUTRAL',
                'slope_strength': 0.0
            }
        
        # Calculate slope over last 3 periods
        recent_ma6 = ma6.tail(3).dropna()
        if len(recent_ma6) < 2:
            return {
                'slope': 0.0,
                'slope_direction': 'NEUTRAL',
                'slope_strength': 0.0
            }
        
        # Linear regression slope
        x = np.arange(len(recent_ma6))
        y = recent_ma6.values
        slope = np.polyfit(x, y, 1)[0]
        
        # Normalize slope
        avg_price = recent_ma6.mean()
        normalized_slope = slope / avg_price if avg_price != 0 else 0
        
        # Determine slope direction and strength
        if normalized_slope > 0.001:
            slope_direction = 'UP'
            slope_strength = min(abs(normalized_slope) * 1000, 1.0)
        elif normalized_slope < -0.001:
            slope_direction = 'DOWN'
            slope_strength = min(abs(normalized_slope) * 1000, 1.0)
        else:
            slope_direction = 'NEUTRAL'
            slope_strength = 0.0
        
        return {
            'slope': normalized_slope,
            'slope_direction': slope_direction,
            'slope_strength': slope_strength
        }
    
    def calculate_crossover_signals(self, prices: pd.Series, ma6: pd.Series) -> Dict:
        """Detect price crossover with MA6"""
        if len(prices) < 2 or len(ma6) < 2:
            return {
                'crossover': 'NONE',
                'crossover_strength': 0.0
            }
        
        # Current and previous positions
        current_above = prices.iloc[-1] > ma6.iloc[-1]
        previous_above = prices.iloc[-2] > ma6.iloc[-2]
        
        crossover = 'NONE'
        crossover_strength = 0.0
        
        if current_above and not previous_above:
            crossover = 'BULLISH'
            # Calculate strength based on momentum
            price_change = (prices.iloc[-1] - prices.iloc[-2]) / prices.iloc[-2]
            crossover_strength = min(abs(price_change) * 100, 1.0)
        elif not current_above and previous_above:
            crossover = 'BEARISH'
            # Calculate strength based on momentum
            price_change = (prices.iloc[-1] - prices.iloc[-2]) / prices.iloc[-2]
            crossover_strength = min(abs(price_change) * 100, 1.0)
        
        return {
            'crossover': crossover,
            'crossover_strength': crossover_strength
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main MA6 analysis function
        Returns comprehensive MA6 analysis
        """
        try:
            if len(data) < self.period:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for MA6 analysis'
                }
            
            # Calculate MA6
            ma6 = self.calculate_ma6(data)
            prices = data['close'] if 'close' in data.columns else data['price']
            
            # Trend analysis
            trend_analysis = self.detect_trend_direction(prices, ma6)
            
            # Slope analysis
            slope_analysis = self.analyze_ma6_slope(ma6)
            
            # Crossover analysis
            crossover_analysis = self.calculate_crossover_signals(prices, ma6)
            
            # Calculate overall score
            score = 0.5  # Neutral base
            confidence = 0.0
            
            # Adjust score based on trend direction
            if trend_analysis['direction'] == 'UP':
                score += 0.3 * trend_analysis['strength']
            elif trend_analysis['direction'] == 'DOWN':
                score -= 0.3 * trend_analysis['strength']
            
            # Adjust score based on slope
            if slope_analysis['slope_direction'] == 'UP':
                score += 0.2 * slope_analysis['slope_strength']
            elif slope_analysis['slope_direction'] == 'DOWN':
                score -= 0.2 * slope_analysis['slope_strength']
            
            # Boost score for crossovers
            if crossover_analysis['crossover'] == 'BULLISH':
                score += 0.2 * crossover_analysis['crossover_strength']
            elif crossover_analysis['crossover'] == 'BEARISH':
                score -= 0.2 * crossover_analysis['crossover_strength']
            
            # Ensure score is within bounds
            score = max(0, min(1, score))
            
            # Calculate confidence
            confidence = (
                trend_analysis['strength'] * 0.4 +
                slope_analysis['slope_strength'] * 0.3 +
                crossover_analysis['crossover_strength'] * 0.3
            )
            
            # Determine final direction
            if score > 0.6:
                direction = 'UP'
            elif score < 0.4:
                direction = 'DOWN'
            else:
                direction = 'NEUTRAL'
            
            return {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'ma6_value': ma6.iloc[-1] if not ma6.empty else 0,
                'current_price': prices.iloc[-1] if not prices.empty else 0,
                'trend_analysis': trend_analysis,
                'slope_analysis': slope_analysis,
                'crossover_analysis': crossover_analysis,
                'details': f'MA6 trend: {direction}, Distance: {trend_analysis.get("distance_percent", 0):.2f}%'
            }
            
        except Exception as e:
            self.logger.error(f"MA6 analysis error: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'details': f'MA6 analysis failed: {str(e)}'
            }
