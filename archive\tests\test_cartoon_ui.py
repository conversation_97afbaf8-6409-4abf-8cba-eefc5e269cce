"""
🎮 Test Cartoon Gaming UI
تست رابط کاربری کارتونی و گیمینگ VIP BIG BANG
"""

import sys
from PySide6.QtWidgets import QApplication

# Import our cartoon gaming UI components
from cartoon_gaming_ui import *
from vip_cartoon_gaming_ui import VIPCartoonGamingUI

def test_cartoon_components():
    """تست اجزای کارتونی"""
    print("🎮 Testing Cartoon Gaming Components...")
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Test window
    window = QMainWindow()
    window.setWindowTitle("🎮 VIP BIG BANG - Cartoon Components Test")
    window.setGeometry(100, 100, 1200, 800)
    
    # Cartoon background
    window.setStyleSheet(f"""
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {CartoonGameColors.APP_DARK},
                stop:0.3 {CartoonGameColors.APP_MEDIUM},
                stop:0.7 {CartoonGameColors.APP_LIGHT},
                stop:1 {CartoonGameColors.APP_DARK});
            color: white;
        }}
    """)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setSpacing(30)
    layout.setContentsMargins(40, 40, 40, 40)
    
    # Title
    title = QLabel("🎮 VIP BIG BANG - Cartoon Gaming Test")
    title.setStyleSheet(f"""
        font-family: 'Comic Sans MS', 'Arial', sans-serif;
        font-size: 32px;
        font-weight: 900;
        color: {CartoonGameColors.CARTOON_CYAN};
        letter-spacing: 3px;
        text-align: center;
        margin-bottom: 20px;
    """)
    title.setAlignment(Qt.AlignCenter)
    layout.addWidget(title)
    
    # Test buttons
    buttons_layout = QHBoxLayout()
    
    primary_btn = CartoonGameButton("شروع تریدینگ", "🚀", "primary", (180, 100))
    success_btn = CartoonGameButton("اجرای سیگنال", "✅", "success", (180, 100))
    warning_btn = CartoonGameButton("هشدار ریسک", "⚠️", "warning", (180, 100))
    danger_btn = CartoonGameButton("توقف اضطراری", "🛑", "danger", (180, 100))
    
    buttons_layout.addWidget(primary_btn)
    buttons_layout.addWidget(success_btn)
    buttons_layout.addWidget(warning_btn)
    buttons_layout.addWidget(danger_btn)
    
    layout.addLayout(buttons_layout)
    
    # Test chart
    chart_display = CartoonChartDisplay()
    layout.addWidget(chart_display)
    
    # Test stats
    stats_layout = QHBoxLayout()
    
    balance_stats = CartoonStatsWidget("موجودی حساب", "$3,250.89", "💰", CartoonGameColors.CARTOON_GREEN)
    winrate_stats = CartoonStatsWidget("نرخ برد", "94.5%", "🏆", CartoonGameColors.CARTOON_BLUE)
    trades_stats = CartoonStatsWidget("تعداد معاملات", "127", "📊", CartoonGameColors.CARTOON_PURPLE)
    profit_stats = CartoonStatsWidget("سود امروز", "$450.23", "💎", CartoonGameColors.CARTOON_ORANGE)
    
    stats_layout.addWidget(balance_stats)
    stats_layout.addWidget(winrate_stats)
    stats_layout.addWidget(trades_stats)
    stats_layout.addWidget(profit_stats)
    
    layout.addLayout(stats_layout)
    
    # Test panels
    panels_layout = QHBoxLayout()
    
    # Control panel
    control_panel = CartoonGamePanel("کنترل ربات", "primary")
    control_layout = QVBoxLayout(control_panel)
    
    control_title = QLabel("🤖 کنترل هوشمند")
    control_title.setStyleSheet(f"""
        font-size: 18px;
        font-weight: 800;
        color: {CartoonGameColors.CARTOON_CYAN};
        text-align: center;
        margin-bottom: 10px;
    """)
    control_title.setAlignment(Qt.AlignCenter)
    control_layout.addWidget(control_title)
    
    auto_btn = CartoonGameButton("حالت خودکار", "🤖", "success", (200, 60))
    manual_btn = CartoonGameButton("حالت دستی", "🎮", "primary", (200, 60))
    learn_btn = CartoonGameButton("حالت یادگیری", "🧠", "warning", (200, 60))
    
    control_layout.addWidget(auto_btn)
    control_layout.addWidget(manual_btn)
    control_layout.addWidget(learn_btn)
    
    panels_layout.addWidget(control_panel)
    
    # Settings panel
    settings_panel = CartoonGamePanel("تنظیمات", "success")
    settings_layout = QVBoxLayout(settings_panel)
    
    settings_title = QLabel("⚙️ تنظیمات سریع")
    settings_title.setStyleSheet(f"""
        font-size: 18px;
        font-weight: 800;
        color: {CartoonGameColors.CARTOON_GREEN};
        text-align: center;
        margin-bottom: 10px;
    """)
    settings_title.setAlignment(Qt.AlignCenter)
    settings_layout.addWidget(settings_title)
    
    risk_btn = CartoonGameButton("مدیریت ریسک", "🛡️", "warning", (200, 60))
    signal_btn = CartoonGameButton("تنظیم سیگنال", "📡", "primary", (200, 60))
    time_btn = CartoonGameButton("زمان‌بندی", "⏰", "success", (200, 60))
    
    settings_layout.addWidget(risk_btn)
    settings_layout.addWidget(signal_btn)
    settings_layout.addWidget(time_btn)
    
    panels_layout.addWidget(settings_panel)
    
    # Info panel
    info_panel = CartoonGamePanel("اطلاعات", "warning")
    info_layout = QVBoxLayout(info_panel)
    
    info_title = QLabel("📋 اطلاعات سیستم")
    info_title.setStyleSheet(f"""
        font-size: 18px;
        font-weight: 800;
        color: {CartoonGameColors.CARTOON_ORANGE};
        text-align: center;
        margin-bottom: 10px;
    """)
    info_title.setAlignment(Qt.AlignCenter)
    info_layout.addWidget(info_title)
    
    status_btn = CartoonGameButton("وضعیت سیستم", "📊", "success", (200, 60))
    log_btn = CartoonGameButton("گزارش‌ها", "📝", "primary", (200, 60))
    help_btn = CartoonGameButton("راهنما", "❓", "warning", (200, 60))
    
    info_layout.addWidget(status_btn)
    info_layout.addWidget(log_btn)
    info_layout.addWidget(help_btn)
    
    panels_layout.addWidget(info_panel)
    
    layout.addLayout(panels_layout)
    
    window.show()
    return app.exec()

def test_full_cartoon_ui():
    """تست رابط کاربری کامل کارتونی"""
    print("🎮 Testing Full Cartoon Gaming UI...")
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Create full cartoon UI
    window = VIPCartoonGamingUI()
    window.show()
    
    return app.exec()

def main():
    """تابع اصلی تست"""
    print("🎮 VIP BIG BANG - Cartoon Gaming UI Test")
    print("=" * 50)
    
    choice = input("""
انتخاب کنید:
1. تست اجزای کارتونی (Cartoon Components Test)
2. تست رابط کاربری کامل (Full Cartoon UI Test)

انتخاب شما (1 یا 2): """)
    
    if choice == "1":
        print("\n🧪 Starting Cartoon Components Test...")
        return test_cartoon_components()
    elif choice == "2":
        print("\n🧪 Starting Full Cartoon UI Test...")
        return test_full_cartoon_ui()
    else:
        print("❌ انتخاب نامعتبر!")
        return 1

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
