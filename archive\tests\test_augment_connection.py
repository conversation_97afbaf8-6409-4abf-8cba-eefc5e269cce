#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 تست اتصال Augment برای ایران
🌐 بررسی اتصال اینترنت و proxy
"""

import requests
import socket
import json
import time
from urllib.parse import urlparse

class AugmentConnectionTester:
    """تست کننده اتصال Augment"""
    
    def __init__(self):
        self.proxy_ports = [10809, 7890, 8080, 1080, 10808]
        self.test_urls = [
            "https://api.github.com",
            "https://www.google.com",
            "https://httpbin.org/ip",
            "https://api.augmentcode.com"  # فرضی
        ]
    
    def test_direct_connection(self):
        """تست اتصال مستقیم"""
        print("🔍 تست اتصال مستقیم...")
        
        try:
            response = requests.get("https://www.google.com", timeout=5)
            if response.status_code == 200:
                print("✅ اتصال مستقیم موفق")
                return True
            else:
                print(f"⚠️ اتصال مستقیم با مشکل: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ اتصال مستقیم ناموفق: {e}")
            return False
    
    def test_proxy_port(self, port):
        """تست پورت proxy"""
        print(f"🔍 تست پورت {port}...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                print(f"✅ پورت {port} فعال است")
                return True
            else:
                print(f"❌ پورت {port} غیرفعال است")
                return False
        except Exception as e:
            print(f"❌ خطا در تست پورت {port}: {e}")
            return False
    
    def test_proxy_connection(self, port):
        """تست اتصال از طریق proxy"""
        print(f"🔍 تست اتصال proxy پورت {port}...")
        
        proxies = {
            'http': f'http://127.0.0.1:{port}',
            'https': f'http://127.0.0.1:{port}'
        }
        
        for url in self.test_urls:
            try:
                response = requests.get(url, proxies=proxies, timeout=10)
                if response.status_code == 200:
                    print(f"✅ اتصال proxy موفق به {url}")
                    return True, port
                else:
                    print(f"⚠️ اتصال proxy با مشکل به {url}: {response.status_code}")
            except Exception as e:
                print(f"❌ خطا در اتصال proxy به {url}: {e}")
        
        return False, port
    
    def find_working_proxy(self):
        """پیدا کردن proxy کارآمد"""
        print("🔍 جستجوی proxy کارآمد...")
        
        for port in self.proxy_ports:
            if self.test_proxy_port(port):
                success, working_port = self.test_proxy_connection(port)
                if success:
                    print(f"🎉 proxy کارآمد پیدا شد: پورت {working_port}")
                    return working_port
        
        print("❌ هیچ proxy کارآمدی پیدا نشد")
        return None
    
    def test_dns(self):
        """تست DNS"""
        print("🔍 تست DNS...")
        
        dns_servers = ["*******", "*******", "*******"]
        
        for dns in dns_servers:
            try:
                socket.setdefaulttimeout(3)
                socket.gethostbyname_ex("www.google.com")
                print(f"✅ DNS کار می‌کند")
                return True
            except Exception as e:
                print(f"⚠️ مشکل DNS: {e}")
        
        return False
    
    def generate_vscode_settings(self, proxy_port):
        """تولید تنظیمات VS Code"""
        settings = {
            "http.proxy": f"http://127.0.0.1:{proxy_port}",
            "http.proxyStrictSSL": False,
            "http.proxySupport": "on",
            "https.proxy": f"http://127.0.0.1:{proxy_port}",
            "augment.proxy.enabled": True,
            "augment.proxy.host": "127.0.0.1",
            "augment.proxy.port": proxy_port,
            "augment.network.timeout": 30000,
            "augment.network.retries": 3
        }
        
        print(f"\n📝 تنظیمات VS Code برای پورت {proxy_port}:")
        print(json.dumps(settings, indent=2, ensure_ascii=False))
        
        # ذخیره در فایل
        with open("vscode_settings_iran.json", "w", encoding="utf-8") as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        
        print("✅ تنظیمات در فایل vscode_settings_iran.json ذخیره شد")
    
    def generate_augmentrc(self, proxy_port):
        """تولید فایل .augmentrc"""
        config = {
            "proxy": {
                "http": f"http://127.0.0.1:{proxy_port}",
                "https": f"http://127.0.0.1:{proxy_port}"
            },
            "network": {
                "timeout": 30000,
                "retries": 3
            },
            "cache": {
                "enabled": True,
                "ttl": 3600
            }
        }
        
        with open(".augmentrc", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ فایل .augmentrc ایجاد شد")
    
    def run_full_test(self):
        """اجرای تست کامل"""
        print("🎯 شروع تست کامل اتصال Augment")
        print("=" * 50)
        
        # تست DNS
        self.test_dns()
        print()
        
        # تست اتصال مستقیم
        direct_ok = self.test_direct_connection()
        print()
        
        if direct_ok:
            print("✅ اتصال مستقیم موجود است - Augment باید کار کند")
            print("💡 اگر Augment کار نمی‌کند، VS Code را restart کنید")
        else:
            print("⚠️ اتصال مستقیم موجود نیست - نیاز به proxy")
            
            # پیدا کردن proxy کارآمد
            working_port = self.find_working_proxy()
            
            if working_port:
                print(f"\n🎉 راه‌حل پیدا شد!")
                print(f"✅ از proxy پورت {working_port} استفاده کنید")
                
                # تولید تنظیمات
                self.generate_vscode_settings(working_port)
                self.generate_augmentrc(working_port)
                
                print(f"\n📋 مراحل بعدی:")
                print(f"1. فیلترشکن را روی پورت {working_port} تنظیم کنید")
                print(f"2. تنظیمات VS Code را از فایل vscode_settings_iran.json کپی کنید")
                print(f"3. VS Code را restart کنید")
                print(f"4. Augment Extension را reload کنید")
            else:
                print("\n❌ هیچ proxy کارآمدی پیدا نشد")
                print("💡 راه‌حل‌ها:")
                print("1. فیلترشکن را روشن کنید")
                print("2. پورت‌های مختلف امتحان کنید")
                print("3. فیلترشکن را عوض کنید")
                print("4. DNS را تغییر دهید")
        
        print("\n🚀 ربات VIP BIG BANG شما مستقل از این مشکلات کار می‌کند!")
        print("=" * 50)


def main():
    """تابع اصلی"""
    tester = AugmentConnectionTester()
    tester.run_full_test()


if __name__ == "__main__":
    main()
