"""
🔍 ADVANCED HARDWARE & BROWSER DETECTOR
🖥️ سیستم پیشرفته تشخیص سخت‌افزار و مرورگر
🛡️ شامل fingerprinting و anti-detection
"""

import os
import sys
import json
import hashlib
import uuid
import platform
import psutil
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Any
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QApplication

# Optional imports
try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    print("⚠️ WMI not available - some advanced features will be limited")

try:
    import cpuinfo
    CPUINFO_AVAILABLE = True
except ImportError:
    CPUINFO_AVAILABLE = False
    print("⚠️ cpuinfo not available - using basic CPU detection")

class AdvancedHardwareDetector(QObject):
    """
    🔍 تشخیص‌گر پیشرفته سخت‌افزار و مرورگر
    شامل تمام قابلیت‌های fingerprinting و anti-detection
    """
    
    # Signals
    hardware_detected = Signal(dict)
    browser_fingerprint_ready = Signal(dict)
    vm_detection_complete = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("AdvancedHardwareDetector")
        
        # اطلاعات سیستم
        self.hardware_info = {}
        self.browser_info = {}
        self.fingerprint_data = {}
        self.vm_detection_results = {}
        
        # WMI برای Windows
        self.wmi_connection = None
        self._initialize_wmi()
        
        # شروع تشخیص کامل
        self.detect_all_hardware()
        
        self.logger.info("🔍 Advanced Hardware Detector initialized")
    
    def _initialize_wmi(self):
        """راه‌اندازی WMI برای Windows"""
        try:
            if platform.system() == "Windows" and WMI_AVAILABLE:
                import wmi
                self.wmi_connection = wmi.WMI()
                self.logger.info("✅ WMI connection established")
            else:
                self.logger.warning("⚠️ WMI not available or not on Windows")
        except Exception as e:
            self.logger.warning(f"⚠️ WMI initialization failed: {e}")
    
    def detect_all_hardware(self):
        """🔍 تشخیص کامل تمام سخت‌افزارها"""
        try:
            self.logger.info("🔍 Starting comprehensive hardware detection...")
            
            # تشخیص CPU پیشرفته
            self.detect_advanced_cpu()
            
            # تشخیص RAM پیشرفته
            self.detect_advanced_ram()
            
            # تشخیص GPU پیشرفته
            self.detect_advanced_gpu()
            
            # تشخیص مادربرد
            self.detect_motherboard()
            
            # تشخیص BIOS
            self.detect_bios()
            
            # تشخیص محیط مجازی
            self.detect_virtual_environment()
            
            # تولید fingerprint
            self.generate_hardware_fingerprint()
            
            # ارسال سیگنال
            self.hardware_detected.emit(self.hardware_info)
            
            self.logger.info("✅ Comprehensive hardware detection completed")
            
        except Exception as e:
            self.logger.error(f"❌ Hardware detection error: {e}")
    
    def detect_advanced_cpu(self):
        """💻 تشخیص پیشرفته CPU"""
        try:
            cpu_info = {
                "name": platform.processor(),
                "architecture": platform.machine(),
                "cores_physical": psutil.cpu_count(logical=False),
                "cores_logical": psutil.cpu_count(logical=True),
                "frequency_max": psutil.cpu_freq().max if psutil.cpu_freq() else 0,
                "frequency_current": psutil.cpu_freq().current if psutil.cpu_freq() else 0,
                "cache_info": {},
                "features": [],
                "vendor": "Unknown",
                "model": "Unknown",
                "stepping": "Unknown"
            }
            
            # اطلاعات پیشرفته CPU با cpuinfo
            if CPUINFO_AVAILABLE:
                try:
                    import cpuinfo
                    cpu_detailed = cpuinfo.get_cpu_info()

                    cpu_info.update({
                        "brand": cpu_detailed.get('brand_raw', 'Unknown'),
                        "vendor": cpu_detailed.get('vendor_id_raw', 'Unknown'),
                        "model": cpu_detailed.get('model', 'Unknown'),
                        "family": cpu_detailed.get('family', 'Unknown'),
                        "stepping": cpu_detailed.get('stepping', 'Unknown'),
                        "features": cpu_detailed.get('flags', []),
                        "cache_size": cpu_detailed.get('l3_cache_size', 'Unknown'),
                        "hz_advertised": cpu_detailed.get('hz_advertised_friendly', 'Unknown'),
                        "hz_actual": cpu_detailed.get('hz_actual_friendly', 'Unknown')
                    })
                except Exception as e:
                    self.logger.warning(f"⚠️ cpuinfo error: {e}")
            else:
                self.logger.warning("⚠️ cpuinfo not available, using basic CPU detection")
            
            # اطلاعات WMI برای Windows
            if self.wmi_connection:
                try:
                    for processor in self.wmi_connection.Win32_Processor():
                        cpu_info.update({
                            "name": processor.Name,
                            "manufacturer": processor.Manufacturer,
                            "max_clock_speed": processor.MaxClockSpeed,
                            "current_clock_speed": processor.CurrentClockSpeed,
                            "l2_cache_size": processor.L2CacheSize,
                            "l3_cache_size": processor.L3CacheSize,
                            "socket_designation": processor.SocketDesignation,
                            "voltage": processor.CurrentVoltage
                        })
                        break  # فقط اولین پردازنده
                except Exception as e:
                    self.logger.warning(f"⚠️ WMI CPU detection failed: {e}")
            
            self.hardware_info["cpu"] = cpu_info
            
        except Exception as e:
            self.logger.error(f"❌ Advanced CPU detection error: {e}")
    
    def detect_advanced_ram(self):
        """🧠 تشخیص پیشرفته RAM"""
        try:
            memory = psutil.virtual_memory()
            
            ram_info = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "percentage": memory.percent,
                "modules": [],
                "total_slots": 0,
                "used_slots": 0,
                "max_capacity": 0,
                "memory_type": "Unknown",
                "speed": "Unknown"
            }
            
            # اطلاعات WMI برای Windows
            if self.wmi_connection:
                try:
                    # اطلاعات ماژول‌های RAM
                    for memory_module in self.wmi_connection.Win32_PhysicalMemory():
                        module_info = {
                            "capacity_gb": round(int(memory_module.Capacity) / (1024**3), 2),
                            "speed": memory_module.Speed,
                            "memory_type": memory_module.MemoryType,
                            "form_factor": memory_module.FormFactor,
                            "manufacturer": memory_module.Manufacturer,
                            "part_number": memory_module.PartNumber,
                            "serial_number": memory_module.SerialNumber,
                            "device_locator": memory_module.DeviceLocator
                        }
                        ram_info["modules"].append(module_info)
                    
                    ram_info["used_slots"] = len(ram_info["modules"])
                    
                    # اطلاعات کلی حافظه
                    for memory_array in self.wmi_connection.Win32_PhysicalMemoryArray():
                        ram_info["total_slots"] = memory_array.MemoryDevices
                        ram_info["max_capacity"] = round(int(memory_array.MaxCapacity) / (1024**2), 2)  # MB to GB
                        break
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ WMI RAM detection failed: {e}")
            
            self.hardware_info["ram"] = ram_info
            
        except Exception as e:
            self.logger.error(f"❌ Advanced RAM detection error: {e}")
    
    def detect_advanced_gpu(self):
        """🎮 تشخیص پیشرفته GPU"""
        try:
            gpu_info = {
                "detected": False,
                "cards": [],
                "primary_gpu": {},
                "total_memory": 0,
                "driver_version": "Unknown"
            }
            
            # تشخیص با WMI
            if self.wmi_connection:
                try:
                    for video_controller in self.wmi_connection.Win32_VideoController():
                        if video_controller.Name and "Microsoft" not in video_controller.Name:
                            card_info = {
                                "name": video_controller.Name,
                                "adapter_ram": video_controller.AdapterRAM,
                                "driver_version": video_controller.DriverVersion,
                                "driver_date": video_controller.DriverDate,
                                "video_processor": video_controller.VideoProcessor,
                                "video_architecture": video_controller.VideoArchitecture,
                                "video_memory_type": video_controller.VideoMemoryType,
                                "current_horizontal_resolution": video_controller.CurrentHorizontalResolution,
                                "current_vertical_resolution": video_controller.CurrentVerticalResolution,
                                "current_refresh_rate": video_controller.CurrentRefreshRate,
                                "pnp_device_id": video_controller.PNPDeviceID
                            }
                            
                            gpu_info["cards"].append(card_info)
                            gpu_info["detected"] = True
                            
                            # اولین کارت به عنوان primary
                            if not gpu_info["primary_gpu"]:
                                gpu_info["primary_gpu"] = card_info
                                
                except Exception as e:
                    self.logger.warning(f"⚠️ WMI GPU detection failed: {e}")
            
            # تشخیص با WMIC
            if not gpu_info["detected"]:
                try:
                    result = subprocess.run(
                        ['wmic', 'path', 'win32_VideoController', 'get', 'name,AdapterRAM,DriverVersion'],
                        capture_output=True, text=True, timeout=10
                    )
                    
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # Skip header
                            if line.strip() and 'Microsoft' not in line:
                                parts = line.strip().split()
                                if parts:
                                    gpu_info["detected"] = True
                                    gpu_info["primary_gpu"]["name"] = ' '.join(parts)
                                    break
                                    
                except Exception as e:
                    self.logger.warning(f"⚠️ WMIC GPU detection failed: {e}")
            
            self.hardware_info["gpu"] = gpu_info
            
        except Exception as e:
            self.logger.error(f"❌ Advanced GPU detection error: {e}")
    
    def detect_motherboard(self):
        """🔧 تشخیص مادربرد"""
        try:
            motherboard_info = {
                "manufacturer": "Unknown",
                "product": "Unknown",
                "version": "Unknown",
                "serial_number": "Unknown"
            }
            
            if self.wmi_connection:
                try:
                    for board in self.wmi_connection.Win32_BaseBoard():
                        motherboard_info.update({
                            "manufacturer": board.Manufacturer,
                            "product": board.Product,
                            "version": board.Version,
                            "serial_number": board.SerialNumber
                        })
                        break
                except Exception as e:
                    self.logger.warning(f"⚠️ WMI Motherboard detection failed: {e}")
            
            self.hardware_info["motherboard"] = motherboard_info
            
        except Exception as e:
            self.logger.error(f"❌ Motherboard detection error: {e}")
    
    def detect_bios(self):
        """⚙️ تشخیص BIOS"""
        try:
            bios_info = {
                "manufacturer": "Unknown",
                "version": "Unknown",
                "release_date": "Unknown",
                "smbios_version": "Unknown"
            }
            
            if self.wmi_connection:
                try:
                    for bios in self.wmi_connection.Win32_BIOS():
                        bios_info.update({
                            "manufacturer": bios.Manufacturer,
                            "version": bios.Version,
                            "release_date": bios.ReleaseDate,
                            "smbios_version": bios.SMBIOSBIOSVersion
                        })
                        break
                except Exception as e:
                    self.logger.warning(f"⚠️ WMI BIOS detection failed: {e}")
            
            self.hardware_info["bios"] = bios_info
            
        except Exception as e:
            self.logger.error(f"❌ BIOS detection error: {e}")

    def detect_virtual_environment(self):
        """🔍 تشخیص محیط مجازی (VM Detection)"""
        try:
            vm_indicators = {
                "is_vm": False,
                "vm_type": "Physical",
                "confidence": 0,
                "indicators": []
            }

            # بررسی نام‌های مشکوک در CPU
            cpu_name = self.hardware_info.get("cpu", {}).get("name", "").lower()
            vm_cpu_indicators = ["virtual", "vmware", "virtualbox", "qemu", "xen", "hyper-v"]

            for indicator in vm_cpu_indicators:
                if indicator in cpu_name:
                    vm_indicators["indicators"].append(f"CPU name contains '{indicator}'")
                    vm_indicators["confidence"] += 20

            # بررسی GPU
            gpu_name = self.hardware_info.get("gpu", {}).get("primary_gpu", {}).get("name", "").lower()
            vm_gpu_indicators = ["microsoft basic", "vmware", "virtualbox", "parallels", "qemu"]

            for indicator in vm_gpu_indicators:
                if indicator in gpu_name:
                    vm_indicators["indicators"].append(f"GPU name contains '{indicator}'")
                    vm_indicators["confidence"] += 25

            # بررسی مادربرد
            motherboard = self.hardware_info.get("motherboard", {})
            mb_manufacturer = motherboard.get("manufacturer", "").lower()
            mb_product = motherboard.get("product", "").lower()

            vm_mb_indicators = ["vmware", "virtualbox", "microsoft corporation", "qemu", "bochs"]

            for indicator in vm_mb_indicators:
                if indicator in mb_manufacturer or indicator in mb_product:
                    vm_indicators["indicators"].append(f"Motherboard contains '{indicator}'")
                    vm_indicators["confidence"] += 30

            # بررسی تعداد هسته‌های CPU (VMs معمولاً کم دارند)
            cpu_cores = self.hardware_info.get("cpu", {}).get("cores_physical", 0)
            if cpu_cores <= 2:
                vm_indicators["indicators"].append("Low CPU core count (suspicious for VM)")
                vm_indicators["confidence"] += 10

            # بررسی RAM (VMs معمولاً RAM کم دارند)
            ram_gb = self.hardware_info.get("ram", {}).get("total_gb", 0)
            if ram_gb <= 4:
                vm_indicators["indicators"].append("Low RAM amount (suspicious for VM)")
                vm_indicators["confidence"] += 15

            # تعیین نوع VM
            if vm_indicators["confidence"] >= 50:
                vm_indicators["is_vm"] = True

                if "vmware" in str(vm_indicators["indicators"]).lower():
                    vm_indicators["vm_type"] = "VMware"
                elif "virtualbox" in str(vm_indicators["indicators"]).lower():
                    vm_indicators["vm_type"] = "VirtualBox"
                elif "hyper-v" in str(vm_indicators["indicators"]).lower():
                    vm_indicators["vm_type"] = "Hyper-V"
                elif "qemu" in str(vm_indicators["indicators"]).lower():
                    vm_indicators["vm_type"] = "QEMU"
                else:
                    vm_indicators["vm_type"] = "Unknown VM"

            self.vm_detection_results = vm_indicators
            self.hardware_info["vm_detection"] = vm_indicators

            # ارسال سیگنال
            self.vm_detection_complete.emit(vm_indicators)

            if vm_indicators["is_vm"]:
                self.logger.warning(f"🚨 Virtual Machine detected: {vm_indicators['vm_type']} (Confidence: {vm_indicators['confidence']}%)")
            else:
                self.logger.info("✅ Physical machine detected")

        except Exception as e:
            self.logger.error(f"❌ VM detection error: {e}")

    def generate_hardware_fingerprint(self):
        """🔐 تولید fingerprint سخت‌افزاری"""
        try:
            # جمع‌آوری اطلاعات کلیدی برای fingerprint
            fingerprint_data = {
                "cpu_name": self.hardware_info.get("cpu", {}).get("name", ""),
                "cpu_cores": self.hardware_info.get("cpu", {}).get("cores_physical", 0),
                "ram_total": self.hardware_info.get("ram", {}).get("total_gb", 0),
                "gpu_name": self.hardware_info.get("gpu", {}).get("primary_gpu", {}).get("name", ""),
                "motherboard": f"{self.hardware_info.get('motherboard', {}).get('manufacturer', '')}-{self.hardware_info.get('motherboard', {}).get('product', '')}",
                "bios": f"{self.hardware_info.get('bios', {}).get('manufacturer', '')}-{self.hardware_info.get('bios', {}).get('version', '')}",
                "os": f"{platform.system()}-{platform.release()}",
                "machine_id": self._get_machine_id()
            }

            # تولید hash
            fingerprint_string = json.dumps(fingerprint_data, sort_keys=True)
            fingerprint_hash = hashlib.sha256(fingerprint_string.encode()).hexdigest()

            self.fingerprint_data = {
                "data": fingerprint_data,
                "hash": fingerprint_hash,
                "short_hash": fingerprint_hash[:16],
                "generated_at": datetime.now().isoformat()
            }

            self.hardware_info["fingerprint"] = self.fingerprint_data

            self.logger.info(f"🔐 Hardware fingerprint generated: {self.fingerprint_data['short_hash']}")

        except Exception as e:
            self.logger.error(f"❌ Fingerprint generation error: {e}")

    def _get_machine_id(self):
        """🆔 دریافت شناسه منحصر به فرد دستگاه"""
        try:
            if platform.system() == "Windows":
                # استفاده از UUID Windows
                result = subprocess.run(
                    ['wmic', 'csproduct', 'get', 'UUID'],
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            return line.strip()

            # fallback به MAC address
            import uuid
            return str(uuid.getnode())

        except Exception:
            return "unknown"

    def get_browser_fingerprint_script(self):
        """📱 تولید اسکریپت JavaScript برای fingerprinting مرورگر"""
        return """
        // 🔍 VIP BIG BANG Advanced Browser Fingerprinting
        (function() {
            const fingerprint = {};

            // === HARDWARE DETECTION === //

            // RAM Detection
            fingerprint.deviceMemory = navigator.deviceMemory || 'unknown';

            // CPU Detection
            fingerprint.hardwareConcurrency = navigator.hardwareConcurrency || 'unknown';

            // === GPU DETECTION === //
            function getGPUInfo() {
                try {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

                    if (gl) {
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        if (debugInfo) {
                            return {
                                vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
                                renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
                            };
                        }
                    }
                    return { vendor: 'unknown', renderer: 'unknown' };
                } catch (e) {
                    return { vendor: 'error', renderer: 'error' };
                }
            }

            const gpuInfo = getGPUInfo();
            fingerprint.gpuVendor = gpuInfo.vendor;
            fingerprint.gpuRenderer = gpuInfo.renderer;

            // === SCREEN DETECTION === //
            fingerprint.screenWidth = screen.width;
            fingerprint.screenHeight = screen.height;
            fingerprint.screenColorDepth = screen.colorDepth;
            fingerprint.screenPixelDepth = screen.pixelDepth;
            fingerprint.screenAvailWidth = screen.availWidth;
            fingerprint.screenAvailHeight = screen.availHeight;

            // === BROWSER DETECTION === //
            fingerprint.userAgent = navigator.userAgent;
            fingerprint.platform = navigator.platform;
            fingerprint.language = navigator.language;
            fingerprint.languages = navigator.languages;
            fingerprint.cookieEnabled = navigator.cookieEnabled;
            fingerprint.doNotTrack = navigator.doNotTrack;
            fingerprint.maxTouchPoints = navigator.maxTouchPoints;

            // === TIMEZONE DETECTION === //
            fingerprint.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            fingerprint.timezoneOffset = new Date().getTimezoneOffset();

            // === CANVAS FINGERPRINTING === //
            function getCanvasFingerprint() {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#f60';
                    ctx.fillRect(125, 1, 62, 20);
                    ctx.fillStyle = '#069';
                    ctx.fillText('VIP BIG BANG 🚀', 2, 15);
                    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
                    ctx.fillText('Advanced Fingerprinting', 4, 45);

                    return canvas.toDataURL();
                } catch (e) {
                    return 'canvas_error';
                }
            }

            fingerprint.canvasFingerprint = getCanvasFingerprint();

            // === WEBGL FINGERPRINTING === //
            function getWebGLFingerprint() {
                try {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl');

                    if (!gl) return 'webgl_not_supported';

                    const vertexShader = gl.createShader(gl.VERTEX_SHADER);
                    gl.shaderSource(vertexShader, 'attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}');
                    gl.compileShader(vertexShader);

                    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
                    gl.shaderSource(fragmentShader, 'precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}');
                    gl.compileShader(fragmentShader);

                    const program = gl.createProgram();
                    gl.attachShader(program, vertexShader);
                    gl.attachShader(program, fragmentShader);
                    gl.linkProgram(program);
                    gl.useProgram(program);

                    program.attrVertex = gl.getAttribLocation(program, 'attrVertex');
                    program.uniformOffset = gl.getUniformLocation(program, 'uniformOffset');

                    const buffer = gl.createBuffer();
                    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
                    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([-0.2, -0.9, 0, 0.4, -0.26, 0, 0, 0.7321, 0]), gl.STATIC_DRAW);

                    gl.enableVertexAttribArray(program.attrVertex);
                    gl.vertexAttribPointer(program.attrVertex, 3, gl.FLOAT, false, 0, 0);
                    gl.uniform2f(program.uniformOffset, 1, 1);
                    gl.drawArrays(gl.TRIANGLES, 0, 3);

                    return canvas.toDataURL();
                } catch (e) {
                    return 'webgl_error';
                }
            }

            fingerprint.webglFingerprint = getWebGLFingerprint();

            // === VM/HEADLESS DETECTION === //
            fingerprint.webdriver = navigator.webdriver;
            fingerprint.phantom = window.callPhantom || window._phantom;
            fingerprint.selenium = window.document.$cdc_asdjflasutopfhvcZLmcfl_;

            // === PLUGINS DETECTION === //
            fingerprint.plugins = Array.from(navigator.plugins).map(p => p.name);

            // === AUDIO CONTEXT FINGERPRINTING === //
            function getAudioFingerprint() {
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const analyser = audioContext.createAnalyser();
                    const gainNode = audioContext.createGain();

                    oscillator.type = 'triangle';
                    oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);

                    gainNode.gain.setValueAtTime(0, audioContext.currentTime);

                    oscillator.connect(analyser);
                    analyser.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.start(0);

                    const frequencyData = new Uint8Array(analyser.frequencyBinCount);
                    analyser.getByteFrequencyData(frequencyData);

                    oscillator.stop();
                    audioContext.close();

                    return Array.from(frequencyData).slice(0, 30).join(',');
                } catch (e) {
                    return 'audio_error';
                }
            }

            fingerprint.audioFingerprint = getAudioFingerprint();

            // === SEND TO PYTHON APPLICATION === //
            window.VIP_BIG_BANG_FINGERPRINT = fingerprint;

            // Try to send via Chrome extension
            if (window.chrome && window.chrome.runtime) {
                try {
                    chrome.runtime.sendMessage({
                        type: 'BROWSER_FINGERPRINT',
                        data: fingerprint
                    });
                } catch (e) {
                    console.log('Extension communication failed:', e);
                }
            }

            // Try to send via WebSocket
            try {
                const ws = new WebSocket('ws://localhost:8765');
                ws.onopen = function() {
                    ws.send(JSON.stringify({
                        type: 'browser_fingerprint',
                        data: fingerprint
                    }));
                    ws.close();
                };
            } catch (e) {
                console.log('WebSocket communication failed:', e);
            }

            console.log('🔍 VIP BIG BANG Browser Fingerprint:', fingerprint);

            return fingerprint;
        })();
        """

    def get_complete_info(self):
        """📊 دریافت اطلاعات کامل"""
        return {
            "hardware": self.hardware_info,
            "vm_detection": self.vm_detection_results,
            "fingerprint": self.fingerprint_data,
            "detection_time": datetime.now().isoformat()
        }

    def is_virtual_machine(self):
        """🔍 بررسی اینکه آیا در VM اجرا می‌شود"""
        return self.vm_detection_results.get("is_vm", False)

    def get_vm_confidence(self):
        """📊 دریافت درصد اطمینان VM"""
        return self.vm_detection_results.get("confidence", 0)

    def get_hardware_fingerprint(self):
        """🔐 دریافت fingerprint سخت‌افزاری"""
        return self.fingerprint_data.get("hash", "")

    def log_detection_summary(self):
        """📊 نمایش خلاصه تشخیص"""
        try:
            self.logger.info("🔍 === ADVANCED HARDWARE DETECTION SUMMARY ===")

            # CPU
            cpu = self.hardware_info.get("cpu", {})
            self.logger.info(f"💻 CPU: {cpu.get('brand', cpu.get('name', 'Unknown'))}")
            self.logger.info(f"   Cores: {cpu.get('cores_physical', 0)}P/{cpu.get('cores_logical', 0)}L")
            self.logger.info(f"   Vendor: {cpu.get('vendor', 'Unknown')}")

            # RAM
            ram = self.hardware_info.get("ram", {})
            self.logger.info(f"🧠 RAM: {ram.get('total_gb', 0)}GB total")
            self.logger.info(f"   Modules: {ram.get('used_slots', 0)}/{ram.get('total_slots', 0)} slots")

            # GPU
            gpu = self.hardware_info.get("gpu", {})
            if gpu.get("detected", False):
                primary = gpu.get("primary_gpu", {})
                self.logger.info(f"🎮 GPU: {primary.get('name', 'Unknown')}")
                self.logger.info(f"   Cards detected: {len(gpu.get('cards', []))}")
            else:
                self.logger.info("🎮 GPU: Not detected")

            # VM Detection
            vm = self.vm_detection_results
            if vm.get("is_vm", False):
                self.logger.warning(f"🚨 VM Detected: {vm.get('vm_type', 'Unknown')} ({vm.get('confidence', 0)}%)")
                for indicator in vm.get("indicators", []):
                    self.logger.warning(f"   - {indicator}")
            else:
                self.logger.info("✅ Physical machine confirmed")

            # Fingerprint
            fp = self.fingerprint_data
            if fp:
                self.logger.info(f"🔐 Fingerprint: {fp.get('short_hash', 'Unknown')}")

            self.logger.info("🏆 === DETECTION COMPLETE ===")

        except Exception as e:
            self.logger.error(f"❌ Summary logging error: {e}")
