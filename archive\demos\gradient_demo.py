"""
🌈 Gradient Backgrounds Demo - تکنیک‌های پس‌زمینه ChatGPT
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import Qt

class GradientDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🌈 Gradient Backgrounds Demo")
        self.setGeometry(100, 100, 1000, 700)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main ChatGPT-style gradient
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:0.3 #1a1a2e, stop:0.7 #16213e, stop:1 #0f3460);
                color: white;
            }
        """)
        
        layout = QGridLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("🌈 Modern Gradient Collection")
        title.setStyleSheet("""
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-align: center;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title, 0, 0, 1, 3)
        
        # 1. ChatGPT Main Background
        chatgpt_bg = self.create_gradient_demo(
            "ChatGPT Main Background",
            """
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0f0f23, stop:0.3 #1a1a2e, 
                stop:0.7 #16213e, stop:1 #0f3460);
            """,
            "Main app background - dark and professional"
        )
        layout.addWidget(chatgpt_bg, 1, 0)
        
        # 2. Panel Background
        panel_bg = self.create_gradient_demo(
            "Panel Background",
            """
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255,255,255,0.12),
                stop:1 rgba(255,255,255,0.06));
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 16px;
            """,
            "Semi-transparent panels with subtle borders"
        )
        layout.addWidget(panel_bg, 1, 1)
        
        # 3. Button Gradient
        button_bg = self.create_gradient_demo(
            "Button Gradient",
            """
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255,255,255,0.15),
                stop:1 rgba(255,255,255,0.05));
            border: 1px solid rgba(255,255,255,0.25);
            border-radius: 12px;
            """,
            "Subtle button gradients for modern look"
        )
        layout.addWidget(button_bg, 1, 2)
        
        # 4. Success Gradient
        success_bg = self.create_gradient_demo(
            "Success/Buy Gradient",
            """
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4CAF50, stop:1 #45a049);
            border-radius: 12px;
            """,
            "Green gradients for positive actions"
        )
        layout.addWidget(success_bg, 2, 0)
        
        # 5. Danger Gradient
        danger_bg = self.create_gradient_demo(
            "Danger/Sell Gradient",
            """
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #F44336, stop:1 #da190b);
            border-radius: 12px;
            """,
            "Red gradients for negative actions"
        )
        layout.addWidget(danger_bg, 2, 1)
        
        # 6. Highlight Gradient
        highlight_bg = self.create_gradient_demo(
            "Highlight Gradient",
            """
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(76, 175, 80, 0.2),
                stop:1 rgba(76, 175, 80, 0.05));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 16px;
            """,
            "Colored highlights for important panels"
        )
        layout.addWidget(highlight_bg, 2, 2)
        
        # Gradient Rules
        rules_widget = QWidget()
        rules_layout = QVBoxLayout(rules_widget)
        
        rules_title = QLabel("📝 Gradient Rules:")
        rules_title.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: #4CAF50;
            margin: 10px 0;
        """)
        rules_layout.addWidget(rules_title)
        
        rules_text = QLabel("""
🎨 Gradient Best Practices:

• Direction: x1:0, y1:0, x2:1, y2:1 (diagonal) یا x2:0, y2:1 (vertical)
• Opacity: 0.05 → 0.15 برای subtle effects
• Colors: از تیره به روشن یا برعکس
• Stop Points: 0, 0.3, 0.7, 1 برای smooth transitions
• Border Radius: 12px, 16px, 20px برای modern look
• Combine: gradient + border + border-radius برای بهترین نتیجه
        """)
        rules_text.setStyleSheet("""
            font-size: 13px;
            color: rgba(255,255,255,0.8);
            line-height: 1.6;
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.1);
        """)
        rules_text.setWordWrap(True)
        rules_layout.addWidget(rules_text)
        
        layout.addWidget(rules_widget, 3, 0, 1, 3)
    
    def create_gradient_demo(self, title, gradient_style, description):
        """Create a gradient demonstration widget"""
        container = QWidget()
        container.setFixedHeight(150)
        
        layout = QVBoxLayout(container)
        layout.setSpacing(8)
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)
        
        # Gradient sample
        sample = QWidget()
        sample.setFixedHeight(80)
        sample.setStyleSheet(gradient_style)
        layout.addWidget(sample)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 11px;
            color: rgba(255,255,255,0.7);
            margin-top: 5px;
        """)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        return container

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = GradientDemo()
    window.show()
    sys.exit(app.exec())
