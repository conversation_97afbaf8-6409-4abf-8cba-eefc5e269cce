#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 GitHub Copilot Assisted Diagnostic
استفاده از GitHub Copilot برای تشخیص مشکلات UI و سیستم
"""

import sys
import os
import subprocess
import threading
import time
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

def check_ui_display_issues():
    """
    GitHub Copilot suggests checking these common UI issues:
    1. Qt Application not starting properly
    2. Display scaling issues
    3. Threading problems
    4. Event loop conflicts
    """
    print("🤖 GitHub Copilot: Checking UI Display Issues...")
    
    issues_found = []
    
    # Check 1: Qt Application initialization
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            issues_found.append("Qt Application not initialized")
            print("  ❌ Qt Application not running")
        else:
            print("  ✅ Qt Application instance found")
    except Exception as e:
        issues_found.append(f"Qt import error: {e}")
        print(f"  ❌ Qt import error: {e}")
    
    # Check 2: Display environment
    display_env = os.environ.get('DISPLAY')
    if sys.platform != "win32" and not display_env:
        issues_found.append("DISPLAY environment variable not set")
        print("  ❌ DISPLAY environment not set")
    else:
        print("  ✅ Display environment OK")
    
    # Check 3: Threading issues
    main_thread = threading.main_thread()
    current_thread = threading.current_thread()
    if main_thread != current_thread:
        issues_found.append("Running in non-main thread")
        print("  ❌ Not running in main thread")
    else:
        print("  ✅ Running in main thread")
    
    return issues_found

def check_process_conflicts():
    """
    GitHub Copilot suggests checking for process conflicts
    """
    print("\n🤖 GitHub Copilot: Checking Process Conflicts...")
    
    conflicts = []
    
    try:
        # Check for multiple Python processes
        if sys.platform == "win32":
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            python_processes = result.stdout.count('python.exe')
        else:
            result = subprocess.run(['pgrep', '-c', 'python'], 
                                  capture_output=True, text=True)
            python_processes = int(result.stdout.strip()) if result.stdout.strip().isdigit() else 0
        
        print(f"  📊 Python processes running: {python_processes}")
        
        if python_processes > 5:
            conflicts.append(f"Too many Python processes: {python_processes}")
            print("  ⚠️ Many Python processes detected")
        
    except Exception as e:
        print(f"  ❌ Error checking processes: {e}")
    
    return conflicts

def test_minimal_ui():
    """
    GitHub Copilot suggests testing with minimal UI
    """
    print("\n🤖 GitHub Copilot: Testing Minimal UI...")
    
    try:
        from PySide6.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PySide6.QtCore import QTimer
        
        # Create minimal test application
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test window
        window = QWidget()
        window.setWindowTitle("VIP BIG BANG - UI Test")
        window.setGeometry(100, 100, 400, 300)
        
        layout = QVBoxLayout()
        label = QLabel("🚀 VIP BIG BANG UI Test\nIf you see this, UI is working!")
        label.setStyleSheet("font-size: 16px; padding: 20px; background: #1a1a2e; color: #00ff00;")
        layout.addWidget(label)
        
        window.setLayout(layout)
        window.show()
        
        # Auto-close after 5 seconds
        timer = QTimer()
        timer.timeout.connect(window.close)
        timer.start(5000)
        
        print("  ✅ Minimal UI created successfully")
        print("  🔍 Check if test window appears on screen")
        
        # Run for 6 seconds
        QTimer.singleShot(6000, app.quit)
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Minimal UI test failed: {e}")
        return False

def check_screen_resolution():
    """
    GitHub Copilot suggests checking screen resolution and scaling
    """
    print("\n🤖 GitHub Copilot: Checking Screen Resolution...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QScreen
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        screens = app.screens()
        print(f"  📺 Number of screens: {len(screens)}")
        
        for i, screen in enumerate(screens):
            geometry = screen.geometry()
            dpi = screen.logicalDotsPerInch()
            scale_factor = screen.devicePixelRatio()
            
            print(f"  Screen {i+1}:")
            print(f"    Resolution: {geometry.width()}x{geometry.height()}")
            print(f"    DPI: {dpi}")
            print(f"    Scale Factor: {scale_factor}")
            
            # Check for common issues
            if scale_factor > 1.5:
                print(f"    ⚠️ High DPI scaling detected: {scale_factor}")
            
            if geometry.width() < 800 or geometry.height() < 600:
                print(f"    ⚠️ Low resolution detected")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Screen check failed: {e}")
        return False

def check_qt_platform():
    """
    GitHub Copilot suggests checking Qt platform plugins
    """
    print("\n🤖 GitHub Copilot: Checking Qt Platform...")
    
    try:
        from PySide6.QtCore import QCoreApplication
        
        # Check available platforms
        platforms = []
        
        # Try to get platform name
        app = QCoreApplication.instance()
        if app:
            platform_name = app.platformName()
            print(f"  🖥️ Current platform: {platform_name}")
        
        # Check environment variables
        qt_qpa_platform = os.environ.get('QT_QPA_PLATFORM')
        if qt_qpa_platform:
            print(f"  🔧 QT_QPA_PLATFORM: {qt_qpa_platform}")
        else:
            print("  ℹ️ QT_QPA_PLATFORM not set (using default)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Qt platform check failed: {e}")
        return False

def suggest_fixes():
    """
    GitHub Copilot suggests these fixes for UI issues
    """
    print("\n🤖 GitHub Copilot: Suggested Fixes...")
    
    fixes = [
        "1. 🔄 Restart VS Code and terminal",
        "2. 🖥️ Check if running in correct environment (not SSH/Remote)",
        "3. 🎯 Set QT_QPA_PLATFORM=windows (on Windows)",
        "4. 📱 Disable high DPI scaling: QT_SCALE_FACTOR=1",
        "5. 🔧 Update PySide6: pip install --upgrade PySide6",
        "6. 🧹 Clear Python cache: python -Bc 'import sys; print(sys.path)'",
        "7. 🎮 Run as administrator (Windows)",
        "8. 🔍 Check Windows Defender/Antivirus blocking",
        "9. 📺 Try different display/monitor",
        "10. 🚀 Use virtual environment: python -m venv venv"
    ]
    
    for fix in fixes:
        print(f"  {fix}")
    
    print("\n💡 Quick Fix Commands:")
    print("  set QT_QPA_PLATFORM=windows")
    print("  set QT_SCALE_FACTOR=1")
    print("  pip install --upgrade PySide6")

def run_comprehensive_test():
    """
    GitHub Copilot comprehensive test
    """
    print("🤖 GitHub Copilot: Running Comprehensive UI Diagnostic")
    print("=" * 60)
    
    # Run all checks
    ui_issues = check_ui_display_issues()
    process_conflicts = check_process_conflicts()
    
    screen_ok = check_screen_resolution()
    qt_platform_ok = check_qt_platform()
    
    print("\n" + "=" * 60)
    print("🤖 GitHub Copilot: Test Summary")
    print("=" * 60)
    
    if ui_issues:
        print("❌ UI Issues Found:")
        for issue in ui_issues:
            print(f"  • {issue}")
    else:
        print("✅ No UI issues detected")
    
    if process_conflicts:
        print("⚠️ Process Conflicts:")
        for conflict in process_conflicts:
            print(f"  • {conflict}")
    else:
        print("✅ No process conflicts")
    
    print(f"📺 Screen Check: {'✅ OK' if screen_ok else '❌ Failed'}")
    print(f"🖥️ Qt Platform: {'✅ OK' if qt_platform_ok else '❌ Failed'}")
    
    # Run minimal UI test
    print("\n🧪 Running Minimal UI Test...")
    ui_test_ok = test_minimal_ui()
    print(f"🎮 Minimal UI Test: {'✅ PASSED' if ui_test_ok else '❌ FAILED'}")
    
    if not ui_test_ok or ui_issues or process_conflicts:
        suggest_fixes()
    else:
        print("\n🎉 All tests passed! UI should be working.")

def main():
    """Main diagnostic function"""
    try:
        run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n👋 Diagnostic cancelled by user")
    except Exception as e:
        print(f"\n❌ Diagnostic error: {e}")
        suggest_fixes()

if __name__ == "__main__":
    main()
