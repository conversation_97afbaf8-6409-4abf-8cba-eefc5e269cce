"""
🎮 VIP BIG BANG - Cartoon Gaming UI
رابط کاربری کارتونی و گیمینگ برای ربات تریدینگ
بهترین طراحی مدرن با المان‌های بازی و کارتون
"""

import sys
import math
import random
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# 🎨 پالت رنگ‌های کارتونی و گیمینگ
class CartoonGameColors:
    # رنگ‌های اصلی کارتونی
    CARTOON_BLUE = "#4A90E2"
    CARTOON_GREEN = "#7ED321"
    CARTOON_ORANGE = "#F5A623"
    CARTOON_RED = "#D0021B"
    CARTOON_PURPLE = "#9013FE"
    CARTOON_PINK = "#FF6B9D"
    CARTOON_YELLOW = "#FFD700"
    CARTOON_CYAN = "#00BCD4"
    
    # رنگ‌های نئونی گیمینگ
    NEON_GREEN = "#39FF14"
    NEON_BLUE = "#1B03A3"
    NEON_PINK = "#FF073A"
    NEON_PURPLE = "#BF00FF"
    NEON_ORANGE = "#FF6600"
    
    # پس‌زمینه‌های اپلیکیشن
    APP_DARK = "#1A1A2E"
    APP_MEDIUM = "#16213E"
    APP_LIGHT = "#0F3460"
    APP_SURFACE = "#2D2D4D"
    
    # گرادیان‌های کارتونی
    GRADIENT_BLUE = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4A90E2, stop:1 #357ABD)"
    GRADIENT_GREEN = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7ED321, stop:1 #5BA617)"
    GRADIENT_ORANGE = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #F5A623, stop:1 #E8931A)"
    GRADIENT_PURPLE = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9013FE, stop:1 #7B1FA2)"

# 🎮 دکمه کارتونی گیمینگ
class CartoonGameButton(QPushButton):
    """دکمه کارتونی با انیمیشن‌های جذاب"""
    
    def __init__(self, text="", icon="", button_type="primary", size=(150, 80)):
        super().__init__()
        self.button_text = text
        self.button_icon = icon
        self.button_type = button_type
        self.button_size = size
        
        # تنظیمات انیمیشن
        self.bounce_animation = QPropertyAnimation(self, b"geometry")
        self.glow_effect = QGraphicsDropShadowEffect()
        
        self.setup_button()
        self.setup_animations()
    
    def setup_button(self):
        """تنظیم ظاهر دکمه کارتونی"""
        self.setFixedSize(*self.button_size)
        
        # محتوای دکمه
        if self.button_icon and self.button_text:
            content = f"""
            <div style="text-align: center; line-height: 1.2;">
                <div style="font-size: 32px; margin-bottom: 5px;">{self.button_icon}</div>
                <div style="font-size: 14px; font-weight: 800; color: white; letter-spacing: 1px;">{self.button_text}</div>
            </div>
            """
            
            content_label = QLabel(content)
            content_label.setAlignment(Qt.AlignCenter)
            content_label.setStyleSheet("background: transparent; border: none;")
            
            layout = QVBoxLayout(self)
            layout.setContentsMargins(8, 8, 8, 8)
            layout.addWidget(content_label)
        else:
            self.setText(self.button_text or self.button_icon)
        
        # اعمال استایل کارتونی
        self.apply_cartoon_style()
        
        # افکت درخشش
        self.add_glow_effect()
    
    def apply_cartoon_style(self):
        """اعمال استایل کارتونی به دکمه"""
        if self.button_type == "primary":
            bg_gradient = CartoonGameColors.GRADIENT_BLUE
            border_color = CartoonGameColors.CARTOON_BLUE
            glow_color = CartoonGameColors.CARTOON_BLUE
        elif self.button_type == "success":
            bg_gradient = CartoonGameColors.GRADIENT_GREEN
            border_color = CartoonGameColors.CARTOON_GREEN
            glow_color = CartoonGameColors.CARTOON_GREEN
        elif self.button_type == "warning":
            bg_gradient = CartoonGameColors.GRADIENT_ORANGE
            border_color = CartoonGameColors.CARTOON_ORANGE
            glow_color = CartoonGameColors.CARTOON_ORANGE
        elif self.button_type == "danger":
            bg_gradient = "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #D0021B, stop:1 #A0021B)"
            border_color = CartoonGameColors.CARTOON_RED
            glow_color = CartoonGameColors.CARTOON_RED
        else:
            bg_gradient = CartoonGameColors.GRADIENT_PURPLE
            border_color = CartoonGameColors.CARTOON_PURPLE
            glow_color = CartoonGameColors.CARTOON_PURPLE
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: {bg_gradient};
                border: 3px solid {border_color};
                border-radius: 25px;
                color: white;
                font-family: 'Comic Sans MS', 'Arial', sans-serif;
                font-weight: 800;
                font-size: 14px;
                padding: 10px;
            }}
            QPushButton:hover {{
                background: {border_color};
                border: 4px solid white;
            }}
            QPushButton:pressed {{
                background: rgba(0, 0, 0, 0.3);
                border: 2px solid {border_color};
            }}
        """)
    
    def add_glow_effect(self):
        """افزودن افکت درخشش کارتونی"""
        self.glow_effect.setBlurRadius(30)
        self.glow_effect.setXOffset(0)
        self.glow_effect.setYOffset(0)
        
        if self.button_type == "primary":
            self.glow_effect.setColor(QColor(74, 144, 226, 100))
        elif self.button_type == "success":
            self.glow_effect.setColor(QColor(126, 211, 33, 100))
        elif self.button_type == "warning":
            self.glow_effect.setColor(QColor(245, 166, 35, 100))
        elif self.button_type == "danger":
            self.glow_effect.setColor(QColor(208, 2, 27, 100))
        else:
            self.glow_effect.setColor(QColor(144, 19, 254, 100))
        
        self.setGraphicsEffect(self.glow_effect)
    
    def setup_animations(self):
        """تنظیم انیمیشن‌های دکمه"""
        # انیمیشن پرش
        self.bounce_animation.setDuration(200)
        self.bounce_animation.setEasingCurve(QEasingCurve.Type.OutBounce)
        
        # رویدادهای ماوس
        self.enterEvent = self.start_hover_animation
        self.leaveEvent = self.stop_hover_animation
    
    def start_hover_animation(self, event):
        """شروع انیمیشن هاور"""
        current_geometry = self.geometry()
        new_geometry = QRect(
            current_geometry.x() - 2,
            current_geometry.y() - 2,
            current_geometry.width() + 4,
            current_geometry.height() + 4
        )
        
        self.bounce_animation.setStartValue(current_geometry)
        self.bounce_animation.setEndValue(new_geometry)
        self.bounce_animation.start()
    
    def stop_hover_animation(self, event):
        """توقف انیمیشن هاور"""
        current_geometry = self.geometry()
        original_geometry = QRect(
            current_geometry.x() + 2,
            current_geometry.y() + 2,
            current_geometry.width() - 4,
            current_geometry.height() - 4
        )
        
        self.bounce_animation.setStartValue(current_geometry)
        self.bounce_animation.setEndValue(original_geometry)
        self.bounce_animation.start()

# 📊 ویجت آمار کارتونی
class CartoonStatsWidget(QWidget):
    """ویجت نمایش آمار با طراحی کارتونی"""
    
    def __init__(self, title="", value="", icon="", color=CartoonGameColors.CARTOON_BLUE):
        super().__init__()
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        
        self.setup_widget()
        self.setup_animations()
    
    def setup_widget(self):
        """تنظیم ویجت آمار کارتونی"""
        self.setFixedSize(220, 140)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # هدر با آیکون
        header_layout = QHBoxLayout()
        
        if self.icon:
            icon_label = QLabel(self.icon)
            icon_label.setStyleSheet(f"""
                font-size: 36px;
                color: {self.color};
                margin-right: 10px;
            """)
            header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-family: 'Comic Sans MS', 'Arial', sans-serif;
            font-size: 14px;
            color: rgba(255,255,255,0.9);
            font-weight: 700;
            letter-spacing: 1px;
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # نمایش مقدار
        self.value_label = QLabel(self.value)
        self.value_label.setStyleSheet(f"""
            font-family: 'Comic Sans MS', 'Arial', sans-serif;
            font-size: 32px;
            font-weight: 900;
            color: {self.color};
            letter-spacing: 2px;
            margin: 10px 0;
        """)
        self.value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.value_label)
        
        # نوار پیشرفت کارتونی
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(random.randint(70, 95))
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {self.color};
                border-radius: 12px;
                background: rgba(0, 0, 0, 0.5);
                height: 12px;
                text-align: center;
            }}
            QProgressBar::chunk {{
                background: {self.color};
                border-radius: 8px;
                margin: 2px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # استایل کلی ویجت
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(0, 0, 0, 0.6));
                border: 3px solid {self.color};
                border-radius: 20px;
            }}
        """)
        
        # افکت درخشش
        self.add_glow_effect()
    
    def add_glow_effect(self):
        """افزودن افکت درخشش"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(self.color).darker(120))
        self.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """تنظیم انیمیشن‌های آمار"""
        # تایمر به‌روزرسانی خودکار
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_value)
        self.update_timer.start(4000)  # هر 4 ثانیه
    
    def update_value(self):
        """به‌روزرسانی مقدار با انیمیشن"""
        if "$" in self.value:
            new_value = random.uniform(1500, 3000)
            self.animate_to_value(f"${new_value:.2f}")
        elif "%" in self.value:
            new_value = random.uniform(85, 98)
            self.animate_to_value(f"{new_value:.1f}%")
        else:
            new_value = random.randint(15, 60)
            self.animate_to_value(str(new_value))
    
    def animate_to_value(self, new_value):
        """انیمیشن تغییر مقدار"""
        self.value = new_value
        self.value_label.setText(new_value)
        
        # به‌روزرسانی نوار پیشرفت
        new_progress = random.randint(70, 95)
        self.progress_bar.setValue(new_progress)

# 🎮 پنل کارتونی گیمینگ
class CartoonGamePanel(QFrame):
    """پنل کارتونی با افکت‌های گیمینگ"""

    def __init__(self, title="", panel_type="default"):
        super().__init__()
        self.panel_title = title
        self.panel_type = panel_type
        self.setup_panel()

    def setup_panel(self):
        """تنظیم پنل کارتونی"""
        if self.panel_type == "primary":
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(74, 144, 226, 0.2),
                    stop:0.5 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(74, 144, 226, 0.2));
            """
            border_color = CartoonGameColors.CARTOON_BLUE
        elif self.panel_type == "success":
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(126, 211, 33, 0.2),
                    stop:0.5 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(126, 211, 33, 0.2));
            """
            border_color = CartoonGameColors.CARTOON_GREEN
        elif self.panel_type == "warning":
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(245, 166, 35, 0.2),
                    stop:0.5 rgba(0, 0, 0, 0.8),
                    stop:1 rgba(245, 166, 35, 0.2));
            """
            border_color = CartoonGameColors.CARTOON_ORANGE
        else:
            bg_gradient = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(0, 0, 0, 0.9),
                    stop:1 rgba(255, 255, 255, 0.1));
            """
            border_color = CartoonGameColors.CARTOON_CYAN

        self.setStyleSheet(f"""
            QFrame {{
                {bg_gradient}
                border: 3px solid {border_color};
                border-radius: 25px;
                padding: 25px;
            }}
        """)

        # افکت درخشش کارتونی
        self.add_cartoon_glow()

    def add_cartoon_glow(self):
        """افزودن افکت درخشش کارتونی"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(35)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(255, 255, 255, 80))
        self.setGraphicsEffect(shadow)

# 📈 نمایشگر چارت کارتونی
class CartoonChartDisplay(QWidget):
    """نمایشگر چارت با طراحی کارتونی و گیمینگ"""

    def __init__(self):
        super().__init__()
        self.setMinimumHeight(350)
        self.data_points = []
        self.generate_data()

        # تایمر انیمیشن
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_data)
        self.animation_timer.start(150)  # هر 150 میلی‌ثانیه

        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 0, 0, 0.9),
                    stop:0.3 rgba(74, 144, 226, 0.15),
                    stop:0.7 rgba(126, 211, 33, 0.15),
                    stop:1 rgba(0, 0, 0, 0.9));
                border: 4px solid {CartoonGameColors.CARTOON_CYAN};
                border-radius: 20px;
            }}
        """)

    def generate_data(self):
        """تولید داده‌های چارت کارتونی"""
        self.data_points = []
        for i in range(60):
            x = i * 8
            y = 175 + 60 * math.sin(i * 0.15) + random.uniform(-25, 25)
            self.data_points.append((x, y))

    def update_data(self):
        """به‌روزرسانی داده‌های چارت با انیمیشن"""
        if self.data_points:
            # جابجایی نقاط
            self.data_points.pop(0)
            last_x = self.data_points[-1][0] if self.data_points else 0
            new_y = 175 + 60 * math.sin(len(self.data_points) * 0.15) + random.uniform(-25, 25)
            self.data_points.append((last_x + 8, new_y))

        self.update()

    def paintEvent(self, event):
        """رسم چارت کارتونی"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # رسم شبکه کارتونی
        painter.setPen(QPen(QColor(255, 255, 255, 30), 1))
        width, height = self.width(), self.height()

        # خطوط عمودی
        for i in range(0, width, 40):
            painter.drawLine(i, 0, i, height)

        # خطوط افقی
        for i in range(0, height, 25):
            painter.drawLine(0, i, width, i)

        # رسم خط داده با افکت درخشش
        if len(self.data_points) > 1:
            # افکت درخشش
            painter.setPen(QPen(QColor(126, 211, 33, 120), 12))
            for i in range(len(self.data_points) - 1):
                x1, y1 = self.data_points[i]
                x2, y2 = self.data_points[i + 1]
                painter.drawLine(int(x1), int(y1), int(x2), int(y2))

            # خط اصلی
            painter.setPen(QPen(QColor(126, 211, 33, 255), 4))
            for i in range(len(self.data_points) - 1):
                x1, y1 = self.data_points[i]
                x2, y2 = self.data_points[i + 1]
                painter.drawLine(int(x1), int(y1), int(x2), int(y2))

        # نمایش قیمت کارتونی
        painter.setPen(QPen(QColor(255, 255, 255), 3))
        painter.setFont(QFont("Comic Sans MS", 20, QFont.Weight.Bold))
        painter.drawText(25, 40, "1.07329")

        # نمایش وضعیت
        painter.setPen(QPen(QColor(126, 211, 33), 2))
        painter.setFont(QFont("Comic Sans MS", 14))
        painter.drawText(25, height - 25, "🟢 تریدینگ فعال - VIP BIG BANG")

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # تست ویندو
    window = QMainWindow()
    window.setWindowTitle("🎮 VIP BIG BANG - Cartoon Gaming Test")
    window.setGeometry(100, 100, 900, 700)

    # پس‌زمینه کارتونی
    window.setStyleSheet(f"""
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {CartoonGameColors.APP_DARK},
                stop:0.5 {CartoonGameColors.APP_MEDIUM},
                stop:1 {CartoonGameColors.APP_LIGHT});
            color: white;
        }}
    """)

    central_widget = QWidget()
    window.setCentralWidget(central_widget)

    layout = QVBoxLayout(central_widget)
    layout.setSpacing(30)
    layout.setContentsMargins(40, 40, 40, 40)

    # تست دکمه‌ها
    buttons_layout = QHBoxLayout()

    primary_btn = CartoonGameButton("شروع", "🚀", "primary", (160, 90))
    success_btn = CartoonGameButton("اجرا", "✅", "success", (160, 90))
    warning_btn = CartoonGameButton("هشدار", "⚠️", "warning", (160, 90))
    danger_btn = CartoonGameButton("توقف", "🛑", "danger", (160, 90))

    buttons_layout.addWidget(primary_btn)
    buttons_layout.addWidget(success_btn)
    buttons_layout.addWidget(warning_btn)
    buttons_layout.addWidget(danger_btn)

    layout.addLayout(buttons_layout)

    # تست چارت
    chart_display = CartoonChartDisplay()
    layout.addWidget(chart_display)

    # تست آمار
    stats_layout = QHBoxLayout()

    balance_stats = CartoonStatsWidget("موجودی", "$2,150.75", "💰", CartoonGameColors.CARTOON_GREEN)
    power_stats = CartoonStatsWidget("قدرت", "92%", "⚡", CartoonGameColors.CARTOON_BLUE)
    level_stats = CartoonStatsWidget("سطح", "47", "🎯", CartoonGameColors.CARTOON_PURPLE)

    stats_layout.addWidget(balance_stats)
    stats_layout.addWidget(power_stats)
    stats_layout.addWidget(level_stats)

    layout.addLayout(stats_layout)

    window.show()
    sys.exit(app.exec())
