"""
VIP BIG BANG Enterprise - Confirm Mode
Advanced signal confirmation system for high-accuracy trading
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from collections import deque
import threading

class ConfirmMode:
    """
    Enterprise-level signal confirmation system
    Ensures only the strongest signals are executed
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("ConfirmMode")
        
        # Confirmation parameters
        self.min_confirmations = settings.signal_processing.confirmation_required
        self.confirmation_window = 30  # seconds
        self.min_confidence_threshold = settings.signal_processing.min_confidence
        
        # Signal tracking
        self.pending_confirmations = {}
        self.confirmed_signals = deque(maxlen=100)
        self.signal_history = deque(maxlen=1000)
        
        # Performance tracking
        self.confirmation_stats = {
            'total_signals': 0,
            'confirmed_signals': 0,
            'rejected_signals': 0,
            'confirmation_rate': 0.0,
            'average_confirmation_time': 0.0
        }
        
        # Thread safety
        self.lock = threading.Lock()
        
        self.logger.info("Confirm Mode initialized")
    
    def process_signal(self, signal: Dict) -> Dict:
        """
        Process incoming signal for confirmation
        Returns confirmation status and decision
        """
        try:
            with self.lock:
                self.confirmation_stats['total_signals'] += 1
            
            signal_id = signal.get('id', f"signal_{int(time.time() * 1000)}")
            current_time = datetime.now()
            
            # Add to signal history
            signal_entry = {
                **signal,
                'received_at': current_time,
                'confirmation_status': 'PENDING'
            }
            
            with self.lock:
                self.signal_history.append(signal_entry)
            
            # Check if signal meets basic requirements
            if not self._meets_basic_requirements(signal):
                self.logger.debug(f"Signal {signal_id} rejected: Basic requirements not met")
                with self.lock:
                    self.confirmation_stats['rejected_signals'] += 1
                return self._create_rejection_response(signal_id, "Basic requirements not met")
            
            # Check if we already have this signal in confirmation
            if signal_id in self.pending_confirmations:
                return self._update_existing_confirmation(signal_id, signal)
            
            # Start new confirmation process
            return self._start_new_confirmation(signal_id, signal)
            
        except Exception as e:
            self.logger.error(f"Signal processing error: {e}")
            return self._create_error_response(signal_id, str(e))
    
    def _meets_basic_requirements(self, signal: Dict) -> bool:
        """Check if signal meets basic confirmation requirements"""
        # Check confidence threshold
        confidence = signal.get('confidence', 0)
        if confidence < self.min_confidence_threshold:
            return False
        
        # Check signal validity
        if not signal.get('valid', False):
            return False
        
        # Check direction
        direction = signal.get('direction')
        if direction not in ['CALL', 'PUT']:
            return False
        
        # Check score range
        score = signal.get('score', 0.5)
        if score < 0.1 or score > 0.9:
            return False
        
        return True
    
    def _start_new_confirmation(self, signal_id: str, signal: Dict) -> Dict:
        """Start new confirmation process for signal"""
        confirmation_data = {
            'signal_id': signal_id,
            'original_signal': signal,
            'confirmations': [signal],
            'start_time': datetime.now(),
            'required_confirmations': self.min_confirmations,
            'status': 'PENDING'
        }
        
        with self.lock:
            self.pending_confirmations[signal_id] = confirmation_data
        
        self.logger.debug(f"Started confirmation for signal {signal_id}")
        
        # If only 1 confirmation required, confirm immediately
        if self.min_confirmations <= 1:
            return self._confirm_signal(signal_id)
        
        return {
            'confirmed': False,
            'signal_id': signal_id,
            'status': 'PENDING',
            'confirmations_received': 1,
            'confirmations_required': self.min_confirmations,
            'message': f'Signal pending confirmation (1/{self.min_confirmations})'
        }
    
    def _update_existing_confirmation(self, signal_id: str, signal: Dict) -> Dict:
        """Update existing confirmation with new signal"""
        confirmation_data = self.pending_confirmations[signal_id]
        
        # Check if confirmation window has expired
        elapsed_time = (datetime.now() - confirmation_data['start_time']).total_seconds()
        if elapsed_time > self.confirmation_window:
            self.logger.debug(f"Confirmation window expired for signal {signal_id}")
            with self.lock:
                del self.pending_confirmations[signal_id]
                self.confirmation_stats['rejected_signals'] += 1
            return self._create_rejection_response(signal_id, "Confirmation window expired")
        
        # Add new confirmation
        confirmation_data['confirmations'].append(signal)
        confirmations_count = len(confirmation_data['confirmations'])
        
        self.logger.debug(f"Signal {signal_id} confirmation {confirmations_count}/{self.min_confirmations}")
        
        # Check if we have enough confirmations
        if confirmations_count >= self.min_confirmations:
            return self._confirm_signal(signal_id)
        
        return {
            'confirmed': False,
            'signal_id': signal_id,
            'status': 'PENDING',
            'confirmations_received': confirmations_count,
            'confirmations_required': self.min_confirmations,
            'message': f'Signal pending confirmation ({confirmations_count}/{self.min_confirmations})'
        }
    
    def _confirm_signal(self, signal_id: str) -> Dict:
        """Confirm signal and create final trading signal"""
        try:
            confirmation_data = self.pending_confirmations[signal_id]
            confirmations = confirmation_data['confirmations']
            
            # Calculate aggregated signal data
            aggregated_signal = self._aggregate_confirmations(confirmations)
            
            # Create confirmed signal
            confirmed_signal = {
                'id': signal_id,
                'confirmed': True,
                'confirmation_time': datetime.now(),
                'confirmations_count': len(confirmations),
                'aggregated_data': aggregated_signal,
                'original_signals': confirmations,
                'confidence_boost': self._calculate_confidence_boost(confirmations)
            }
            
            # Add to confirmed signals
            with self.lock:
                self.confirmed_signals.append(confirmed_signal)
                del self.pending_confirmations[signal_id]
                self.confirmation_stats['confirmed_signals'] += 1
                
                # Update confirmation rate
                total = self.confirmation_stats['confirmed_signals'] + self.confirmation_stats['rejected_signals']
                if total > 0:
                    self.confirmation_stats['confirmation_rate'] = self.confirmation_stats['confirmed_signals'] / total
            
            # Calculate confirmation time
            confirmation_time = (datetime.now() - confirmation_data['start_time']).total_seconds()
            self._update_average_confirmation_time(confirmation_time)
            
            self.logger.info(f"Signal {signal_id} CONFIRMED after {confirmation_time:.1f}s with {len(confirmations)} confirmations")
            
            return {
                'confirmed': True,
                'signal_id': signal_id,
                'status': 'CONFIRMED',
                'signal_data': aggregated_signal,
                'confirmations_received': len(confirmations),
                'confirmation_time': confirmation_time,
                'message': f'Signal confirmed with {len(confirmations)} confirmations'
            }
            
        except Exception as e:
            self.logger.error(f"Signal confirmation error: {e}")
            return self._create_error_response(signal_id, str(e))
    
    def _aggregate_confirmations(self, confirmations: List[Dict]) -> Dict:
        """Aggregate multiple confirmations into single signal"""
        if not confirmations:
            return {}
        
        # Calculate weighted averages
        total_weight = sum(conf.get('confidence', 0) for conf in confirmations)
        
        if total_weight == 0:
            return confirmations[0]  # Return first if no confidence data
        
        # Weighted score
        weighted_score = sum(
            conf.get('score', 0.5) * conf.get('confidence', 0) 
            for conf in confirmations
        ) / total_weight
        
        # Weighted confidence
        weighted_confidence = sum(
            conf.get('confidence', 0) * conf.get('confidence', 0) 
            for conf in confirmations
        ) / total_weight
        
        # Consensus direction
        directions = [conf.get('direction') for conf in confirmations]
        direction_counts = {d: directions.count(d) for d in set(directions)}
        consensus_direction = max(direction_counts, key=direction_counts.get)
        
        # Agreement percentage
        agreement = direction_counts[consensus_direction] / len(confirmations)
        
        return {
            'score': weighted_score,
            'confidence': weighted_confidence,
            'direction': consensus_direction,
            'agreement': agreement,
            'confirmations_count': len(confirmations),
            'timestamp': datetime.now().isoformat()
        }
    
    def _calculate_confidence_boost(self, confirmations: List[Dict]) -> float:
        """Calculate confidence boost from multiple confirmations"""
        base_boost = 0.1  # 10% base boost for confirmation
        confirmation_boost = (len(confirmations) - 1) * 0.05  # 5% per additional confirmation
        
        # Agreement boost
        directions = [conf.get('direction') for conf in confirmations]
        agreement = directions.count(directions[0]) / len(directions) if directions else 0
        agreement_boost = (agreement - 0.5) * 0.2  # Up to 10% boost for perfect agreement
        
        total_boost = base_boost + confirmation_boost + agreement_boost
        return min(total_boost, 0.3)  # Cap at 30% boost
    
    def _update_average_confirmation_time(self, confirmation_time: float):
        """Update average confirmation time"""
        with self.lock:
            current_avg = self.confirmation_stats['average_confirmation_time']
            confirmed_count = self.confirmation_stats['confirmed_signals']
            
            if confirmed_count == 1:
                self.confirmation_stats['average_confirmation_time'] = confirmation_time
            else:
                # Running average
                self.confirmation_stats['average_confirmation_time'] = (
                    (current_avg * (confirmed_count - 1) + confirmation_time) / confirmed_count
                )
    
    def _create_rejection_response(self, signal_id: str, reason: str) -> Dict:
        """Create rejection response"""
        return {
            'confirmed': False,
            'signal_id': signal_id,
            'status': 'REJECTED',
            'reason': reason,
            'message': f'Signal rejected: {reason}'
        }
    
    def _create_error_response(self, signal_id: str, error: str) -> Dict:
        """Create error response"""
        return {
            'confirmed': False,
            'signal_id': signal_id,
            'status': 'ERROR',
            'error': error,
            'message': f'Signal processing error: {error}'
        }
    
    def get_latest_confirmed_signal(self) -> Optional[Dict]:
        """Get the most recent confirmed signal"""
        with self.lock:
            if self.confirmed_signals:
                return self.confirmed_signals[-1]
        return None
    
    def get_pending_confirmations(self) -> Dict:
        """Get all pending confirmations"""
        with self.lock:
            return self.pending_confirmations.copy()
    
    def get_confirmation_statistics(self) -> Dict:
        """Get confirmation statistics"""
        with self.lock:
            stats = self.confirmation_stats.copy()
            stats['pending_confirmations'] = len(self.pending_confirmations)
            stats['confirmed_signals_count'] = len(self.confirmed_signals)
        
        return stats
    
    def cleanup_expired_confirmations(self):
        """Clean up expired pending confirmations"""
        current_time = datetime.now()
        expired_signals = []
        
        with self.lock:
            for signal_id, confirmation_data in self.pending_confirmations.items():
                elapsed_time = (current_time - confirmation_data['start_time']).total_seconds()
                if elapsed_time > self.confirmation_window:
                    expired_signals.append(signal_id)
            
            for signal_id in expired_signals:
                del self.pending_confirmations[signal_id]
                self.confirmation_stats['rejected_signals'] += 1
        
        if expired_signals:
            self.logger.debug(f"Cleaned up {len(expired_signals)} expired confirmations")
