#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔐 VIP BIG BANG Quotex Login System
🚪 سیستم ورود حرفه‌ای به Quotex
🛡️ Professional login with multiple methods and security
"""

import asyncio
import json
import os
import random
import time
from typing import Dict, Optional, Tuple
from playwright.async_api import Page
import logging
from cryptography.fernet import Fernet
import keyring

class QuotexLoginManager:
    """🔐 Professional Quotex Login Manager"""
    
    def __init__(self, page: Page):
        self.page = page
        self.is_logged_in = False
        self.login_method = None
        self.user_data = {}
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Encryption key for storing credentials
        self.encryption_key = self._get_or_create_encryption_key()
    
    def _get_or_create_encryption_key(self) -> bytes:
        """🔑 Get or create encryption key"""
        try:
            key = keyring.get_password("vip_big_bang", "encryption_key")
            if not key:
                key = Fernet.generate_key().decode()
                keyring.set_password("vip_big_bang", "encryption_key", key)
            return key.encode()
        except:
            # Fallback to file-based key
            key_file = "vip_encryption.key"
            if os.path.exists(key_file):
                with open(key_file, 'rb') as f:
                    return f.read()
            else:
                key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(key)
                return key
    
    def encrypt_credentials(self, credentials: Dict) -> str:
        """🔒 Encrypt credentials"""
        try:
            fernet = Fernet(self.encryption_key)
            data = json.dumps(credentials).encode()
            encrypted = fernet.encrypt(data)
            return encrypted.decode()
        except Exception as e:
            self.logger.error(f"❌ Error encrypting credentials: {e}")
            return ""
    
    def decrypt_credentials(self, encrypted_data: str) -> Dict:
        """🔓 Decrypt credentials"""
        try:
            fernet = Fernet(self.encryption_key)
            decrypted = fernet.decrypt(encrypted_data.encode())
            return json.loads(decrypted.decode())
        except Exception as e:
            self.logger.error(f"❌ Error decrypting credentials: {e}")
            return {}
    
    def save_credentials(self, email: str, password: str, method: str = "email"):
        """💾 Save encrypted credentials"""
        try:
            credentials = {
                'email': email,
                'password': password,
                'method': method,
                'saved_at': int(time.time())
            }
            
            encrypted = self.encrypt_credentials(credentials)
            keyring.set_password("vip_big_bang", "quotex_credentials", encrypted)
            
            self.logger.info("💾 Credentials saved securely")
            
        except Exception as e:
            self.logger.error(f"❌ Error saving credentials: {e}")
    
    def load_credentials(self) -> Optional[Dict]:
        """📂 Load encrypted credentials"""
        try:
            encrypted = keyring.get_password("vip_big_bang", "quotex_credentials")
            if encrypted:
                credentials = self.decrypt_credentials(encrypted)
                self.logger.info("📂 Credentials loaded successfully")
                return credentials
            return None
        except Exception as e:
            self.logger.error(f"❌ Error loading credentials: {e}")
            return None
    
    async def human_like_typing(self, selector: str, text: str, clear_first: bool = True):
        """⌨️ Human-like typing simulation"""
        try:
            element = await self.page.query_selector(selector)
            if not element:
                return False
            
            await element.click()
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            if clear_first:
                await element.fill('')
                await asyncio.sleep(random.uniform(0.1, 0.2))
            
            # Type with human-like delays
            for char in text:
                await element.type(char, delay=random.randint(50, 200))
                
            await asyncio.sleep(random.uniform(0.2, 0.5))
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error in human typing: {e}")
            return False
    
    async def find_login_elements(self) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """🔍 Find login form elements"""
        try:
            # Email/Username selectors
            email_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="email"]',
                'input[data-test="email"]',
                '.email-input',
                'input[name="username"]',
                'input[placeholder*="username"]'
            ]
            
            # Password selectors
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="password"]',
                'input[data-test="password"]',
                '.password-input'
            ]
            
            # Login button selectors
            login_button_selectors = [
                'button[type="submit"]',
                'button[data-test="login"]',
                '.login-button',
                'button[class*="login"]',
                'input[type="submit"]',
                'button:has-text("Login")',
                'button:has-text("Sign in")',
                'button:has-text("Log in")'
            ]
            
            email_selector = None
            password_selector = None
            login_button_selector = None
            
            # Find working selectors
            for selector in email_selectors:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    email_selector = selector
                    break
            
            for selector in password_selectors:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    password_selector = selector
                    break
            
            for selector in login_button_selectors:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    login_button_selector = selector
                    break
            
            self.logger.info(f"🔍 Login elements found: Email={bool(email_selector)}, Password={bool(password_selector)}, Button={bool(login_button_selector)}")
            
            return email_selector, password_selector, login_button_selector
            
        except Exception as e:
            self.logger.error(f"❌ Error finding login elements: {e}")
            return None, None, None
    
    async def login_with_email(self, email: str, password: str, save_credentials: bool = True) -> bool:
        """📧 Login with email and password"""
        try:
            self.logger.info(f"📧 Attempting login with email: {email}")
            
            # Find login elements
            email_selector, password_selector, login_button_selector = await self.find_login_elements()
            
            if not all([email_selector, password_selector, login_button_selector]):
                self.logger.error("❌ Login form elements not found")
                return False
            
            # Fill email
            if not await self.human_like_typing(email_selector, email):
                self.logger.error("❌ Failed to enter email")
                return False
            
            await asyncio.sleep(random.uniform(0.5, 1.0))
            
            # Fill password
            if not await self.human_like_typing(password_selector, password):
                self.logger.error("❌ Failed to enter password")
                return False
            
            await asyncio.sleep(random.uniform(0.5, 1.5))
            
            # Click login button
            await self.page.click(login_button_selector)
            await asyncio.sleep(random.uniform(1.0, 2.0))
            
            # Wait for login to complete
            await self.page.wait_for_load_state('networkidle', timeout=10000)
            
            # Check if login was successful
            if await self.check_login_success():
                self.is_logged_in = True
                self.login_method = "email"
                
                if save_credentials:
                    self.save_credentials(email, password, "email")
                
                self.logger.info("✅ Email login successful")
                return True
            else:
                self.logger.error("❌ Email login failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error in email login: {e}")
            return False
    
    async def login_with_google(self) -> bool:
        """🔍 Login with Google OAuth"""
        try:
            self.logger.info("🔍 Attempting Google login...")
            
            # Find Google login button
            google_selectors = [
                'button:has-text("Google")',
                '.google-login',
                'button[class*="google"]',
                '[data-provider="google"]',
                'button:has-text("Continue with Google")'
            ]
            
            google_button = None
            for selector in google_selectors:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    google_button = selector
                    break
            
            if not google_button:
                self.logger.error("❌ Google login button not found")
                return False
            
            # Click Google login
            await self.page.click(google_button)
            await asyncio.sleep(2)
            
            # Handle Google OAuth popup
            # Note: This requires handling popup windows
            self.logger.info("🔍 Google OAuth popup should appear...")
            
            # Wait for login completion
            await asyncio.sleep(5)
            
            if await self.check_login_success():
                self.is_logged_in = True
                self.login_method = "google"
                self.logger.info("✅ Google login successful")
                return True
            else:
                self.logger.error("❌ Google login failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error in Google login: {e}")
            return False
    
    async def check_login_success(self) -> bool:
        """✅ Check if login was successful"""
        try:
            # Wait a bit for page to load
            await asyncio.sleep(2)
            
            # Check for login success indicators
            success_indicators = [
                '.user-menu',
                '.account-info',
                '.balance',
                '.trading-interface',
                '.user-profile',
                '[data-test="user-menu"]'
            ]
            
            # Check for login failure indicators
            failure_indicators = [
                '.error-message',
                '.login-error',
                'text="Invalid credentials"',
                'text="Login failed"',
                '.alert-danger'
            ]
            
            # Check for success
            for selector in success_indicators:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    self.logger.info(f"✅ Login success indicator found: {selector}")
                    return True
            
            # Check for failure
            for selector in failure_indicators:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    error_text = await element.inner_text()
                    self.logger.error(f"❌ Login error: {error_text}")
                    return False
            
            # Check URL change
            current_url = self.page.url
            if 'login' not in current_url.lower() and 'trade' in current_url.lower():
                self.logger.info("✅ URL indicates successful login")
                return True
            
            # Check page title
            title = await self.page.title()
            if 'login' not in title.lower() and ('trade' in title.lower() or 'quotex' in title.lower()):
                self.logger.info("✅ Page title indicates successful login")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error checking login success: {e}")
            return False
    
    async def auto_login(self) -> bool:
        """🤖 Automatic login using saved credentials"""
        try:
            self.logger.info("🤖 Attempting automatic login...")
            
            # Load saved credentials
            credentials = self.load_credentials()
            if not credentials:
                self.logger.warning("⚠️ No saved credentials found")
                return False
            
            method = credentials.get('method', 'email')
            
            if method == 'email':
                email = credentials.get('email')
                password = credentials.get('password')
                
                if email and password:
                    return await self.login_with_email(email, password, save_credentials=False)
                else:
                    self.logger.error("❌ Invalid saved credentials")
                    return False
            
            elif method == 'google':
                return await self.login_with_google()
            
            else:
                self.logger.error(f"❌ Unknown login method: {method}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error in auto login: {e}")
            return False
    
    async def logout(self) -> bool:
        """🚪 Logout from Quotex"""
        try:
            self.logger.info("🚪 Attempting logout...")
            
            logout_selectors = [
                '.logout',
                '.sign-out',
                'button:has-text("Logout")',
                'button:has-text("Sign out")',
                '[data-test="logout"]'
            ]
            
            for selector in logout_selectors:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    await element.click()
                    await asyncio.sleep(2)
                    
                    self.is_logged_in = False
                    self.login_method = None
                    self.user_data = {}
                    
                    self.logger.info("✅ Logout successful")
                    return True
            
            self.logger.warning("⚠️ Logout button not found")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Error in logout: {e}")
            return False
    
    def get_login_status(self) -> Dict:
        """📊 Get current login status"""
        return {
            'is_logged_in': self.is_logged_in,
            'login_method': self.login_method,
            'user_data': self.user_data
        }


# Example usage
async def test_login():
    """Test the login system"""
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # Navigate to Quotex
        await page.goto("https://qxbroker.com/en/sign-in")
        
        # Initialize login manager
        login_manager = QuotexLoginManager(page)
        
        # Test auto login
        success = await login_manager.auto_login()
        print(f"Auto login result: {success}")
        
        # Get status
        status = login_manager.get_login_status()
        print(f"Login status: {json.dumps(status, indent=2)}")
        
        await asyncio.sleep(5)
        await browser.close()


# Alias for compatibility
QuotexLogin = QuotexLoginManager

if __name__ == "__main__":
    asyncio.run(test_login())
