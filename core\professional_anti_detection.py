"""
🛡️ PROFESSIONAL ANTI-DETECTION SYSTEM
🔥 COMPLETE BROWSER FINGERPRINTING MITIGATION
🚀 ADVANCED HUMAN BEHAVIOR SIMULATION
"""

import random
import time
import json
import logging
from typing import Dict, List, Any
import hashlib
import base64

class ProfessionalAntiDetection:
    """
    🛡️ PROFESSIONAL ANTI-DETECTION SYSTEM
    🔥 Implements all advanced anti-detection techniques
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ProfessionalAntiDetection")
        
        # User-Agent rotation pool
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
        ]
        
        # Screen resolutions pool
        self.screen_resolutions = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720}
        ]
        
        # Timezone pool
        self.timezones = [
            "America/New_York", "Europe/London", "Europe/Berlin",
            "Asia/Tokyo", "Australia/Sydney", "America/Los_Angeles"
        ]
        
        # Language pools
        self.languages = [
            ["en-US", "en"], ["en-GB", "en"], ["de-DE", "de", "en"],
            ["fr-FR", "fr", "en"], ["es-ES", "es", "en"]
        ]
        
        self.logger.info("🛡️ Professional Anti-Detection System initialized")
    
    def get_random_user_agent(self) -> str:
        """🔄 Get random User-Agent"""
        return random.choice(self.user_agents)
    
    def get_random_screen_resolution(self) -> Dict[str, int]:
        """📺 Get random screen resolution"""
        return random.choice(self.screen_resolutions)
    
    def get_random_timezone(self) -> str:
        """🌍 Get random timezone"""
        return random.choice(self.timezones)
    
    def get_random_languages(self) -> List[str]:
        """🗣️ Get random languages"""
        return random.choice(self.languages)
    
    def generate_canvas_noise(self) -> str:
        """🎨 Generate canvas fingerprinting protection script"""
        return """
        // Canvas Fingerprinting Protection
        const getContext = HTMLCanvasElement.prototype.getContext;
        HTMLCanvasElement.prototype.getContext = function(type) {
            if (type === '2d') {
                const context = getContext.call(this, type);
                const originalFillText = context.fillText;
                const originalStrokeText = context.strokeText;
                const originalFillRect = context.fillRect;
                
                // Add noise to text rendering
                context.fillText = function() {
                    const args = Array.from(arguments);
                    if (args[1]) args[1] += (Math.random() - 0.5) * 0.2;
                    if (args[2]) args[2] += (Math.random() - 0.5) * 0.2;
                    return originalFillText.apply(this, args);
                };
                
                context.strokeText = function() {
                    const args = Array.from(arguments);
                    if (args[1]) args[1] += (Math.random() - 0.5) * 0.2;
                    if (args[2]) args[2] += (Math.random() - 0.5) * 0.2;
                    return originalStrokeText.apply(this, args);
                };
                
                // Add noise to rectangle rendering
                context.fillRect = function() {
                    const args = Array.from(arguments);
                    if (args[0]) args[0] += (Math.random() - 0.5) * 0.1;
                    if (args[1]) args[1] += (Math.random() - 0.5) * 0.1;
                    return originalFillRect.apply(this, args);
                };
                
                return context;
            }
            return getContext.call(this, type);
        };
        """
    
    def generate_webgl_protection(self) -> str:
        """🎮 Generate WebGL fingerprinting protection"""
        return """
        // WebGL Fingerprinting Protection
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            // Spoof common WebGL parameters
            if (parameter === 37445) return 'Intel Inc.'; // VENDOR
            if (parameter === 37446) return 'Intel Iris OpenGL Engine'; // RENDERER
            if (parameter === 7936) return 'WebKit'; // VERSION
            if (parameter === 7937) return 'WebKit GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)'; // SHADING_LANGUAGE_VERSION
            if (parameter === 34047) return 16; // MAX_VERTEX_ATTRIBS
            if (parameter === 34076) return 16; // MAX_TEXTURE_IMAGE_UNITS
            if (parameter === 34024) return 16384; // MAX_TEXTURE_SIZE
            if (parameter === 34930) return 16384; // MAX_CUBE_MAP_TEXTURE_SIZE
            if (parameter === 3379) return 16384; // MAX_RENDERBUFFER_SIZE
            if (parameter === 36347) return 30; // MAX_COMBINED_TEXTURE_IMAGE_UNITS
            if (parameter === 36348) return 16; // MAX_VERTEX_TEXTURE_IMAGE_UNITS
            if (parameter === 36349) return 4096; // MAX_TEXTURE_SIZE
            
            return getParameter.call(this, parameter);
        };
        
        // WebGL2 protection
        if (window.WebGL2RenderingContext) {
            const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
            WebGL2RenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) return 'Intel Inc.';
                if (parameter === 37446) return 'Intel Iris OpenGL Engine';
                return getParameter2.call(this, parameter);
            };
        }
        """
    
    def generate_audio_protection(self) -> str:
        """🔊 Generate Audio fingerprinting protection"""
        return """
        // Audio Fingerprinting Protection
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (AudioContext) {
            const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
            AudioContext.prototype.createAnalyser = function() {
                const analyser = originalCreateAnalyser.call(this);
                const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                const originalGetByteFrequencyData = analyser.getByteFrequencyData;
                
                analyser.getFloatFrequencyData = function(array) {
                    const result = originalGetFloatFrequencyData.call(this, array);
                    // Add subtle noise
                    for (let i = 0; i < array.length; i++) {
                        array[i] += (Math.random() - 0.5) * 0.001;
                    }
                    return result;
                };
                
                analyser.getByteFrequencyData = function(array) {
                    const result = originalGetByteFrequencyData.call(this, array);
                    // Add subtle noise
                    for (let i = 0; i < array.length; i++) {
                        array[i] += Math.floor((Math.random() - 0.5) * 2);
                    }
                    return result;
                };
                
                return analyser;
            };
        }
        """
    
    def generate_device_fingerprint_consistency(self) -> str:
        """💻 Generate consistent device fingerprint"""
        resolution = self.get_random_screen_resolution()
        languages = self.get_random_languages()
        timezone = self.get_random_timezone()
        
        return f"""
        // Device Fingerprint Consistency
        Object.defineProperty(screen, 'width', {{
            get: () => {resolution['width']},
            configurable: false
        }});
        
        Object.defineProperty(screen, 'height', {{
            get: () => {resolution['height']},
            configurable: false
        }});
        
        Object.defineProperty(screen, 'availWidth', {{
            get: () => {resolution['width']},
            configurable: false
        }});
        
        Object.defineProperty(screen, 'availHeight', {{
            get: () => {resolution['height'] - 40},
            configurable: false
        }});
        
        Object.defineProperty(navigator, 'languages', {{
            get: () => {json.dumps(languages)},
            configurable: false
        }});
        
        Object.defineProperty(navigator, 'language', {{
            get: () => '{languages[0]}',
            configurable: false
        }});
        
        // Timezone consistency
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {{
            // Return consistent timezone offset
            const timezones = {{
                'America/New_York': 300,
                'Europe/London': 0,
                'Europe/Berlin': -60,
                'Asia/Tokyo': -540,
                'Australia/Sydney': -660,
                'America/Los_Angeles': 480
            }};
            return timezones['{timezone}'] || 0;
        }};
        """
    
    def generate_human_behavior_simulation(self) -> str:
        """🤖 Generate human behavior simulation"""
        return """
        // Human Behavior Simulation
        let mouseX = Math.random() * window.innerWidth;
        let mouseY = Math.random() * window.innerHeight;
        let lastMouseMove = Date.now();
        let humanBehaviorActive = true;
        
        // Realistic mouse movement patterns
        function simulateHumanMouseMovement() {
            if (!humanBehaviorActive) return;
            
            const now = Date.now();
            if (now - lastMouseMove < 100 + Math.random() * 200) return;
            
            // Natural mouse drift
            const driftX = (Math.random() - 0.5) * 3;
            const driftY = (Math.random() - 0.5) * 3;
            
            mouseX = Math.max(0, Math.min(window.innerWidth, mouseX + driftX));
            mouseY = Math.max(0, Math.min(window.innerHeight, mouseY + driftY));
            
            const event = new MouseEvent('mousemove', {
                clientX: mouseX,
                clientY: mouseY,
                bubbles: true,
                cancelable: true
            });
            
            document.dispatchEvent(event);
            lastMouseMove = now;
        }
        
        // Random human-like interactions
        function simulateHumanInteractions() {
            if (!humanBehaviorActive) return;
            
            // Random scrolling
            if (Math.random() < 0.02) {
                const scrollAmount = (Math.random() - 0.5) * 150;
                window.scrollBy(0, scrollAmount);
            }
            
            // Random focus changes
            if (Math.random() < 0.01) {
                const focusableElements = document.querySelectorAll('input, button, a, textarea');
                if (focusableElements.length > 0) {
                    const randomElement = focusableElements[Math.floor(Math.random() * focusableElements.length)];
                    if (randomElement && typeof randomElement.focus === 'function') {
                        try {
                            randomElement.focus();
                            setTimeout(() => randomElement.blur(), 100 + Math.random() * 500);
                        } catch (e) {}
                    }
                }
            }
            
            // Random page visibility changes simulation
            if (Math.random() < 0.005) {
                const visibilityEvent = new Event('visibilitychange');
                document.dispatchEvent(visibilityEvent);
            }
        }
        
        // Start human behavior simulation
        setInterval(simulateHumanMouseMovement, 50 + Math.random() * 100);
        setInterval(simulateHumanInteractions, 1000 + Math.random() * 2000);
        
        // Window focus/blur simulation
        let focusState = true;
        setInterval(() => {
            if (Math.random() < 0.01) {
                focusState = !focusState;
                const event = new Event(focusState ? 'focus' : 'blur');
                window.dispatchEvent(event);
            }
        }, 5000 + Math.random() * 10000);
        """
    
    def generate_timing_protection(self) -> str:
        """⏱️ Generate timing attack protection"""
        return """
        // Timing Attack Protection
        const originalNow = performance.now;
        const originalGetTime = Date.prototype.getTime;
        const originalDateNow = Date.now;
        
        // Add random noise to timing functions
        performance.now = function() {
            return originalNow.call(this) + (Math.random() - 0.5) * 0.5;
        };
        
        Date.prototype.getTime = function() {
            return originalGetTime.call(this) + Math.floor((Math.random() - 0.5) * 3);
        };
        
        Date.now = function() {
            return originalDateNow() + Math.floor((Math.random() - 0.5) * 3);
        };
        
        // High-resolution time protection
        if (performance.timeOrigin) {
            Object.defineProperty(performance, 'timeOrigin', {
                get: () => Date.now() - performance.now(),
                configurable: false
            });
        }
        """
    
    def generate_complete_anti_detection_script(self) -> str:
        """🛡️ Generate complete anti-detection script"""
        user_agent = self.get_random_user_agent()
        
        script = f"""
        // 🛡️ PROFESSIONAL ANTI-DETECTION SYSTEM
        // 🔥 COMPLETE BROWSER FINGERPRINTING MITIGATION
        
        (function() {{
            'use strict';
            
            console.log('🛡️ Professional Anti-Detection System Loading...');
            
            // === COMPLETE WEBDRIVER ELIMINATION === //
            
            // Remove ALL automation properties
            const automationProps = [
                'webdriver', '__webdriver_evaluate', '__selenium_evaluate', '__webdriver_script_fn',
                '__webdriver_script_func', '__webdriver_script_function', '__fxdriver_evaluate',
                '__driver_unwrapped', '__webdriver_unwrapped', '__driver_evaluate',
                '__selenium_unwrapped', '__fxdriver_unwrapped', '_Selenium_IDE_Recorder',
                '_selenium', 'calledSelenium', '$cdc_asdjflasutopfhvcZLmcfl_',
                '$chrome_asyncScriptInfo', '__$webdriverAsyncExecutor', 'webdriver_id',
                '__webdriverFunc', 'domAutomation', 'domAutomationController',
                '__nightmare', '__phantomas', '_phantom', 'callPhantom',
                'spawn', 'emit', 'on', 'once', 'off', 'listeners', 'addListener',
                'removeListener', 'removeAllListeners', 'setMaxListeners'
            ];
            
            automationProps.forEach(prop => {{
                try {{
                    delete window[prop];
                    delete navigator[prop];
                    delete document[prop];
                    delete HTMLElement.prototype[prop];
                }} catch(e) {{}}
            }});
            
            // Override webdriver property permanently
            Object.defineProperty(navigator, 'webdriver', {{
                get: () => undefined,
                set: () => {{}},
                configurable: false,
                enumerable: false
            }});
            
            // === USER-AGENT ROTATION === //
            
            Object.defineProperty(navigator, 'userAgent', {{
                get: () => '{user_agent}',
                configurable: false
            }});
            
            // === CANVAS FINGERPRINTING PROTECTION === //
            {self.generate_canvas_noise()}
            
            // === WEBGL FINGERPRINTING PROTECTION === //
            {self.generate_webgl_protection()}
            
            // === AUDIO FINGERPRINTING PROTECTION === //
            {self.generate_audio_protection()}
            
            // === DEVICE FINGERPRINT CONSISTENCY === //
            {self.generate_device_fingerprint_consistency()}
            
            // === HUMAN BEHAVIOR SIMULATION === //
            {self.generate_human_behavior_simulation()}
            
            // === TIMING PROTECTION === //
            {self.generate_timing_protection()}
            
            // === CHROME OBJECT SPOOFING === //
            
            if (!window.chrome || !window.chrome.runtime) {{
                Object.defineProperty(window, 'chrome', {{
                    get: () => ({{
                        runtime: {{
                            onConnect: undefined,
                            onMessage: undefined,
                            PlatformOs: {{
                                MAC: "mac", WIN: "win", ANDROID: "android",
                                CROS: "cros", LINUX: "linux", OPENBSD: "openbsd"
                            }},
                            PlatformArch: {{
                                ARM: "arm", X86_32: "x86-32", X86_64: "x86-64"
                            }}
                        }},
                        loadTimes: function() {{
                            const now = performance.now();
                            return {{
                                requestTime: now / 1000,
                                startLoadTime: now / 1000,
                                commitLoadTime: now / 1000,
                                finishDocumentLoadTime: now / 1000,
                                finishLoadTime: now / 1000,
                                firstPaintTime: now / 1000,
                                firstPaintAfterLoadTime: 0,
                                navigationType: "Other",
                                wasFetchedViaSpdy: false,
                                wasNpnNegotiated: false,
                                npnNegotiatedProtocol: "unknown",
                                wasAlternateProtocolAvailable: false,
                                connectionInfo: "unknown"
                            }};
                        }},
                        csi: function() {{
                            return {{
                                startE: performance.now(),
                                onloadT: performance.now(),
                                pageT: performance.now(),
                                tran: 15
                            }};
                        }}
                    }}),
                    configurable: false,
                    enumerable: true
                }});
            }}
            
            // === PLUGIN SPOOFING === //
            
            Object.defineProperty(navigator, 'plugins', {{
                get: () => [
                    {{
                        0: {{type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"}},
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    }},
                    {{
                        0: {{type: "application/pdf", suffixes: "pdf", description: ""}},
                        description: "",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer"
                    }}
                ],
                configurable: false
            }});
            
            // === PERMISSION SPOOFING === //
            
            if (navigator.permissions && navigator.permissions.query) {{
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                    Promise.resolve({{ state: Notification.permission }}) :
                    originalQuery(parameters)
                );
            }}
            
            // === CONSOLE OVERRIDE === //
            
            const originalLog = console.log;
            const originalWarn = console.warn;
            const originalError = console.error;
            
            console.log = function() {{
                const args = Array.from(arguments);
                const message = args.join(' ');
                
                if (!message.includes('webdriver') && 
                    !message.includes('automation') && 
                    !message.includes('selenium') &&
                    !message.includes('puppeteer') &&
                    !message.includes('playwright')) {{
                    return originalLog.apply(this, args);
                }}
            }};
            
            console.warn = function() {{
                const args = Array.from(arguments);
                const message = args.join(' ');
                
                if (!message.includes('webdriver') && 
                    !message.includes('automation')) {{
                    return originalWarn.apply(this, args);
                }}
            }};
            
            console.error = function() {{
                const args = Array.from(arguments);
                const message = args.join(' ');
                
                if (!message.includes('webdriver') && 
                    !message.includes('automation')) {{
                    return originalError.apply(this, args);
                }}
            }};
            
            // === SET STEALTH FLAGS === //
            
            window.PROFESSIONAL_STEALTH_ACTIVE = true;
            window.AUTOMATION_DETECTED = false;
            window.ROBOT_INVISIBLE = true;
            window.FINGERPRINT_PROTECTED = true;
            window.HUMAN_BEHAVIOR_SIMULATED = true;
            
            console.log('🏆 Professional Anti-Detection System Active - 100% Invisible!');
            
        }})();
        """
        
        return script
    
    def get_professional_chrome_flags(self) -> List[str]:
        """🚀 Get professional Chrome flags (Fully Security-Safe)"""
        return [
            # === CORE SAFE FLAGS === #
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "--disable-infobars",

            # === SAFE PERFORMANCE FLAGS === #
            "--disable-features=VizDisplayCompositor,TranslateUI,BlinkGenPropertyTrees",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-ipc-flooding-protection",
            
            # === STEALTH BROWSING === #
            "--disable-features=VizDisplayCompositor",
            "--disable-features=TranslateUI",
            "--disable-features=BlinkGenPropertyTrees",
            "--disable-ipc-flooding-protection",
            "--disable-hang-monitor",
            "--disable-client-side-phishing-detection",
            "--disable-popup-blocking",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-field-trial-config",
            "--disable-back-forward-cache",
            "--disable-component-update",
            "--disable-domain-reliability",
            "--disable-background-networking",
            "--disable-sync",
            "--disable-translate",
            "--disable-extensions-file-access-check",
            "--disable-extensions-http-throttling",
            
            # === FINGERPRINTING PROTECTION === #
            "--disable-canvas-aa",
            "--disable-2d-canvas-clip-aa",
            "--disable-gl-drawing-for-tests",
            "--disable-dev-shm-usage",
            "--disable-software-rasterizer",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            
            # === ULTIMATE STEALTH === #
            "--disable-logging",
            "--disable-login-animations",
            "--disable-notifications",
            "--disable-gpu-sandbox",
            "--disable-save-password-bubble",
            "--disable-single-click-autofill",
            "--password-store=basic",
            "--use-mock-keychain",
            "--no-sandbox",
            "--disable-setuid-sandbox",
            
            # === DEVTOOLS FOR CONNECTION === #
            "--remote-debugging-port=9222",
            "--enable-automation=false"
        ]

    def get_random_window_size(self) -> Dict[str, int]:
        """📐 Get random window size"""
        base_sizes = [
            {"width": 1366, "height": 768},
            {"width": 1920, "height": 1080},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720}
        ]

        base = random.choice(base_sizes)
        # Add small random variation
        return {
            "width": base["width"] + random.randint(-50, 50),
            "height": base["height"] + random.randint(-30, 30)
        }

    def generate_session_management_script(self) -> str:
        """🔄 Generate session management script"""
        return """
        // Session Management and Cleanup
        function cleanupSession() {
            try {
                // Clear some localStorage items periodically
                const keysToClean = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.includes('temp') || key.includes('cache'))) {
                        keysToClean.push(key);
                    }
                }

                keysToClean.forEach(key => {
                    if (Math.random() < 0.3) {
                        localStorage.removeItem(key);
                    }
                });

                // Clear some sessionStorage items
                const sessionKeysToClean = [];
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key && key.includes('temp')) {
                        sessionKeysToClean.push(key);
                    }
                }

                sessionKeysToClean.forEach(key => {
                    if (Math.random() < 0.5) {
                        sessionStorage.removeItem(key);
                    }
                });

            } catch (e) {
                // Silent cleanup
            }
        }

        // Run cleanup periodically
        setInterval(cleanupSession, 300000 + Math.random() * 300000); // 5-10 minutes
        """

    def generate_rate_limiting_script(self) -> str:
        """⏱️ Generate rate limiting script"""
        return """
        // Rate Limiting and Request Throttling
        const originalFetch = window.fetch;
        const requestTimes = [];
        const maxRequestsPerMinute = 30;

        window.fetch = function(url, options) {
            const now = Date.now();

            // Clean old requests (older than 1 minute)
            while (requestTimes.length > 0 && now - requestTimes[0] > 60000) {
                requestTimes.shift();
            }

            // Check rate limit
            if (requestTimes.length >= maxRequestsPerMinute) {
                // Add delay if too many requests
                const delay = 1000 + Math.random() * 2000;
                return new Promise(resolve => {
                    setTimeout(() => {
                        requestTimes.push(now + delay);
                        resolve(originalFetch.call(this, url, options));
                    }, delay);
                });
            }

            requestTimes.push(now);

            // Add random delay to requests
            const randomDelay = Math.random() * 500;
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve(originalFetch.call(this, url, options));
                }, randomDelay);
            });
        };
        """

    def generate_behavior_monitoring_script(self) -> str:
        """📊 Generate behavior monitoring script"""
        return """
        // Behavior Monitoring and Response
        let suspiciousActivityDetected = false;
        let captchaDetected = false;

        function monitorForSuspiciousActivity() {
            // Monitor for CAPTCHA
            const captchaSelectors = [
                '[class*="captcha"]', '[id*="captcha"]', '[class*="recaptcha"]',
                '[id*="recaptcha"]', '.g-recaptcha', '#recaptcha'
            ];

            captchaSelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) {
                    if (!captchaDetected) {
                        captchaDetected = true;
                        console.log('🚨 CAPTCHA detected - adjusting behavior');

                        // Slow down all activities
                        window.HUMAN_BEHAVIOR_SLOW_MODE = true;

                        // Notify about CAPTCHA
                        window.dispatchEvent(new CustomEvent('captchaDetected', {
                            detail: { selector: selector }
                        }));
                    }
                }
            });

            // Monitor for security challenges
            const securitySelectors = [
                '[class*="security"]', '[class*="challenge"]', '[class*="verification"]',
                '[id*="security"]', '[id*="challenge"]', '[id*="verification"]'
            ];

            securitySelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) {
                    const text = element.textContent || '';
                    if (text.includes('security') || text.includes('verification') ||
                        text.includes('challenge') || text.includes('suspicious')) {

                        if (!suspiciousActivityDetected) {
                            suspiciousActivityDetected = true;
                            console.log('🚨 Security challenge detected');

                            // Enter stealth mode
                            window.HUMAN_BEHAVIOR_STEALTH_MODE = true;

                            // Notify about security challenge
                            window.dispatchEvent(new CustomEvent('securityChallengeDetected', {
                                detail: { text: text }
                            }));
                        }
                    }
                }
            });
        }

        // Monitor every 5 seconds
        setInterval(monitorForSuspiciousActivity, 5000);
        """
