#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 VIP BIG BANG - Advanced Learning System
سیستم یادگیری پیشرفته و هوش مصنوعی
"""

import sys
import json
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Fix encoding
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

class VIPLearningSystem:
    """
    🧠 سیستم یادگیری پیشرفته VIP BIG BANG
    یادگیری از تجربیات و بهبود مداوم عملکرد
    """
    
    def __init__(self):
        self.logger = logging.getLogger("VIPLearning")
        
        # پایگاه داده یادگیری
        self.learning_database = {
            'trades': [],
            'patterns': {},
            'performance_metrics': {},
            'market_conditions': {},
            'strategy_effectiveness': {}
        }
        
        # تنظیمات یادگیری
        self.learning_config = {
            'min_trades_for_learning': 10,
            'pattern_recognition_threshold': 0.75,
            'adaptation_speed': 0.1,
            'memory_retention_days': 30,
            'confidence_threshold': 0.85
        }
        
        # متریک‌های عملکرد
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'average_profit': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'profit_factor': 0.0
        }
        
        # الگوهای شناسایی شده
        self.recognized_patterns = {}
        
        # بارگذاری داده‌های قبلی
        self.load_learning_data()
        
        print("🧠 VIP Learning System initialized")
    
    def record_trade(self, trade_data):
        """ثبت معامله برای یادگیری"""
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'symbol': trade_data.get('symbol', 'EUR/USD'),
            'direction': trade_data.get('direction'),
            'entry_price': trade_data.get('entry_price'),
            'exit_price': trade_data.get('exit_price'),
            'duration': trade_data.get('duration'),
            'profit_loss': trade_data.get('profit_loss'),
            'win': trade_data.get('profit_loss', 0) > 0,
            'market_conditions': trade_data.get('market_conditions', {}),
            'indicators_used': trade_data.get('indicators_used', {}),
            'signal_strength': trade_data.get('signal_strength', 0),
            'confidence': trade_data.get('confidence', 0)
        }
        
        self.learning_database['trades'].append(trade_record)
        self.update_performance_metrics()
        
        # یادگیری از معامله
        self.learn_from_trade(trade_record)
        
        print(f"📊 Trade recorded: {trade_record['direction']} - {'WIN' if trade_record['win'] else 'LOSS'}")
    
    def learn_from_trade(self, trade_record):
        """یادگیری از معامله انجام شده"""
        
        # 1. یادگیری الگوهای موفق
        if trade_record['win']:
            self.learn_successful_pattern(trade_record)
        else:
            self.learn_failed_pattern(trade_record)
        
        # 2. تحلیل شرایط بازار
        self.analyze_market_conditions(trade_record)
        
        # 3. ارزیابی اثربخشی اندیکاتورها
        self.evaluate_indicator_effectiveness(trade_record)
        
        # 4. تنظیم پارامترها
        if len(self.learning_database['trades']) >= self.learning_config['min_trades_for_learning']:
            self.adapt_parameters()
    
    def learn_successful_pattern(self, trade_record):
        """یادگیری الگوهای موفق"""
        pattern_key = self.create_pattern_key(trade_record)
        
        if pattern_key not in self.recognized_patterns:
            self.recognized_patterns[pattern_key] = {
                'success_count': 0,
                'total_count': 0,
                'average_profit': 0,
                'confidence': 0,
                'conditions': trade_record['market_conditions'],
                'indicators': trade_record['indicators_used']
            }
        
        pattern = self.recognized_patterns[pattern_key]
        pattern['success_count'] += 1
        pattern['total_count'] += 1
        pattern['average_profit'] = (pattern['average_profit'] + trade_record['profit_loss']) / 2
        pattern['confidence'] = pattern['success_count'] / pattern['total_count']
        
        print(f"✅ Successful pattern learned: {pattern_key[:20]}... (Confidence: {pattern['confidence']:.2%})")
    
    def learn_failed_pattern(self, trade_record):
        """یادگیری از الگوهای ناموفق"""
        pattern_key = self.create_pattern_key(trade_record)
        
        if pattern_key not in self.recognized_patterns:
            self.recognized_patterns[pattern_key] = {
                'success_count': 0,
                'total_count': 0,
                'average_profit': 0,
                'confidence': 0,
                'conditions': trade_record['market_conditions'],
                'indicators': trade_record['indicators_used']
            }
        
        pattern = self.recognized_patterns[pattern_key]
        pattern['total_count'] += 1
        pattern['average_profit'] = (pattern['average_profit'] + trade_record['profit_loss']) / 2
        pattern['confidence'] = pattern['success_count'] / pattern['total_count']
        
        print(f"❌ Failed pattern recorded: {pattern_key[:20]}... (Confidence: {pattern['confidence']:.2%})")
    
    def create_pattern_key(self, trade_record):
        """ایجاد کلید الگو"""
        # ترکیب شرایط مهم برای ایجاد الگو
        key_elements = [
            trade_record['direction'],
            str(round(trade_record['signal_strength'], 1)),
            str(len(trade_record['indicators_used'])),
            trade_record['market_conditions'].get('trend', 'neutral'),
            trade_record['market_conditions'].get('volatility', 'medium')
        ]
        
        return "_".join(key_elements)
    
    def analyze_market_conditions(self, trade_record):
        """تحلیل شرایط بازار"""
        conditions = trade_record['market_conditions']
        
        for condition, value in conditions.items():
            if condition not in self.learning_database['market_conditions']:
                self.learning_database['market_conditions'][condition] = {
                    'values': [],
                    'success_rate': 0,
                    'total_trades': 0,
                    'winning_trades': 0
                }
            
            condition_data = self.learning_database['market_conditions'][condition]
            condition_data['values'].append(value)
            condition_data['total_trades'] += 1
            
            if trade_record['win']:
                condition_data['winning_trades'] += 1
            
            condition_data['success_rate'] = condition_data['winning_trades'] / condition_data['total_trades']
    
    def evaluate_indicator_effectiveness(self, trade_record):
        """ارزیابی اثربخشی اندیکاتورها"""
        for indicator, value in trade_record['indicators_used'].items():
            if indicator not in self.learning_database['strategy_effectiveness']:
                self.learning_database['strategy_effectiveness'][indicator] = {
                    'total_uses': 0,
                    'successful_uses': 0,
                    'effectiveness': 0,
                    'average_signal_strength': 0
                }
            
            indicator_data = self.learning_database['strategy_effectiveness'][indicator]
            indicator_data['total_uses'] += 1
            
            if trade_record['win']:
                indicator_data['successful_uses'] += 1
            
            indicator_data['effectiveness'] = indicator_data['successful_uses'] / indicator_data['total_uses']
            indicator_data['average_signal_strength'] = (
                indicator_data['average_signal_strength'] + value.get('strength', 0)
            ) / 2
    
    def adapt_parameters(self):
        """تنظیم خودکار پارامترها بر اساس یادگیری"""
        print("🔄 Adapting parameters based on learning...")
        
        # تحلیل عملکرد کلی
        recent_trades = self.get_recent_trades(days=7)
        if len(recent_trades) < 5:
            return
        
        recent_win_rate = sum(1 for t in recent_trades if t['win']) / len(recent_trades)
        
        # تنظیم آستانه اعتماد
        if recent_win_rate > 0.8:
            # عملکرد خوب - کاهش آستانه برای معاملات بیشتر
            self.learning_config['confidence_threshold'] *= 0.95
            print(f"📈 High performance detected. Lowering confidence threshold to {self.learning_config['confidence_threshold']:.2f}")
        elif recent_win_rate < 0.6:
            # عملکرد ضعیف - افزایش آستانه برای احتیاط بیشتر
            self.learning_config['confidence_threshold'] *= 1.05
            print(f"📉 Low performance detected. Raising confidence threshold to {self.learning_config['confidence_threshold']:.2f}")
        
        # محدود کردن آستانه
        self.learning_config['confidence_threshold'] = max(0.7, min(0.95, self.learning_config['confidence_threshold']))
    
    def get_recent_trades(self, days=7):
        """دریافت معاملات اخیر"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_trades = []
        
        for trade in self.learning_database['trades']:
            trade_date = datetime.fromisoformat(trade['timestamp'])
            if trade_date >= cutoff_date:
                recent_trades.append(trade)
        
        return recent_trades
    
    def update_performance_metrics(self):
        """به‌روزرسانی متریک‌های عملکرد"""
        trades = self.learning_database['trades']
        
        if not trades:
            return
        
        self.performance_metrics['total_trades'] = len(trades)
        self.performance_metrics['winning_trades'] = sum(1 for t in trades if t['win'])
        self.performance_metrics['losing_trades'] = self.performance_metrics['total_trades'] - self.performance_metrics['winning_trades']
        
        if self.performance_metrics['total_trades'] > 0:
            self.performance_metrics['win_rate'] = self.performance_metrics['winning_trades'] / self.performance_metrics['total_trades']
        
        profits = [t['profit_loss'] for t in trades]
        if profits:
            self.performance_metrics['average_profit'] = np.mean(profits)
    
    def get_trading_recommendation(self, current_conditions):
        """دریافت توصیه معاملاتی بر اساس یادگیری"""
        pattern_key = self.create_pattern_key_from_conditions(current_conditions)
        
        if pattern_key in self.recognized_patterns:
            pattern = self.recognized_patterns[pattern_key]
            
            if pattern['confidence'] >= self.learning_config['confidence_threshold']:
                return {
                    'recommendation': 'TRADE',
                    'confidence': pattern['confidence'],
                    'expected_profit': pattern['average_profit'],
                    'pattern_matches': pattern['total_count'],
                    'reason': f"Pattern recognized with {pattern['confidence']:.1%} confidence"
                }
        
        return {
            'recommendation': 'WAIT',
            'confidence': 0,
            'reason': 'No reliable pattern found'
        }
    
    def create_pattern_key_from_conditions(self, conditions):
        """ایجاد کلید الگو از شرایط فعلی"""
        key_elements = [
            conditions.get('direction', 'NEUTRAL'),
            str(round(conditions.get('signal_strength', 0), 1)),
            str(len(conditions.get('indicators_used', {}))),
            conditions.get('market_conditions', {}).get('trend', 'neutral'),
            conditions.get('market_conditions', {}).get('volatility', 'medium')
        ]
        
        return "_".join(key_elements)
    
    def save_learning_data(self):
        """ذخیره داده‌های یادگیری"""
        try:
            data = {
                'learning_database': self.learning_database,
                'learning_config': self.learning_config,
                'performance_metrics': self.performance_metrics,
                'recognized_patterns': self.recognized_patterns,
                'last_updated': datetime.now().isoformat()
            }
            
            with open('vip_learning_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print("💾 Learning data saved successfully")
            
        except Exception as e:
            print(f"❌ Error saving learning data: {e}")
    
    def load_learning_data(self):
        """بارگذاری داده‌های یادگیری"""
        try:
            if Path('vip_learning_data.json').exists():
                with open('vip_learning_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.learning_database = data.get('learning_database', self.learning_database)
                self.learning_config = data.get('learning_config', self.learning_config)
                self.performance_metrics = data.get('performance_metrics', self.performance_metrics)
                self.recognized_patterns = data.get('recognized_patterns', {})
                
                print("📚 Learning data loaded successfully")
                print(f"📊 Total trades in memory: {len(self.learning_database['trades'])}")
                print(f"🧠 Recognized patterns: {len(self.recognized_patterns)}")
            
        except Exception as e:
            print(f"❌ Error loading learning data: {e}")
    
    def print_learning_summary(self):
        """نمایش خلاصه یادگیری"""
        print("\n" + "="*60)
        print("🧠 VIP BIG BANG Learning System Summary")
        print("="*60)
        
        print(f"📊 Performance Metrics:")
        print(f"   Total Trades: {self.performance_metrics['total_trades']}")
        print(f"   Win Rate: {self.performance_metrics['win_rate']:.1%}")
        print(f"   Average Profit: ${self.performance_metrics['average_profit']:.2f}")
        
        print(f"\n🧠 Learning Status:")
        print(f"   Recognized Patterns: {len(self.recognized_patterns)}")
        print(f"   Confidence Threshold: {self.learning_config['confidence_threshold']:.1%}")
        
        print(f"\n🎯 Top Performing Patterns:")
        sorted_patterns = sorted(
            self.recognized_patterns.items(),
            key=lambda x: x[1]['confidence'],
            reverse=True
        )[:5]
        
        for pattern_key, pattern_data in sorted_patterns:
            print(f"   {pattern_key[:30]}... - Confidence: {pattern_data['confidence']:.1%}")
        
        print("="*60)

def main():
    """تست سیستم یادگیری"""
    learning_system = VIPLearningSystem()
    
    # شبیه‌سازی چند معامله برای تست
    sample_trades = [
        {
            'direction': 'UP',
            'entry_price': 1.0750,
            'exit_price': 1.0760,
            'duration': 300,
            'profit_loss': 10,
            'market_conditions': {'trend': 'bullish', 'volatility': 'low'},
            'indicators_used': {'ma6': {'strength': 0.8}, 'vortex': {'strength': 0.9}},
            'signal_strength': 0.85,
            'confidence': 0.9
        },
        {
            'direction': 'DOWN',
            'entry_price': 1.0760,
            'exit_price': 1.0750,
            'duration': 300,
            'profit_loss': 10,
            'market_conditions': {'trend': 'bearish', 'volatility': 'medium'},
            'indicators_used': {'momentum': {'strength': 0.7}, 'trend_analyzer': {'strength': 0.8}},
            'signal_strength': 0.75,
            'confidence': 0.8
        }
    ]
    
    print("🧪 Testing learning system with sample trades...")
    
    for trade in sample_trades:
        learning_system.record_trade(trade)
    
    learning_system.print_learning_summary()
    learning_system.save_learning_data()

if __name__ == "__main__":
    main()
