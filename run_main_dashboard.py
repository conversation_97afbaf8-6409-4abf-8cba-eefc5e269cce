"""
🚀 VIP BIG BANG - Main Dashboard Runner
Launch the complete VIP BIG BANG Dashboard with Extension integration
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QPalette, QColor

# Add paths
sys.path.append(str(Path(__file__).parent))

# Import main dashboard
from ui.vip_main_dashboard import VIPMainDashboard

def setup_application():
    """Setup application with professional styling"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("VIP BIG BANG Trading System")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("VIP BIG BANG")
    
    # Set application style
    app.setStyle('Fusion')
    
    # Apply professional dark theme
    palette = QPalette()
    
    # Window colors
    palette.setColor(QPalette.ColorRole.Window, QColor(31, 41, 55))
    palette.setColor(QPalette.ColorRole.WindowText, QColor(243, 244, 246))
    
    # Base colors
    palette.setColor(QPalette.ColorRole.Base, QColor(17, 24, 39))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(55, 65, 81))
    
    # Text colors
    palette.setColor(QPalette.ColorRole.Text, QColor(243, 244, 246))
    palette.setColor(QPalette.ColorRole.BrightText, QColor(239, 68, 68))
    
    # Button colors
    palette.setColor(QPalette.ColorRole.Button, QColor(55, 65, 81))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor(243, 244, 246))
    
    # Highlight colors
    palette.setColor(QPalette.ColorRole.Highlight, QColor(59, 130, 246))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(255, 255, 255))
    
    # Link colors
    palette.setColor(QPalette.ColorRole.Link, QColor(96, 165, 250))
    
    # Tooltip colors
    palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(17, 24, 39))
    palette.setColor(QPalette.ColorRole.ToolTipText, QColor(243, 244, 246))
    
    app.setPalette(palette)
    
    return app

def main():
    """Main function"""
    print("🚀 VIP BIG BANG - Main Dashboard")
    print("=" * 50)
    print("🎯 Professional Trading System")
    print("🔌 Extension Integration Active")
    print("💎 Quantum-Level Performance")
    print("=" * 50)
    
    # Setup application
    app = setup_application()
    
    try:
        # Create main dashboard
        print("🎮 Creating VIP BIG BANG Dashboard...")
        dashboard = VIPMainDashboard()
        
        # Show dashboard
        print("✅ Showing dashboard...")
        dashboard.show()
        
        print("🎉 VIP BIG BANG Dashboard launched successfully!")
        print("🔌 Extension Data Widget integrated")
        print("📊 Real-time data monitoring active")
        print("💰 Professional trading interface ready")
        print("=" * 50)
        print("🎯 Ready for professional trading!")
        
        # Run application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ Error launching dashboard: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main()
