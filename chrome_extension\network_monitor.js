// 🌐 VIP BIG BANG - Network Request Monitor
// Monitors XHR and <PERSON>tch requests for Quotex data

console.log('🌐 VIP BIG BANG Network Monitor Loaded');

class NetworkMonitor {
    constructor() {
        this.originalXHR = window.XMLHttpRequest;
        this.originalFetch = window.fetch;
        this.vipWebSocket = null;
        this.requestCount = 0;
        this.init();
    }

    init() {
        this.interceptXHR();
        this.interceptFetch();
        this.connectToVIPSystem();
        console.log('🚀 Network Monitor initialized');
    }

    interceptXHR() {
        const self = this;
        
        window.XMLHttpRequest = function() {
            const xhr = new self.originalXHR();
            const originalOpen = xhr.open;
            const originalSend = xhr.send;
            
            xhr.open = function(method, url, ...args) {
                this._vip_method = method;
                this._vip_url = url;
                
                if (self.isQuotexRequest(url)) {
                    console.log('📡 Quotex XHR intercepted:', method, url);
                    
                    // Monitor response
                    this.addEventListener('load', function() {
                        if (this.status === 200) {
                            try {
                                const data = JSON.parse(this.responseText);
                                self.processQuotexData(data, url, 'XHR');
                            } catch (e) {
                                self.processQuotexData(this.responseText, url, 'XHR');
                            }
                        }
                    });
                }
                
                return originalOpen.apply(this, [method, url, ...args]);
            };
            
            xhr.send = function(...args) {
                if (self.isQuotexRequest(this._vip_url)) {
                    self.requestCount++;
                }
                return originalSend.apply(this, args);
            };
            
            return xhr;
        };
    }

    interceptFetch() {
        const self = this;
        
        window.fetch = function(url, options = {}) {
            if (self.isQuotexRequest(url)) {
                console.log('📡 Quotex Fetch intercepted:', url);
                self.requestCount++;
                
                return self.originalFetch(url, options)
                    .then(response => {
                        if (response.ok) {
                            // Clone response to read it
                            const clonedResponse = response.clone();
                            clonedResponse.text().then(text => {
                                try {
                                    const data = JSON.parse(text);
                                    self.processQuotexData(data, url, 'FETCH');
                                } catch (e) {
                                    self.processQuotexData(text, url, 'FETCH');
                                }
                            }).catch(() => {});
                        }
                        return response;
                    });
            }
            
            return self.originalFetch(url, options);
        };
    }

    isQuotexRequest(url) {
        if (!url) return false;
        
        const quotexPatterns = [
            'qxbroker.com',
            'quotex.io',
            'quotex.com',
            'api/v1',
            'socket.io',
            'trade',
            'balance',
            'asset'
        ];
        
        return quotexPatterns.some(pattern => url.includes(pattern));
    }

    processQuotexData(data, url, method) {
        const processedData = {
            timestamp: new Date().toISOString(),
            source: 'NETWORK_MONITOR',
            method: method,
            url: url,
            data: data,
            requestCount: this.requestCount
        };

        // Extract specific data types
        if (typeof data === 'object' && data !== null) {
            // Look for balance data
            if (data.balance !== undefined) {
                processedData.extractedBalance = data.balance;
            }
            
            // Look for asset data
            if (data.asset || data.symbol) {
                processedData.extractedAsset = data.asset || data.symbol;
            }
            
            // Look for price data
            if (data.price || data.rate || data.quote) {
                processedData.extractedPrice = data.price || data.rate || data.quote;
            }
        }

        this.sendToVIPSystem(processedData);
        
        console.log('📊 Quotex network data processed:', {
            method: method,
            url: url.substring(0, 50) + '...',
            hasData: !!data
        });
    }

    connectToVIPSystem() {
        try {
            this.vipWebSocket = new WebSocket('ws://localhost:8765');
            
            this.vipWebSocket.onopen = () => {
                console.log('✅ Network Monitor connected to VIP BIG BANG System');
                this.sendToVIPSystem({
                    type: 'network_monitor_connected',
                    timestamp: new Date().toISOString(),
                    monitor: 'Network Request Monitor'
                });
            };

            this.vipWebSocket.onclose = () => {
                console.log('❌ Network Monitor disconnected - reconnecting...');
                setTimeout(() => this.connectToVIPSystem(), 3000);
            };

            this.vipWebSocket.onerror = (error) => {
                console.error('❌ Network Monitor WebSocket error:', error);
            };

        } catch (error) {
            console.error('❌ Failed to connect Network Monitor:', error);
            setTimeout(() => this.connectToVIPSystem(), 5000);
        }
    }

    sendToVIPSystem(data) {
        if (this.vipWebSocket && this.vipWebSocket.readyState === WebSocket.OPEN) {
            try {
                this.vipWebSocket.send(JSON.stringify({
                    type: 'network_data',
                    data: data,
                    monitor: true
                }));
            } catch (error) {
                console.error('❌ Failed to send network data:', error);
            }
        }
    }

    getStatus() {
        return {
            requestCount: this.requestCount,
            vipConnected: this.vipWebSocket && this.vipWebSocket.readyState === WebSocket.OPEN,
            interceptorsActive: window.XMLHttpRequest !== this.originalXHR && window.fetch !== this.originalFetch
        };
    }
}

// Initialize the network monitor
const networkMonitor = new NetworkMonitor();

// Export for external access
window.vipNetworkMonitor = networkMonitor;

console.log('✅ VIP BIG BANG Network Monitor Ready');
