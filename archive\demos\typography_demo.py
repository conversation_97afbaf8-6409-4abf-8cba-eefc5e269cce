"""
🔤 Modern Typography Demo - تکنیک‌های فونت ChatGPT
"""

import sys
from PySide6.QtWidgets import *
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class TypographyDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔤 Modern Typography Demo")
        self.setGeometry(100, 100, 800, 600)
        
        # ChatGPT-style background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:1 #1a1a2e);
                color: white;
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(30)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # Typography Hierarchy مثل ChatGPT
        
        # 1. Display Text (عناوین اصلی)
        display_text = QLabel("VIP BIG BANG")
        display_text.setStyleSheet("""
            font-family: 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
            font-size: 48px;
            font-weight: 800;
            color: white;
            letter-spacing: 4px;
            margin: 20px 0;
        """)
        display_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(display_text)
        
        # 2. Heading Text (عناوین فرعی)
        heading_text = QLabel("Ultimate Trading System")
        heading_text.setStyleSheet("""
            font-family: 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
            font-size: 24px;
            font-weight: 600;
            color: rgba(255,255,255,0.9);
            letter-spacing: 2px;
            margin: 10px 0;
        """)
        heading_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(heading_text)
        
        # 3. Subheading Text (زیرعناوین)
        subheading_text = QLabel("Professional Trading Interface")
        subheading_text.setStyleSheet("""
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 18px;
            font-weight: 500;
            color: rgba(255,255,255,0.8);
            letter-spacing: 1px;
            margin: 8px 0;
        """)
        subheading_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(subheading_text)
        
        # 4. Body Text (متن اصلی)
        body_text = QLabel("This is body text used for main content. It's readable and comfortable for extended reading.")
        body_text.setStyleSheet("""
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 14px;
            font-weight: 400;
            color: rgba(255,255,255,0.9);
            line-height: 1.6;
            margin: 6px 0;
        """)
        body_text.setAlignment(Qt.AlignCenter)
        body_text.setWordWrap(True)
        layout.addWidget(body_text)
        
        # 5. Caption Text (توضیحات کوچک)
        caption_text = QLabel("Caption text for small details and metadata")
        caption_text.setStyleSheet("""
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 12px;
            font-weight: 500;
            color: rgba(255,255,255,0.7);
            letter-spacing: 0.5px;
            margin: 4px 0;
        """)
        caption_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(caption_text)
        
        # 6. Button Text (متن دکمه‌ها)
        button_demo = QPushButton("Button Text")
        button_demo.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', system-ui, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: white;
                background: #4CAF50;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                letter-spacing: 0.5px;
            }
            QPushButton:hover {
                background: #45a049;
            }
        """)
        layout.addWidget(button_demo)
        
        # Typography Rules
        rules_label = QLabel("📝 Typography Rules:")
        rules_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: #4CAF50;
            margin-top: 20px;
        """)
        layout.addWidget(rules_label)
        
        rules_text = QLabel("""
• Font Stack: 'Segoe UI', 'SF Pro Display', system-ui, sans-serif
• Font Weights: 400 (normal), 500 (medium), 600 (semibold), 700 (bold), 800 (extrabold)
• Letter Spacing: 0.5px-4px برای عناوین
• Line Height: 1.4-1.6 برای خوانایی بهتر
• Color Hierarchy: white → rgba(255,255,255,0.9) → rgba(255,255,255,0.8) → rgba(255,255,255,0.7)
        """)
        rules_text.setStyleSheet("""
            font-size: 13px;
            color: rgba(255,255,255,0.8);
            line-height: 1.5;
            margin: 10px 0;
        """)
        rules_text.setWordWrap(True)
        layout.addWidget(rules_text)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TypographyDemo()
    window.show()
    sys.exit(app.exec())
