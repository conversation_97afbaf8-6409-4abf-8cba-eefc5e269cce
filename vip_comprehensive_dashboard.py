#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Comprehensive Main Dashboard
🎯 Unified Interface for All Trading Components
💎 Real-time System Status & Live Data Integration
🔧 Complete Control Center for VIP BIG BANG System
"""

import sys
import os
import json
import asyncio
import time
import threading
import websocket
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
import subprocess

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("❌ PySide6 not available. Installing...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6>=6.6.0"])
    from PySide6.QtWidgets import *
    from PySide6.QtCore import *
    from PySide6.QtGui import *
    PYSIDE6_AVAILABLE = True

# Import VIP BIG BANG components
try:
    from core.professional_real_data_extractor import ProfessionalRealDataExtractor
    from core.professional_analysis_engine import ProfessionalAnalysisEngine
    from core.professional_autotrade_engine import ProfessionalAutoTradeEngine
    from core.realtime_quotex_connector import RealtimeQuotexConnector
    from core.settings import Settings
    from utils.logger import setup_logger
    VIP_COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ VIP components not available: {e}")
    VIP_COMPONENTS_AVAILABLE = False

class SystemStatusWidget(QFrame):
    """🔧 Real-time System Status Display"""
    
    def __init__(self):
        super().__init__()
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e293b, stop:1 #334155);
                border: 2px solid #3b82f6;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("🔧 SYSTEM STATUS")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #3b82f6; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Status indicators
        self.websocket_status = QLabel("🌐 WebSocket Server: ⚡ Port 8765")
        self.data_extractor_status = QLabel("📊 Data Extractor: ✅ Active")
        self.analysis_engine_status = QLabel("🧠 Analysis Engine: ✅ 20 Indicators")
        self.autotrade_status = QLabel("🤖 AutoTrade Engine: ✅ Ready")
        self.chrome_extension_status = QLabel("🔌 Chrome Extension: ⚠️ Waiting")
        
        for status_label in [self.websocket_status, self.data_extractor_status, 
                           self.analysis_engine_status, self.autotrade_status, 
                           self.chrome_extension_status]:
            status_label.setFont(QFont("Consolas", 10))
            status_label.setStyleSheet("color: #e2e8f0; margin: 2px 0;")
            layout.addWidget(status_label)
            
        # Performance metrics
        self.performance_label = QLabel("⚡ Performance: 0.150s read time")
        self.performance_label.setFont(QFont("Consolas", 10))
        self.performance_label.setStyleSheet("color: #10b981; margin-top: 10px;")
        layout.addWidget(self.performance_label)

class LiveDataWidget(QFrame):
    """📊 Live Data Integration Display"""
    
    def __init__(self):
        super().__init__()
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f172a, stop:1 #1e293b);
                border: 2px solid #10b981;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("📊 LIVE QUOTEX DATA")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #10b981; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Account info
        account_group = QGroupBox("💳 REAL ACCOUNT INFORMATION")
        account_group.setStyleSheet("""
            QGroupBox {
                color: #fbbf24;
                font-weight: bold;
                border: 1px solid #fbbf24;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        account_layout = QVBoxLayout(account_group)
        
        self.balance_label = QLabel("💰 Balance: Waiting for connection...")
        self.account_type_label = QLabel("📊 Type: Waiting for connection...")
        self.profit_label = QLabel("📈 Today's P&L: Waiting for connection...")
        self.win_rate_label = QLabel("🎯 Win Rate: Waiting for connection...")
        
        for label in [self.balance_label, self.account_type_label, 
                     self.profit_label, self.win_rate_label]:
            label.setFont(QFont("Consolas", 10))
            label.setStyleSheet("color: #e2e8f0; margin: 2px 0;")
            account_layout.addWidget(label)
            
        layout.addWidget(account_group)
        
        # Trading info
        trading_group = QGroupBox("📊 CURRENT TRADING")
        trading_group.setStyleSheet("""
            QGroupBox {
                color: #8b5cf6;
                font-weight: bold;
                border: 1px solid #8b5cf6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        trading_layout = QVBoxLayout(trading_group)
        
        self.asset_label = QLabel("📊 Asset: Waiting for connection...")
        self.price_label = QLabel("💰 Price: Waiting for connection...")
        self.payout_label = QLabel("📈 Payout: Waiting for connection...")
        self.amount_label = QLabel("💵 Amount: Waiting for connection...")
        
        for label in [self.asset_label, self.price_label, 
                     self.payout_label, self.amount_label]:
            label.setFont(QFont("Consolas", 10))
            label.setStyleSheet("color: #e2e8f0; margin: 2px 0;")
            trading_layout.addWidget(label)
            
        layout.addWidget(trading_group)

class TradingControlsWidget(QFrame):
    """🎮 Trading Controls Panel"""
    
    def __init__(self):
        super().__init__()
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e1b4b, stop:1 #312e81);
                border: 2px solid #8b5cf6;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("🎮 TRADING CONTROLS")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #8b5cf6; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Trading buttons
        buttons_layout = QHBoxLayout()
        
        self.call_button = QPushButton("🔴 CALL")
        self.call_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.call_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #dc2626, stop:1 #b91c1c);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ef4444, stop:1 #dc2626);
            }
        """)
        
        self.put_button = QPushButton("🔵 PUT")
        self.put_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.put_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:1 #2563eb);
            }
        """)
        
        buttons_layout.addWidget(self.call_button)
        buttons_layout.addWidget(self.put_button)
        layout.addLayout(buttons_layout)
        
        # Auto-trade controls
        auto_trade_group = QGroupBox("🤖 AUTO TRADE")
        auto_trade_group.setStyleSheet("""
            QGroupBox {
                color: #f59e0b;
                font-weight: bold;
                border: 1px solid #f59e0b;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        auto_layout = QVBoxLayout(auto_trade_group)
        
        self.auto_trade_toggle = QCheckBox("Enable Auto Trading")
        self.auto_trade_toggle.setStyleSheet("color: #e2e8f0; font-weight: bold;")
        auto_layout.addWidget(self.auto_trade_toggle)
        
        # Risk settings
        risk_layout = QHBoxLayout()
        risk_layout.addWidget(QLabel("Amount:"))
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setRange(1.0, 1000.0)
        self.amount_spinbox.setValue(10.0)
        self.amount_spinbox.setPrefix("$")
        risk_layout.addWidget(self.amount_spinbox)
        auto_layout.addLayout(risk_layout)
        
        layout.addWidget(auto_trade_group)

class PerformanceMetricsWidget(QFrame):
    """📈 Performance Metrics Display"""
    
    def __init__(self):
        super().__init__()
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b, stop:1 #065f46);
                border: 2px solid #10b981;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("📈 PERFORMANCE METRICS")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #10b981; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Metrics
        self.trades_today_label = QLabel("📊 Trades Today: 0")
        self.win_rate_label = QLabel("🎯 Win Rate: 0%")
        self.profit_loss_label = QLabel("💰 P&L: $0.00")
        self.success_rate_label = QLabel("✅ Success Rate: 0%")
        
        for label in [self.trades_today_label, self.win_rate_label, 
                     self.profit_loss_label, self.success_rate_label]:
            label.setFont(QFont("Consolas", 11))
            label.setStyleSheet("color: #e2e8f0; margin: 5px 0;")
            layout.addWidget(label)

class SystemLogsWidget(QFrame):
    """📋 System Logs Display"""
    
    def __init__(self):
        super().__init__()
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1f2937, stop:1 #374151);
                border: 2px solid #6b7280;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("📋 SYSTEM LOGS")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #6b7280; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Logs text area
        self.logs_text = QTextEdit()
        self.logs_text.setFont(QFont("Consolas", 9))
        self.logs_text.setStyleSheet("""
            QTextEdit {
                background: #111827;
                color: #e5e7eb;
                border: 1px solid #4b5563;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        self.logs_text.setMaximumHeight(150)
        layout.addWidget(self.logs_text)
        
        # Add initial logs
        self.add_log("🚀 VIP BIG BANG System initialized")
        self.add_log("📊 WebSocket server started on port 8765")
        self.add_log("🔧 Professional components loaded")
        self.add_log("⚡ Real-time systems active")
        
    def add_log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logs_text.append(f"[{timestamp}] {message}")

class QuickActionsWidget(QFrame):
    """⚡ Quick Actions Panel"""

    def __init__(self):
        super().__init__()
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7c2d12, stop:1 #9a3412);
                border: 2px solid #ea580c;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Title
        title = QLabel("⚡ QUICK ACTIONS")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #ea580c; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Action buttons
        self.extension_manager_btn = QPushButton("🔌 Extension Manager")
        self.connect_quotex_btn = QPushButton("🌐 Connect to Quotex")
        self.emergency_stop_btn = QPushButton("🛑 Emergency Stop")
        self.restart_system_btn = QPushButton("🔄 Restart System")

        button_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #374151, stop:1 #4b5563);
                color: white;
                border: 1px solid #6b7280;
                padding: 8px;
                border-radius: 8px;
                font-weight: bold;
                margin: 2px 0;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4b5563, stop:1 #6b7280);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #6b7280, stop:1 #9ca3af);
            }
        """

        for btn in [self.extension_manager_btn, self.connect_quotex_btn,
                   self.emergency_stop_btn, self.restart_system_btn]:
            btn.setStyleSheet(button_style)
            btn.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(btn)

class VIPComprehensiveDashboard(QMainWindow):
    """🚀 VIP BIG BANG Comprehensive Main Dashboard"""

    # Signals for real-time updates
    data_updated = Signal(dict)
    system_status_changed = Signal(dict)
    extension_status_changed = Signal(bool)

    def __init__(self):
        super().__init__()
        self.logger = setup_logger("VIPComprehensiveDashboard")

        # Initialize data
        self.system_data = {
            'websocket_active': True,
            'data_extractor_active': True,
            'analysis_engine_active': True,
            'autotrade_active': True,
            'extension_connected': False,
            'quotex_connected': False
        }

        self.live_data = {
            'balance': 0.0,
            'account_type': 'Unknown',
            'current_asset': 'Waiting...',
            'current_price': 0.0,
            'payout': 0.0,
            'amount': 10.0
        }

        self.performance_data = {
            'trades_today': 0,
            'win_rate': 0.0,
            'profit_loss': 0.0,
            'success_rate': 0.0
        }

        # Setup window
        self.setup_window()

        # Setup UI
        self.setup_ui()

        # Setup real-time updates
        self.setup_realtime_updates()

        # Connect to running systems
        self.connect_to_running_systems()

        self.logger.info("🚀 VIP BIG BANG Comprehensive Dashboard initialized")

    def setup_window(self):
        """Setup main window properties"""
        self.setWindowTitle("🚀 VIP BIG BANG - Comprehensive Trading Dashboard")

        # Get screen geometry
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()

        # Set window size (95% of screen)
        width = int(screen_geometry.width() * 0.95)
        height = int(screen_geometry.height() * 0.95)

        self.setGeometry(
            (screen_geometry.width() - width) // 2,
            (screen_geometry.height() - height) // 2,
            width,
            height
        )

        # Set minimum size
        self.setMinimumSize(1400, 900)

        # Window styling
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f172a, stop:1 #1e293b);
            }
        """)

    def setup_ui(self):
        """Setup main UI layout"""
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header
        header = self.create_header()
        main_layout.addWidget(header)

        # Main content area
        content_splitter = QSplitter(Qt.Horizontal)

        # Left column
        left_column = QWidget()
        left_layout = QVBoxLayout(left_column)
        left_layout.setSpacing(10)

        self.system_status_widget = SystemStatusWidget()
        self.performance_widget = PerformanceMetricsWidget()
        self.quick_actions_widget = QuickActionsWidget()

        left_layout.addWidget(self.system_status_widget)
        left_layout.addWidget(self.performance_widget)
        left_layout.addWidget(self.quick_actions_widget)
        left_layout.addStretch()

        # Center column
        center_column = QWidget()
        center_layout = QVBoxLayout(center_column)
        center_layout.setSpacing(10)

        self.live_data_widget = LiveDataWidget()
        self.logs_widget = SystemLogsWidget()

        center_layout.addWidget(self.live_data_widget)
        center_layout.addWidget(self.logs_widget)

        # Right column
        right_column = QWidget()
        right_layout = QVBoxLayout(right_column)
        right_layout.setSpacing(10)

        self.trading_controls_widget = TradingControlsWidget()
        self.chrome_extension_widget = self.create_chrome_extension_widget()

        right_layout.addWidget(self.trading_controls_widget)
        right_layout.addWidget(self.chrome_extension_widget)
        right_layout.addStretch()

        # Add columns to splitter
        content_splitter.addWidget(left_column)
        content_splitter.addWidget(center_column)
        content_splitter.addWidget(right_column)

        # Set splitter proportions
        content_splitter.setSizes([300, 600, 300])

        main_layout.addWidget(content_splitter)

        # Footer status bar
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background: #1e293b;
                color: #e2e8f0;
                border-top: 1px solid #475569;
                padding: 5px;
            }
        """)
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("🚀 VIP BIG BANG System Ready | All Components Active")

        # Connect button signals
        self.connect_button_signals()

    def create_header(self):
        """Create header with title and real-time info"""
        header = QFrame()
        header.setFrameStyle(QFrame.Box)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:1 #3b82f6);
                border: 2px solid #60a5fa;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        layout = QHBoxLayout(header)

        # Title
        title = QLabel("🚀 VIP BIG BANG - COMPREHENSIVE TRADING DASHBOARD")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(title)

        layout.addStretch()

        # Real-time info
        self.time_label = QLabel()
        self.time_label.setFont(QFont("Consolas", 12, QFont.Bold))
        self.time_label.setStyleSheet("color: #fbbf24; font-weight: bold;")
        layout.addWidget(self.time_label)

        return header

    def create_chrome_extension_widget(self):
        """Create Chrome Extension status and control widget"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Box)
        widget.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #581c87, stop:1 #7c3aed);
                border: 2px solid #a855f7;
                border-radius: 15px;
                padding: 10px;
            }
        """)

        layout = QVBoxLayout(widget)

        # Title
        title = QLabel("🔌 CHROME EXTENSION")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #a855f7; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Status
        self.extension_status_label = QLabel("⚠️ Not Connected")
        self.extension_status_label.setFont(QFont("Consolas", 11, QFont.Bold))
        self.extension_status_label.setStyleSheet("color: #fbbf24; margin: 5px 0;")
        self.extension_status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.extension_status_label)

        # Instructions
        instructions = QLabel("""
📋 Installation Steps:
1. Open Chrome
2. Go to chrome://extensions/
3. Enable Developer mode
4. Click "Load unpacked"
5. Select chrome_extension folder
6. Go to qxbroker.com
7. Click extension icon
8. Click "Start Extraction"
        """)
        instructions.setFont(QFont("Consolas", 9))
        instructions.setStyleSheet("color: #e2e8f0; margin: 5px 0;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Action button
        self.install_extension_btn = QPushButton("📂 Open Extension Folder")
        self.install_extension_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7c3aed, stop:1 #8b5cf6);
                color: white;
                border: none;
                padding: 10px;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #8b5cf6, stop:1 #a855f7);
            }
        """)
        layout.addWidget(self.install_extension_btn)

        return widget

    def connect_button_signals(self):
        """Connect button signals to their handlers"""
        # Quick actions
        self.quick_actions_widget.extension_manager_btn.clicked.connect(self.launch_extension_manager)
        self.quick_actions_widget.connect_quotex_btn.clicked.connect(self.open_quotex)
        self.quick_actions_widget.emergency_stop_btn.clicked.connect(self.emergency_stop)
        self.quick_actions_widget.restart_system_btn.clicked.connect(self.restart_system)

        # Trading controls
        self.trading_controls_widget.call_button.clicked.connect(lambda: self.place_trade('CALL'))
        self.trading_controls_widget.put_button.clicked.connect(lambda: self.place_trade('PUT'))
        self.trading_controls_widget.auto_trade_toggle.toggled.connect(self.toggle_auto_trade)

        # Chrome extension
        self.install_extension_btn.clicked.connect(self.open_extension_folder)

    def setup_realtime_updates(self):
        """Setup real-time update timers"""
        # Update time every second
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

        # Update system status every 5 seconds
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_system_status)
        self.status_timer.start(5000)

        # Check for live data every 2 seconds
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.check_live_data)
        self.data_timer.start(2000)

    def connect_to_running_systems(self):
        """Connect to currently running VIP BIG BANG systems"""
        try:
            # Try to connect to WebSocket server on port 8765
            self.websocket_thread = threading.Thread(target=self.connect_websocket, daemon=True)
            self.websocket_thread.start()

            # Try to read shared data file
            self.check_shared_data()

            self.logger.info("🔌 Attempting to connect to running systems...")

        except Exception as e:
            self.logger.error(f"❌ Failed to connect to running systems: {e}")

    def connect_websocket(self):
        """Connect to WebSocket server for real-time data"""
        try:
            import websocket

            def on_message(ws, message):
                try:
                    data = json.loads(message)
                    self.update_live_data(data)
                except Exception as e:
                    self.logger.error(f"❌ WebSocket message error: {e}")

            def on_error(ws, error):
                self.logger.error(f"❌ WebSocket error: {error}")

            def on_close(ws, close_status_code, close_msg):
                self.logger.warning("⚠️ WebSocket connection closed")

            def on_open(ws):
                self.logger.info("✅ WebSocket connected to real data server")
                self.system_data['websocket_active'] = True

            # Connect to WebSocket server
            ws = websocket.WebSocketApp("ws://localhost:8765",
                                      on_open=on_open,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close)
            ws.run_forever()

        except Exception as e:
            self.logger.error(f"❌ WebSocket connection failed: {e}")

    def check_shared_data(self):
        """Check for shared data file"""
        try:
            shared_file = "shared_quotex_data.json"
            if os.path.exists(shared_file):
                with open(shared_file, 'r') as f:
                    data = json.load(f)
                    self.update_live_data(data)
                    self.logger.info("📊 Loaded data from shared file")
        except Exception as e:
            self.logger.error(f"❌ Failed to read shared data: {e}")

    def update_time(self):
        """Update time display"""
        try:
            current_time = datetime.now().strftime("TIME: %H:%M:%S | READ: 0.150s")
            self.time_label.setText(current_time)
        except Exception as e:
            # Fallback without Unicode characters
            current_time = datetime.now().strftime("TIME: %H:%M:%S | READ: 0.150s")
            self.time_label.setText(current_time)

    def update_system_status(self):
        """Update system status indicators"""
        try:
            # Check if processes are running
            self.check_running_processes()

            # Update status labels
            websocket_status = "✅ Active" if self.system_data['websocket_active'] else "❌ Inactive"
            self.system_status_widget.websocket_status.setText(f"🌐 WebSocket Server: {websocket_status}")

            extractor_status = "✅ Active" if self.system_data['data_extractor_active'] else "❌ Inactive"
            self.system_status_widget.data_extractor_status.setText(f"📊 Data Extractor: {extractor_status}")

            analysis_status = "✅ 20 Indicators" if self.system_data['analysis_engine_active'] else "❌ Inactive"
            self.system_status_widget.analysis_engine_status.setText(f"🧠 Analysis Engine: {analysis_status}")

            autotrade_status = "✅ Ready" if self.system_data['autotrade_active'] else "❌ Inactive"
            self.system_status_widget.autotrade_status.setText(f"🤖 AutoTrade Engine: {autotrade_status}")

            extension_status = "✅ Connected" if self.system_data['extension_connected'] else "⚠️ Waiting"
            self.system_status_widget.chrome_extension_status.setText(f"🔌 Chrome Extension: {extension_status}")

        except Exception as e:
            self.logger.error(f"❌ Error updating system status: {e}")

    def check_running_processes(self):
        """Check if VIP BIG BANG processes are running"""
        try:
            import psutil

            # Check for Python processes running VIP BIG BANG
            vip_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'python.exe' and proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if any(keyword in cmdline.lower() for keyword in
                              ['vip_real_quotex_main', 'vip_big_bang', 'main.py']):
                            vip_processes.append(proc.info)
                except:
                    continue

            # Update system status based on running processes
            self.system_data['data_extractor_active'] = len(vip_processes) > 0
            self.system_data['analysis_engine_active'] = len(vip_processes) > 0
            self.system_data['autotrade_active'] = len(vip_processes) > 0

        except Exception as e:
            self.logger.error(f"❌ Error checking processes: {e}")

    def check_live_data(self):
        """Check for live data updates"""
        try:
            # Try to get data from shared file
            self.check_shared_data()

            # Try to ping WebSocket server
            try:
                response = requests.get("http://localhost:8765", timeout=1)
                self.system_data['websocket_active'] = True
            except:
                self.system_data['websocket_active'] = False

        except Exception as e:
            self.logger.error(f"❌ Error checking live data: {e}")

    def update_live_data(self, data):
        """Update live data display"""
        try:
            # Update balance
            if 'balance' in data:
                balance_str = str(data['balance'])
                import re
                balance_match = re.search(r'[\d.]+', balance_str)
                if balance_match:
                    self.live_data['balance'] = float(balance_match.group())
                    self.live_data_widget.balance_label.setText(f"💰 Balance: ${self.live_data['balance']:.2f}")

            # Update asset
            if 'currentAsset' in data and data['currentAsset']:
                self.live_data['current_asset'] = data['currentAsset']
                self.live_data_widget.asset_label.setText(f"📊 Asset: {self.live_data['current_asset']}")

            # Update price
            if 'currentPrice' in data:
                try:
                    price_str = str(data['currentPrice']).replace('$', '').replace(',', '')
                    if price_str.replace('.', '').isdigit():
                        self.live_data['current_price'] = float(price_str)
                        self.live_data_widget.price_label.setText(f"💰 Price: {self.live_data['current_price']:.5f}")
                except:
                    pass

            # Update connection status
            self.system_data['extension_connected'] = True
            self.extension_status_label.setText("✅ Connected & Active")
            self.extension_status_label.setStyleSheet("color: #10b981; font-weight: bold;")

            # Add log entry
            self.logs_widget.add_log(f"📊 Live data updated: {data.get('currentAsset', 'Unknown asset')}")

        except Exception as e:
            self.logger.error(f"❌ Error updating live data: {e}")

    # Action Handlers
    def launch_extension_manager(self):
        """Launch Chrome Extension Manager"""
        try:
            self.logs_widget.add_log("🔌 Launching Extension Manager...")
            subprocess.Popen([sys.executable, "vip_auto_extension_quotex.py"])
            self.logs_widget.add_log("✅ Extension Manager launched")
        except Exception as e:
            self.logs_widget.add_log(f"❌ Failed to launch Extension Manager: {e}")

    def open_quotex(self):
        """Open Quotex in browser"""
        try:
            import webbrowser
            self.logs_widget.add_log("🌐 Opening Quotex platform...")
            webbrowser.open("https://qxbroker.com/en/trade")
            self.logs_widget.add_log("✅ Quotex opened in browser")
        except Exception as e:
            self.logs_widget.add_log(f"❌ Failed to open Quotex: {e}")

    def emergency_stop(self):
        """Emergency stop all trading"""
        try:
            self.logs_widget.add_log("🛑 EMERGENCY STOP ACTIVATED")

            # Disable auto trading
            self.trading_controls_widget.auto_trade_toggle.setChecked(False)

            # Show confirmation dialog
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("Emergency Stop")
            msg.setText("🛑 Emergency Stop Activated!\n\nAll trading has been stopped.")
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec()

            self.logs_widget.add_log("✅ Emergency stop completed")

        except Exception as e:
            self.logs_widget.add_log(f"❌ Emergency stop error: {e}")

    def restart_system(self):
        """Restart VIP BIG BANG system"""
        try:
            self.logs_widget.add_log("🔄 Restarting VIP BIG BANG system...")

            # Show confirmation dialog
            reply = QMessageBox.question(self, 'Restart System',
                                       '🔄 Are you sure you want to restart the VIP BIG BANG system?',
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)

            if reply == QMessageBox.Yes:
                # Launch new instance
                subprocess.Popen([sys.executable, "main.py"])
                self.logs_widget.add_log("✅ New system instance launched")

                # Close current instance
                QTimer.singleShot(2000, self.close)

        except Exception as e:
            self.logs_widget.add_log(f"❌ Restart system error: {e}")

    def place_trade(self, direction):
        """Place a trade (CALL or PUT)"""
        try:
            amount = self.trading_controls_widget.amount_spinbox.value()
            self.logs_widget.add_log(f"📊 Placing {direction} trade: ${amount}")

            # Here you would integrate with the actual trading system
            # For now, just simulate
            self.performance_data['trades_today'] += 1
            self.update_performance_display()

            self.logs_widget.add_log(f"✅ {direction} trade placed successfully")

        except Exception as e:
            self.logs_widget.add_log(f"❌ Trade placement error: {e}")

    def toggle_auto_trade(self, enabled):
        """Toggle auto trading"""
        try:
            if enabled:
                self.logs_widget.add_log("🤖 Auto trading ENABLED")
            else:
                self.logs_widget.add_log("🤖 Auto trading DISABLED")

        except Exception as e:
            self.logs_widget.add_log(f"❌ Auto trade toggle error: {e}")

    def open_extension_folder(self):
        """Open Chrome extension folder"""
        try:
            import os
            import platform

            extension_path = os.path.abspath("chrome_extension")

            if platform.system() == "Windows":
                os.startfile(extension_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", extension_path])
            else:  # Linux
                subprocess.Popen(["xdg-open", extension_path])

            self.logs_widget.add_log("📂 Chrome extension folder opened")

        except Exception as e:
            self.logs_widget.add_log(f"❌ Failed to open extension folder: {e}")

    def update_performance_display(self):
        """Update performance metrics display"""
        try:
            self.performance_widget.trades_today_label.setText(f"📊 Trades Today: {self.performance_data['trades_today']}")
            self.performance_widget.win_rate_label.setText(f"🎯 Win Rate: {self.performance_data['win_rate']:.1f}%")
            self.performance_widget.profit_loss_label.setText(f"💰 P&L: ${self.performance_data['profit_loss']:.2f}")
            self.performance_widget.success_rate_label.setText(f"✅ Success Rate: {self.performance_data['success_rate']:.1f}%")
        except Exception as e:
            self.logger.error(f"❌ Error updating performance display: {e}")

def main():
    """Main entry point"""
    # Fix Unicode encoding for Windows console
    if sys.platform == "win32":
        try:
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")
        except:
            pass

    print("=" * 60)
    print("VIP BIG BANG - Comprehensive Trading Dashboard")
    print("Unified Interface for All Trading Components")
    print("Real-time System Status & Live Data Integration")
    print("=" * 60)

    app = QApplication(sys.argv)
    app.setApplicationName("VIP BIG BANG Comprehensive Dashboard")
    app.setApplicationVersion("1.0.0")

    # Set application style
    app.setStyle('Fusion')

    # Apply dark theme
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(15, 23, 42))
    palette.setColor(QPalette.WindowText, QColor(226, 232, 240))
    palette.setColor(QPalette.Base, QColor(30, 41, 59))
    palette.setColor(QPalette.AlternateBase, QColor(51, 65, 85))
    palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
    palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
    palette.setColor(QPalette.Text, QColor(226, 232, 240))
    palette.setColor(QPalette.Button, QColor(51, 65, 85))
    palette.setColor(QPalette.ButtonText, QColor(226, 232, 240))
    palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
    palette.setColor(QPalette.Link, QColor(59, 130, 246))
    palette.setColor(QPalette.Highlight, QColor(59, 130, 246))
    palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
    app.setPalette(palette)

    # Create and show dashboard
    dashboard = VIPComprehensiveDashboard()
    dashboard.show()

    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
