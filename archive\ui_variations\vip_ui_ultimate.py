"""
🚀 VIP BIG BANG Ultimate UI - ChatGPT Style
راز ساخت UI زیبا مثل ChatGPT با تکنیک‌های حرفه‌ای
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                               QFrame, QProgressBar, QGraphicsDropShadowEffect)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QPainter, QColor, QPen, QBrush, QLinearGradient

class UltimateButton(QPushButton):
    """Ultimate button with perfect typography and effects"""
    
    def __init__(self, text="", icon="", button_type="default", size=(90, 90)):
        super().__init__()
        self.button_text = text
        self.button_icon = icon
        self.button_type = button_type
        self.button_size = size
        self.setup_button()
        
    def setup_button(self):
        """Setup ultimate button with perfect design"""
        self.setFixedSize(*self.button_size)
        
        # Perfect layout for icon and text
        if self.button_icon and self.button_text:
            # Create custom layout
            self.setText("")  # Clear default text
            
            # Use rich text for better control
            content = f"""
            <div style="text-align: center; line-height: 1.2;">
                <div style="font-size: 28px; margin-bottom: 4px;">{self.button_icon}</div>
                <div style="font-size: 11px; font-weight: 600; color: white; word-wrap: break-word;">{self.button_text}</div>
            </div>
            """
            
            # Create a label for content
            self.content_label = QLabel(content)
            self.content_label.setAlignment(Qt.AlignCenter)
            self.content_label.setWordWrap(True)
            self.content_label.setStyleSheet("""
                QLabel {
                    background: transparent;
                    border: none;
                    color: white;
                    padding: 8px;
                }
            """)
            
            # Layout
            layout = QVBoxLayout(self)
            layout.setContentsMargins(4, 4, 4, 4)
            layout.addWidget(self.content_label)
        else:
            self.setText(self.button_text or self.button_icon)
        
        # Apply styling
        self.apply_ultimate_style()
        self.add_ultimate_effects()
    
    def apply_ultimate_style(self):
        """Apply ultimate styling based on type"""
        
        # Base modern style
        base_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.15),
                    stop:0.5 rgba(255,255,255,0.10),
                    stop:1 rgba(255,255,255,0.05));
                border: 1px solid rgba(255,255,255,0.25);
                border-radius: 18px;
                color: white;
                font-family: 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
                font-weight: 600;
                font-size: 11px;
                text-align: center;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.25),
                    stop:0.5 rgba(255,255,255,0.20),
                    stop:1 rgba(255,255,255,0.15));
                border: 1px solid rgba(255,255,255,0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255,255,255,0.05),
                    stop:1 rgba(255,255,255,0.20));
            }
        """
        
        # Special styles for different types
        if self.button_type == "buy":
            base_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #45a049);
                    border: none;
                    border-radius: 25px;
                    color: white;
                    font-family: 'Segoe UI', system-ui, sans-serif;
                    font-weight: 700;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #45a049, stop:1 #4CAF50);
                }
            """
        elif self.button_type == "sell":
            base_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #F44336, stop:1 #da190b);
                    border: none;
                    border-radius: 25px;
                    color: white;
                    font-family: 'Segoe UI', system-ui, sans-serif;
                    font-weight: 700;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #da190b, stop:1 #F44336);
                }
            """
        elif self.button_type == "active":
            base_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(76, 175, 80, 0.3),
                        stop:1 rgba(76, 175, 80, 0.1));
                    border: 2px solid #4CAF50;
                    border-radius: 18px;
                    color: #4CAF50;
                    font-family: 'Segoe UI', system-ui, sans-serif;
                    font-weight: 700;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(76, 175, 80, 0.4),
                        stop:1 rgba(76, 175, 80, 0.2));
                }
            """
        
        self.setStyleSheet(base_style)
    
    def add_ultimate_effects(self):
        """Add ultimate shadow and effects"""
        # Modern shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(6)
        shadow.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(shadow)

class UltimatePanel(QFrame):
    """Ultimate panel with perfect modern design"""
    
    def __init__(self, title="", icon="", panel_type="default"):
        super().__init__()
        self.panel_title = title
        self.panel_icon = icon
        self.panel_type = panel_type
        self.setup_panel()
    
    def setup_panel(self):
        """Setup ultimate panel styling"""
        
        # Base modern panel style
        base_style = """
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.12),
                    stop:0.5 rgba(255,255,255,0.08),
                    stop:1 rgba(255,255,255,0.06));
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 20px;
                padding: 20px;
            }
        """
        
        if self.panel_type == "highlight":
            base_style = """
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(76, 175, 80, 0.15),
                        stop:1 rgba(76, 175, 80, 0.05));
                    border: 1px solid rgba(76, 175, 80, 0.3);
                    border-radius: 20px;
                    padding: 20px;
                }
            """
        
        self.setStyleSheet(base_style)
        
        # Ultimate shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setXOffset(0)
        shadow.setYOffset(8)
        shadow.setColor(QColor(0, 0, 0, 25))
        self.setGraphicsEffect(shadow)

class VIPBigBangUltimateUI(QMainWindow):
    """Ultimate VIP BIG BANG UI with ChatGPT-level design"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 VIP BIG BANG Ultimate - ChatGPT Style")
        self.setGeometry(100, 100, 1350, 900)
        
        # Ultimate typography setup
        self.setup_ultimate_fonts()
        
        # Ultimate gradient background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:0.2 #1a1a2e, stop:0.5 #16213e, 
                    stop:0.8 #0f3460, stop:1 #533483);
                color: white;
            }
            QLabel {
                color: white;
                font-family: 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', system-ui, sans-serif;
                font-weight: 500;
            }
            QWidget {
                font-family: 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
            }
        """)
        
        self.setup_ui()
    
    def setup_ultimate_fonts(self):
        """Setup ultimate typography system"""
        # Modern font stack
        app_font = QFont("Segoe UI", 10)
        app_font.setWeight(QFont.Weight.Medium)
        app_font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
        QApplication.instance().setFont(app_font)
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Ultimate layout with perfect spacing
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(20)
        
        # Ultimate header
        self.create_ultimate_header(main_layout)
        
        # Ultimate content with perfect proportions
        content_layout = QHBoxLayout()
        content_layout.setSpacing(25)
        
        self.create_ultimate_left_panel(content_layout)
        self.create_ultimate_center_panel(content_layout)
        self.create_ultimate_right_panel(content_layout)
        
        main_layout.addLayout(content_layout)

    def create_ultimate_header(self, main_layout):
        """Ultimate header with perfect design"""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)

        # Left - Currency pairs with ultimate design
        currency_layout = QHBoxLayout()

        # BUG/USD active button with perfect styling
        bug_btn = UltimateButton("BUG/USD", "✓", "active", (130, 50))
        currency_layout.addWidget(bug_btn)

        # Other currency pairs with perfect spacing
        for pair in ["GBP/USD", "EUR/JPY", "LIVE"]:
            btn = UltimateButton(pair, "", "default", (110, 50))
            currency_layout.addWidget(btn)

        header_layout.addLayout(currency_layout)
        header_layout.addStretch()

        # Center - Ultimate title with perfect typography
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(0)

        main_title = QLabel("VIP BIG BANG")
        main_title.setStyleSheet("""
            font-size: 42px;
            font-weight: 800;
            color: white;
            letter-spacing: 4px;
            margin: 0;
            padding: 0;
        """)
        main_title.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(main_title)

        subtitle = QLabel("Ultimate Trading System")
        subtitle.setStyleSheet("""
            font-size: 14px;
            font-weight: 500;
            color: rgba(255,255,255,0.7);
            letter-spacing: 2px;
            margin: 0;
            padding: 0;
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(subtitle)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        # Right - Ultimate controls with perfect spacing
        right_layout = QHBoxLayout()
        right_layout.setSpacing(12)

        # Mode buttons with perfect states
        modes = [("OTC", False), ("LIVE", True), ("DEMO", False)]
        for mode, active in modes:
            btn = UltimateButton(mode, "", "active" if active else "default", (90, 50))
            right_layout.addWidget(btn)

        # Separator
        separator = QLabel("|")
        separator.setStyleSheet("""
            color: rgba(255,255,255,0.3);
            font-size: 20px;
            margin: 0 10px;
        """)
        right_layout.addWidget(separator)

        # BUY label with perfect typography
        buy_label = QLabel("BUY")
        buy_label.setStyleSheet("""
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin: 0 15px;
            letter-spacing: 1px;
        """)
        right_layout.addWidget(buy_label)

        # Ultimate BUY/SELL buttons
        buy_btn = UltimateButton("BUY", "", "buy", (110, 50))
        right_layout.addWidget(buy_btn)

        sell_btn = UltimateButton("SELL", "", "sell", (110, 50))
        right_layout.addWidget(sell_btn)

        # Ultimate menu button
        menu_btn = UltimateButton("≡", "", "default", (60, 50))
        right_layout.addWidget(menu_btn)

        header_layout.addLayout(right_layout)
        main_layout.addLayout(header_layout)

    def create_ultimate_left_panel(self, content_layout):
        """Ultimate left panel with perfect typography"""
        left_widget = QWidget()
        left_widget.setFixedWidth(260)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(25)

        # Manual Trading with ultimate design
        manual_panel = UltimatePanel("Manual Trading", "🖱️")
        manual_layout = QVBoxLayout(manual_panel)

        # Perfect header
        header_layout = QHBoxLayout()
        icon_label = QLabel("🖱️")
        icon_label.setStyleSheet("font-size: 26px; margin-right: 12px;")
        header_layout.addWidget(icon_label)

        title_label = QLabel("Manual Trading")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: 700;
            color: white;
            letter-spacing: 0.5px;
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        manual_layout.addLayout(header_layout)

        # Ultimate toggle switch
        toggle_container = QWidget()
        toggle_container.setFixedHeight(55)
        toggle_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 27px;
                margin: 15px 40px;
            }
        """)

        # Add ON label
        toggle_layout = QHBoxLayout(toggle_container)
        toggle_layout.setContentsMargins(20, 0, 20, 0)

        on_label = QLabel("ON")
        on_label.setStyleSheet("""
            color: white;
            font-size: 16px;
            font-weight: 700;
            letter-spacing: 2px;
        """)
        on_label.setAlignment(Qt.AlignCenter)
        toggle_layout.addWidget(on_label)

        manual_layout.addWidget(toggle_container)
        left_layout.addWidget(manual_panel)

        # Ultimate Account Summary
        account_panel = UltimatePanel("Account", "💰", "highlight")
        account_layout = QVBoxLayout(account_panel)

        account_title = QLabel("Account Summary")
        account_title.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: rgba(255,255,255,0.8);
            margin-bottom: 12px;
            letter-spacing: 0.5px;
        """)
        account_layout.addWidget(account_title)

        # Ultimate balance display
        balance_container = QWidget()
        balance_layout = QVBoxLayout(balance_container)
        balance_layout.setContentsMargins(0, 0, 0, 0)
        balance_layout.setSpacing(8)

        balance_label = QLabel("$1,251.76")
        balance_label.setStyleSheet("""
            font-size: 36px;
            font-weight: 800;
            color: #4CAF50;
            letter-spacing: 2px;
            margin: 0;
        """)
        balance_layout.addWidget(balance_label)

        # Perfect profit indicator
        profit_container = QWidget()
        profit_layout = QHBoxLayout(profit_container)
        profit_layout.setContentsMargins(0, 0, 0, 0)
        profit_layout.setSpacing(8)

        profit_icon = QLabel("↗")
        profit_icon.setStyleSheet("""
            font-size: 18px;
            color: #4CAF50;
            font-weight: 800;
        """)
        profit_layout.addWidget(profit_icon)

        profit_text = QLabel("+$251.76 (25.17%)")
        profit_text.setStyleSheet("""
            font-size: 15px;
            font-weight: 700;
            color: #4CAF50;
            letter-spacing: 0.5px;
        """)
        profit_layout.addWidget(profit_text)
        profit_layout.addStretch()

        balance_layout.addWidget(profit_container)
        account_layout.addWidget(balance_container)
        left_layout.addWidget(account_panel)

        # Ultimate AutoTrade panel
        autotrade_panel = UltimatePanel("AutoTrade", "🤖")
        autotrade_layout = QVBoxLayout(autotrade_panel)

        # Perfect header with status
        autotrade_header = QHBoxLayout()
        autotrade_icon = QLabel("🤖")
        autotrade_icon.setStyleSheet("font-size: 22px; margin-right: 10px;")
        autotrade_header.addWidget(autotrade_icon)

        autotrade_title = QLabel("AutoTrade")
        autotrade_title.setStyleSheet("""
            font-size: 18px;
            font-weight: 700;
            color: white;
            letter-spacing: 0.5px;
        """)
        autotrade_header.addWidget(autotrade_title)

        # Perfect status badge
        status_badge = QLabel("ON")
        status_badge.setStyleSheet("""
            background: #4CAF50;
            color: white;
            font-size: 11px;
            font-weight: 800;
            padding: 6px 12px;
            border-radius: 12px;
            letter-spacing: 1px;
        """)
        autotrade_header.addWidget(status_badge)
        autotrade_header.addStretch()
        autotrade_layout.addLayout(autotrade_header)

        # Perfect stats with ultimate typography
        stats_data = [
            ("Trade Amount", "$5.00", "rgba(255,255,255,0.8)"),
            ("Session P/L", "+$10.00", "#4CAF50"),
            ("Win Rate", "87%", "#4CAF50"),
            ("Total Trades", "15", "rgba(255,255,255,0.8)")
        ]

        for label, value, color in stats_data:
            stat_container = QWidget()
            stat_layout = QHBoxLayout(stat_container)
            stat_layout.setContentsMargins(0, 4, 0, 4)

            stat_label = QLabel(label + ":")
            stat_label.setStyleSheet(f"""
                font-size: 13px;
                color: rgba(255,255,255,0.7);
                font-weight: 500;
                letter-spacing: 0.3px;
            """)
            stat_layout.addWidget(stat_label)

            stat_value = QLabel(value)
            stat_value.setStyleSheet(f"""
                font-size: 13px;
                color: {color};
                font-weight: 700;
                letter-spacing: 0.5px;
            """)
            stat_layout.addStretch()
            stat_layout.addWidget(stat_value)

            autotrade_layout.addWidget(stat_container)

        left_layout.addWidget(autotrade_panel)
        left_layout.addStretch()
        content_layout.addWidget(left_widget)

    def create_ultimate_center_panel(self, content_layout):
        """Ultimate center panel - placeholder"""
        center_widget = QLabel("CENTER PANEL")
        center_widget.setAlignment(Qt.AlignCenter)
        center_widget.setStyleSheet("font-size: 24px; color: white;")
        content_layout.addWidget(center_widget, 2)

    def create_ultimate_right_panel(self, content_layout):
        """Ultimate right panel with perfect buttons"""
        right_widget = QWidget()
        right_widget.setFixedWidth(260)
        right_layout = QGridLayout(right_widget)
        right_layout.setSpacing(18)

        # Ultimate control buttons with perfect design
        buttons_data = [
            (0, 0, "🚀", "AutoTrade"),
            (0, 1, "✓", "Confirm Mode"),
            (1, 0, "🎯", "Signal Mode"),
            (1, 1, "🔥", "Heatmap"),
            (2, 0, "📊", "Economic News"),
            (2, 1, "😊", "Brothers Can"),
            (3, 0, "⚙️", "Settings"),
            (3, 1, "🔒", "Security")
        ]

        for row, col, icon, text in buttons_data:
            btn = UltimateButton(text, icon, "default", (110, 110))
            right_layout.addWidget(btn, row, col)

        content_layout.addWidget(right_widget)

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Ultimate application setup
    app.setStyle('Fusion')
    app.setApplicationName("VIP BIG BANG Ultimate")
    app.setApplicationVersion("2.0.0")

    window = VIPBigBangUltimateUI()
    window.show()
    sys.exit(app.exec())
