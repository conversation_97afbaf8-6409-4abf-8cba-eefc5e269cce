import sys
from PySide6.QtWidgets import (QApp<PERSON>, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                               QFrame, QProgressBar, QSlider)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QPainter, QColor, QLinearGradient

class AnimatedButton(QPushButton):
    def __init__(self, text, icon="", color="#4CAF50"):
        super().__init__()
        self.setText(f"{icon}\n{text}")
        self.color = color
        self.setFixedSize(85, 85)
        self.setStyleSheet(f"""
            QPushButton {{
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 15px;
                color: white;
                font-size: 10px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background: {color};
                border: 1px solid {color};
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: {color};
                transform: scale(0.95);
            }}
        """)

class LiveChart(QWidget):
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(250)
        self.price_data = [1.07320, 1.07325, 1.07329, 1.07327, 1.07330]
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_chart)
        self.timer.start(1000)  # Update every second
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Background
        painter.fillRect(self.rect(), QColor(0, 0, 0, 80))
        
        # Draw candlesticks simulation
        width = self.width()
        height = self.height()
        
        # Draw grid
        painter.setPen(QColor(255, 255, 255, 30))
        for i in range(5):
            y = height * i / 5
            painter.drawLine(0, y, width, y)
        
        for i in range(10):
            x = width * i / 10
            painter.drawLine(x, 0, x, height)
        
        # Draw price line
        painter.setPen(QColor(76, 175, 80, 200))
        painter.setBrush(QColor(76, 175, 80, 100))
        
        points = []
        for i, price in enumerate(self.price_data):
            x = width * i / (len(self.price_data) - 1)
            y = height - (price - 1.07300) * height / 0.0050
            points.append((x, y))
        
        # Draw line chart
        for i in range(len(points) - 1):
            painter.drawLine(points[i][0], points[i][1], points[i+1][0], points[i+1][1])
            
    def update_chart(self):
        # Simulate price movement
        import random
        last_price = self.price_data[-1]
        new_price = last_price + random.uniform(-0.0005, 0.0005)
        self.price_data.append(new_price)
        if len(self.price_data) > 20:
            self.price_data.pop(0)
        self.update()

class VIPBigBangEnhancedUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 VIP BIG BANG Enterprise Trading System")
        self.setGeometry(100, 100, 1200, 800)
        
        # Enhanced gradient background
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4A2C7A, stop:0.3 #3D1A6B, stop:0.7 #2D1B69, stop:1 #1A0E3D);
                color: white;
            }
        """)
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # Create enhanced header
        self.create_enhanced_header(main_layout)
        
        # Create main content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)
        
        self.create_enhanced_left_panel(content_layout)
        self.create_enhanced_center_panel(content_layout)
        self.create_enhanced_right_panel(content_layout)
        
        main_layout.addLayout(content_layout)
    
    def create_enhanced_header(self, main_layout):
        """Enhanced header with animations"""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        # Left - Currency pairs with status indicators
        currency_layout = QHBoxLayout()
        
        # BUG/USD with animated checkmark
        bug_btn = QPushButton("✓ BUG/USD")
        bug_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(76, 175, 80, 0.4), stop:1 rgba(76, 175, 80, 0.2));
                border: 2px solid #4CAF50;
                border-radius: 10px;
                padding: 10px 15px;
                color: #4CAF50;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(76, 175, 80, 0.6), stop:1 rgba(76, 175, 80, 0.3));
            }
        """)
        currency_layout.addWidget(bug_btn)
        
        # Other pairs
        pairs = ["GBP/USD", "EUR/JPY", "LIVE"]
        for pair in pairs:
            btn = QPushButton(pair)
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255,255,255,0.1);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 8px;
                    padding: 10px 15px;
                    color: white;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.5);
                }
            """)
            currency_layout.addWidget(btn)
        
        header_layout.addLayout(currency_layout)
        header_layout.addStretch()
        
        # Center - Enhanced title with glow effect
        title = QLabel("VIP BIG BANG")
        title.setStyleSheet("""
            font-size: 32px; 
            font-weight: bold; 
            color: white;
            letter-spacing: 3px;
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
        """)
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Right - Enhanced controls
        right_layout = QHBoxLayout()
        
        # Mode buttons with status
        modes = [("OTC", False), ("LIVE", True), ("DEMO", False)]
        for mode, active in modes:
            btn = QPushButton(mode)
            if active:
                btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(76, 175, 80, 0.4), stop:1 rgba(76, 175, 80, 0.2));
                        border: 2px solid #4CAF50;
                        border-radius: 10px;
                        padding: 10px 18px;
                        color: #4CAF50;
                        font-weight: bold;
                        font-size: 12px;
                    }
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        background: rgba(255,255,255,0.1);
                        border: 1px solid rgba(255,255,255,0.3);
                        border-radius: 8px;
                        padding: 10px 18px;
                        color: white;
                        font-size: 12px;
                    }
                """)
            right_layout.addWidget(btn)
        
        # Enhanced BUY/SELL buttons
        buy_label = QLabel("BUY")
        buy_label.setStyleSheet("color: white; font-size: 14px; margin: 0 10px;")
        right_layout.addWidget(buy_label)
        
        buy_btn = QPushButton("BUY")
        buy_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                border: none;
                border-radius: 25px;
                padding: 12px 30px;
                font-weight: bold;
                color: white;
                font-size: 14px;
                min-width: 90px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #4CAF50);
                transform: scale(1.05);
            }
            QPushButton:pressed {
                transform: scale(0.95);
            }
        """)
        right_layout.addWidget(buy_btn)
        
        sell_btn = QPushButton("SELL")
        sell_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F44336, stop:1 #da190b);
                border: none;
                border-radius: 25px;
                padding: 12px 30px;
                font-weight: bold;
                color: white;
                font-size: 14px;
                min-width: 90px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #da190b, stop:1 #F44336);
                transform: scale(1.05);
            }
            QPushButton:pressed {
                transform: scale(0.95);
            }
        """)
        right_layout.addWidget(sell_btn)
        
        # Menu button
        menu_btn = QPushButton("≡")
        menu_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 10px;
                padding: 10px 15px;
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.2);
            }
        """)
        right_layout.addWidget(menu_btn)
        
        header_layout.addLayout(right_layout)
        main_layout.addLayout(header_layout)
    
    def setup_animations(self):
        """Setup UI animations"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.animate_elements)
        self.timer.start(2000)  # Animate every 2 seconds
        
    def animate_elements(self):
        """Animate various UI elements"""
        pass  # Will add specific animations
    
    def create_enhanced_left_panel(self, content_layout):
        """Enhanced left panel with animations"""
        left_widget = QWidget()
        left_widget.setFixedWidth(220)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)

        # Manual Trading with animated toggle
        manual_frame = self.create_rounded_frame()
        manual_layout = QVBoxLayout(manual_frame)

        manual_header = QHBoxLayout()
        manual_icon = QLabel("🖱️")
        manual_icon.setStyleSheet("font-size: 24px;")
        manual_header.addWidget(manual_icon)

        manual_title = QLabel("Manual Trading")
        manual_title.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        manual_header.addWidget(manual_title)
        manual_header.addStretch()
        manual_layout.addLayout(manual_header)

        # Enhanced toggle switch
        toggle_container = QWidget()
        toggle_container.setFixedHeight(50)
        toggle_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 25px;
                margin: 5px 20px;
            }
        """)
        manual_layout.addWidget(toggle_container)

        left_layout.addWidget(manual_frame)

        # Enhanced Account Summary
        account_frame = self.create_rounded_frame()
        account_layout = QVBoxLayout(account_frame)

        account_title = QLabel("Account Summary")
        account_title.setStyleSheet("font-size: 14px; color: rgba(255,255,255,0.8);")
        account_layout.addWidget(account_title)

        balance_label = QLabel("$1,251.76")
        balance_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: #4CAF50;
            margin: 8px 0;
            text-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        """)
        account_layout.addWidget(balance_label)

        # Profit indicator
        profit_label = QLabel("↗ +$251.76 (25.17%)")
        profit_label.setStyleSheet("font-size: 12px; color: #4CAF50; font-weight: bold;")
        account_layout.addWidget(profit_label)

        left_layout.addWidget(account_frame)

        # Enhanced AutoTrade section
        autotrade_frame = self.create_rounded_frame()
        autotrade_layout = QVBoxLayout(autotrade_frame)

        autotrade_header = QHBoxLayout()
        autotrade_icon = QLabel("🤖")
        autotrade_icon.setStyleSheet("font-size: 20px;")
        autotrade_header.addWidget(autotrade_icon)

        autotrade_title = QLabel("AutoTrade ON")
        autotrade_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #4CAF50;")
        autotrade_header.addWidget(autotrade_title)
        autotrade_header.addStretch()
        autotrade_layout.addLayout(autotrade_header)

        trade_info = QLabel("Trade Amount: $5.00")
        trade_info.setStyleSheet("font-size: 12px; color: rgba(255,255,255,0.8);")
        autotrade_layout.addWidget(trade_info)

        profit_info = QLabel("Session P/L: +$10.00")
        profit_info.setStyleSheet("font-size: 12px; color: #4CAF50; font-weight: bold;")
        autotrade_layout.addWidget(profit_info)

        trades_info = QLabel("Trades: 15 | Win Rate: 87%")
        trades_info.setStyleSheet("font-size: 11px; color: rgba(255,255,255,0.7);")
        autotrade_layout.addWidget(trades_info)

        left_layout.addWidget(autotrade_frame)

        # Enhanced PulseBar
        pulsebar_frame = self.create_rounded_frame()
        pulsebar_layout = QVBoxLayout(pulsebar_frame)

        pulsebar_header = QHBoxLayout()
        pulsebar_icon = QLabel("📊")
        pulsebar_icon.setStyleSheet("font-size: 18px;")
        pulsebar_header.addWidget(pulsebar_icon)

        pulsebar_title = QLabel("Market PulseBar")
        pulsebar_title.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        pulsebar_header.addWidget(pulsebar_title)
        pulsebar_header.addStretch()
        pulsebar_layout.addLayout(pulsebar_header)

        # Animated color bars
        colors = ["#FF4444", "#FF6644", "#FF8844", "#FFAA44", "#FFDD44", "#DDFF44", "#88FF44"]
        for i, color in enumerate(colors):
            bar = QWidget()
            bar.setFixedHeight(8)
            bar.setStyleSheet(f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color}, stop:1 rgba(255,255,255,0.1));
                border-radius: 4px;
                margin: 2px 0;
            """)
            pulsebar_layout.addWidget(bar)

        # Market strength indicator
        strength_label = QLabel("Market Strength: STRONG 💪")
        strength_label.setStyleSheet("font-size: 11px; color: #4CAF50; font-weight: bold; margin-top: 5px;")
        pulsebar_layout.addWidget(strength_label)

        left_layout.addWidget(pulsebar_frame)
        left_layout.addStretch()
        content_layout.addWidget(left_widget)

    def create_enhanced_center_panel(self, content_layout):
        """Enhanced center panel with live chart"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(12)

        # Enhanced chart area
        chart_frame = self.create_rounded_frame()
        chart_frame.setMinimumHeight(380)
        chart_layout = QVBoxLayout(chart_frame)

        # Price header with alerts
        price_header = QHBoxLayout()

        # Animated alert bell
        alert_bell = QLabel("🔔")
        alert_bell.setStyleSheet("""
            font-size: 28px;
            color: #FFD700;
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
        """)
        price_header.addWidget(alert_bell)

        price_header.addStretch()

        # Enhanced price display
        price_display = QLabel("1.07329")
        price_display.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4CAF50, stop:1 #45a049);
            border-radius: 15px;
            padding: 12px 25px;
            font-size: 20px;
            font-weight: bold;
            color: white;
            text-shadow: 0 0 10px rgba(0,0,0,0.5);
        """)
        price_header.addWidget(price_display)

        # Price movement indicators
        price_details = QVBoxLayout()
        price_labels = [
            ("H: 1.07325", "#FF9800"),
            ("L: 1.07320", "#2196F3"),
            ("O: 1.07320", "#9E9E9E"),
            ("T: 1.07330", "#4CAF50")
        ]

        for text, color in price_labels:
            label = QLabel(text)
            label.setStyleSheet(f"font-size: 11px; color: {color}; font-weight: bold;")
            price_details.addWidget(label)

        price_header.addLayout(price_details)
        chart_layout.addLayout(price_header)

        # Live chart widget
        live_chart = LiveChart()
        chart_layout.addWidget(live_chart)

        center_layout.addWidget(chart_frame)

        # Enhanced bottom indicators
        indicators_layout = QHBoxLayout()
        indicators_layout.setSpacing(12)

        # Enhanced VORTEX
        vortex_frame = self.create_rounded_frame()
        vortex_frame.setFixedWidth(140)
        vortex_layout = QVBoxLayout(vortex_frame)

        vortex_header = QHBoxLayout()
        vortex_icon = QLabel("🌀")
        vortex_icon.setStyleSheet("font-size: 16px;")
        vortex_header.addWidget(vortex_icon)

        vortex_title = QLabel("VORTEX")
        vortex_title.setStyleSheet("font-size: 12px; font-weight: bold; color: white;")
        vortex_header.addWidget(vortex_title)
        vortex_layout.addLayout(vortex_header)

        vortex_wave = QLabel("〰️〰️〰️〰️〰️")
        vortex_wave.setStyleSheet("color: #4CAF50; font-size: 16px;")
        vortex_layout.addWidget(vortex_wave)

        vortex_value = QLabel("0.0436 ↗")
        vortex_value.setStyleSheet("font-size: 12px; color: #4CAF50; font-weight: bold;")
        vortex_layout.addWidget(vortex_value)

        indicators_layout.addWidget(vortex_frame)

        center_layout.addLayout(indicators_layout)
        content_layout.addWidget(center_widget, 2)

    def create_enhanced_right_panel(self, content_layout):
        """Enhanced right panel with animated buttons"""
        right_widget = QWidget()
        right_widget.setFixedWidth(220)
        right_layout = QGridLayout(right_widget)
        right_layout.setSpacing(12)

        # Enhanced control buttons
        buttons_data = [
            (0, 0, "🚀", "AutoTrade", "#4CAF50"),
            (0, 1, "✓", "Confirm Mode", "#9C27B0"),
            (1, 0, "🎯", "Signal Mode", "#FF5722"),
            (1, 1, "🔥", "Heatmap", "#FF9800"),
            (2, 0, "📊", "Economic News", "#2196F3"),
            (2, 1, "😊", "Brothers Can", "#FFC107"),
            (3, 0, "⚙️", "Settings", "#9E9E9E"),
            (3, 1, "🔒", "Security", "#795548")
        ]

        for row, col, icon, text, color in buttons_data:
            btn = AnimatedButton(text, icon, color)
            right_layout.addWidget(btn, row, col)

        content_layout.addWidget(right_widget)

    def create_rounded_frame(self, extra_style=""):
        """Create enhanced rounded frame"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.15), stop:1 rgba(255,255,255,0.05));
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 18px;
                padding: 18px;
                {extra_style}
            }}
        """)
        return frame

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = VIPBigBangEnhancedUI()
    window.show()
    sys.exit(app.exec())
