#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
VIP BIG BANG Project Cleanup Script
Automatically organizes and cleans up the project structure
"""

import os
import shutil
import glob
from pathlib import Path

def create_archive_structure():
    """Create archive directory structure"""
    archive_dirs = [
        "archive",
        "archive/demos",
        "archive/tests", 
        "archive/experiments",
        "archive/ui_variations",
        "archive/quantum_systems",
        "archive/complete_systems",
        "archive/enterprise_systems"
    ]
    
    for dir_path in archive_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"✅ Created: {dir_path}")

def move_files_by_pattern(pattern, destination):
    """Move files matching pattern to destination"""
    files = glob.glob(pattern)
    moved_count = 0
    
    for file_path in files:
        if os.path.isfile(file_path):
            try:
                filename = os.path.basename(file_path)
                dest_path = os.path.join(destination, filename)
                shutil.move(file_path, dest_path)
                print(f"📁 Moved: {filename} → {destination}")
                moved_count += 1
            except Exception as e:
                print(f"❌ Error moving {file_path}: {e}")
    
    return moved_count

def cleanup_project():
    """Main cleanup function"""
    print("🧹 Starting VIP BIG BANG Project Cleanup...")
    print("="*60)
    
    # Create archive structure
    print("\n📁 Creating archive structure...")
    create_archive_structure()
    
    # Move demo files
    print("\n🎮 Moving demo files...")
    demo_patterns = [
        "*demo*.py",
        "demo_*.py", 
        "run_demo.py",
        "run_all_demos.py",
        "live_demo_results.py",
        "component_demo.py",
        "final_demo.py",
        "gradient_demo.py",
        "shadow_demo.py",
        "spacing_demo.py",
        "typography_demo.py",
        "performance_analysis.py",
        "trading_example.py"
    ]
    
    demo_count = 0
    for pattern in demo_patterns:
        demo_count += move_files_by_pattern(pattern, "archive/demos")
    
    print(f"✅ Moved {demo_count} demo files")
    
    # Move test files
    print("\n🧪 Moving test files...")
    test_patterns = [
        "test_*.py",
        "*_test.py",
        "simple_test*.py",
        "timing_test.py",
        "quick_test*.py",
        "basic_test*.py"
    ]
    
    test_count = 0
    for pattern in test_patterns:
        test_count += move_files_by_pattern(pattern, "archive/tests")
    
    print(f"✅ Moved {test_count} test files")
    
    # Move quantum files
    print("\n⚛️ Moving quantum files...")
    quantum_patterns = [
        "quantum_*.py",
        "vip_quantum_*.py",
        "*_quantum_*.py"
    ]
    
    quantum_count = 0
    for pattern in quantum_patterns:
        quantum_count += move_files_by_pattern(pattern, "archive/quantum_systems")
    
    print(f"✅ Moved {quantum_count} quantum files")
    
    # Move UI variations
    print("\n🎨 Moving UI variations...")
    ui_patterns = [
        "cartoon_*.py",
        "vip_cartoon_*.py",
        "gaming_*.py",
        "vip_gaming_*.py",
        "vip_ui_*.py",
        "figma_*.py",
        "qml_*.py",
        "vip_professional_ui.py"
    ]
    
    ui_count = 0
    for pattern in ui_patterns:
        ui_count += move_files_by_pattern(pattern, "archive/ui_variations")
    
    print(f"✅ Moved {ui_count} UI variation files")
    
    # Move ultimate/enterprise files
    print("\n🏢 Moving ultimate/enterprise files...")
    ultimate_patterns = [
        "ultimate_*.py",
        "vip_ultimate_*.py",
        "enterprise_*.py",
        "vip_enterprise_*.py",
        "advanced_*.py",
        "vip_advanced_*.py"
    ]
    
    ultimate_count = 0
    for pattern in ultimate_patterns:
        ultimate_count += move_files_by_pattern(pattern, "archive/enterprise_systems")
    
    print(f"✅ Moved {ultimate_count} ultimate/enterprise files")
    
    # Move complete system variations
    print("\n🔧 Moving complete system variations...")
    complete_patterns = [
        "complete_*.py",
        "vip_complete_*.py",
        "vip_final_*.py",
        "vip_big_bang_complete.py",
        "vip_working_*.py"
    ]
    
    complete_count = 0
    for pattern in complete_patterns:
        complete_count += move_files_by_pattern(pattern, "archive/complete_systems")
    
    print(f"✅ Moved {complete_count} complete system files")
    
    # Move experimental files
    print("\n🔬 Moving experimental files...")
    experimental_patterns = [
        "simple_*.py",
        "direct_*.py", 
        "manual_*.py",
        "persistent_*.py",
        "professional_*.py",
        "current_*.py",
        "connect_to_*.py",
        "extension_installer.py",
        "settings_manager.py",
        "websocket_realtime.py",
        "winrate_95_system.py",
        "ultra_high_winrate_config.py"
    ]
    
    experimental_count = 0
    for pattern in experimental_patterns:
        experimental_count += move_files_by_pattern(pattern, "archive/experiments")
    
    print(f"✅ Moved {experimental_count} experimental files")
    
    # Move Quotex variations (keep only main ones)
    print("\n🌐 Moving Quotex variations...")
    quotex_patterns = [
        "vip_quotex_*.py",  # Keep vip_real_quotex_main.py manually
        "vip_embedded_*.py",
        "vip_offline_*.py",
        "vip_direct_*.py"
    ]
    
    # Manually exclude the main files we want to keep
    keep_files = [
        "vip_real_quotex_main.py",
        "vip_auto_extension_quotex.py"
    ]
    
    quotex_count = 0
    for pattern in quotex_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            filename = os.path.basename(file_path)
            if filename not in keep_files and os.path.isfile(file_path):
                try:
                    dest_path = os.path.join("archive/experiments", filename)
                    shutil.move(file_path, dest_path)
                    print(f"📁 Moved: {filename} → archive/experiments")
                    quotex_count += 1
                except Exception as e:
                    print(f"❌ Error moving {file_path}: {e}")
    
    print(f"✅ Moved {quotex_count} Quotex variation files")
    
    # Summary
    print("\n" + "="*60)
    print("🎉 CLEANUP COMPLETED!")
    print("="*60)
    
    total_moved = demo_count + test_count + quantum_count + ui_count + ultimate_count + complete_count + experimental_count + quotex_count
    print(f"📊 Total files moved: {total_moved}")
    
    # Show remaining files in root
    print("\n📋 Remaining Python files in root:")
    remaining_py_files = glob.glob("*.py")
    for file in sorted(remaining_py_files):
        print(f"  ✅ {file}")
    
    print(f"\n🎯 Root directory now has {len(remaining_py_files)} Python files (target: 5-10)")
    
    if len(remaining_py_files) <= 10:
        print("✅ SUCCESS: Project structure is now clean and organized!")
    else:
        print("⚠️  WARNING: Still too many files in root. Manual review needed.")
    
    print("\n📁 Archive structure created:")
    print("  📂 archive/demos - Demo and example files")
    print("  📂 archive/tests - Test files")
    print("  📂 archive/experiments - Experimental files")
    print("  📂 archive/ui_variations - UI variations")
    print("  📂 archive/quantum_systems - Quantum variations")
    print("  📂 archive/complete_systems - Complete system variations")
    print("  📂 archive/enterprise_systems - Ultimate/enterprise variations")

if __name__ == "__main__":
    try:
        cleanup_project()
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        import traceback
        traceback.print_exc()
