#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG 5-SECOND TRADER
💎 ترید 5 ثانیه‌ای خالص + اجرای فوری
⚡ تحلیل هر 15 ثانیه + ترید 5 ثانیه‌ای
"""

import sys
import os
import time
import random
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class VIP5SecondTrader(QMainWindow):
    """🚀 VIP 5-Second Trader"""
    
    def __init__(self):
        super().__init__()
        
        # Trading state
        self.trading_active = False
        self.analysis_running = False
        self.trade_count = 0
        self.successful_trades = 0
        
        # Setup UI
        self.setup_trader_ui()
        self.setup_trader_styles()
        
        # Auto-start
        QTimer.singleShot(1000, self.auto_start_trading)
    
    def setup_trader_ui(self):
        """🎨 Setup trader UI"""
        self.setWindowTitle("🚀 VIP BIG BANG - 5-Second Trader")
        self.setGeometry(200, 200, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header
        header = self.create_trader_header()
        main_layout.addWidget(header)
        
        # Content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # Left panel - Trading Controls
        left_panel = self.create_trading_controls()
        content_layout.addWidget(left_panel)
        
        # Right panel - Live Trading
        right_panel = self.create_live_trading_panel()
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
        
        # Status bar
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("🚀 5-Second Trader Ready")
    
    def create_trader_header(self):
        """🎨 Create trader header"""
        header = QFrame()
        header.setProperty("class", "trader-header")
        header.setFixedHeight(120)
        
        layout = QVBoxLayout(header)
        layout.setContentsMargins(30, 15, 30, 15)
        layout.setSpacing(10)
        
        # Title
        title = QLabel("🚀 VIP BIG BANG 5-SECOND TRADER")
        title.setProperty("class", "trader-title")
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("⚡ تحلیل هر 15 ثانیه + ترید 5 ثانیه‌ای فوری")
        subtitle.setProperty("class", "trader-subtitle")
        layout.addWidget(subtitle)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.start_trading_btn = QPushButton("🚀 START 5-SECOND TRADING")
        self.start_trading_btn.setProperty("class", "start-trading-btn")
        self.start_trading_btn.clicked.connect(self.start_5_second_trading)
        controls_layout.addWidget(self.start_trading_btn)
        
        self.stop_trading_btn = QPushButton("🛑 STOP TRADING")
        self.stop_trading_btn.setProperty("class", "stop-trading-btn")
        self.stop_trading_btn.clicked.connect(self.stop_5_second_trading)
        self.stop_trading_btn.setEnabled(False)
        controls_layout.addWidget(self.stop_trading_btn)
        
        controls_layout.addStretch()
        
        self.next_analysis_label = QLabel("⏰ Next Analysis: 15s")
        self.next_analysis_label.setProperty("class", "next-analysis")
        controls_layout.addWidget(self.next_analysis_label)
        
        layout.addLayout(controls_layout)
        
        return header
    
    def create_trading_controls(self):
        """🎮 Create trading controls"""
        panel = QFrame()
        panel.setProperty("class", "trading-controls")
        panel.setFixedWidth(400)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Trading Settings
        settings_group = QGroupBox("⚙️ 5-Second Trading Settings")
        settings_layout = QVBoxLayout(settings_group)
        
        # Asset and amount
        trade_settings = QHBoxLayout()
        
        asset_layout = QVBoxLayout()
        asset_layout.addWidget(QLabel("📈 Asset:"))
        self.asset_combo = QComboBox()
        self.asset_combo.addItems(["EUR/USD", "GBP/USD", "USD/JPY", "AUD/USD"])
        asset_layout.addWidget(self.asset_combo)
        trade_settings.addLayout(asset_layout)
        
        amount_layout = QVBoxLayout()
        amount_layout.addWidget(QLabel("💰 Amount:"))
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(1.0, 1000.0)
        self.amount_spin.setValue(10.0)
        self.amount_spin.setSuffix(" $")
        amount_layout.addWidget(self.amount_spin)
        trade_settings.addLayout(amount_layout)
        
        settings_layout.addLayout(trade_settings)
        
        # Trade duration (fixed at 5 seconds)
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("⏱️ Trade Duration:"))
        self.duration_label = QLabel("5 seconds (Fixed)")
        self.duration_label.setProperty("class", "duration-fixed")
        duration_layout.addWidget(self.duration_label)
        settings_layout.addLayout(duration_layout)
        
        # Auto-trade settings
        self.auto_trade_check = QCheckBox("🤖 Auto 5-Second Trading")
        self.auto_trade_check.setChecked(True)
        settings_layout.addWidget(self.auto_trade_check)
        
        self.fast_mode_check = QCheckBox("⚡ Ultra Fast Mode")
        self.fast_mode_check.setChecked(True)
        settings_layout.addWidget(self.fast_mode_check)
        
        layout.addWidget(settings_group)
        
        # Current Signal
        signal_group = QGroupBox("🎯 Current Signal")
        signal_layout = QVBoxLayout(signal_group)
        
        self.current_signal = QLabel("🎯 Signal: Waiting...")
        self.current_signal.setProperty("class", "current-signal")
        signal_layout.addWidget(self.current_signal)
        
        self.signal_strength = QLabel("💪 Strength: 0%")
        signal_layout.addWidget(self.signal_strength)
        
        self.trade_recommendation = QLabel("📊 Recommendation: Analyzing...")
        signal_layout.addWidget(self.trade_recommendation)
        
        layout.addWidget(signal_group)
        
        # Manual Trading
        manual_group = QGroupBox("🎮 Manual 5-Second Trading")
        manual_layout = QVBoxLayout(manual_group)
        
        manual_buttons = QHBoxLayout()
        
        self.call_5s_btn = QPushButton("📈 CALL 5s")
        self.call_5s_btn.setProperty("class", "call-5s-btn")
        self.call_5s_btn.clicked.connect(lambda: self.manual_5s_trade("CALL"))
        manual_buttons.addWidget(self.call_5s_btn)
        
        self.put_5s_btn = QPushButton("📉 PUT 5s")
        self.put_5s_btn.setProperty("class", "put-5s-btn")
        self.put_5s_btn.clicked.connect(lambda: self.manual_5s_trade("PUT"))
        manual_buttons.addWidget(self.put_5s_btn)
        
        manual_layout.addLayout(manual_buttons)
        
        layout.addWidget(manual_group)
        
        return panel
    
    def create_live_trading_panel(self):
        """📊 Create live trading panel"""
        panel = QFrame()
        panel.setProperty("class", "live-trading-panel")
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Live Market Data
        market_group = QGroupBox("📊 Live Market Data")
        market_layout = QVBoxLayout(market_group)
        
        self.current_price = QLabel("💰 EUR/USD: 1.07500")
        self.current_price.setProperty("class", "current-price")
        market_layout.addWidget(self.current_price)
        
        self.price_change = QLabel("📈 Change: +0.00%")
        market_layout.addWidget(self.price_change)
        
        self.market_volatility = QLabel("📊 Volatility: Medium")
        market_layout.addWidget(self.market_volatility)
        
        layout.addWidget(market_group)
        
        # Trading Statistics
        stats_group = QGroupBox("📈 5-Second Trading Stats")
        stats_layout = QVBoxLayout(stats_group)
        
        self.trades_today = QLabel("📊 5s Trades Today: 0")
        stats_layout.addWidget(self.trades_today)
        
        self.success_rate = QLabel("✅ Success Rate: 0%")
        stats_layout.addWidget(self.success_rate)
        
        self.avg_profit = QLabel("💰 Avg Profit: $0.00")
        stats_layout.addWidget(self.avg_profit)
        
        self.total_profit = QLabel("💎 Total Profit: $0.00")
        stats_layout.addWidget(self.total_profit)
        
        layout.addWidget(stats_group)
        
        # Live 5-Second Trades
        trades_group = QGroupBox("🚀 Live 5-Second Trades")
        trades_layout = QVBoxLayout(trades_group)
        
        self.trades_list = QTextEdit()
        self.trades_list.setProperty("class", "trades-list")
        self.trades_list.setFixedHeight(200)
        self.trades_list.setPlainText("🚀 5-second trades will appear here...")
        trades_layout.addWidget(self.trades_list)
        
        layout.addWidget(trades_group)
        
        # Trading Logs
        logs_group = QGroupBox("📝 Trading Logs")
        logs_layout = QVBoxLayout(logs_group)
        
        self.trading_logs = QTextEdit()
        self.trading_logs.setProperty("class", "trading-logs")
        self.trading_logs.setFixedHeight(150)
        self.trading_logs.setPlainText("📝 Trading logs will appear here...")
        logs_layout.addWidget(self.trading_logs)
        
        layout.addWidget(logs_group)
        
        return panel
    
    def setup_trader_styles(self):
        """🎨 Setup trader styles"""
        style = """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #000000, stop:0.5 #1a1a2e, stop:1 #16213e);
            color: white;
        }
        
        .trader-header {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #FF4500, stop:0.5 #FF6347, stop:1 #FF4500);
            border: 3px solid #FFD700;
            border-radius: 20px;
        }
        
        .trader-title {
            font-size: 32px;
            font-weight: bold;
            color: white;
            text-align: center;
        }
        
        .trader-subtitle {
            font-size: 16px;
            color: white;
            text-align: center;
            font-style: italic;
        }
        
        .start-trading-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #32CD32, stop:1 #228B22);
            color: white;
            font-weight: bold;
            padding: 18px 30px;
            border-radius: 18px;
            font-size: 18px;
            border: none;
        }
        
        .stop-trading-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #FF6B6B, stop:1 #CC5555);
            color: white;
            font-weight: bold;
            padding: 18px 30px;
            border-radius: 18px;
            font-size: 18px;
            border: none;
        }
        
        .next-analysis {
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: rgba(0,0,0,0.5);
            padding: 8px 15px;
            border-radius: 12px;
        }
        
        .trading-controls, .live-trading-panel {
            background: rgba(30, 30, 60, 0.9);
            border: 2px solid #FF4500;
            border-radius: 15px;
        }
        
        .duration-fixed {
            font-size: 14px;
            font-weight: bold;
            color: #FF4500;
            background: rgba(255, 69, 0, 0.2);
            padding: 5px 10px;
            border-radius: 8px;
        }
        
        .current-signal {
            font-size: 20px;
            font-weight: bold;
            color: #32CD32;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 10px;
            text-align: center;
        }
        
        .current-price {
            font-size: 18px;
            font-weight: bold;
            color: #32CD32;
            background: rgba(0,0,0,0.5);
            padding: 8px;
            border-radius: 8px;
            text-align: center;
        }
        
        .call-5s-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #32CD32, stop:1 #228B22);
            color: white;
            font-weight: bold;
            padding: 15px;
            border-radius: 12px;
            font-size: 16px;
            border: none;
        }
        
        .put-5s-btn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #FF4444, stop:1 #CC2222);
            color: white;
            font-weight: bold;
            padding: 15px;
            border-radius: 12px;
            font-size: 16px;
            border: none;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #FF4500;
            border-radius: 12px;
            margin-top: 15px;
            padding-top: 15px;
            color: #FFD700;
            font-size: 14px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
        }
        
        .trades-list, .trading-logs {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #FF4500;
            border-radius: 10px;
            color: #00FF00;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            padding: 5px;
        }
        
        QPushButton {
            background: rgba(75, 50, 150, 0.8);
            color: white;
            font-weight: bold;
            padding: 8px;
            border-radius: 8px;
            border: 2px solid #FF4500;
            font-size: 12px;
        }
        
        QPushButton:hover {
            background: rgba(120, 80, 200, 0.9);
            border: 2px solid #FFD700;
        }
        
        QDoubleSpinBox, QComboBox {
            background: rgba(50, 50, 100, 0.8);
            color: white;
            border: 2px solid #FF4500;
            border-radius: 8px;
            padding: 5px;
            font-size: 12px;
        }
        
        QCheckBox {
            color: white;
            font-weight: bold;
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 15px;
            height: 15px;
        }
        
        QCheckBox::indicator:checked {
            background: #32CD32;
            border: 2px solid #228B22;
            border-radius: 8px;
        }
        
        QCheckBox::indicator:unchecked {
            background: rgba(100, 100, 100, 0.5);
            border: 2px solid #666666;
            border-radius: 8px;
        }
        """
        
        self.setStyleSheet(style)

    def log_trading_message(self, message: str):
        """📝 Add message to trading logs"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.trading_logs.append(log_entry)
        print(f"TRADING LOG: {log_entry}")

    def auto_start_trading(self):
        """🚀 Auto-start trading system"""
        try:
            self.log_trading_message("🚀 Auto-starting 5-Second Trading System...")

            # Start price monitoring
            self.start_price_monitoring()

            # Update UI
            self.current_signal.setText("🎯 Signal: Ready for 5s trading")
            self.signal_strength.setText("💪 Strength: Waiting for analysis...")
            self.trade_recommendation.setText("📊 Recommendation: System ready")

            self.log_trading_message("✅ 5-Second Trading System ready")
            self.status_bar.showMessage("✅ 5-Second Trading System ready - Click START to begin")

        except Exception as e:
            self.log_trading_message(f"❌ Auto-start error: {e}")

    def start_5_second_trading(self):
        """🚀 Start 5-second trading"""
        try:
            self.log_trading_message("🚀 Starting 5-Second Trading...")
            self.trading_active = True
            self.analysis_running = True

            # Update UI
            self.start_trading_btn.setEnabled(False)
            self.stop_trading_btn.setEnabled(True)

            # Start analysis timer (every 15 seconds)
            self.analysis_timer = QTimer()
            self.analysis_timer.timeout.connect(self.perform_15s_analysis)
            self.analysis_timer.start(15000)  # 15 seconds

            # Start countdown timer
            self.countdown_timer = QTimer()
            self.countdown_timer.timeout.connect(self.update_countdown)
            self.countdown_timer.start(1000)  # 1 second
            self.countdown = 15

            # Perform first analysis immediately
            QTimer.singleShot(2000, self.perform_15s_analysis)

            self.log_trading_message("✅ 5-Second Trading started - Analysis every 15s")
            self.status_bar.showMessage("🚀 5-Second Trading ACTIVE - Next analysis in 15s")

        except Exception as e:
            self.log_trading_message(f"❌ Trading start error: {e}")

    def stop_5_second_trading(self):
        """🛑 Stop 5-second trading"""
        try:
            self.log_trading_message("🛑 Stopping 5-Second Trading...")
            self.trading_active = False
            self.analysis_running = False

            # Update UI
            self.start_trading_btn.setEnabled(True)
            self.stop_trading_btn.setEnabled(False)

            # Stop timers
            if hasattr(self, 'analysis_timer'):
                self.analysis_timer.stop()
            if hasattr(self, 'countdown_timer'):
                self.countdown_timer.stop()

            # Reset displays
            self.current_signal.setText("🎯 Signal: Stopped")
            self.signal_strength.setText("💪 Strength: 0%")
            self.trade_recommendation.setText("📊 Recommendation: Trading stopped")

            self.log_trading_message("✅ 5-Second Trading stopped")
            self.status_bar.showMessage("🛑 5-Second Trading stopped")

        except Exception as e:
            self.log_trading_message(f"❌ Trading stop error: {e}")

    def update_countdown(self):
        """⏰ Update countdown to next analysis"""
        if self.analysis_running:
            self.countdown -= 1
            if self.countdown <= 0:
                self.countdown = 15

            self.next_analysis_label.setText(f"⏰ Next Analysis: {self.countdown}s")

    def perform_15s_analysis(self):
        """🧠 Perform 15-second analysis for 5-second trading"""
        if not self.analysis_running:
            return

        try:
            self.log_trading_message("🧠 Performing 15s analysis for 5s trading...")

            # Simulate market analysis
            signals = ['CALL', 'PUT', 'NEUTRAL']
            signal = random.choice(signals)
            confidence = random.uniform(0.70, 0.95)

            # Update signal display
            self.current_signal.setText(f"🎯 Signal: {signal}")
            self.signal_strength.setText(f"💪 Strength: {confidence*100:.1f}%")

            # Set signal color
            if signal == 'CALL':
                self.current_signal.setStyleSheet("color: #32CD32; background: rgba(50, 205, 50, 0.2); padding: 10px; border-radius: 10px; text-align: center;")
            elif signal == 'PUT':
                self.current_signal.setStyleSheet("color: #FF4444; background: rgba(255, 68, 68, 0.2); padding: 10px; border-radius: 10px; text-align: center;")
            else:
                self.current_signal.setStyleSheet("color: #FFD700; background: rgba(255, 215, 0, 0.2); padding: 10px; border-radius: 10px; text-align: center;")

            # Determine trade recommendation
            if signal != 'NEUTRAL' and confidence >= 0.80:
                recommendation = "🚀 EXECUTE 5-SECOND TRADE"
                rec_color = "#32CD32"

                # Auto-execute if enabled
                if self.auto_trade_check.isChecked():
                    self.execute_5s_auto_trade(signal, confidence)

            elif signal != 'NEUTRAL' and confidence >= 0.70:
                recommendation = "⚠️ MODERATE SIGNAL - Consider trade"
                rec_color = "#FFD700"
            else:
                recommendation = "❌ NO TRADE - Wait for better signal"
                rec_color = "#FF4444"

            self.trade_recommendation.setText(f"📊 Recommendation: {recommendation}")
            self.trade_recommendation.setStyleSheet(f"color: {rec_color};")

            self.log_trading_message(f"📊 Analysis result: {signal} ({confidence*100:.1f}%) - {recommendation}")

        except Exception as e:
            self.log_trading_message(f"❌ Analysis error: {e}")

    def execute_5s_auto_trade(self, signal: str, confidence: float):
        """🚀 Execute 5-second auto trade"""
        try:
            asset = self.asset_combo.currentText()
            amount = self.amount_spin.value()

            self.log_trading_message(f"🚀 AUTO 5s TRADE: {signal} {asset} ${amount} ({confidence*100:.1f}%)")

            # Add to trades list
            timestamp = time.strftime("%H:%M:%S")
            trade_entry = f"[{timestamp}] AUTO 5s {signal} {asset} ${amount} ({confidence*100:.1f}%)"
            self.trades_list.append(trade_entry)

            # Update statistics
            self.trade_count += 1

            # Simulate trade result (for demo)
            success = random.choice([True, True, True, False])  # 75% success rate
            if success:
                self.successful_trades += 1
                profit = amount * random.uniform(0.7, 0.9)  # 70-90% profit
                self.trades_list.append(f"    ✅ WIN: +${profit:.2f}")
                self.log_trading_message(f"✅ 5s trade WON: +${profit:.2f}")
            else:
                self.trades_list.append(f"    ❌ LOSS: -${amount:.2f}")
                self.log_trading_message(f"❌ 5s trade LOST: -${amount:.2f}")

            # Update stats
            self.update_trading_statistics()

        except Exception as e:
            self.log_trading_message(f"❌ Auto-trade error: {e}")

    def manual_5s_trade(self, direction: str):
        """🎮 Execute manual 5-second trade"""
        try:
            asset = self.asset_combo.currentText()
            amount = self.amount_spin.value()

            self.log_trading_message(f"🎮 MANUAL 5s TRADE: {direction} {asset} ${amount}")

            # Add to trades list
            timestamp = time.strftime("%H:%M:%S")
            trade_entry = f"[{timestamp}] MANUAL 5s {direction} {asset} ${amount}"
            self.trades_list.append(trade_entry)

            # Update statistics
            self.trade_count += 1

            # Simulate trade result (for demo)
            success = random.choice([True, True, False])  # 66% success rate for manual
            if success:
                self.successful_trades += 1
                profit = amount * random.uniform(0.7, 0.9)
                self.trades_list.append(f"    ✅ WIN: +${profit:.2f}")
                self.log_trading_message(f"✅ Manual 5s trade WON: +${profit:.2f}")
            else:
                self.trades_list.append(f"    ❌ LOSS: -${amount:.2f}")
                self.log_trading_message(f"❌ Manual 5s trade LOST: -${amount:.2f}")

            # Update stats
            self.update_trading_statistics()

        except Exception as e:
            self.log_trading_message(f"❌ Manual trade error: {e}")

    def start_price_monitoring(self):
        """💰 Start price monitoring"""
        try:
            self.price_timer = QTimer()
            self.price_timer.timeout.connect(self.update_price_display)
            self.price_timer.start(1000)  # Update every second

            self.log_trading_message("💰 Price monitoring started")

        except Exception as e:
            self.log_trading_message(f"❌ Price monitoring error: {e}")

    def update_price_display(self):
        """💰 Update price display"""
        try:
            # Simulate price data
            base_price = 1.07500
            price_change = random.uniform(-0.00050, 0.00050)
            current_price = base_price + price_change

            asset = self.asset_combo.currentText()
            self.current_price.setText(f"💰 {asset}: {current_price:.5f}")

            # Price change
            change_percent = (price_change / base_price) * 100
            if change_percent >= 0:
                self.price_change.setText(f"📈 Change: +{change_percent:.3f}%")
                self.price_change.setStyleSheet("color: #32CD32;")
            else:
                self.price_change.setText(f"📉 Change: {change_percent:.3f}%")
                self.price_change.setStyleSheet("color: #FF4444;")

            # Volatility
            volatility_levels = ['Low', 'Medium', 'High']
            volatility = random.choice(volatility_levels)
            self.market_volatility.setText(f"📊 Volatility: {volatility}")

        except Exception as e:
            self.log_trading_message(f"❌ Price update error: {e}")

    def update_trading_statistics(self):
        """📈 Update trading statistics"""
        try:
            # Update basic stats
            self.trades_today.setText(f"📊 5s Trades Today: {self.trade_count}")

            # Success rate
            if self.trade_count > 0:
                success_rate = (self.successful_trades / self.trade_count) * 100
                self.success_rate.setText(f"✅ Success Rate: {success_rate:.1f}%")
            else:
                self.success_rate.setText("✅ Success Rate: 0%")

            # Simulate profits
            if self.successful_trades > 0:
                avg_profit = random.uniform(5, 15)
                total_profit = self.successful_trades * avg_profit - (self.trade_count - self.successful_trades) * 10

                self.avg_profit.setText(f"💰 Avg Profit: ${avg_profit:.2f}")

                if total_profit >= 0:
                    self.total_profit.setText(f"💎 Total Profit: +${total_profit:.2f}")
                    self.total_profit.setStyleSheet("color: #32CD32;")
                else:
                    self.total_profit.setText(f"💎 Total Profit: ${total_profit:.2f}")
                    self.total_profit.setStyleSheet("color: #FF4444;")

        except Exception as e:
            self.log_trading_message(f"❌ Statistics update error: {e}")

def main():
    """🚀 Main function"""
    print("🚀" + "=" * 60 + "🚀")
    print("⚡" + " " * 15 + "VIP BIG BANG 5-SECOND TRADER" + " " * 15 + "⚡")
    print("💎" + " " * 10 + "تحلیل هر 15 ثانیه + ترید 5 ثانیه‌ای فوری" + " " * 10 + "💎")
    print("🚀" + "=" * 60 + "🚀")
    print()
    print("📊 5-Second Trading Features:")
    print("   ⏰ Analysis every 15 seconds")
    print("   🚀 5-second trade execution")
    print("   🤖 Auto trading mode")
    print("   🎮 Manual trading controls")
    print("   📈 Live statistics")
    print("   💰 Real-time profit tracking")
    print()

    app = QApplication(sys.argv)

    # Create and show 5-second trader
    window = VIP5SecondTrader()
    window.show()

    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
