@echo off
echo 🚀 VIP BIG BANG Auto Extension Loader
echo.

set EXTENSION_PATH=C:\Users\<USER>\VIP_BIG_BANG\temp_extension
set CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe

echo Extension Path: %EXTENSION_PATH%
echo Chrome Path: %CHROME_PATH%
echo.

echo Killing existing Chrome processes...
taskkill /f /im chrome.exe >nul 2>&1

echo Starting Chrome with VIP BIG BANG extension...
start "" "%CHROME_PATH%" --load-extension="%EXTENSION_PATH%" --disable-extensions-file-access-check --no-first-run --no-default-browser-check "https://qxbroker.com/en/trade"

echo.
echo ✅ Chrome started with VIP BIG BANG extension!
echo 📋 Extension should be automatically loaded
echo.
echo 🔧 If extension is not working:
echo 1. Go to chrome://extensions/
echo 2. Enable "Developer mode"
echo 3. Check if VIP BIG BANG is enabled
echo.
pause
