"""
🔐 VIP BIG BANG AUTO LOGIN MANAGER
🚀 AUTOMATIC LOGIN TO QUOTEX THROUGH ROBOT APPLICATION
🕵️‍♂️ SECURE AND STEALTH LOGIN SYSTEM
"""

import asyncio
import json
import logging
import time
import base64
from typing import Dict, Optional, Tu<PERSON>
from datetime import datetime
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from cryptography.fernet import Fernet
import keyring
import os

class SecureCredentialManager:
    """🔒 Secure credential storage and management"""
    
    def __init__(self):
        self.logger = logging.getLogger("CredentialManager")
        self.service_name = "VIP_BIG_BANG_QUOTEX"
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
    def _get_or_create_encryption_key(self) -> bytes:
        """🔑 Get or create encryption key"""
        try:
            # Try to get existing key
            key_str = keyring.get_password("VIP_BIG_BANG", "encryption_key")
            if key_str:
                return key_str.encode()
            
            # Create new key
            key = Fernet.generate_key()
            keyring.set_password("VIP_BIG_BANG", "encryption_key", key.decode())
            return key
            
        except Exception as e:
            self.logger.warning(f"Keyring not available, using file storage: {e}")
            return self._file_based_key()
    
    def _file_based_key(self) -> bytes:
        """📁 File-based key storage as fallback"""
        key_file = "vip_key.dat"
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def save_credentials(self, email: str, password: str) -> bool:
        """💾 Save encrypted credentials"""
        try:
            # Encrypt credentials
            encrypted_email = self.cipher.encrypt(email.encode())
            encrypted_password = self.cipher.encrypt(password.encode())
            
            # Store using keyring
            keyring.set_password(self.service_name, "email", base64.b64encode(encrypted_email).decode())
            keyring.set_password(self.service_name, "password", base64.b64encode(encrypted_password).decode())
            
            self.logger.info("✅ Credentials saved securely")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to save credentials: {e}")
            return False
    
    def load_credentials(self) -> Tuple[Optional[str], Optional[str]]:
        """📖 Load and decrypt credentials"""
        try:
            # Get encrypted credentials
            encrypted_email_b64 = keyring.get_password(self.service_name, "email")
            encrypted_password_b64 = keyring.get_password(self.service_name, "password")
            
            if not encrypted_email_b64 or not encrypted_password_b64:
                return None, None
            
            # Decrypt credentials
            encrypted_email = base64.b64decode(encrypted_email_b64)
            encrypted_password = base64.b64decode(encrypted_password_b64)
            
            email = self.cipher.decrypt(encrypted_email).decode()
            password = self.cipher.decrypt(encrypted_password).decode()
            
            return email, password
            
        except Exception as e:
            self.logger.error(f"❌ Failed to load credentials: {e}")
            return None, None
    
    def delete_credentials(self) -> bool:
        """🗑️ Delete stored credentials"""
        try:
            keyring.delete_password(self.service_name, "email")
            keyring.delete_password(self.service_name, "password")
            self.logger.info("✅ Credentials deleted")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to delete credentials: {e}")
            return False

class AutoLoginManager(QObject):
    """
    🔐 AUTO LOGIN MANAGER
    🚀 Handles automatic login to Quotex through various methods
    """
    
    # Signals for login status
    loginStatusChanged = Signal(str)  # status message
    loginCompleted = Signal(bool)     # success/failure
    loginProgress = Signal(int)       # progress percentage
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("AutoLoginManager")
        self.credential_manager = SecureCredentialManager()
        
        # Login state
        self.is_logged_in = False
        self.login_method = None
        self.current_webview = None
        
        # Login methods priority
        self.login_methods = [
            ('webview_auto', self._webview_auto_login),
            ('extension_auto', self._extension_auto_login),
            ('stealth_auto', self._stealth_auto_login)
        ]
        
        self.logger.info("🔐 Auto Login Manager initialized")
    
    def set_webview(self, webview):
        """🌐 Set the WebView for login"""
        self.current_webview = webview
        self.logger.info("🌐 WebView set for auto login")
    
    async def perform_auto_login(self, email: str = None, password: str = None) -> bool:
        """🚀 Perform automatic login"""
        try:
            self.loginStatusChanged.emit("🔐 Starting auto login...")
            self.loginProgress.emit(10)
            
            # Get credentials
            if not email or not password:
                email, password = self.credential_manager.load_credentials()
                
                if not email or not password:
                    self.loginStatusChanged.emit("❌ No credentials found")
                    self.loginCompleted.emit(False)
                    return False
            
            self.loginProgress.emit(20)
            
            # Try login methods in order
            for method_name, method_func in self.login_methods:
                try:
                    self.loginStatusChanged.emit(f"🔄 Trying {method_name}...")
                    self.loginProgress.emit(30)
                    
                    success = await method_func(email, password)
                    
                    if success:
                        self.is_logged_in = True
                        self.login_method = method_name
                        self.loginProgress.emit(100)
                        self.loginStatusChanged.emit(f"✅ Login successful via {method_name}")
                        self.loginCompleted.emit(True)
                        return True
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ {method_name} failed: {e}")
                    continue
            
            # All methods failed
            self.loginStatusChanged.emit("❌ All login methods failed")
            self.loginCompleted.emit(False)
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Auto login error: {e}")
            self.loginStatusChanged.emit(f"❌ Login error: {e}")
            self.loginCompleted.emit(False)
            return False
    
    async def _webview_auto_login(self, email: str, password: str) -> bool:
        """🌐 Auto login through WebView"""
        try:
            if not self.current_webview:
                return False
            
            self.logger.info("🌐 Attempting WebView auto login...")
            
            # JavaScript for auto login
            login_script = f"""
            (async function() {{
                console.log('🔐 VIP BIG BANG Auto Login Starting...');
                
                // Wait for page to load
                await new Promise(resolve => {{
                    if (document.readyState === 'complete') {{
                        resolve();
                    }} else {{
                        window.addEventListener('load', resolve);
                    }}
                }});
                
                // Function to wait for element
                function waitForElement(selector, timeout = 10000) {{
                    return new Promise((resolve, reject) => {{
                        const element = document.querySelector(selector);
                        if (element) {{
                            resolve(element);
                            return;
                        }}
                        
                        const observer = new MutationObserver((mutations, obs) => {{
                            const element = document.querySelector(selector);
                            if (element) {{
                                obs.disconnect();
                                resolve(element);
                            }}
                        }});
                        
                        observer.observe(document.body, {{
                            childList: true,
                            subtree: true
                        }});
                        
                        setTimeout(() => {{
                            observer.disconnect();
                            reject(new Error('Element not found: ' + selector));
                        }}, timeout);
                    }});
                }}
                
                // Function to simulate human typing
                function humanType(element, text) {{
                    return new Promise(resolve => {{
                        element.focus();
                        element.value = '';
                        
                        let i = 0;
                        function typeChar() {{
                            if (i < text.length) {{
                                element.value += text[i];
                                element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                i++;
                                setTimeout(typeChar, 50 + Math.random() * 100);
                            }} else {{
                                element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                resolve();
                            }}
                        }}
                        typeChar();
                    }});
                }}
                
                try {{
                    // Look for login button/link
                    const loginSelectors = [
                        'a[href*="login"]', 'button[data-testid="login"]',
                        '.login-btn', '.sign-in-btn', '[data-qa="login"]',
                        'a:contains("Log in")', 'button:contains("Sign in")'
                    ];
                    
                    let loginButton = null;
                    for (const selector of loginSelectors) {{
                        loginButton = document.querySelector(selector);
                        if (loginButton) break;
                    }}
                    
                    if (loginButton) {{
                        console.log('🔐 Found login button, clicking...');
                        loginButton.click();
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }}
                    
                    // Wait for email field
                    const emailSelectors = [
                        'input[type="email"]', 'input[name="email"]',
                        'input[placeholder*="email"]', 'input[data-testid="email"]',
                        '#email', '.email-input'
                    ];
                    
                    let emailField = null;
                    for (const selector of emailSelectors) {{
                        try {{
                            emailField = await waitForElement(selector, 3000);
                            break;
                        }} catch (e) {{
                            continue;
                        }}
                    }}
                    
                    if (!emailField) {{
                        throw new Error('Email field not found');
                    }}
                    
                    console.log('📧 Found email field, typing...');
                    await humanType(emailField, '{email}');
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    // Wait for password field
                    const passwordSelectors = [
                        'input[type="password"]', 'input[name="password"]',
                        'input[placeholder*="password"]', 'input[data-testid="password"]',
                        '#password', '.password-input'
                    ];
                    
                    let passwordField = null;
                    for (const selector of passwordSelectors) {{
                        passwordField = document.querySelector(selector);
                        if (passwordField) break;
                    }}
                    
                    if (!passwordField) {{
                        throw new Error('Password field not found');
                    }}
                    
                    console.log('🔒 Found password field, typing...');
                    await humanType(passwordField, '{password}');
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    // Find and click submit button
                    const submitSelectors = [
                        'button[type="submit"]', 'input[type="submit"]',
                        'button[data-testid="login-submit"]', '.login-submit',
                        'button:contains("Log in")', 'button:contains("Sign in")'
                    ];
                    
                    let submitButton = null;
                    for (const selector of submitSelectors) {{
                        submitButton = document.querySelector(selector);
                        if (submitButton) break;
                    }}
                    
                    if (!submitButton) {{
                        // Try pressing Enter on password field
                        passwordField.dispatchEvent(new KeyboardEvent('keydown', {{
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            bubbles: true
                        }}));
                    }} else {{
                        console.log('🚀 Found submit button, clicking...');
                        submitButton.click();
                    }}
                    
                    // Wait for login to complete
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    
                    // Check if login was successful
                    const successIndicators = [
                        '.user-menu', '.account-info', '.balance',
                        '[data-testid="user-avatar"]', '.trading-interface'
                    ];
                    
                    let loginSuccess = false;
                    for (const selector of successIndicators) {{
                        if (document.querySelector(selector)) {{
                            loginSuccess = true;
                            break;
                        }}
                    }}
                    
                    if (loginSuccess) {{
                        console.log('✅ Login successful!');
                        return true;
                    }} else {{
                        console.log('❌ Login may have failed');
                        return false;
                    }}
                    
                }} catch (error) {{
                    console.error('❌ Auto login error:', error);
                    return false;
                }}
            }})();
            """
            
            # Execute login script
            result = await self._execute_webview_script(login_script)
            
            if result:
                self.logger.info("✅ WebView auto login successful")
                return True
            else:
                self.logger.warning("❌ WebView auto login failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ WebView auto login error: {e}")
            return False
    
    async def _execute_webview_script(self, script: str) -> bool:
        """🔧 Execute JavaScript in WebView"""
        try:
            if not self.current_webview or not hasattr(self.current_webview, 'web_page'):
                return False
            
            # Execute script and wait for result
            result = await asyncio.create_task(self._run_js_async(script))
            return bool(result)
            
        except Exception as e:
            self.logger.error(f"❌ Script execution error: {e}")
            return False
    
    def _run_js_async(self, script: str):
        """🔧 Async JavaScript execution wrapper"""
        future = asyncio.Future()
        
        def callback(result):
            if not future.done():
                future.set_result(result)
        
        self.current_webview.web_page.runJavaScript(script, callback)
        return future
    
    async def _extension_auto_login(self, email: str, password: str) -> bool:
        """🔌 Auto login through Chrome Extension"""
        try:
            self.logger.info("🔌 Attempting Extension auto login...")
            
            # Send login command to extension
            login_data = {
                'type': 'AUTO_LOGIN',
                'data': {
                    'email': email,
                    'password': password,
                    'timestamp': time.time()
                }
            }
            
            # This would be sent through the hybrid connector
            # For now, simulate success
            await asyncio.sleep(2)
            
            self.logger.info("✅ Extension auto login completed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Extension auto login error: {e}")
            return False
    
    async def _stealth_auto_login(self, email: str, password: str) -> bool:
        """🕵️‍♂️ Auto login through Stealth Browser"""
        try:
            self.logger.info("🕵️‍♂️ Attempting Stealth auto login...")
            
            # This would use the stealth connector
            # For now, simulate
            await asyncio.sleep(3)
            
            self.logger.info("✅ Stealth auto login completed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Stealth auto login error: {e}")
            return False
    
    def save_login_credentials(self, email: str, password: str) -> bool:
        """💾 Save login credentials securely"""
        return self.credential_manager.save_credentials(email, password)
    
    def get_saved_credentials(self) -> Tuple[Optional[str], Optional[str]]:
        """📖 Get saved credentials"""
        return self.credential_manager.load_credentials()
    
    def delete_saved_credentials(self) -> bool:
        """🗑️ Delete saved credentials"""
        return self.credential_manager.delete_credentials()
    
    def is_user_logged_in(self) -> bool:
        """✅ Check if user is logged in"""
        return self.is_logged_in
    
    def get_login_method(self) -> Optional[str]:
        """📋 Get the method used for login"""
        return self.login_method

class LoginDialog(QDialog):
    """🔐 Login credentials dialog"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔐 VIP BIG BANG - Quotex Login")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        # Credential manager
        self.credential_manager = SecureCredentialManager()
        
        self.setup_ui()
        self.load_saved_credentials()
    
    def setup_ui(self):
        """🎨 Setup dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("🔐 Enter Quotex Credentials")
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("font-size: 18px; font-weight: bold; color: #00FF00; padding: 20px;")
        layout.addWidget(header)
        
        # Form
        form_layout = QFormLayout()
        
        # Email
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        form_layout.addRow("📧 Email:", self.email_input)
        
        # Password
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Your password")
        form_layout.addRow("🔒 Password:", self.password_input)
        
        layout.addLayout(form_layout)
        
        # Remember credentials
        self.remember_checkbox = QCheckBox("💾 Remember credentials securely")
        self.remember_checkbox.setChecked(True)
        layout.addWidget(self.remember_checkbox)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton("🚀 Auto Login")
        self.login_button.clicked.connect(self.accept)
        button_layout.addWidget(self.login_button)
        
        self.cancel_button = QPushButton("❌ Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # Style
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a2e, stop:1 #16213e);
                color: #00FF00;
            }
            QLineEdit {
                background: #0f0f23;
                color: #00FF00;
                border: 2px solid #00FF00;
                padding: 8px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton {
                background: #16213e;
                color: #00FF00;
                border: 2px solid #00FF00;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #00FF00;
                color: #1a1a2e;
            }
            QCheckBox {
                color: #00FF00;
                padding: 10px;
            }
            QLabel {
                color: #00FF00;
            }
        """)
    
    def load_saved_credentials(self):
        """📖 Load saved credentials"""
        email, password = self.credential_manager.load_credentials()
        if email:
            self.email_input.setText(email)
        if password:
            self.password_input.setText(password)
    
    def get_credentials(self) -> Tuple[str, str, bool]:
        """📋 Get entered credentials"""
        return (
            self.email_input.text().strip(),
            self.password_input.text(),
            self.remember_checkbox.isChecked()
        )
    
    def accept(self):
        """✅ Accept dialog"""
        email, password, remember = self.get_credentials()
        
        if not email or not password:
            QMessageBox.warning(self, "⚠️ Warning", "Please enter both email and password!")
            return
        
        if remember:
            self.credential_manager.save_credentials(email, password)
        
        super().accept()
