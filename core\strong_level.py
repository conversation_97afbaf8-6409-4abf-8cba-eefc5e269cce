"""
VIP BIG BANG Enterprise - Strong Level Analyzer
Advanced support/resistance level detection and strength analysis
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import logging
from datetime import datetime
from collections import defaultdict

class StrongLevelAnalyzer:
    """
    Enterprise-level support/resistance analysis
    Identifies key price levels and their strength
    """
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = logging.getLogger("StrongLevelAnalyzer")
        
        # Level detection parameters
        self.lookback_period = 50
        self.min_touches = 2
        self.level_tolerance = 0.001  # 0.1% tolerance for level matching
        self.volume_weight = 0.3
        
        # Strength calculation parameters
        self.touch_weight = 0.4
        self.volume_weight = 0.3
        self.age_weight = 0.2
        self.break_penalty = 0.1
        
        self.logger.debug("Strong Level Analyzer initialized")
    
    def find_pivot_points(self, data: pd.DataFrame, window: int = 5) -> Dict:
        """Find pivot highs and lows"""
        high_prices = data['high'] if 'high' in data.columns else data['price']
        low_prices = data['low'] if 'low' in data.columns else data['price']
        
        pivot_highs = []
        pivot_lows = []
        
        for i in range(window, len(data) - window):
            # Check for pivot high
            is_pivot_high = True
            for j in range(i - window, i + window + 1):
                if j != i and high_prices.iloc[j] >= high_prices.iloc[i]:
                    is_pivot_high = False
                    break
            
            if is_pivot_high:
                pivot_highs.append({
                    'index': i,
                    'price': high_prices.iloc[i],
                    'timestamp': data.index[i] if hasattr(data.index, '__getitem__') else i,
                    'volume': data['volume'].iloc[i] if 'volume' in data.columns else 1
                })
            
            # Check for pivot low
            is_pivot_low = True
            for j in range(i - window, i + window + 1):
                if j != i and low_prices.iloc[j] <= low_prices.iloc[i]:
                    is_pivot_low = False
                    break
            
            if is_pivot_low:
                pivot_lows.append({
                    'index': i,
                    'price': low_prices.iloc[i],
                    'timestamp': data.index[i] if hasattr(data.index, '__getitem__') else i,
                    'volume': data['volume'].iloc[i] if 'volume' in data.columns else 1
                })
        
        return {'highs': pivot_highs, 'lows': pivot_lows}
    
    def cluster_levels(self, levels: List[Dict], tolerance: float) -> List[Dict]:
        """Cluster nearby price levels together"""
        if not levels:
            return []
        
        # Sort levels by price
        sorted_levels = sorted(levels, key=lambda x: x['price'])
        clusters = []
        current_cluster = [sorted_levels[0]]
        
        for level in sorted_levels[1:]:
            # Check if this level is close to the current cluster
            cluster_avg_price = np.mean([l['price'] for l in current_cluster])
            
            if abs(level['price'] - cluster_avg_price) / cluster_avg_price <= tolerance:
                current_cluster.append(level)
            else:
                # Finalize current cluster and start new one
                clusters.append(self.merge_cluster(current_cluster))
                current_cluster = [level]
        
        # Add the last cluster
        if current_cluster:
            clusters.append(self.merge_cluster(current_cluster))
        
        return clusters
    
    def merge_cluster(self, cluster: List[Dict]) -> Dict:
        """Merge a cluster of levels into a single strong level"""
        if not cluster:
            return {}
        
        # Calculate weighted average price (weighted by volume)
        total_volume = sum(level['volume'] for level in cluster)
        if total_volume > 0:
            weighted_price = sum(level['price'] * level['volume'] for level in cluster) / total_volume
        else:
            weighted_price = np.mean([level['price'] for level in cluster])
        
        return {
            'price': weighted_price,
            'touches': len(cluster),
            'total_volume': total_volume,
            'first_touch': min(level['index'] for level in cluster),
            'last_touch': max(level['index'] for level in cluster),
            'levels': cluster
        }
    
    def calculate_level_strength(self, level: Dict, current_index: int) -> float:
        """Calculate the strength of a support/resistance level"""
        # Touch strength (more touches = stronger)
        touch_strength = min(level['touches'] / 5, 1.0)  # Cap at 5 touches
        
        # Volume strength (higher volume = stronger)
        avg_volume = level['total_volume'] / level['touches'] if level['touches'] > 0 else 0
        volume_strength = min(avg_volume / 1000, 1.0)  # Normalize volume
        
        # Age strength (older levels that still hold = stronger)
        age = current_index - level['first_touch']
        age_strength = min(age / 50, 1.0)  # Normalize age
        
        # Recency factor (recent touches are more relevant)
        recency = current_index - level['last_touch']
        recency_factor = max(0.1, 1.0 - recency / 20)  # Decay over 20 periods
        
        # Calculate weighted strength
        strength = (
            touch_strength * self.touch_weight +
            volume_strength * self.volume_weight +
            age_strength * self.age_weight
        ) * recency_factor
        
        return min(strength, 1.0)
    
    def identify_current_level_interaction(self, data: pd.DataFrame, levels: List[Dict]) -> Dict:
        """Identify how current price interacts with strong levels"""
        if not levels or len(data) == 0:
            return {'interaction': 'NONE', 'level': None, 'distance': 0}
        
        current_price = data['close'].iloc[-1] if 'close' in data.columns else data['price'].iloc[-1]
        
        # Find the closest level
        closest_level = min(levels, key=lambda x: abs(x['price'] - current_price))
        distance = abs(current_price - closest_level['price']) / current_price
        
        interaction = 'NONE'
        
        # Check if price is near a level (within 0.2%)
        if distance <= 0.002:
            if current_price > closest_level['price']:
                interaction = 'ABOVE_SUPPORT'
            else:
                interaction = 'BELOW_RESISTANCE'
        
        # Check for breakouts
        elif distance <= 0.005:  # Within 0.5%
            # Look at recent price action
            recent_prices = data['close'].tail(3) if 'close' in data.columns else data['price'].tail(3)
            
            if len(recent_prices) >= 2:
                price_direction = 'UP' if recent_prices.iloc[-1] > recent_prices.iloc[0] else 'DOWN'
                
                if price_direction == 'UP' and current_price > closest_level['price']:
                    interaction = 'RESISTANCE_BREAK'
                elif price_direction == 'DOWN' and current_price < closest_level['price']:
                    interaction = 'SUPPORT_BREAK'
        
        return {
            'interaction': interaction,
            'level': closest_level,
            'distance': distance,
            'current_price': current_price
        }
    
    def analyze(self, data: pd.DataFrame) -> Dict:
        """
        Main strong level analysis function
        Returns score between 0 (bearish) and 1 (bullish)
        """
        try:
            if len(data) < 20:
                return {
                    'score': 0.5,
                    'direction': 'NEUTRAL',
                    'confidence': 0.0,
                    'details': 'Insufficient data for strong level analysis'
                }
            
            # Find pivot points
            pivots = self.find_pivot_points(data)
            
            # Cluster resistance levels (pivot highs)
            resistance_levels = self.cluster_levels(pivots['highs'], self.level_tolerance)
            
            # Cluster support levels (pivot lows)
            support_levels = self.cluster_levels(pivots['lows'], self.level_tolerance)
            
            # Calculate strength for each level
            current_index = len(data) - 1
            
            for level in resistance_levels:
                level['strength'] = self.calculate_level_strength(level, current_index)
                level['type'] = 'RESISTANCE'
            
            for level in support_levels:
                level['strength'] = self.calculate_level_strength(level, current_index)
                level['type'] = 'SUPPORT'
            
            # Combine and sort by strength
            all_levels = resistance_levels + support_levels
            strong_levels = [level for level in all_levels if level['strength'] > 0.3]
            strong_levels.sort(key=lambda x: x['strength'], reverse=True)
            
            # Analyze current price interaction with levels
            interaction = self.identify_current_level_interaction(data, strong_levels[:10])  # Top 10 levels
            
            # Scoring based on level interaction
            score = 0.5  # Neutral starting point
            confidence = 0.0
            
            if interaction['interaction'] == 'ABOVE_SUPPORT':
                # Price holding above support is bullish
                level_strength = interaction['level']['strength'] if interaction['level'] else 0
                score = 0.6 + (level_strength * 0.3)
                confidence = level_strength
            
            elif interaction['interaction'] == 'BELOW_RESISTANCE':
                # Price rejected at resistance is bearish
                level_strength = interaction['level']['strength'] if interaction['level'] else 0
                score = 0.4 - (level_strength * 0.3)
                confidence = level_strength
            
            elif interaction['interaction'] == 'RESISTANCE_BREAK':
                # Breaking resistance is very bullish
                level_strength = interaction['level']['strength'] if interaction['level'] else 0
                score = 0.7 + (level_strength * 0.2)
                confidence = level_strength * 1.2
            
            elif interaction['interaction'] == 'SUPPORT_BREAK':
                # Breaking support is very bearish
                level_strength = interaction['level']['strength'] if interaction['level'] else 0
                score = 0.3 - (level_strength * 0.2)
                confidence = level_strength * 1.2
            
            # Ensure bounds
            score = max(0, min(1, score))
            confidence = max(0, min(1, confidence))
            
            # Determine direction
            if score > 0.6:
                direction = 'CALL'
            elif score < 0.4:
                direction = 'PUT'
            else:
                direction = 'NEUTRAL'
            
            result = {
                'score': score,
                'direction': direction,
                'confidence': confidence,
                'interaction': interaction,
                'strong_levels': strong_levels[:5],  # Top 5 strongest levels
                'level_counts': {
                    'resistance': len(resistance_levels),
                    'support': len(support_levels),
                    'strong': len(strong_levels)
                },
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.debug(f"Strong Level analysis: Score={score:.3f}, Direction={direction}, Interaction={interaction['interaction']}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Strong level analysis failed: {e}")
            return {
                'score': 0.5,
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
