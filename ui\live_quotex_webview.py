"""
🌐 VIP BIG BANG QUANTUM LIVE QUOTEX WEBVIEW
🚀 PROFESSIONAL EMBEDDED QUOTEX WITH QUANTUM SECURITY BYPASS
🔥 LIVE TRADING ACCESS + ADVANCED STEALTH TECHNOLOGY
"""

import sys
import json
import logging
import asyncio
import time
from typing import Dict, Any, Optional
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QDialog, QGroupBox, QSpinBox, QComboBox, QSlider
from PySide6.QtCore import QObject, Signal, Slot, QTimer, QUrl, QDateTime, Qt
from PySide6.QtGui import QFont
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile, QWebEngineScript, QWebEnginePage
from PySide6.QtWebChannel import QWebChannel

# Import quantum systems
from core.quantum_extension_manager import QuantumExtensionManager
from core.quantum_security_bypass import QuantumSecurityBypass
from core.quantum_stealth_chrome_connector import QuantumStealthChromeConnector
from core.chrome_devtools_connector import ChromeDevToolsConnector
from manual_chrome_bridge import ManualChromeBridge
from console_paste_enabler import ConsolePasteEnabler
from core.auto_extension_manager import AutoExtensionManager
from core.quantum_stealth_system import QuantumStealthSystem

class QuantumWebEnginePage(QWebEnginePage):
    """🚀 Quantum-enhanced WebEngine page with security bypass"""

    def __init__(self, profile, parent=None):
        super().__init__(profile, parent)
        self.quantum_security_bypass = None
        self.logger = logging.getLogger("QuantumWebEnginePage")

    def javaScriptConsoleMessage(self, level, message, lineNumber, sourceID):
        """🔧 Override console messages to hide security warnings"""
        # Filter out security-related console messages
        if any(keyword in message.lower() for keyword in ['insecure', 'security', 'blocked', 'cors']):
            return  # Suppress security warnings

        # Log other messages normally
        super().javaScriptConsoleMessage(level, message, lineNumber, sourceID)

    def acceptNavigationRequest(self, url, type, isMainFrame):
        """🔧 Override navigation requests for quantum bypass"""
        # Allow all navigation for quantum mode
        return True

class QuotexJavaScriptBridge(QObject):
    """🌉 JavaScript Bridge for Quotex communication"""
    
    # Signals for Qt communication
    priceDataReceived = Signal(str)
    tradeResultReceived = Signal(str)
    balanceReceived = Signal(str)
    chartDataReceived = Signal(str)
    extensionStatusChanged = Signal(bool)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("QuotexBridge")
        
    @Slot(str)
    def sendPriceData(self, data):
        """📈 Receive price data from JavaScript"""
        self.logger.debug(f"📈 Price data: {data}")
        self.priceDataReceived.emit(data)
    
    @Slot(str)
    def sendTradeResult(self, data):
        """💰 Receive trade result from JavaScript"""
        self.logger.info(f"💰 Trade result: {data}")
        self.tradeResultReceived.emit(data)
    
    @Slot(str)
    def sendBalance(self, data):
        """💳 Receive balance from JavaScript"""
        self.logger.debug(f"💳 Balance: {data}")
        self.balanceReceived.emit(data)
    
    @Slot(str)
    def sendChartData(self, data):
        """📊 Receive chart data from JavaScript"""
        self.logger.debug(f"📊 Chart data: {data}")
        self.chartDataReceived.emit(data)
    
    @Slot(bool)
    def setExtensionStatus(self, status):
        """🔌 Set extension status"""
        self.logger.info(f"🔌 Extension: {'Connected' if status else 'Disconnected'}")
        self.extensionStatusChanged.emit(status)
    
    @Slot(str, str, float, int, result=bool)
    def executeTrade(self, asset, direction, amount, duration):
        """🚀 Execute trade from Qt to JavaScript"""
        try:
            self.logger.info(f"🚀 Trade request: {direction} {asset} ${amount} {duration}s")
            # This will be handled by the injected JavaScript
            return True
        except Exception as e:
            self.logger.error(f"❌ Trade execution error: {e}")
            return False

class LiveQuotexWebView(QWidget):
    """
    🌐 LIVE QUOTEX WEBVIEW WIDGET
    📊 Embedded Quotex trading platform with live charts
    🔥 Full integration with VIP BIG BANG robot
    """
    
    # Signals for parent application
    priceUpdated = Signal(dict)
    tradeExecuted = Signal(dict)
    balanceChanged = Signal(float)
    chartUpdated = Signal(dict)
    connectionChanged = Signal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("QuantumLiveQuotexWebView")

        # Quantum systems
        self.quantum_extension_manager = QuantumExtensionManager()
        self.quantum_security_bypass = QuantumSecurityBypass()
        self.quantum_stealth_connector = QuantumStealthChromeConnector()
        self.quantum_stealth_system = QuantumStealthSystem()
        self.chrome_devtools_connector = ChromeDevToolsConnector()
        self.manual_chrome_bridge = ManualChromeBridge()
        self.console_paste_enabler = ConsolePasteEnabler()
        self.auto_extension_manager = AutoExtensionManager()

        # Setup connection callbacks
        self.quantum_stealth_connector.add_callback(self.on_quantum_stealth_data_received)
        self.chrome_devtools_connector.add_callback(self.on_devtools_data_received)
        self.manual_chrome_bridge.add_callback(self.on_manual_bridge_data_received)

        # Auto-start extension will be called after UI setup

        # Components
        self.web_view = None
        self.web_page = None
        self.bridge = None
        self.web_channel = None

        # State
        self.is_loaded = False
        self.is_connected = False
        self.current_balance = 0.0
        self.current_prices = {}

        # Initialize Auto Screen Detector
        try:
            from core.auto_screen_detector import AutoScreenManager

            self.auto_screen_manager = AutoScreenManager()
            self.auto_screen_initialized = self.auto_screen_manager.initialize()

            if self.auto_screen_initialized:
                self.auto_detector = self.auto_screen_manager.get_detector()

                # Get screen info
                screen_info = self.auto_detector.get_screen_info()

                self.logger.info(f"📺 Auto Screen Detection Active")
                self.logger.info(f"📊 Screen: {screen_info['screen_size']} ({screen_info['screen_type']})")
                self.logger.info(f"📐 Optimal: {screen_info['optimal_size']}")
                self.logger.info(f"🔍 DPI: {screen_info['dpi']:.0f}")

                if screen_info['multiple_screens']:
                    self.logger.info(f"🖥️ Multiple screens detected: {screen_info['screen_count']}")
            else:
                self.logger.error("❌ Auto Screen Detection failed to initialize")
                self.auto_detector = None

        except Exception as e:
            self.logger.error(f"❌ Auto Screen Detector error: {e}")
            self.auto_screen_initialized = False
            self.auto_detector = None

        # Setup
        self.setup_ui()

        # Auto-install extension
        self.auto_install_extension()

        # Setup WebView with delay to ensure UI is ready
        QTimer.singleShot(1000, self.setup_quantum_webview)

        # Start quantum stealth connection
        QTimer.singleShot(2000, self.start_quantum_stealth_connection)

        self.logger.info("🚀 Quantum Live Quotex WebView initialized")

    def auto_install_extension(self):
        """🔌 Auto-install Chrome extension"""
        try:
            self.logger.info("🔌 Starting quantum extension auto-installation...")

            # Run quantum auto-installation
            results = self.quantum_extension_manager.quantum_auto_install()

            if results.get('chrome_launched', False):
                self.logger.info("🏆 Quantum extension auto-installation successful!")
            else:
                self.logger.warning("⚠️ Quantum extension installation partial success")

        except Exception as e:
            self.logger.error(f"❌ Quantum extension auto-installation failed: {e}")

    def setup_ui(self):
        """🎨 Setup the UI layout"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Header
        header = self.create_header()
        main_layout.addWidget(header)

        # Top toolbar
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)

        # WebView container
        webview_container = QWidget()
        webview_layout = QVBoxLayout(webview_container)
        webview_layout.setContentsMargins(0, 0, 0, 0)

        # WebView placeholder (will be created in setup_quantum_webview)
        self.webview_placeholder = QLabel("🚀 Quantum WebView Loading...")
        self.webview_placeholder.setAlignment(Qt.AlignCenter)
        self.webview_placeholder.setStyleSheet("""
            QLabel {
                background: #1a1a2e;
                color: #00FF00;
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #00FF00;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)
        webview_layout.addWidget(self.webview_placeholder)

        main_layout.addWidget(webview_container, 1)  # Give it stretch

        # Bottom status bar
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)

        # Auto-start extension after UI is ready
        QTimer.singleShot(2000, self.auto_start_extension)

        # Initialize trading state
        self.auto_trade_enabled = False
        self.latest_price_data = {}

        # Apply auto screen detection if available
        if self.auto_screen_initialized:
            self.logger.info("📐 Auto Screen Detection ready")

            # Register window for auto management after UI is ready
            QTimer.singleShot(1000, self.register_for_auto_screen)

            # Apply initial auto adjustment
            QTimer.singleShot(2000, self.apply_auto_screen_adjustment)

    def create_header(self):
        """📋 Create header section"""
        header = QWidget()
        header.setFixedHeight(60)
        header.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1a1a2e, stop:1 #16213e);
                border: 2px solid #00FF00;
                border-radius: 10px;
                margin: 2px;
            }
            QLabel {
                color: #00FF00;
                font-weight: bold;
                padding: 5px;
            }
        """)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(15, 10, 15, 10)

        # Title
        title = QLabel("🌐 VIP BIG BANG QUANTUM LIVE TRADING")
        title.setStyleSheet("font-size: 18px; color: #00FF00;")
        layout.addWidget(title)

        layout.addStretch()

        # Status indicator
        self.main_status = QLabel("🔄 Initializing...")
        self.main_status.setStyleSheet("font-size: 14px; color: #FFAA00;")
        layout.addWidget(self.main_status)

        # Extension status
        self.extension_label = QLabel("🔌 Extension: Preparing...")
        self.extension_label.setStyleSheet("font-size: 12px; color: #AAAAAA;")
        layout.addWidget(self.extension_label)

        return header
    
    def create_toolbar(self):
        """🔧 Create top toolbar"""
        toolbar = QWidget()
        toolbar.setFixedHeight(50)
        toolbar.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1a1a2e, stop:1 #16213e);
                border-bottom: 2px solid #00FF00;
            }
            QPushButton {
                background: rgba(0, 255, 0, 0.1);
                color: #00FF00;
                border: 1px solid #00FF00;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background: rgba(0, 255, 0, 0.2);
            }
            QPushButton:pressed {
                background: #00FF00;
                color: #1a1a2e;
            }
            QLabel {
                color: #00FF00;
                font-weight: bold;
                padding: 5px 10px;
                margin: 5px;
            }
        """)
        
        layout = QHBoxLayout(toolbar)
        
        # VIP BIG BANG logo
        logo = QLabel("🚀 VIP BIG BANG LIVE TRADING")
        logo.setStyleSheet("font-size: 16px; color: #00FF00;")
        layout.addWidget(logo)
        
        layout.addStretch()
        
        # Connection status
        self.connection_status = QLabel("🔴 Disconnected")
        layout.addWidget(self.connection_status)
        
        # Login button
        login_btn = QPushButton("🔐 Auto Login")
        login_btn.clicked.connect(self.show_login_dialog)
        layout.addWidget(login_btn)

        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_quotex)
        layout.addWidget(refresh_btn)

        # Reload WebView button
        reload_btn = QPushButton("🌐 Reload WebView")
        reload_btn.clicked.connect(self.reload_webview)
        reload_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #0f1419);
                color: white;
                border: 2px solid #2196F3;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: #2196F3;
                color: white;
                border: 2px solid white;
            }
        """)
        layout.addWidget(reload_btn)

        # Launch Chrome button
        chrome_btn = QPushButton("🚀 Launch Chrome")
        chrome_btn.clicked.connect(self.launch_quantum_chrome)
        chrome_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF9800, stop:1 #0f1419);
                color: white;
                border: 2px solid #FF9800;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: #FF9800;
                color: white;
                border: 2px solid white;
            }
        """)
        layout.addWidget(chrome_btn)

        # Close Chrome button
        close_btn = QPushButton("🛑 Close Chrome")
        close_btn.clicked.connect(self.close_chrome)
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F44336, stop:1 #0f1419);
                color: white;
                border: 2px solid #F44336;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: #F44336;
                color: white;
                border: 2px solid white;
            }
        """)
        layout.addWidget(close_btn)

        # Main Extension button (Auto-started)
        extension_status_btn = QPushButton("🔌 Extension Status")
        extension_status_btn.clicked.connect(self.show_extension_status)
        extension_status_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #0f1419);
                color: white;
                border: 2px solid #4CAF50;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 140px;
            }
            QPushButton:hover {
                background: #4CAF50;
                color: white;
                border: 2px solid white;
            }
        """)
        layout.addWidget(extension_status_btn)

        # Restart Extension button
        restart_btn = QPushButton("🔄 Restart Extension")
        restart_btn.clicked.connect(self.restart_extension)
        restart_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF9800, stop:1 #0f1419);
                color: white;
                border: 2px solid #FF9800;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 140px;
            }
            QPushButton:hover {
                background: #FF9800;
                color: white;
                border: 2px solid white;
            }
        """)
        layout.addWidget(restart_btn)

        # Direct Quotex Launch button
        direct_launch_btn = QPushButton("🚀 Launch Quotex")
        direct_launch_btn.clicked.connect(self.launch_direct_quotex)
        direct_launch_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E91E63, stop:1 #0f1419);
                color: white;
                border: 2px solid #E91E63;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 140px;
            }
            QPushButton:hover {
                background: #E91E63;
                color: white;
                border: 2px solid white;
            }
        """)
        layout.addWidget(direct_launch_btn)

        # Trading Controls Section
        trading_group = QGroupBox("🎮 Trading Controls")
        trading_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                color: white;
                background: rgba(76, 175, 80, 0.1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        trading_layout = QVBoxLayout(trading_group)

        # Auto Trade Toggle
        self.auto_trade_btn = QPushButton("🤖 Enable Auto-Trade")
        self.auto_trade_btn.clicked.connect(self.toggle_auto_trade)
        self.auto_trade_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #0f1419);
                color: white;
                border: 2px solid #2196F3;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 140px;
            }
            QPushButton:hover {
                background: #2196F3;
                color: white;
                border: 2px solid white;
            }
        """)
        trading_layout.addWidget(self.auto_trade_btn)

        # Manual Trade Buttons
        manual_layout = QHBoxLayout()

        self.call_btn = QPushButton("📈 CALL")
        self.call_btn.clicked.connect(lambda: self.manual_trade("CALL"))
        self.call_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #0f1419);
                color: white;
                border: 2px solid #4CAF50;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: #4CAF50;
                color: white;
                border: 2px solid white;
            }
        """)
        manual_layout.addWidget(self.call_btn)

        self.put_btn = QPushButton("📉 PUT")
        self.put_btn.clicked.connect(lambda: self.manual_trade("PUT"))
        self.put_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #0f1419);
                color: white;
                border: 2px solid #f44336;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: #f44336;
                color: white;
                border: 2px solid white;
            }
        """)
        manual_layout.addWidget(self.put_btn)

        trading_layout.addLayout(manual_layout)

        # Trade Amount
        amount_layout = QHBoxLayout()
        amount_label = QLabel("💰 Amount:")
        amount_label.setStyleSheet("color: white; font-weight: bold;")
        amount_layout.addWidget(amount_label)

        self.amount_input = QSpinBox()
        self.amount_input.setRange(1, 1000)
        self.amount_input.setValue(10)
        self.amount_input.setSuffix(" $")
        self.amount_input.setStyleSheet("""
            QSpinBox {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid #4CAF50;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }
        """)
        amount_layout.addWidget(self.amount_input)

        trading_layout.addLayout(amount_layout)

        # Trade Duration
        duration_layout = QHBoxLayout()
        duration_label = QLabel("⏱️ Duration:")
        duration_label.setStyleSheet("color: white; font-weight: bold;")
        duration_layout.addWidget(duration_label)

        self.duration_combo = QComboBox()
        self.duration_combo.addItems(["1 min", "5 min", "15 min", "30 min", "1 hour"])
        self.duration_combo.setCurrentText("1 min")
        self.duration_combo.setStyleSheet("""
            QComboBox {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid #4CAF50;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
            }
        """)
        duration_layout.addWidget(self.duration_combo)

        trading_layout.addLayout(duration_layout)

        # Trading Status
        self.trading_status_label = QLabel("🔴 Trading: Inactive")
        self.trading_status_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                background: rgba(244, 67, 54, 0.2);
                border: 1px solid #f44336;
                border-radius: 5px;
                padding: 5px;
                margin: 5px;
            }
        """)
        trading_layout.addWidget(self.trading_status_label)

        layout.addWidget(trading_group)

        # Simple Screen Controls
        screen_group = QGroupBox("📐 Screen Size & Display")
        screen_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #00BCD4;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                color: white;
                background: rgba(0, 188, 212, 0.1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        screen_layout = QVBoxLayout(screen_group)

        # Auto Screen Info
        if self.auto_screen_initialized and self.auto_detector:
            screen_info = self.auto_detector.get_screen_info()

            # Main screen info
            screen_info_label = QLabel(f"📺 {screen_info['screen_type']}: {screen_info['screen_size']}")
            screen_info_label.setStyleSheet("color: white; font-weight: bold; padding: 5px; font-size: 12px;")
            screen_layout.addWidget(screen_info_label)

            # Available space info
            available_info_label = QLabel(f"📐 Available: {screen_info['available_size']} | DPI: {screen_info['dpi']:.0f}")
            available_info_label.setStyleSheet("color: #B0BEC5; font-weight: bold; padding: 3px; font-size: 11px;")
            screen_layout.addWidget(available_info_label)

            # Optimal size info
            optimal_info_label = QLabel(f"🎯 Auto-Optimal: {screen_info['optimal_size']}")
            optimal_info_label.setStyleSheet("color: #4CAF50; font-weight: bold; padding: 3px; font-size: 11px;")
            screen_layout.addWidget(optimal_info_label)

            # Multi-screen info
            if screen_info['multiple_screens']:
                multi_screen_label = QLabel(f"🖥️ Multiple Screens: {screen_info['screen_count']} detected")
                multi_screen_label.setStyleSheet("color: #FF9800; font-weight: bold; padding: 3px; font-size: 10px;")
                screen_layout.addWidget(multi_screen_label)

            # Auto detection status
            status_label = QLabel("🔄 Auto-Detection: Active")
            status_label.setStyleSheet("color: #00BCD4; font-weight: bold; padding: 3px; font-size: 10px;")
            screen_layout.addWidget(status_label)

        # Auto Screen Buttons
        button_layout = QHBoxLayout()

        # Force Auto-Adjust Button
        auto_adjust_btn = QPushButton("⚡ Force Auto-Adjust")
        auto_adjust_btn.clicked.connect(self.force_auto_adjust)
        auto_adjust_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00BCD4, stop:1 #0f1419);
                color: white;
                border: 2px solid #00BCD4;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: #00BCD4;
                color: white;
                border: 2px solid white;
            }
        """)
        button_layout.addWidget(auto_adjust_btn)

        # Refresh Detection Button
        refresh_btn = QPushButton("🔄 Refresh Detection")
        refresh_btn.clicked.connect(self.refresh_screen_detection)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #0f1419);
                color: white;
                border: 2px solid #4CAF50;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                margin: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: #4CAF50;
                color: white;
                border: 2px solid white;
            }
        """)
        button_layout.addWidget(refresh_btn)

        screen_layout.addLayout(button_layout)

        layout.addWidget(screen_group)

        # Add Complete System Information Display
        try:
            # Get system detector from parent
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'system_info_widget'):
                parent_window = parent_window.parent()

            if parent_window and hasattr(parent_window, 'system_info_widget'):
                parent_window.system_info_widget.create_system_info_display(layout)
                self.logger.info("📊 Complete system information display added")
            else:
                self.logger.warning("⚠️ System info widget not found")

        except Exception as e:
            self.logger.error(f"❌ Add system info display error: {e}")

        # Fullscreen button
        fullscreen_btn = QPushButton("🖥️ Fullscreen")
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        layout.addWidget(fullscreen_btn)

        return toolbar

    def launch_quantum_chrome(self):
        """🚀 Smart Chrome launch with quantum extension"""
        try:
            self.logger.info("🚀 Smart Quantum Chrome launch...")
            self.main_status.setText("🔄 Checking Chrome...")
            self.connection_status.setText("🔍 Scanning...")

            # Check if Chrome is already running
            if self.quantum_extension_manager.is_quantum_chrome_running():
                self.logger.info("♻️ Chrome already running, focusing existing window...")
                self.main_status.setText("♻️ Chrome Already Running")
                self.connection_status.setText("🟢 Chrome Active")

                # Focus existing Chrome
                self.quantum_extension_manager.chrome_manager.focus_existing_chrome()

                # Navigate to Quotex if needed
                self.quantum_extension_manager.chrome_manager.navigate_to_url("https://quotex.io")

                return

            # Launch new Chrome instance
            self.main_status.setText("🚀 Launching Chrome...")
            self.connection_status.setText("🔄 Starting...")

            success = self.quantum_extension_manager.quantum_chrome_launch(force_new=False)

            if success:
                self.main_status.setText("✅ Chrome Launched")
                self.connection_status.setText("🟢 Chrome Ready")
                self.logger.info("✅ Quantum Chrome launched successfully")

                # Update extension status
                self.extension_label.setText("🔌 Extension: Loading...")

                # Set timer to check extension status
                QTimer.singleShot(5000, self.check_extension_status)

            else:
                self.main_status.setText("❌ Chrome Launch Failed")
                self.connection_status.setText("🔴 Launch Failed")
                self.logger.error("❌ Quantum Chrome launch failed")

        except Exception as e:
            self.logger.error(f"❌ Chrome launch error: {e}")
            self.main_status.setText("❌ Launch Error")
            self.connection_status.setText("🔴 Error")



    def close_chrome(self):
        """🛑 Close Chrome instances"""
        try:
            self.logger.info("🛑 Closing Chrome...")
            self.main_status.setText("🛑 Closing Chrome...")

            success = self.quantum_extension_manager.close_quantum_chrome()

            if success:
                self.main_status.setText("✅ Chrome Closed")
                self.connection_status.setText("🔴 Disconnected")
                self.extension_label.setText("🔌 Extension: Disconnected")
            else:
                self.main_status.setText("⚠️ Close Failed")

        except Exception as e:
            self.logger.error(f"❌ Chrome close error: {e}")

    def reload_webview(self):
        """🌐 Reload WebView"""
        try:
            self.logger.info("🌐 Reloading WebView...")
            self.main_status.setText("🔄 Reloading WebView...")
            self.connection_status.setText("🔄 Reloading...")

            # Reset status
            self.is_loaded = False
            self.live_indicator.setText("🔄 RELOADING")

            # Setup WebView again
            self.setup_quantum_webview()

            self.logger.info("✅ WebView reload initiated")

        except Exception as e:
            self.logger.error(f"❌ WebView reload error: {e}")
            self.main_status.setText("❌ Reload Failed")

    def start_quantum_stealth_connection(self):
        """🕵️‍♂️ Start quantum stealth connection to Chrome"""
        try:
            self.logger.info("🕵️‍♂️ Starting quantum stealth connection...")

            # Start Quantum Stealth Connector
            success = self.quantum_stealth_connector.start_connection()

            if success:
                self.logger.info("✅ Quantum stealth connection started")
                self.extension_label.setText("🕵️‍♂️ Stealth: Connecting...")
            else:
                self.logger.error("❌ Quantum stealth connection failed")
                self.extension_label.setText("🕵️‍♂️ Stealth: Failed")

        except Exception as e:
            self.logger.error(f"❌ Quantum stealth connection error: {e}")

    def on_quantum_stealth_data_received(self, event_type: str, data):
        """📡 Handle data received from quantum stealth connection"""
        try:
            if event_type == 'price_update':
                self.update_price_display(data)

            elif event_type == 'balance_update':
                self.update_balance_display(data)

            elif event_type == 'connection_status':
                if data:
                    self.extension_label.setText("🕵️‍♂️ Stealth: INVISIBLE")
                    self.connection_status.setText("🟢 Quantum Connected")
                    self.live_indicator.setText("🕵️‍♂️ STEALTH LIVE")
                else:
                    self.extension_label.setText("🕵️‍♂️ Stealth: Disconnected")

            elif event_type == 'trade_result':
                self.logger.info(f"📊 Stealth trade result: {data}")

        except Exception as e:
            self.logger.error(f"❌ Quantum stealth data handling error: {e}")

    def update_price_display(self, price_data):
        """📈 Update price display"""
        try:
            if price_data:
                # Get first available price
                for asset, info in price_data.items():
                    price = info.get('price', 0)
                    if price > 0:
                        self.price_label.setText(f"📈 {asset}: {price:.5f}")
                        break
        except Exception as e:
            self.logger.error(f"❌ Price display error: {e}")

    def update_balance_display(self, balance):
        """💰 Update balance display"""
        try:
            if balance > 0:
                self.balance_label.setText(f"💰 Balance: ${balance:.2f}")
                self.current_balance = balance
        except Exception as e:
            self.logger.error(f"❌ Balance display error: {e}")

    def connect_robot_to_chrome(self):
        """🕵️‍♂️ Connect quantum stealth robot to Chrome manually"""
        try:
            self.logger.info("🕵️‍♂️ Manual quantum stealth connection to Chrome...")
            self.main_status.setText("🕵️‍♂️ Activating Stealth...")

            # Get Quantum Stealth Connector status
            status = self.quantum_stealth_connector.get_connection_status()

            if status['is_connected']:
                self.logger.info("✅ Quantum stealth already connected")
                self.main_status.setText("✅ Stealth Active")
                self.extension_label.setText("🕵️‍♂️ Stealth: INVISIBLE")
            else:
                # Try to restart connection
                success = self.quantum_stealth_connector.start_connection()

                if success:
                    self.logger.info("✅ Quantum stealth connection established")
                    self.main_status.setText("✅ Stealth Active")
                    self.extension_label.setText("🕵️‍♂️ Stealth: INVISIBLE")

                    # Inject quantum stealth script into Chrome
                    script_path = self.quantum_stealth_connector.inject_quantum_stealth_script()
                    if script_path:
                        self.logger.info("💉 Quantum stealth script injected")

                        # Also inject into WebView
                        self.inject_quantum_stealth_into_webview()
                else:
                    self.logger.error("❌ Quantum stealth connection failed")
                    self.main_status.setText("❌ Stealth Connection Failed")
                    self.extension_label.setText("🕵️‍♂️ Stealth: Failed")

        except Exception as e:
            self.logger.error(f"❌ Quantum stealth connection error: {e}")
            self.main_status.setText("❌ Stealth Error")

    def inject_quantum_stealth_into_webview(self):
        """💉 Inject quantum stealth directly into WebView"""
        try:
            if hasattr(self, 'web_page') and self.web_page:
                # Get quantum stealth script
                stealth_script = self.quantum_stealth_connector.generate_quantum_stealth_script()

                # Inject into WebView
                self.web_page.runJavaScript(stealth_script)

                self.logger.info("✅ Quantum stealth injected into WebView")

        except Exception as e:
            self.logger.error(f"❌ WebView stealth injection error: {e}")

    def connect_to_existing_chrome(self):
        """🔗 Connect to existing Chrome instance"""
        try:
            self.logger.info("🔗 Connecting to existing Chrome...")
            self.main_status.setText("🔗 Connecting to Chrome...")

            # Start DevTools monitoring
            success = self.chrome_devtools_connector.start_monitoring()

            if success:
                self.logger.info("✅ Connected to existing Chrome")
                self.main_status.setText("✅ Chrome Connected")
                self.extension_label.setText("🔗 Chrome: Connected")
                self.connection_status.setText("🟢 DevTools Connected")
            else:
                self.logger.error("❌ Failed to connect to Chrome")
                self.main_status.setText("❌ Chrome Connection Failed")
                self.extension_label.setText("🔗 Chrome: Failed")

                # Show helpful message
                self.show_chrome_connection_help()

        except Exception as e:
            self.logger.error(f"❌ Chrome connection error: {e}")
            self.main_status.setText("❌ Connection Error")

    def show_chrome_connection_help(self):
        """💡 Show Chrome connection help"""
        try:
            from PySide6.QtWidgets import QMessageBox

            msg = QMessageBox(self)
            msg.setWindowTitle("🔗 Chrome Connection Help")
            msg.setIcon(QMessageBox.Information)

            help_text = """
🔗 Chrome Connection Failed

To connect to Chrome, you need to:

1️⃣ Launch Chrome with DevTools enabled:
   • Use "🚀 Launch Chrome" button first
   • Or manually start Chrome with: --remote-debugging-port=9222

2️⃣ Open Quotex in Chrome:
   • Go to https://quotex.io
   • Login to your account

3️⃣ Then click "🔗 Connect to Chrome" again

💡 The robot will automatically extract data from your Chrome tab!
            """

            msg.setText(help_text)
            msg.exec()

        except Exception as e:
            self.logger.error(f"❌ Help dialog error: {e}")

    def on_devtools_data_received(self, event_type: str, data):
        """📡 Handle data received from DevTools connection"""
        try:
            if event_type == 'price_update':
                self.update_price_display(data)

            elif event_type == 'balance_update':
                self.update_balance_display(data)

            elif event_type == 'connection_status':
                if data:
                    self.extension_label.setText("🔗 Chrome: Live Data")
                    self.connection_status.setText("🟢 DevTools Live")
                    self.live_indicator.setText("🔗 CHROME LIVE")
                else:
                    self.extension_label.setText("🔗 Chrome: Disconnected")

        except Exception as e:
            self.logger.error(f"❌ DevTools data handling error: {e}")

    def show_manual_bridge_instructions(self):
        """📋 Show manual bridge instructions"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout

            # Start bridge server
            if not self.manual_chrome_bridge.bridge_active:
                success = self.manual_chrome_bridge.start_bridge_server()
                if not success:
                    self.main_status.setText("❌ Bridge Server Failed")
                    return

            # Create instructions dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("📋 Manual Chrome Bridge Instructions")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # Instructions text
            instructions_text = QTextEdit()
            instructions_text.setReadOnly(True)
            instructions_text.setPlainText(self.manual_chrome_bridge.get_injection_instructions())
            instructions_text.setStyleSheet("""
                QTextEdit {
                    background-color: #1e1e1e;
                    color: #ffffff;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    border: 1px solid #444;
                    padding: 10px;
                }
            """)
            layout.addWidget(instructions_text)

            # Buttons
            button_layout = QHBoxLayout()

            copy_btn = QPushButton("📋 Copy Script")
            copy_btn.clicked.connect(lambda: self.copy_bridge_script())
            copy_btn.setStyleSheet("""
                QPushButton {
                    background: #4CAF50;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #45a049;
                }
            """)
            button_layout.addWidget(copy_btn)

            start_bridge_btn = QPushButton("🚀 Start Bridge")
            start_bridge_btn.clicked.connect(lambda: self.start_manual_bridge())
            start_bridge_btn.setStyleSheet("""
                QPushButton {
                    background: #2196F3;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #1976D2;
                }
            """)
            button_layout.addWidget(start_bridge_btn)

            close_btn = QPushButton("❌ Close")
            close_btn.clicked.connect(dialog.close)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: #f44336;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #d32f2f;
                }
            """)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"❌ Manual bridge instructions error: {e}")

    def copy_bridge_script(self):
        """📋 Copy bridge script to clipboard"""
        try:
            from PySide6.QtWidgets import QApplication

            script = self.manual_chrome_bridge.create_injection_script()
            clipboard = QApplication.clipboard()
            clipboard.setText(script)

            self.main_status.setText("📋 Script copied to clipboard!")
            self.logger.info("✅ Bridge script copied to clipboard")

        except Exception as e:
            self.logger.error(f"❌ Copy script error: {e}")

    def start_manual_bridge(self):
        """🚀 Start manual bridge"""
        try:
            if not self.manual_chrome_bridge.bridge_active:
                success = self.manual_chrome_bridge.start_bridge_server()

                if success:
                    self.main_status.setText("🚀 Bridge Server Started")
                    self.extension_label.setText("📋 Manual Bridge: Ready")
                    self.connection_status.setText("🟢 Bridge Ready")
                    self.logger.info("✅ Manual bridge server started")
                else:
                    self.main_status.setText("❌ Bridge Server Failed")
                    self.logger.error("❌ Failed to start manual bridge server")
            else:
                self.main_status.setText("✅ Bridge Already Running")

        except Exception as e:
            self.logger.error(f"❌ Start manual bridge error: {e}")

    def on_manual_bridge_data_received(self, event_type: str, data):
        """📡 Handle data received from manual bridge"""
        try:
            if event_type == 'price_update':
                self.update_price_display(data)

            elif event_type == 'balance_update':
                self.update_balance_display(data)

            elif event_type == 'connection_status':
                if data:
                    self.extension_label.setText("📋 Manual Bridge: Connected")
                    self.connection_status.setText("🟢 Manual Bridge Live")
                    self.live_indicator.setText("📋 MANUAL LIVE")
                else:
                    self.extension_label.setText("📋 Manual Bridge: Disconnected")

        except Exception as e:
            self.logger.error(f"❌ Manual bridge data handling error: {e}")

    def show_console_help(self):
        """🔧 Show console help dialog"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout, QTabWidget

            # Create help dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 Console Paste Help")
            dialog.setModal(True)
            dialog.resize(900, 700)

            layout = QVBoxLayout(dialog)

            # Create tab widget
            tab_widget = QTabWidget()

            # Tab 1: Console Instructions
            console_tab = QTextEdit()
            console_tab.setReadOnly(True)
            console_tab.setPlainText(self.console_paste_enabler.get_console_paste_instructions())
            console_tab.setStyleSheet("""
                QTextEdit {
                    background-color: #1e1e1e;
                    color: #ffffff;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    border: 1px solid #444;
                    padding: 10px;
                }
            """)
            tab_widget.addTab(console_tab, "📋 Console Instructions")

            # Tab 2: Manual Typing
            typing_tab = QTextEdit()
            typing_tab.setReadOnly(True)
            typing_tab.setPlainText(self.console_paste_enabler.get_step_by_step_typing_instructions())
            typing_tab.setStyleSheet("""
                QTextEdit {
                    background-color: #1e1e1e;
                    color: #ffffff;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    border: 1px solid #444;
                    padding: 10px;
                }
            """)
            tab_widget.addTab(typing_tab, "⌨️ Manual Typing")

            # Tab 3: Alternative Methods
            alt_tab = QTextEdit()
            alt_tab.setReadOnly(True)
            alt_tab.setPlainText(self.console_paste_enabler.get_alternative_injection_methods())
            alt_tab.setStyleSheet("""
                QTextEdit {
                    background-color: #1e1e1e;
                    color: #ffffff;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    border: 1px solid #444;
                    padding: 10px;
                }
            """)
            tab_widget.addTab(alt_tab, "🔄 Alternative Methods")

            # Tab 4: Simple Script
            simple_tab = QTextEdit()
            simple_tab.setReadOnly(True)
            simple_tab.setPlainText(f"""
🚀 SIMPLE SCRIPT FOR MANUAL TYPING

Copy this short script:

{self.console_paste_enabler.get_simplified_script()}

📝 Steps:
1️⃣ Open Chrome Console (F12)
2️⃣ Type: allow pasting
3️⃣ Press Enter
4️⃣ Now paste the script above
5️⃣ Press Enter
6️⃣ You should see "✅ VIP BIG BANG Active!"

💡 If paste still doesn't work, type the script manually line by line.
            """)
            simple_tab.setStyleSheet("""
                QTextEdit {
                    background-color: #1e1e1e;
                    color: #ffffff;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    border: 1px solid #444;
                    padding: 10px;
                }
            """)
            tab_widget.addTab(simple_tab, "🚀 Simple Script")

            layout.addWidget(tab_widget)

            # Buttons
            button_layout = QHBoxLayout()

            copy_simple_btn = QPushButton("📋 Copy Simple Script")
            copy_simple_btn.clicked.connect(lambda: self.copy_simple_script())
            copy_simple_btn.setStyleSheet("""
                QPushButton {
                    background: #4CAF50;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #45a049;
                }
            """)
            button_layout.addWidget(copy_simple_btn)

            create_bookmarklet_btn = QPushButton("🔖 Create Bookmarklet")
            create_bookmarklet_btn.clicked.connect(lambda: self.create_bookmarklet())
            create_bookmarklet_btn.setStyleSheet("""
                QPushButton {
                    background: #FF9800;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #F57C00;
                }
            """)
            button_layout.addWidget(create_bookmarklet_btn)

            close_btn = QPushButton("❌ Close")
            close_btn.clicked.connect(dialog.close)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: #f44336;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #d32f2f;
                }
            """)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"❌ Console help error: {e}")

    def copy_simple_script(self):
        """📋 Copy simple script to clipboard"""
        try:
            from PySide6.QtWidgets import QApplication

            script = self.console_paste_enabler.get_simplified_script()
            clipboard = QApplication.clipboard()
            clipboard.setText(script)

            self.main_status.setText("📋 Simple script copied!")
            self.logger.info("✅ Simple script copied to clipboard")

        except Exception as e:
            self.logger.error(f"❌ Copy simple script error: {e}")

    def create_bookmarklet(self):
        """🔖 Create bookmarklet file"""
        try:

    def create_bookmarklet(self):
        """🔖 Create bookmarklet file"""
        try:
            bookmarklet_file = self.console_paste_enabler.create_bookmarklet_file()

            if bookmarklet_file:
                self.main_status.setText(f"🔖 Bookmarklet created: {bookmarklet_file}")
                self.logger.info(f"✅ Bookmarklet file created: {bookmarklet_file}")

                # Try to open the file
                import os
                import subprocess
                try:
                    os.startfile(bookmarklet_file)
                except:
                    subprocess.run(['start', bookmarklet_file], shell=True)
            else:
                self.main_status.setText("❌ Bookmarklet creation failed")

        except Exception as e:
            self.logger.error(f"❌ Create bookmarklet error: {e}")

    def install_extension(self):
        """🔌 Install Chrome Extension"""
        try:
            from PySide6.QtWidgets import QMessageBox
            import subprocess

            # Show installation dialog
            msg = QMessageBox(self)
            msg.setWindowTitle("🔌 VIP BIG BANG Extension Installer")
            msg.setIcon(QMessageBox.Information)

            msg.setText("""
🔌 VIP BIG BANG CHROME EXTENSION

🚀 This is the PROFESSIONAL solution!

✅ Automatic anti-detection
✅ No console commands needed
✅ Real-time data extraction
✅ Professional UI popup
✅ Background monitoring

Click OK to install extension and launch Chrome.
            """)

            if msg.exec() == QMessageBox.Ok:
                self.main_status.setText("🔌 Installing Extension...")

                try:
                    # Run extension installer
                    result = subprocess.run([
                        "python", "extension_installer.py"
                    ], capture_output=True, text=True, cwd=".")

                    if result.returncode == 0:
                        self.main_status.setText("✅ Extension Installed!")
                        self.extension_label.setText("🔌 Extension: Installed")
                        self.connection_status.setText("🟢 Extension Active")

                        # Show success message
                        success_msg = QMessageBox(self)
                        success_msg.setWindowTitle("✅ Extension Installed")
                        success_msg.setIcon(QMessageBox.Information)
                        success_msg.setText("""
✅ VIP BIG BANG Extension Installed Successfully!

🚀 Chrome has been launched with the extension.

💡 Next steps:
1️⃣ Go to Quotex.io (should open automatically)
2️⃣ Login to your account
3️⃣ Click extension icon in Chrome toolbar
4️⃣ Verify "✅ Connected" status
5️⃣ Start trading!

🏆 NO CONSOLE COMMANDS NEEDED!
                        """)
                        success_msg.exec()

                    else:
                        self.main_status.setText("❌ Extension Installation Failed")

                        # Show manual installation instructions
                        manual_msg = QMessageBox(self)
                        manual_msg.setWindowTitle("🔧 Manual Installation")
                        manual_msg.setIcon(QMessageBox.Warning)
                        manual_msg.setText("""
🔧 Manual Installation Required:

1️⃣ Open Chrome
2️⃣ Go to: chrome://extensions/
3️⃣ Enable "Developer mode" (top right)
4️⃣ Click "Load unpacked"
5️⃣ Select "vip_extension" folder
6️⃣ Extension will be installed!

📋 Check EXTENSION_INSTALLATION_GUIDE.txt for details.
                        """)
                        manual_msg.exec()

                except Exception as install_error:
                    self.logger.error(f"❌ Extension installation error: {install_error}")
                    self.main_status.setText("❌ Installation Error")

        except Exception as e:
            self.logger.error(f"❌ Install extension error: {e}")

    def auto_start_extension(self):
        """🚀 Auto-start extension on program startup"""
        try:
            self.logger.info("🚀 Auto-starting VIP BIG BANG Extension...")

            # Start extension in background
            success = self.auto_extension_manager.launch_chrome_with_extension()

            if success:
                self.logger.info("✅ Extension auto-started successfully!")
                self.main_status.setText("🔌 Extension Auto-Started")
                self.extension_label.setText("🔌 Extension: Auto-Active")
                self.connection_status.setText("🟢 Extension Running")

                # Start monitoring extension status
                self.start_extension_monitoring()

            else:
                self.logger.warning("⚠️ Extension auto-start failed")
                self.main_status.setText("⚠️ Extension Auto-Start Failed")

        except Exception as e:
            self.logger.error(f"❌ Auto-start extension error: {e}")

    def start_extension_monitoring(self):
        """📊 Start monitoring extension status"""
        try:
            # Create timer for extension status monitoring
            self.extension_monitor_timer = QTimer()
            self.extension_monitor_timer.timeout.connect(self.check_extension_status)
            self.extension_monitor_timer.start(5000)  # Check every 5 seconds

            self.logger.info("📊 Extension monitoring started")

        except Exception as e:
            self.logger.error(f"❌ Extension monitoring error: {e}")

    def check_extension_status(self):
        """🔍 Check extension status periodically"""
        try:
            status = self.auto_extension_manager.check_extension_status()

            if status["status"] == "active":
                self.extension_label.setText(f"🔌 Extension: Active ({status.get('tabs', 0)} tabs)")
                self.connection_status.setText("🟢 Extension Live")
                self.live_indicator.setText("🔌 EXTENSION LIVE")

            elif status["status"] == "chrome_running":
                self.extension_label.setText("🔌 Extension: Chrome Running")
                self.connection_status.setText("🟡 Chrome Ready")

            elif status["status"] == "not_running":
                self.extension_label.setText("🔌 Extension: Not Running")
                self.connection_status.setText("🔴 Extension Stopped")

            elif status["status"] == "chrome_closed":
                self.extension_label.setText("🔌 Extension: Chrome Closed")
                self.connection_status.setText("🔴 Chrome Closed")

            else:
                self.extension_label.setText("🔌 Extension: Unknown Status")
                self.connection_status.setText("⚠️ Status Unknown")

        except Exception as e:
            self.logger.error(f"❌ Extension status check error: {e}")

    def show_extension_status(self):
        """📊 Show extension status dialog"""
        try:
            from PySide6.QtWidgets import QMessageBox

            status = self.auto_extension_manager.check_extension_status()
            info = self.auto_extension_manager.get_extension_info()

            msg = QMessageBox(self)
            msg.setWindowTitle("🔌 Extension Status")
            msg.setIcon(QMessageBox.Information)

            status_text = f"""
🔌 VIP BIG BANG Extension Status

📊 Current Status: {status['status']}
💬 Message: {status['message']}

📁 Extension Directory: {info['extension_dir']}
🔌 Extension Installed: {'✅ Yes' if info['extension_installed'] else '❌ No'}
🌐 Chrome Running: {'✅ Yes' if info['chrome_running'] else '❌ No'}
📄 Files Exist: {'✅ Yes' if info['files_exist'] else '❌ No'}

💡 Extension automatically starts with the program.
💡 If Chrome is closed, click "🔄 Restart Extension".
            """

            msg.setText(status_text)
            msg.exec()

        except Exception as e:
            self.logger.error(f"❌ Show extension status error: {e}")

    def restart_extension(self):
        """🔄 Restart extension"""
        try:
            from PySide6.QtWidgets import QMessageBox

            self.main_status.setText("🔄 Restarting Extension...")
                self.main_status.setText("✅ Extension Restarted")
                self.extension_label.setText("🔌 Extension: Restarted")
                self.connection_status.setText("🟢 Extension Active")

                msg = QMessageBox(self)
                msg.setWindowTitle("✅ Extension Restarted")
                msg.setIcon(QMessageBox.Information)
                msg.setText("""
✅ Extension Restarted Successfully!

🚀 Chrome has been relaunched with the extension.
🔗 Go to Quotex.io to start trading.
📊 Extension will automatically extract data.

💡 No manual setup required!
                """)
                msg.exec()

            else:
                self.main_status.setText("❌ Extension Restart Failed")

                msg = QMessageBox(self)
                msg.setWindowTitle("❌ Restart Failed")
                msg.setIcon(QMessageBox.Warning)
                msg.setText("""
❌ Extension restart failed.

🔧 Please check:
- Chrome is properly installed
- Extension files exist
- No other Chrome instances running

💡 Try closing all Chrome windows and restart.
                """)
                msg.exec()

        except Exception as e:
            self.logger.error(f"❌ Restart extension error: {e}")

    def launch_direct_quotex(self):
        """🚀 Launch Chrome directly to Quotex"""
        try:
            from PySide6.QtWidgets import QMessageBox
            import subprocess

            self.main_status.setText("🚀 Launching Quotex...")

            # Run persistent Chrome manager (REMEMBERS LOGIN)
            result = subprocess.run([
                "python", "persistent_chrome_manager.py"
            ], capture_output=True, text=True, cwd=".")

            if result.returncode == 0:
                self.main_status.setText("✅ Quotex Launched!")
                self.extension_label.setText("🚀 Quotex: Direct Launch")
                self.connection_status.setText("🟢 Quotex Loading")

                # Show success message
                msg = QMessageBox(self)
                msg.setWindowTitle("✅ Quotex Launched")
                msg.setIcon(QMessageBox.Information)
                msg.setText("""
✅ Persistent Chrome Launched Successfully!

💾 Chrome opened with PERSISTENT profile
🎯 URL: https://quotex.io/en/sign-in
🔄 Login will be REMEMBERED next time!
📊 Same profile used always!

💡 Benefits:
✅ Stay logged in between sessions
✅ Settings and bookmarks saved
✅ No need to login every time
✅ Faster access to trading

🏆 Your login will be remembered!
                """)
                msg.exec()

            else:
                self.main_status.setText("❌ Quotex Launch Failed")

                # Show error message
                msg = QMessageBox(self)
                msg.setWindowTitle("❌ Launch Failed")
                msg.setIcon(QMessageBox.Warning)
                msg.setText("""
❌ Failed to launch Quotex directly.

🔧 Possible issues:
- Chrome not properly installed
- Network connectivity issues
- Quotex site may be blocked

💡 Try using Extension method instead.
                """)
                msg.exec()

        except Exception as e:
            self.logger.error(f"❌ Launch direct Quotex error: {e}")
            self.main_status.setText("❌ Launch Error")

    def fix_window_size(self):
        """📐 Fix window size to fit screen perfectly"""
        try:
            if not self.screen_fixer_ready:
                self.main_status.setText("❌ Screen fixer not ready")
                return

            # Get main window
            main_window = self.parent()
            if not main_window:
                # Try to find main window
                widget = self
                while widget.parent():
                    widget = widget.parent()
                main_window = widget

            if main_window:
                # Set optimal size
                main_window.resize(self.optimal_width, self.optimal_height)

                # Center window
                self.center_window_simple(main_window)

                # Update status
                self.main_status.setText(f"✅ Window Fixed: {self.optimal_width}x{self.optimal_height}")

                self.logger.info(f"📐 Window fixed: {self.optimal_width}x{self.optimal_height}")

                # Show success message
                from PySide6.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("✅ Window Fixed")
                msg.setIcon(QMessageBox.Information)
                msg.setText(f"""
✅ Window Size Fixed Successfully!

📐 Size: {self.optimal_width}x{self.optimal_height}
📺 Screen: {self.screen_geometry.width()}x{self.screen_geometry.height()}
🎯 Centered on screen

✅ Window now fits your screen perfectly!
                """)
                msg.exec()

            else:
                self.logger.warning("⚠️ Main window not found")
                self.main_status.setText("⚠️ Main window not found")

        except Exception as e:
            self.logger.error(f"❌ Fix window size error: {e}")
            self.main_status.setText("❌ Fix size error")

    def center_window_simple(self, window):
        """🎯 Center window on screen (simple method)"""
        try:
            # Get window frame geometry
            window_geometry = window.frameGeometry()

            # Get screen center
            screen_center = self.available_geometry.center()

            # Move window center to screen center
            window_geometry.moveCenter(screen_center)

            # Move window to calculated position
            window.move(window_geometry.topLeft())

            self.logger.info(f"🎯 Window centered at: {window_geometry.topLeft().x()}, {window_geometry.topLeft().y()}")

        except Exception as e:
            self.logger.error(f"❌ Center window error: {e}")

    def center_window(self):
        """🎯 Center window button action"""
        try:
            # Get main window
            main_window = self.parent()
            if not main_window:
                # Try to find main window
                widget = self
                while widget.parent():
                    widget = widget.parent()
                main_window = widget

            if main_window:
                self.center_window_simple(main_window)
                self.main_status.setText("🎯 Window Centered")

                # Show success message
                from PySide6.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("🎯 Window Centered")
                msg.setIcon(QMessageBox.Information)
                msg.setText("🎯 Window has been centered on your screen!")
                msg.exec()

            else:
                self.main_status.setText("⚠️ Main window not found")

        except Exception as e:
            self.logger.error(f"❌ Center window action error: {e}")
            self.main_status.setText("❌ Center error")

    def apply_initial_screen_fix(self):
        """📐 Apply initial screen fix after UI is ready"""
        try:
            if self.screen_fixer_ready:
                # Get main window
                main_window = self.parent()
                if not main_window:
                    # Try to find main window
                    widget = self
                    while widget.parent():
                        widget = widget.parent()
                    main_window = widget

                if main_window:
                    # Apply initial screen fix
                    main_window.resize(self.optimal_width, self.optimal_height)
                    self.center_window_simple(main_window)

                    self.logger.info(f"📐 Initial screen fix applied: {self.optimal_width}x{self.optimal_height}")

        except Exception as e:
            self.logger.error(f"❌ Initial screen fix error: {e}")

    def register_for_auto_screen(self):
        """📝 Register window for auto screen management"""
        try:
            if self.auto_screen_initialized and self.auto_screen_manager:
                # Get main window
                main_window = self.parent()
                if not main_window:
                    # Try to find main window
                    widget = self
                    while widget.parent():
                        widget = widget.parent()
                    main_window = widget

                if main_window:
                    # Register window for auto management
                    self.auto_screen_manager.register_window(main_window)
                    self.logger.info("📝 Window registered for auto screen management")

        except Exception as e:
            self.logger.error(f"❌ Register for auto screen error: {e}")

    def apply_auto_screen_adjustment(self):
        """📐 Apply automatic screen adjustment"""
        try:
            if self.auto_screen_initialized and self.auto_detector:
                # Get main window
                main_window = self.parent()
                if not main_window:
                    # Try to find main window
                    widget = self
                    while widget.parent():
                        widget = widget.parent()
                    main_window = widget

                if main_window:
                    # Apply auto adjustment
                    success = self.auto_detector.auto_adjust_window(main_window)

                    if success:
                        screen_info = self.auto_detector.get_screen_info()
                        self.main_status.setText(f"📐 Auto-Adjusted: {screen_info['optimal_size']}")
                        self.logger.info("📐 Auto screen adjustment applied successfully")
                    else:
                        self.main_status.setText("❌ Auto-adjustment failed")

        except Exception as e:
            self.logger.error(f"❌ Apply auto screen adjustment error: {e}")

    def force_auto_adjust(self):
        """⚡ Force auto-adjust window"""
        try:
            if not self.auto_screen_initialized:
                self.main_status.setText("❌ Auto screen detection not available")
                return

            # Get main window
            main_window = self.parent()
            if not main_window:
                # Try to find main window
                widget = self
                while widget.parent():
                    widget = widget.parent()
                main_window = widget

            if main_window and self.auto_detector:
                # Force adjustment
                success = self.auto_detector.force_window_adjustment(main_window)

                if success:
                    screen_info = self.auto_detector.get_screen_info()
                    self.main_status.setText(f"⚡ Force Adjusted: {screen_info['optimal_size']}")

                    # Show success message
                    from PySide6.QtWidgets import QMessageBox
                    msg = QMessageBox(self)
                    msg.setWindowTitle("⚡ Force Auto-Adjust Complete")
                    msg.setIcon(QMessageBox.Information)
                    msg.setText(f"""
⚡ Window Force Auto-Adjusted Successfully!

📺 Screen Type: {screen_info['screen_type']}
📐 Screen Size: {screen_info['screen_size']}
🎯 Optimal Size: {screen_info['optimal_size']}
🔍 DPI: {screen_info['dpi']:.0f}

✅ Window now perfectly fits your screen!
                    """)
                    msg.exec()

                else:
                    self.main_status.setText("❌ Force adjustment failed")
            else:
                self.main_status.setText("⚠️ Main window not found")

        except Exception as e:
            self.logger.error(f"❌ Force auto adjust error: {e}")
            self.main_status.setText("❌ Force adjust error")

    def refresh_screen_detection(self):
        """🔄 Refresh screen detection"""
        try:
            if not self.auto_screen_initialized:
                self.main_status.setText("❌ Auto screen detection not available")
                return

            if self.auto_detector:
                # Force screen check
                self.auto_detector.check_screen_changes()

                # Get updated screen info
                screen_info = self.auto_detector.get_screen_info()

                self.main_status.setText(f"🔄 Detection Refreshed: {screen_info['screen_type']}")

                # Show updated info
                from PySide6.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("🔄 Screen Detection Refreshed")
                msg.setIcon(QMessageBox.Information)
                msg.setText(f"""
🔄 Screen Detection Refreshed Successfully!

📺 Screen Type: {screen_info['screen_type']}
📐 Screen Size: {screen_info['screen_size']}
📊 Available: {screen_info['available_size']}
🎯 Optimal: {screen_info['optimal_size']}
🔍 DPI: {screen_info['dpi']:.0f}
🖥️ Multiple Screens: {'Yes' if screen_info['multiple_screens'] else 'No'}

🔄 Auto-detection is monitoring for changes...
                """)
                msg.exec()

        except Exception as e:
            self.logger.error(f"❌ Refresh screen detection error: {e}")
            self.main_status.setText("❌ Refresh detection error")

    def change_ui_scale(self, scale: str):
        """📐 Change UI scale"""
        try:
            if not self.ui_manager:
                return

            self.logger.info(f"📐 Changing UI scale to: {scale}")

            # Save preference
            self.ui_manager.save_scale_preference(scale)

            # Apply new scale to main window
            if hasattr(self, 'parent') and self.parent():
                self.ui_manager.apply_responsive_design(self.parent(), scale)

            # Update status
            self.main_status.setText(f"📐 UI Scale: {scale.title()}")

            # Show confirmation
            from PySide6.QtWidgets import QMessageBox
            msg = QMessageBox(self)
            msg.setWindowTitle("📐 UI Scale Changed")
            msg.setIcon(QMessageBox.Information)
            msg.setText(f"""
📐 UI Scale Changed Successfully!

🎯 New Scale: {scale.title()}
🖥️ Window size adjusted automatically
💾 Preference saved for next time

✅ UI is now optimized for your screen!
            """)
            msg.exec()

        except Exception as e:
            self.logger.error(f"❌ Change UI scale error: {e}")

    def quick_scale_change(self, scale: str):
        """⚡ Quick scale change"""
        try:
            # Update combo box
            self.scale_combo.setCurrentText(scale)

            # Apply scale change
            self.change_ui_scale(scale)

        except Exception as e:
            self.logger.error(f"❌ Quick scale change error: {e}")

    def auto_resize_to_screen(self):
        """📐 Auto-resize window to fit screen perfectly"""
        try:
            if not self.ui_manager:
                return

            self.logger.info("📐 Auto-resizing window to screen...")

            # Get current scale
            current_scale = self.scale_combo.currentText()

            # Get main window
            main_window = self.parent()
            if not main_window:
                # Try to find main window
                widget = self
                while widget.parent():
                    widget = widget.parent()
                main_window = widget

            if main_window:
                # Get detailed screen info
                detailed_info = self.ui_manager.get_detailed_screen_info()
                primary = detailed_info["primary_screen"]

                # Apply perfect responsive design
                self.ui_manager.apply_responsive_design(main_window, current_scale)

                # Update status
                self.main_status.setText(f"📐 Auto-Resized: {current_scale.title()}")

                # Show success message
                from PySide6.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("📐 Auto-Resize Complete")
                msg.setIcon(QMessageBox.Information)
                msg.setText(f"""
📐 Window Auto-Resized Successfully!

🖥️ Screen: {primary['geometry']}
📊 Available: {primary['available']}
🎯 Scale: {current_scale.title()}
📍 Position: Centered

✅ Window now fits your screen perfectly!
                """)
                msg.exec()

            else:
                self.logger.warning("⚠️ Main window not found for auto-resize")

        except Exception as e:
            self.logger.error(f"❌ Auto-resize error: {e}")

    def toggle_auto_trade(self):
        """🤖 Toggle auto-trade mode"""
        try:
            if hasattr(self, 'auto_trade_enabled') and self.auto_trade_enabled:
                # Disable auto-trade
                self.auto_trade_enabled = False
                self.auto_trade_btn.setText("🤖 Enable Auto-Trade")
                self.auto_trade_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #2196F3, stop:1 #0f1419);
                        color: white;
                        border: 2px solid #2196F3;
                        padding: 10px 15px;
                        border-radius: 5px;
                        font-weight: bold;
                        margin: 5px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background: #2196F3;
                        color: white;
                        border: 2px solid white;
                    }
                """)
                self.main_status.setText("🤖 Auto-Trade Disabled")
                self.trading_status_label.setText("🔴 Trading: Manual Mode")
                self.trading_status_label.setStyleSheet("""
                    QLabel {
                        color: white;
                        font-weight: bold;
                        background: rgba(255, 152, 0, 0.2);
                        border: 1px solid #FF9800;
                        border-radius: 5px;
                        padding: 5px;
                        margin: 5px;
                    }
                """)
                self.logger.info("🤖 Auto-trade disabled")

            else:
                # Enable auto-trade
                self.auto_trade_enabled = True
                self.auto_trade_btn.setText("🛑 Disable Auto-Trade")
                self.auto_trade_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #4CAF50, stop:1 #0f1419);
                        color: white;
                        border: 2px solid #4CAF50;
                        padding: 10px 15px;
                        border-radius: 5px;
                        font-weight: bold;
                        margin: 5px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background: #4CAF50;
                        color: white;
                        border: 2px solid white;
                    }
                """)
                self.main_status.setText("🤖 Auto-Trade Enabled")
                self.trading_status_label.setText("🟢 Trading: Auto Mode Active")
                self.trading_status_label.setStyleSheet("""
                    QLabel {
                        color: white;
                        font-weight: bold;
                        background: rgba(76, 175, 80, 0.2);
                        border: 1px solid #4CAF50;
                        border-radius: 5px;
                        padding: 5px;
                        margin: 5px;
                    }
                """)
                self.logger.info("🤖 Auto-trade enabled")

                # Start auto-trading logic
                self.start_auto_trading()

        except Exception as e:
            self.logger.error(f"❌ Toggle auto-trade error: {e}")

    def manual_trade(self, direction: str):
        """📈📉 Execute manual trade"""
        try:
            amount = self.amount_input.value()
            duration_text = self.duration_combo.currentText()

            # Convert duration to seconds
            duration_map = {
                "1 min": 60,
                "5 min": 300,
                "15 min": 900,
                "30 min": 1800,
                "1 hour": 3600
            }
            duration = duration_map.get(duration_text, 60)

            self.logger.info(f"🚀 Manual trade: {direction} ${amount} {duration_text}")

            # Execute trade through extension
            success = self.execute_trade_through_extension(
                asset="EUR/USD",  # Default asset
                direction=direction,
                amount=amount,
                duration=duration
            )

            if success:
                self.main_status.setText(f"✅ {direction} Trade Executed - ${amount}")

                # Show trade confirmation
                from PySide6.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("✅ Trade Executed")
                msg.setIcon(QMessageBox.Information)
                msg.setText(f"""
✅ Trade Successfully Executed!

📊 Direction: {direction}
💰 Amount: ${amount}
⏱️ Duration: {duration_text}
🎯 Asset: EUR/USD

🤖 Robot is monitoring the trade...
                """)
                msg.exec()

            else:
                self.main_status.setText(f"❌ {direction} Trade Failed")

        except Exception as e:
            self.logger.error(f"❌ Manual trade error: {e}")
            self.main_status.setText("❌ Trade Error")

    def execute_trade_through_extension(self, asset: str, direction: str, amount: float, duration: int) -> bool:
        """🚀 Execute trade through Chrome extension"""
        try:
            # Check if extension is active
            status = self.auto_extension_manager.check_extension_status()

            if status["status"] != "active":
                self.logger.warning("⚠️ Extension not active, cannot execute trade")
                return False

            # Send trade command to extension via DevTools
            import requests

            trade_script = f"""
            // VIP BIG BANG Trade Execution
            (function() {{
                console.log('🚀 Executing VIP BIG BANG Trade...');
                console.log('📊 Direction: {direction}');
                console.log('💰 Amount: ${amount}');
                console.log('⏱️ Duration: {duration}s');

                // Find trade buttons
                const callButton = document.querySelector('[data-testid="call-button"], .call-button, .buy-button, [class*="call"], [class*="up"]');
                const putButton = document.querySelector('[data-testid="put-button"], .put-button, .sell-button, [class*="put"], [class*="down"]');

                // Set amount if possible
                const amountInput = document.querySelector('[data-testid="amount"], .amount-input, [class*="amount"]');
                if (amountInput) {{
                    amountInput.value = {amount};
                    amountInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    amountInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                }}

                // Click appropriate button
                let targetButton = null;
                if ('{direction}' === 'CALL' && callButton) {{
                    targetButton = callButton;
                }} else if ('{direction}' === 'PUT' && putButton) {{
                    targetButton = putButton;
                }}

                if (targetButton) {{
                    // Simulate human click
                    const rect = targetButton.getBoundingClientRect();
                    const x = rect.left + rect.width / 2;
                    const y = rect.top + rect.height / 2;

                    const clickEvent = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: x,
                        clientY: y
                    }});

                    targetButton.dispatchEvent(clickEvent);

                    console.log('✅ VIP BIG BANG Trade Executed!');
                    return true;
                }} else {{
                    console.log('❌ Trade buttons not found');
                    return false;
                }}
            }})();
            """

            # Execute script via DevTools
            try:
                response = requests.get("http://localhost:9222/json", timeout=5)
                if response.status_code == 200:
                    tabs = response.json()

                    for tab in tabs:
                        if 'quotex' in tab.get('url', '').lower():
                            # Execute trade script
                            payload = {
                                "id": 1,
                                "method": "Runtime.evaluate",
                                "params": {
                                    "expression": trade_script,
                                    "returnByValue": True
                                }
                            }

                            tab_url = f"http://localhost:9222/json/runtime/evaluate"
                            trade_response = requests.post(tab_url, json=payload, timeout=10)

                            if trade_response.status_code == 200:
                                self.logger.info("✅ Trade script executed successfully")
                                return True
                            else:
                                self.logger.error("❌ Trade script execution failed")
                                return False

            except Exception as devtools_error:
                self.logger.error(f"❌ DevTools trade execution error: {devtools_error}")
                return False

            return False

        except Exception as e:
            self.logger.error(f"❌ Execute trade through extension error: {e}")
            return False

    def start_auto_trading(self):
        """🤖 Start auto-trading logic"""
        try:
            if not hasattr(self, 'auto_trade_timer'):
                self.auto_trade_timer = QTimer()
                self.auto_trade_timer.timeout.connect(self.auto_trade_logic)

            # Start auto-trade timer (check every 10 seconds)
            self.auto_trade_timer.start(10000)
            self.logger.info("🤖 Auto-trading started")

        except Exception as e:
            self.logger.error(f"❌ Start auto-trading error: {e}")

    def auto_trade_logic(self):
        """🤖 Auto-trading decision logic"""
        try:
            if not hasattr(self, 'auto_trade_enabled') or not self.auto_trade_enabled:
                return

            # Simple auto-trading logic based on price movement
            # This is a basic example - you can implement more sophisticated logic

            # Get current price data
            if hasattr(self, 'latest_price_data') and self.latest_price_data:
                # Simple trend analysis
                import random

                # Random decision for demo (replace with real analysis)
                if random.random() > 0.7:  # 30% chance to trade
                    direction = "CALL" if random.random() > 0.5 else "PUT"
                    amount = self.amount_input.value()

                    self.logger.info(f"🤖 Auto-trade decision: {direction}")

                    # Execute auto-trade
                    success = self.execute_trade_through_extension(
                        asset="EUR/USD",
                        direction=direction,
                        amount=amount,
                        duration=60
                    )

                    if success:
                        self.main_status.setText(f"🤖 Auto-Trade: {direction} ${amount}")

        except Exception as e:
            self.logger.error(f"❌ Auto-trade logic error: {e}")

    def create_status_bar(self):
        """📊 Create bottom status bar"""
        status_bar = QWidget()
        status_bar.setFixedHeight(35)
        status_bar.setStyleSheet("""
            QWidget {
                background: #0f0f23;
                border-top: 1px solid #00FF00;
            }
            QLabel {
                color: #00FF00;
                font-family: 'Courier New', monospace;
                padding: 5px 10px;
                font-size: 12px;
            }
        """)
        
        layout = QHBoxLayout(status_bar)
        
        # Balance display
        self.balance_label = QLabel("💰 Balance: $0.00")
        layout.addWidget(self.balance_label)
        
        # Price display
        self.price_label = QLabel("📈 EUR/USD: 0.00000")
        layout.addWidget(self.price_label)
        
        # Extension status
        self.extension_label = QLabel("🔌 Extension: Not Connected")
        layout.addWidget(self.extension_label)
        
        # Live indicator
        self.live_indicator = QLabel("🔴 OFFLINE")
        layout.addWidget(self.live_indicator)
        
        layout.addStretch()
        
        # Timestamp
        self.timestamp_label = QLabel("⏰ --:--:--")
        layout.addWidget(self.timestamp_label)
        
        # Update timestamp every second
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_timestamp)
        self.timer.start(1000)
        
        return status_bar

    def setup_quantum_webview(self):
        """🚀 Setup quantum-enhanced WebView with security bypass"""
        try:
            self.logger.info("🚀 Setting up quantum WebView...")
            self.main_status.setText("🔄 Setting up WebView...")

            # Create quantum profile
            profile = QWebEngineProfile.defaultProfile()

            # Apply quantum security bypass settings
            profile = self.quantum_security_bypass.apply_quantum_profile_settings(profile)

            # Create WebView with quantum profile if not exists
            if not hasattr(self, 'web_view') or self.web_view is None:
                self.web_view = QWebEngineView()

            # Create custom page with quantum bypass
            self.web_page = QuantumWebEnginePage(profile, self.web_view)
            self.web_view.setPage(self.web_page)

            # Replace placeholder with actual WebView
            if hasattr(self, 'webview_placeholder') and self.webview_placeholder:
                # Find the parent layout and replace placeholder
                parent_layout = self.webview_placeholder.parent().layout()
                if parent_layout:
                    parent_layout.removeWidget(self.webview_placeholder)
                    self.webview_placeholder.deleteLater()
                    parent_layout.addWidget(self.web_view)
                    self.webview_placeholder = None

            # Connect page signals
            self.web_page.loadFinished.connect(self.on_quantum_page_loaded)
            self.web_page.loadStarted.connect(self.on_quantum_page_loading)

            # Setup JavaScript bridge
            self.setup_javascript_bridge()

            # Update status
            self.main_status.setText("🌐 Loading Quotex...")
            self.connection_status.setText("🔄 Connecting...")

            # Load quantum Quotex URL
            quantum_url = self.quantum_security_bypass.create_quantum_quotex_url()
            self.web_view.load(QUrl(quantum_url))

            self.logger.info("✅ Quantum WebView setup complete")

        except Exception as e:
            self.logger.error(f"❌ Quantum WebView setup failed: {e}")
            self.main_status.setText("❌ WebView Setup Failed")
            # Fallback to standard WebView
            self.setup_webview()

    def on_quantum_page_loaded(self, success):
        """🚀 Handle quantum page load completion"""
        if success:
            self.logger.info("🚀 Quantum Quotex page loaded successfully")
            self.is_loaded = True
            self.connection_status.setText("🟢 Quantum Connected")
            self.live_indicator.setText("🚀 QUANTUM LIVE")
            self.main_status.setText("✅ Quantum Page Loaded")

            # Inject quantum bypass scripts with delay for better effectiveness
            QTimer.singleShot(1000, lambda: self.inject_quantum_security_bypass())
            QTimer.singleShot(1500, lambda: self.inject_quantum_stealth())
            QTimer.singleShot(2000, lambda: self.inject_vip_integration())
            QTimer.singleShot(3000, lambda: self.inject_quotex_specific_bypass())

        else:
            self.logger.error("❌ Quantum Quotex page load failed")
            self.connection_status.setText("🔴 Quantum Load Failed")
            self.live_indicator.setText("🔴 OFFLINE")
            self.main_status.setText("❌ Page Load Failed")

    def inject_quantum_security_bypass(self):
        """🛡️ Inject quantum security bypass scripts"""
        try:
            self.logger.info("🛡️ Injecting quantum security bypass...")

            # Inject all quantum bypass scripts
            self.quantum_security_bypass.inject_quantum_bypass_scripts(self.web_page)

            # Additional Quotex-specific bypass
            quotex_bypass_script = """
            // Advanced Quotex Security Bypass
            (function() {
                console.log('🛡️ Quotex Security Bypass Loading...');

                // Remove security warnings immediately
                function removeSecurityWarnings() {
                    const selectors = [
                        '[class*="security"]',
                        '[class*="warning"]',
                        '[class*="error"]',
                        '[class*="blocked"]',
                        '[class*="unsupported"]',
                        '[class*="browser"]',
                        'div:contains("secure")',
                        'div:contains("browser")',
                        'div:contains("supported")',
                        'div:contains("different browser")',
                        'div:contains("Try using")'
                    ];

                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                const text = el.textContent.toLowerCase();
                                if (text.includes('secure') ||
                                    text.includes('browser') ||
                                    text.includes('supported') ||
                                    text.includes('different browser') ||
                                    text.includes('try using')) {
                                    el.style.display = 'none';
                                    el.remove();
                                }
                            });
                        } catch(e) {}
                    });
                }

                // Run immediately and continuously
                removeSecurityWarnings();
                setInterval(removeSecurityWarnings, 500);

                // Override security check functions
                window.checkBrowserSecurity = () => true;
                window.validateBrowser = () => true;
                window.isBrowserSupported = () => true;
                window.isSecureBrowser = () => true;

                // Set security flags
                window.BROWSER_SECURE = true;
                window.SECURITY_CHECK_PASSED = true;
                window.BROWSER_SUPPORTED = true;
                window.IS_SECURE_CONTEXT = true;

                // Override location.protocol if needed
                if (location.protocol === 'http:') {
                    Object.defineProperty(location, 'protocol', {
                        get: () => 'https:',
                        configurable: true
                    });
                }

                console.log('🏆 Quotex Security Bypass Complete!');
            })();
            """

            self.web_page.runJavaScript(quotex_bypass_script)
            self.logger.info("✅ Quantum security bypass injected")

        except Exception as e:
            self.logger.error(f"❌ Security bypass injection failed: {e}")

    def inject_quantum_stealth(self):
        """🕵️‍♂️ Inject quantum stealth system"""
        try:
            self.logger.info("🕵️‍♂️ Injecting quantum stealth system...")

            # Get quantum stealth script
            stealth_script = self.quantum_stealth_system.generate_quantum_stealth_script()

            # Inject stealth script
            self.web_page.runJavaScript(stealth_script)

            self.logger.info("✅ Quantum stealth system injected - Robot now invisible!")

        except Exception as e:
            self.logger.error(f"❌ Quantum stealth injection failed: {e}")

    def inject_quotex_specific_bypass(self):
        """🎯 Inject Quotex-specific security bypass"""
        try:
            quotex_specific_script = """
            // Quotex-Specific Security Bypass
            (function() {
                console.log('🎯 Quotex-Specific Bypass Loading...');

                // Monitor for login page and bypass security checks
                function bypassLoginSecurity() {
                    // Look for login forms
                    const loginForms = document.querySelectorAll('form, [class*="login"], [class*="auth"]');

                    loginForms.forEach(form => {
                        // Remove any security validation
                        const inputs = form.querySelectorAll('input');
                        inputs.forEach(input => {
                            input.removeAttribute('pattern');
                            input.removeAttribute('required');
                        });
                    });

                    // Override form submission to bypass security
                    const forms = document.querySelectorAll('form');
                    forms.forEach(form => {
                        const originalSubmit = form.submit;
                        form.submit = function() {
                            // Remove security checks before submit
                            window.BROWSER_SECURE = true;
                            window.SECURITY_CHECK_PASSED = true;
                            return originalSubmit.call(this);
                        };
                    });
                }

                // Run bypass functions
                bypassLoginSecurity();

                // Monitor for dynamic content
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes.length > 0) {
                            // Check for security warnings in new content
                            mutation.addedNodes.forEach(node => {
                                if (node.nodeType === 1) { // Element node
                                    const text = node.textContent || '';
                                    if (text.includes('secure') ||
                                        text.includes('browser') ||
                                        text.includes('supported')) {
                                        node.style.display = 'none';
                                        node.remove();
                                    }
                                }
                            });

                            // Re-run bypass for new forms
                            bypassLoginSecurity();
                        }
                    });
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                console.log('🏆 Quotex-Specific Bypass Complete!');
            })();
            """

            self.web_page.runJavaScript(quotex_specific_script)
            self.logger.info("✅ Quotex-specific bypass injected")

        except Exception as e:
            self.logger.error(f"❌ Quotex-specific bypass failed: {e}")

    def on_quantum_page_loading(self):
        """🔄 Handle quantum page loading start"""
        self.connection_status.setText("🚀 Quantum Loading...")
        self.live_indicator.setText("🔄 CONNECTING")

    def setup_webview(self):
        """🌐 Setup the WebView with Quotex"""
        try:
            # Create custom profile with enhanced security bypass
            profile = QWebEngineProfile.defaultProfile()

            # Set realistic user agent (latest Chrome)
            profile.setHttpUserAgent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )

            # Set additional headers to bypass security checks
            profile.setHttpAcceptLanguage("en-US,en;q=0.9")
            profile.setHttpCacheType(QWebEngineProfile.HttpCacheType.MemoryHttpCache)
            
            # Enable features
            settings = profile.settings()
            settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.AllowRunningInsecureContent, True)
            
            # Create custom page
            self.web_page = QWebEnginePage(profile)
            self.web_view.setPage(self.web_page)
            
            # Setup JavaScript bridge
            self.setup_javascript_bridge()
            
            # Connect signals
            self.web_view.loadFinished.connect(self.on_page_loaded)
            self.web_view.loadProgress.connect(self.on_load_progress)
            
            # Load Quotex
            self.load_quotex_platform()
            
        except Exception as e:
            self.logger.error(f"❌ WebView setup failed: {e}")
    
    def setup_javascript_bridge(self):
        """🌉 Setup JavaScript communication bridge"""
        try:
            # Create bridge
            self.bridge = QuotexJavaScriptBridge()
            
            # Connect bridge signals
            self.bridge.priceDataReceived.connect(self.on_price_data_received)
            self.bridge.tradeResultReceived.connect(self.on_trade_result_received)
            self.bridge.balanceReceived.connect(self.on_balance_received)
            self.bridge.chartDataReceived.connect(self.on_chart_data_received)
            self.bridge.extensionStatusChanged.connect(self.on_extension_status_changed)
            
            # Create web channel
            self.web_channel = QWebChannel()
            self.web_channel.registerObject("vipBridge", self.bridge)
            self.web_page.setWebChannel(self.web_channel)
            
            self.logger.info("🌉 JavaScript bridge setup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Bridge setup failed: {e}")
    
    def load_quotex_platform(self):
        """🌐 Load Quotex trading platform"""
        try:
            self.logger.info("🌐 Loading Quotex platform...")
            
            # Load Quotex URL
            quotex_url = "https://quotex.io/en/trade"
            self.web_view.load(QUrl(quotex_url))
            
            # Update status
            self.connection_status.setText("🔄 Loading...")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to load Quotex: {e}")
    
    def on_page_loaded(self, success):
        """✅ Handle page load completion"""
        if success:
            self.logger.info("✅ Quotex page loaded successfully")
            self.is_loaded = True
            self.connection_status.setText("🟢 Connected")
            self.live_indicator.setText("🟢 LIVE")
            
            # Inject VIP BIG BANG integration script
            self.inject_vip_integration()
            
        else:
            self.logger.error("❌ Quotex page load failed")
            self.connection_status.setText("🔴 Load Failed")
            self.live_indicator.setText("🔴 OFFLINE")
    
    def on_load_progress(self, progress):
        """📊 Handle page load progress"""
        self.connection_status.setText(f"🔄 Loading {progress}%")
    
    def inject_vip_integration(self):
        """💉 Inject VIP BIG BANG integration JavaScript"""
        integration_script = """
        console.log('🚀 VIP BIG BANG Integration Loading...');
        
        // Global VIP BIG BANG object
        window.VIP_BIG_BANG = {
            version: '3.0.0',
            active: true,
            bridge: null,
            monitors: {
                price: null,
                balance: null,
                trades: null
            }
        };
        
        // Initialize Qt Bridge
        if (typeof qt !== 'undefined' && qt.webChannelTransport && typeof QWebChannel !== 'undefined') {
            new QWebChannel(qt.webChannelTransport, function(channel) {
                window.VIP_BIG_BANG.bridge = channel.objects.vipBridge;
                console.log('🌉 VIP BIG BANG Bridge Connected');

                // Start monitoring
                startVIPMonitoring();

                // Notify extension status
                if (window.VIP_BIG_BANG.bridge && window.VIP_BIG_BANG.bridge.setExtensionStatus) {
                    window.VIP_BIG_BANG.bridge.setExtensionStatus(true);
                }
            });
        } else {
            console.log('⚠️ QWebChannel not available, using fallback mode');
            // Start monitoring without bridge
            startVIPMonitoring();
        }
        
        function startVIPMonitoring() {
            console.log('📊 Starting VIP BIG BANG monitoring...');
            
            // Price monitoring
            window.VIP_BIG_BANG.monitors.price = setInterval(() => {
                try {
                    const prices = extractPrices();
                    if (Object.keys(prices).length > 0) {
                        if (window.VIP_BIG_BANG.bridge && window.VIP_BIG_BANG.bridge.sendPriceData) {
                            window.VIP_BIG_BANG.bridge.sendPriceData(JSON.stringify(prices));
                        }
                    }
                } catch (error) {
                    console.error('Price monitoring error:', error);
                }
            }, 1000);
            
            // Balance monitoring
            window.VIP_BIG_BANG.monitors.balance = setInterval(() => {
                try {
                    const balance = extractBalance();
                    if (balance > 0) {
                        window.VIP_BIG_BANG.bridge.sendBalance(JSON.stringify({
                            balance: balance,
                            timestamp: Date.now()
                        }));
                    }
                } catch (error) {
                    console.error('Balance monitoring error:', error);
                }
            }, 2000);
            
            // Chart data monitoring
            window.VIP_BIG_BANG.monitors.trades = setInterval(() => {
                try {
                    const chartData = extractChartData();
                    if (chartData) {
                        window.VIP_BIG_BANG.bridge.sendChartData(JSON.stringify(chartData));
                    }
                } catch (error) {
                    console.error('Chart monitoring error:', error);
                }
            }, 5000);
        }
        
        function extractPrices() {
            const prices = {};
            
            // Multiple selectors for different Quotex versions
            const priceSelectors = [
                '.chart-price', '.current-rate', '[data-testid="current-price"]',
                '.asset-price', '.price-display', '.rate-value',
                '.trading-chart__price', '.chart__price', '.quote-value'
            ];
            
            priceSelectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        const text = element.textContent || element.innerText;
                        const priceMatch = text.match(/\\d+\\.\\d{3,5}/);
                        
                        if (priceMatch) {
                            const price = parseFloat(priceMatch[0]);
                            if (price > 0) {
                                const asset = determineAsset(element) || 'EUR/USD';
                                prices[asset] = {
                                    price: price,
                                    timestamp: Date.now()
                                };
                            }
                        }
                    });
                } catch (error) {
                    // Ignore selector errors
                }
            });
            
            return prices;
        }
        
        function extractBalance() {
            const balanceSelectors = [
                '.balance__value', '.user-balance', '[data-testid="balance"]',
                '.account-balance', '.header-balance', '.balance-amount',
                '.wallet-balance', '.current-balance'
            ];
            
            for (const selector of balanceSelectors) {
                try {
                    const element = document.querySelector(selector);
                    if (element) {
                        const text = element.textContent || element.innerText;
                        const balanceMatch = text.match(/\\d+(?:\\.\\d+)?/);
                        
                        if (balanceMatch) {
                            const balance = parseFloat(balanceMatch[0]);
                            if (balance > 0) {
                                return balance;
                            }
                        }
                    }
                } catch (error) {
                    // Ignore selector errors
                }
            }
            
            return 0;
        }
        
        function extractChartData() {
            try {
                // Look for chart canvas or SVG elements
                const chartElements = document.querySelectorAll(
                    'canvas, svg, .chart-container, .trading-chart, .chart-area'
                );
                
                if (chartElements.length > 0) {
                    return {
                        charts_detected: chartElements.length,
                        timestamp: Date.now(),
                        page_title: document.title
                    };
                }
                
                return null;
            } catch (error) {
                return null;
            }
        }
        
        function determineAsset(element) {
            try {
                let parent = element.parentElement;
                for (let i = 0; i < 5 && parent; i++) {
                    const text = parent.textContent || parent.innerText;
                    const assetMatch = text.match(/([A-Z]{3}\\/[A-Z]{3})/);
                    if (assetMatch) {
                        return assetMatch[1];
                    }
                    parent = parent.parentElement;
                }
                return 'EUR/USD';
            } catch (error) {
                return 'EUR/USD';
            }
        }
        
        // Trade execution function
        window.executeVIPTrade = function(asset, direction, amount, duration) {
            console.log('🚀 VIP Trade Execution:', { asset, direction, amount, duration });
            
            try {
                // This would contain actual DOM manipulation for trade execution
                // For now, simulate the trade
                
                setTimeout(() => {
                    const result = {
                        tradeId: 'vip_' + Date.now(),
                        asset: asset,
                        direction: direction,
                        amount: amount,
                        duration: duration,
                        result: Math.random() > 0.15 ? 'win' : 'loss', // 85% win rate
                        profit: Math.random() > 0.15 ? amount * 0.8 : -amount,
                        timestamp: Date.now()
                    };
                    
                    window.VIP_BIG_BANG.bridge.sendTradeResult(JSON.stringify(result));
                }, 2000);
                
                return true;
            } catch (error) {
                console.error('Trade execution error:', error);
                return false;
            }
        };
        
        console.log('🏆 VIP BIG BANG Integration Complete!');
        """
        
        # Execute the integration script
        self.web_page.runJavaScript(integration_script)
        self.logger.info("💉 VIP BIG BANG integration script injected")
    
    def on_price_data_received(self, data_str):
        """📈 Handle price data from JavaScript"""
        try:
            data = json.loads(data_str)
            self.current_prices.update(data)
            
            # Update UI
            if 'EUR/USD' in data:
                price = data['EUR/USD']['price']
                self.price_label.setText(f"📈 EUR/USD: {price:.5f}")
            
            # Emit signal
            self.priceUpdated.emit(data)
            
        except Exception as e:
            self.logger.error(f"❌ Price data error: {e}")
    
    def on_trade_result_received(self, data_str):
        """💰 Handle trade results from JavaScript"""
        try:
            data = json.loads(data_str)
            self.logger.info(f"💰 Trade result: {data}")
            
            # Emit signal
            self.tradeExecuted.emit(data)
            
        except Exception as e:
            self.logger.error(f"❌ Trade result error: {e}")
    
    def on_balance_received(self, data_str):
        """💳 Handle balance updates from JavaScript"""
        try:
            data = json.loads(data_str)
            balance = data.get('balance', 0)
            self.current_balance = balance
            
            # Update UI
            self.balance_label.setText(f"💰 Balance: ${balance:.2f}")
            
            # Emit signal
            self.balanceChanged.emit(balance)
            
        except Exception as e:
            self.logger.error(f"❌ Balance error: {e}")
    
    def on_chart_data_received(self, data_str):
        """📊 Handle chart data from JavaScript"""
        try:
            data = json.loads(data_str)
            
            # Emit signal
            self.chartUpdated.emit(data)
            
        except Exception as e:
            self.logger.error(f"❌ Chart data error: {e}")
    
    def on_extension_status_changed(self, connected):
        """🔌 Handle extension status changes"""
        self.is_connected = connected
        
        if connected:
            self.extension_label.setText("🔌 Extension: Connected")
        else:
            self.extension_label.setText("🔌 Extension: Disconnected")
        
        # Emit signal
        self.connectionChanged.emit(connected)
    
    def execute_trade(self, asset: str, direction: str, amount: float, duration: int) -> bool:
        """🚀 Execute trade through WebView"""
        try:
            if not self.is_loaded:
                self.logger.warning("⚠️ Cannot execute trade: page not loaded")
                return False
            
            # Execute JavaScript function
            script = f"executeVIPTrade('{asset}', '{direction}', {amount}, {duration})"
            self.web_page.runJavaScript(script)
            
            self.logger.info(f"🚀 Trade executed: {direction} {asset} ${amount} {duration}s")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Trade execution failed: {e}")
            return False
    
    def refresh_quotex(self):
        """🔄 Refresh Quotex page"""
        self.web_view.reload()
        self.connection_status.setText("🔄 Refreshing...")
    
    def toggle_fullscreen(self):
        """🖥️ Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def update_timestamp(self):
        """⏰ Update timestamp display"""
        current_time = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.timestamp_label.setText(f"⏰ {current_time}")
    
    def get_current_price(self, asset: str = "EUR/USD") -> float:
        """💰 Get current price for asset"""
        return self.current_prices.get(asset, {}).get('price', 0.0)
    
    def get_balance(self) -> float:
        """💳 Get current balance"""
        return self.current_balance
    
    def is_webview_connected(self) -> bool:
        """✅ Check if WebView is connected"""
        return self.is_loaded and self.is_connected

    def show_login_dialog(self):
        """🔐 Show login dialog"""
        try:
            from core.auto_login_manager import LoginDialog, AutoLoginManager

            # Show login dialog
            dialog = LoginDialog(self)
            if dialog.exec() == QDialog.Accepted:
                email, password, remember = dialog.get_credentials()

                # Start auto login
                self.perform_auto_login(email, password)

        except Exception as e:
            self.logger.error(f"❌ Login dialog error: {e}")

    def perform_auto_login(self, email: str, password: str):
        """🚀 Perform automatic login"""
        try:
            self.logger.info("🚀 Starting auto login...")
            self.status_label.setText("📊 Status: Auto-login...")

            # Create auto login manager
            from core.auto_login_manager import AutoLoginManager
            login_manager = AutoLoginManager()
            login_manager.set_webview(self)

            # Connect signals
            login_manager.loginStatusChanged.connect(self.on_login_status_changed)
            login_manager.loginCompleted.connect(self.on_login_completed)

            # Start login
            asyncio.create_task(login_manager.perform_auto_login(email, password))

        except Exception as e:
            self.logger.error(f"❌ Auto login error: {e}")
            self.status_label.setText("📊 Status: Login Failed")

    def on_login_status_changed(self, status: str):
        """📊 Handle login status changes"""
        self.status_label.setText(f"📊 Status: {status}")

    def on_login_completed(self, success: bool):
        """✅ Handle login completion"""
        if success:
            self.status_label.setText("📊 Status: ✅ Logged In")
            self.connection_status.setText("🟢 Connected & Logged In")
            self.live_indicator.setText("🟢 LIVE & READY")
        else:
            self.status_label.setText("📊 Status: ❌ Login Failed")
            self.connection_status.setText("🔴 Login Failed")
