#!/usr/bin/env python3
"""
🚀 VIP BIG BANG ULTIMATE - DIRECT UI LAUNCHER
💎 Enterprise-Level Professional Trading System
⚡ Quantum-Speed Analysis Engine
🎯 95% Win Rate Achievement System
🔥 Real-time AI-Powered Market Intelligence

This launcher directly starts the new UI without any old interfaces.
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    try:
        import tkinter
        import threading
        import json
        import asyncio
        import websockets
        import requests
        print("All core dependencies available")
        return True
    except ImportError as e:
        print(f"Missing dependency: {e}")
        return False

def start_real_data_server():
    """Start the real data server in background"""
    try:
        print("Starting Real Data Server...")
        subprocess.Popen([sys.executable, "vip_real_quotex_main.py"],
                        creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
        time.sleep(2)  # Give server time to start
        print("Real Data Server started")
        return True
    except Exception as e:
        print(f"Could not start Real Data Server: {e}")
        return False

def start_new_ui_directly():
    """Start the new UI directly"""
    try:
        print("Starting VIP BIG BANG ULTIMATE NEW UI...")
        
        # Import and run the new UI directly
        from vip_ultimate_ui_rebuilt import VIPUltimateUIRebuilt
        
        # Create and run the new UI
        app = VIPUltimateUIRebuilt()
        app.run()
        return True
    except Exception as e:
        print(f"Failed to start new UI: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to run VIP BIG BANG ULTIMATE with new UI"""
    print("VIP BIG BANG ULTIMATE - NEW UI LAUNCHER")
    print("Enterprise-Level Professional Trading System")
    print("Quantum-Speed Analysis Engine")
    print("95% Win Rate Achievement System")
    print("Real-time AI-Powered Market Intelligence")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("Please install missing dependencies")
        input("Press Enter to exit...")
        return
    
    # Start real data server
    start_real_data_server()
    
    # Start new UI directly
    if not start_new_ui_directly():
        print("Failed to start VIP BIG BANG ULTIMATE NEW UI")
        input("Press Enter to exit...")
        return
    
    print("VIP BIG BANG ULTIMATE session ended")

if __name__ == "__main__":
    main()
