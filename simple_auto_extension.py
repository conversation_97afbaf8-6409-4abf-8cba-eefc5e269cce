#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 VIP BIG BANG - Simple Auto Extension Installer
📦 فقط نصب خودکار Extension - بدون پیچیدگی
🎯 یک کار ساده: نصب Extension به کروم اصلی شما
"""

import os
import shutil
import subprocess
import time
import json
from pathlib import Path

def simple_auto_install():
    """🔧 Simple Auto Extension Install"""
    print("🔧 VIP BIG BANG - Simple Auto Extension Installer")
    print("=" * 50)
    
    try:
        # Step 1: Find Chrome
        print("🔍 Step 1: Finding your Chrome...")
        
        username = os.getenv('USERNAME')
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            rf"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
        ]
        
        chrome_exe = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_exe = path
                print(f"✅ Chrome found: {path}")
                break
        
        if not chrome_exe:
            print("❌ Chrome not found!")
            print("💡 Please install Google Chrome first")
            return False
        
        # Step 2: Find extension folder
        print("\n🔍 Step 2: Finding extension folder...")
        
        extension_path = Path(__file__).parent / "chrome_extension"
        
        if not extension_path.exists():
            print("❌ Extension folder not found!")
            print(f"💡 Looking for: {extension_path}")
            return False
        
        print(f"✅ Extension found: {extension_path}")
        
        # Step 3: Close existing Chrome
        print("\n🔄 Step 3: Closing existing Chrome...")
        
        try:
            subprocess.run(["taskkill", "/f", "/im", "chrome.exe"], 
                         capture_output=True, check=False)
            time.sleep(2)
            print("✅ Chrome closed")
        except:
            print("⚠️ Chrome was not running")
        
        # Step 4: Start Chrome with extension
        print("\n🚀 Step 4: Starting Chrome with extension...")
        
        cmd = [
            chrome_exe,
            f"--load-extension={extension_path}",
            "--no-first-run",
            "--no-default-browser-check",
            "https://qxbroker.com/en/sign-in"
        ]
        
        print(f"🚀 Command: {' '.join(cmd)}")
        
        # Start Chrome
        process = subprocess.Popen(cmd)
        
        print("✅ Chrome started with extension!")
        print("🌐 Quotex should open automatically")
        print("🔗 Extension is now installed and active")
        
        # Step 5: Instructions
        print("\n📋 Next steps:")
        print("1. ✅ Chrome opened with extension")
        print("2. 🔐 Login to your Quotex account")
        print("3. 📊 Go to trading page")
        print("4. 🚀 Start VIP BIG BANG robot")
        print("5. 📊 Click 'READ REAL DATA'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_extension_installed():
    """✅ Check if extension is installed"""
    try:
        print("🔍 Checking if extension is installed...")
        
        username = os.getenv('USERNAME')
        user_data_paths = [
            rf"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data",
            rf"C:\Users\<USER>\AppData\Roaming\Google\Chrome\User Data"
        ]
        
        for user_data_dir in user_data_paths:
            if os.path.exists(user_data_dir):
                extensions_dir = Path(user_data_dir) / "Default" / "Extensions"
                
                if extensions_dir.exists():
                    extensions = list(extensions_dir.iterdir())
                    print(f"✅ Found {len(extensions)} extensions in Chrome")
                    
                    for ext_dir in extensions:
                        if ext_dir.is_dir():
                            print(f"📦 Extension: {ext_dir.name}")
                    
                    return True
        
        print("⚠️ No extensions found")
        return False
        
    except Exception as e:
        print(f"❌ Check error: {e}")
        return False

def main():
    """🚀 Main function"""
    print("🚀 VIP BIG BANG - Simple Auto Extension")
    print("🎯 Goal: Install extension to your main Chrome")
    print("📦 Simple and clean - no complications")
    print()
    
    # Check current status
    check_extension_installed()
    print()
    
    # Ask user
    choice = input("🔧 Install extension automatically? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '']:
        print("\n🚀 Starting automatic installation...")
        
        if simple_auto_install():
            print("\n🎉 SUCCESS!")
            print("✅ Extension installed successfully")
            print("🌐 Chrome opened with Quotex")
            print("🔗 Extension is active and ready")
            print("\n💡 Now you can:")
            print("1. Login to Quotex")
            print("2. Start VIP BIG BANG robot")
            print("3. Use real data reading")
        else:
            print("\n❌ Installation failed")
            print("💡 Please try manual installation")
    
    else:
        print("❌ Installation cancelled")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
