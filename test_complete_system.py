#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 VIP BIG BANG - Complete System Test
🚀 Test All Components Integration
⚡ Professional Trading System Validation
💎 Multi-Layer Architecture Test
"""

import time
import json
from datetime import datetime

def test_browser_core():
    """🧪 Test Browser Core"""
    print("🧪 Testing Browser Core...")
    
    try:
        from core.browser_core import StealthBrowserCore
        
        browser = StealthBrowserCore()
        
        print("🌐 Creating stealth browser...")
        if browser.create_stealth_browser():
            print("✅ Stealth browser created successfully")
            
            print("🔗 Connecting to Quotex...")
            if browser.connect_to_quotex():
                print("✅ Connected to Quotex successfully")
                
                # Test data extraction
                print("📊 Testing data extraction...")
                for i in range(3):
                    data = browser.get_quotex_data()
                    if data:
                        print(f"📊 Data {i+1}: {data.get('asset', 'Unknown')} - {data.get('price', 'N/A')}")
                    time.sleep(2)
                
                browser.close_browser()
                print("✅ Browser core test completed successfully")
                return True
            else:
                print("❌ Failed to connect to Quotex")
                browser.close_browser()
                return False
        else:
            print("❌ Failed to create stealth browser")
            return False
            
    except Exception as e:
        print(f"❌ Browser core test error: {e}")
        return False

def test_dom_scraper():
    """🧪 Test DOM Scraper"""
    print("🧪 Testing DOM Scraper...")
    
    try:
        from core.browser_core import StealthBrowserCore
        from core.dom_scraper import SmartDOMScraper
        
        browser = StealthBrowserCore()
        
        if browser.create_stealth_browser() and browser.connect_to_quotex():
            scraper = SmartDOMScraper(browser)
            
            print("💉 Injecting advanced extractor...")
            if scraper.inject_advanced_extractor():
                print("✅ Advanced extractor injected successfully")
                
                print("📊 Testing data extraction...")
                for i in range(3):
                    data = scraper.extract_real_time_data()
                    if data:
                        print(f"📊 Extracted data {i+1}: {json.dumps(data, indent=2)}")
                    time.sleep(2)
                
                browser.close_browser()
                print("✅ DOM scraper test completed successfully")
                return True
            else:
                print("❌ Failed to inject extractor")
                browser.close_browser()
                return False
        else:
            print("❌ Failed to initialize browser")
            return False
            
    except Exception as e:
        print(f"❌ DOM scraper test error: {e}")
        return False

def test_human_clicker():
    """🧪 Test Human Clicker"""
    print("🧪 Testing Human Clicker...")
    
    try:
        from core.browser_core import StealthBrowserCore
        from core.human_clicker import HumanClicker
        
        browser = StealthBrowserCore()
        
        if browser.create_stealth_browser() and browser.connect_to_quotex():
            clicker = HumanClicker(browser)
            
            print("🖱️ Testing mouse movement...")
            if hasattr(clicker, 'human_move_to'):
                # Test mouse movement (safe test)
                print("✅ Human clicker initialized successfully")
                
                # Test click statistics
                stats = clicker.get_click_statistics()
                print(f"📊 Click statistics: {stats}")
                
                browser.close_browser()
                print("✅ Human clicker test completed successfully")
                return True
            else:
                print("❌ Human clicker methods not available")
                browser.close_browser()
                return False
        else:
            print("❌ Failed to initialize browser")
            return False
            
    except Exception as e:
        print(f"❌ Human clicker test error: {e}")
        return False

def test_decision_engine():
    """🧪 Test Decision Engine"""
    print("🧪 Testing Decision Engine...")
    
    try:
        from core.browser_core import StealthBrowserCore
        from core.dom_scraper import SmartDOMScraper
        from core.human_clicker import HumanClicker
        from core.decision_engine import CoreDecisionEngine
        
        browser = StealthBrowserCore()
        
        if browser.create_stealth_browser() and browser.connect_to_quotex():
            scraper = SmartDOMScraper(browser)
            clicker = HumanClicker(browser)
            
            if scraper.inject_advanced_extractor():
                engine = CoreDecisionEngine(browser, scraper, clicker)
                
                print("🎯 Testing signal collection...")
                signals = engine.collect_signals()
                print(f"🎯 Signals: {signals}")
                
                print("📊 Testing market analysis...")
                market_data = scraper.extract_real_time_data()
                if market_data:
                    analysis = engine.analyze_market_conditions(market_data)
                    print(f"📊 Market analysis: {analysis}")
                
                print("📈 Testing performance stats...")
                stats = engine.get_performance_stats()
                print(f"📈 Performance: {stats}")
                
                browser.close_browser()
                print("✅ Decision engine test completed successfully")
                return True
            else:
                print("❌ Failed to inject extractor")
                browser.close_browser()
                return False
        else:
            print("❌ Failed to initialize browser")
            return False
            
    except Exception as e:
        print(f"❌ Decision engine test error: {e}")
        return False

def test_complete_autotrade():
    """🧪 Test Complete AutoTrade System"""
    print("🧪 Testing Complete AutoTrade System...")
    
    try:
        from core.complete_autotrade import CompleteAutoTradeSystem
        
        autotrade = CompleteAutoTradeSystem()
        
        print("🚀 Testing system initialization...")
        if autotrade.initialize_system():
            print("✅ System initialized successfully")
            
            print("📊 Testing real-time data...")
            data = autotrade.get_real_time_data()
            if data:
                print(f"📊 Real-time data: {json.dumps(data, indent=2)}")
            
            print("🎯 Testing signal collection...")
            signals = autotrade.get_current_signals()
            if signals:
                print(f"🎯 Signals: {json.dumps(signals, indent=2)}")
            
            print("📊 Testing system status...")
            status = autotrade.get_system_status()
            print(f"📊 System status: {json.dumps(status, indent=2)}")
            
            print("⚙️ Testing settings update...")
            settings = {
                'auto_mode': True,
                'min_confirmations': 5,
                'trade_amount': 5
            }
            autotrade.update_settings(settings)
            
            # Cleanup
            autotrade.cleanup_system()
            print("✅ Complete AutoTrade test completed successfully")
            return True
        else:
            print("❌ Failed to initialize system")
            return False
            
    except Exception as e:
        print(f"❌ Complete AutoTrade test error: {e}")
        return False

def run_all_tests():
    """🧪 Run All System Tests"""
    print("🧪 VIP BIG BANG - Complete System Test Suite")
    print("=" * 60)
    
    test_results = {}
    
    # Test individual components
    print("\n🔧 Testing Individual Components...")
    print("-" * 40)
    
    test_results['browser_core'] = test_browser_core()
    print()
    
    test_results['dom_scraper'] = test_dom_scraper()
    print()
    
    test_results['human_clicker'] = test_human_clicker()
    print()
    
    test_results['decision_engine'] = test_decision_engine()
    print()
    
    # Test complete system
    print("\n🤖 Testing Complete System...")
    print("-" * 40)
    
    test_results['complete_autotrade'] = test_complete_autotrade()
    print()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for trading.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    print("🚀 Starting VIP BIG BANG Complete System Test...")
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = run_all_tests()
    
    print()
    print(f"📅 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("🎉 VIP BIG BANG system is fully operational!")
    else:
        print("⚠️ Please fix the issues and run tests again.")
