"""
VIP BIG BANG Enterprise - System Test
Test all components to ensure everything works correctly
"""

import pandas as pd  # type: ignore
from datetime import datetime, timedelta
import logging
from typing import Dict, Any, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)

def create_test_data():
    """Create sample market data for testing"""
    import random

    # Create simple test data
    num_candles = 50
    base_price = 1.2000

    data = []
    current_price = base_price

    for i in range(num_candles):
        # Simple price movement
        change = random.uniform(-0.001, 0.001)
        current_price += change
        current_price = max(current_price, 0.5)  # Prevent negative prices

        # Create OHLC
        high = current_price + random.uniform(0, 0.0005)
        low = current_price - random.uniform(0, 0.0005)
        volume = random.randint(100, 1000)

        data.append({
            'timestamp': datetime.now() - timedelta(minutes=num_candles-i),
            'open': current_price,
            'high': high,
            'low': low,
            'close': current_price,
            'volume': volume,
            'price': current_price
        })

    return pd.DataFrame(data)

def test_primary_analysis():
    """Test primary analysis engine"""
    print("🔍 Testing Primary Analysis Engine...")
    
    try:
        from core.analysis_engine import AnalysisEngine
        from core.settings import Settings
        
        # Initialize
        settings = Settings()
        engine = AnalysisEngine(settings)
        
        # Create test data
        data = create_test_data()
        
        # Set data and run analysis
        for _, row in data.iterrows():
            engine.update_market_data(row.to_dict())
        results = engine.analyze()
        
        print(f"✅ Primary Analysis: {len(results)} indicators analyzed")
        for name, result in results.items():
            if isinstance(result, dict):
                direction = result.get('direction', 'UNKNOWN')
                confidence = result.get('confidence', 0)
                print(f"   - {name}: {direction} ({confidence:.2f})")
        
        return results
        
    except Exception as e:
        print(f"❌ Primary Analysis Error: {e}")
        return {}

def test_complementary_analysis(primary_results: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Test complementary analysis engine"""
    print("\n🔍 Testing Complementary Analysis Engine...")
    
    try:
        from core.complementary_engine import ComplementaryEngine
        from core.settings import Settings
        
        # Initialize
        settings = Settings()
        engine = ComplementaryEngine(settings)
        
        # Create test data
        data = create_test_data()
        
        # Mock account data
        account_data = {
            'balance': 100.0,
            'daily_trades': 5,
            'daily_pnl': 2.5,
            'initial_balance': 100.0,
            'max_balance': 105.0,
            'performance': {
                'consecutive_losses': 1,
                'win_rate': 0.75
            }
        }
        
        # Mock market conditions
        market_conditions = {
            'volatility': 'NORMAL',
            'news_risk': 'LOW',
            'otc_mode': False
        }
        
        # Run complementary analysis
        comp_results = engine.run_all_complementary_analyses(
            data, primary_results, account_data, market_conditions
        )
        
        print(f"✅ Complementary Analysis: {len(comp_results)} filters analyzed")
        for name, result in comp_results.items():
            if isinstance(result, dict):
                score = result.get('score', 0)
                details = result.get('details', 'No details')
                print(f"   - {name}: {score:.2f} - {details}")
        
        # Calculate final decision
        final_decision = engine.calculate_final_trading_decision(primary_results, comp_results)
        
        print(f"\n🎯 Final Decision: {final_decision['final_decision']}")
        print(f"   - Direction: {final_decision['direction']}")
        print(f"   - Confidence: {final_decision['confidence']:.2f}")
        print(f"   - Allow Trading: {final_decision['allow_trading']}")
        print(f"   - Final Score: {final_decision['final_score']:.2f}")
        
        if final_decision['blocking_factors']:
            print(f"   - Blocking Factors: {final_decision['blocking_factors']}")
        if final_decision['warning_factors']:
            print(f"   - Warning Factors: {final_decision['warning_factors']}")
        
        return comp_results, final_decision
        
    except Exception as e:
        print(f"❌ Complementary Analysis Error: {e}")
        return {}, {}

def test_individual_analyzers():
    """Test individual analyzers"""
    print("\n🔍 Testing Individual Analyzers...")
    
    data = create_test_data()
    
    # Test MA6
    try:
        from core.ma6_analyzer import MA6Analyzer
        from core.settings import Settings
        settings = Settings()
        analyzer = MA6Analyzer(settings)
        result = analyzer.analyze(data)
        print(f"✅ MA6 Analyzer: {result['direction']} ({result['confidence']:.2f})")
    except Exception as e:
        print(f"❌ MA6 Analyzer Error: {e}")
    
    # Test Volume Analyzer
    try:
        from core.volume_analyzer import VolumeAnalyzer
        analyzer = VolumeAnalyzer(settings)
        result = analyzer.analyze(data)
        print(f"✅ Volume Analyzer: {result['direction']} ({result['confidence']:.2f})")
    except Exception as e:
        print(f"❌ Volume Analyzer Error: {e}")
    
    # Test Heatmap & PulseBar
    try:
        from core.heatmap_pulsebar import HeatmapPulseBarAnalyzer
        analyzer = HeatmapPulseBarAnalyzer(settings)
        result = analyzer.analyze(data)
        print(f"✅ Heatmap & PulseBar: {result['direction']} ({result['confidence']:.2f})")
    except Exception as e:
        print(f"❌ Heatmap & PulseBar Error: {e}")

def main():
    """Main test function"""
    print("🚀 VIP BIG BANG Enterprise - System Test")
    print("=" * 50)
    
    # Test primary analysis
    primary_results = test_primary_analysis()
    
    # Test complementary analysis
    comp_results, _ = test_complementary_analysis(primary_results)
    
    # Test individual analyzers
    test_individual_analyzers()
    
    print("\n" + "=" * 50)
    print("🎉 System Test Completed!")
    
    if primary_results and comp_results:
        print("✅ All major components working correctly")
        print(f"📊 Total Analyzers: {len(primary_results)} primary + {len(comp_results)} complementary")
        print("🚀 System ready for production!")
    else:
        print("❌ Some components have issues - check logs above")

if __name__ == "__main__":
    main()
