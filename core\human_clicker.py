#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🖱️ VIP BIG BANG - Human Clicker System
🤖 Human-Like Mouse Movement & Clicking
⚡ Bézier Curve Natural Movement
💎 Professional Auto-Trading Execution
"""

import time
import random
import math
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By

try:
    import pyautogui
    # Disable pyautogui failsafe
    pyautogui.FAILSAFE = False
    PYAUTOGUI_AVAILABLE = True
    print("✅ PyAutoGUI available")
except ImportError:
    PYAUTOGUI_AVAILABLE = False
    print("⚠️ PyAutoGUI not available, installing...")
    import subprocess
    subprocess.run(["pip", "install", "pyautogui"], check=True)
    import pyautogui
    pyautogui.FAILSAFE = False
    PYAUTOGUI_AVAILABLE = True

class HumanClicker:
    """
    🖱️ Human Clicker System
    🤖 Natural Mouse Movement
    ⚡ Professional Trading Execution
    💎 Anti-Detection Clicking
    """

    def __init__(self, browser_core):
        self.browser = browser_core
        self.last_click_time = 0
        self.click_history = []
        
        # Human behavior settings
        self.min_delay = 0.3
        self.max_delay = 1.2
        self.movement_speed = 0.5
        self.click_variance = 5  # pixels
        
        print("🖱️ Human Clicker initialized")

    def human_delay(self, min_delay=None, max_delay=None):
        """⏱️ Human-Like Delay"""
        min_d = min_delay or self.min_delay
        max_d = max_delay or self.max_delay
        delay = random.uniform(min_d, max_d)
        time.sleep(delay)
        return delay

    def bezier_curve(self, start, end, control_points=2):
        """📈 Generate Bézier Curve Path"""
        try:
            # Generate control points
            points = [start]
            
            for i in range(control_points):
                # Random control points between start and end
                x = random.randint(min(start[0], end[0]), max(start[0], end[0]))
                y = random.randint(min(start[1], end[1]), max(start[1], end[1]))
                
                # Add some randomness
                x += random.randint(-50, 50)
                y += random.randint(-30, 30)
                
                points.append((x, y))
            
            points.append(end)
            
            # Generate curve
            curve_points = []
            steps = random.randint(15, 25)
            
            for i in range(steps + 1):
                t = i / steps
                point = self.calculate_bezier_point(points, t)
                curve_points.append(point)
            
            return curve_points
            
        except Exception as e:
            print(f"❌ Bézier curve error: {e}")
            return [start, end]

    def calculate_bezier_point(self, points, t):
        """🔢 Calculate Bézier Point"""
        try:
            n = len(points) - 1
            x, y = 0, 0
            
            for i, (px, py) in enumerate(points):
                # Binomial coefficient
                coeff = math.comb(n, i) * (t ** i) * ((1 - t) ** (n - i))
                x += coeff * px
                y += coeff * py
            
            return (int(x), int(y))
            
        except Exception as e:
            print(f"❌ Bézier calculation error: {e}")
            return points[0] if points else (0, 0)

    def human_move_to(self, x, y, duration=None):
        """🚶 Human-Like Mouse Movement"""
        try:
            current_pos = pyautogui.position()
            target_pos = (x, y)
            
            # Add random variance to target
            target_x = x + random.randint(-self.click_variance, self.click_variance)
            target_y = y + random.randint(-self.click_variance, self.click_variance)
            target_pos = (target_x, target_y)
            
            # Generate Bézier curve path
            path = self.bezier_curve(current_pos, target_pos)
            
            # Move duration
            move_duration = duration or random.uniform(0.3, 0.8)
            step_delay = move_duration / len(path)
            
            # Move along curve
            for point in path:
                pyautogui.moveTo(point[0], point[1])
                time.sleep(step_delay)
            
            # Final precise move
            pyautogui.moveTo(target_x, target_y, duration=0.1)
            
            print(f"🚶 Moved to ({target_x}, {target_y})")
            return True
            
        except Exception as e:
            print(f"❌ Human move error: {e}")
            return False

    def human_click(self, x=None, y=None, button='left', clicks=1):
        """🖱️ Human-Like Click"""
        try:
            # Move to position if specified
            if x is not None and y is not None:
                if not self.human_move_to(x, y):
                    return False
            
            # Pre-click delay
            self.human_delay(0.1, 0.3)
            
            # Click with slight randomness
            for i in range(clicks):
                if i > 0:
                    self.human_delay(0.05, 0.15)
                
                # Add micro-movement before click
                current_pos = pyautogui.position()
                micro_x = current_pos[0] + random.randint(-2, 2)
                micro_y = current_pos[1] + random.randint(-2, 2)
                pyautogui.moveTo(micro_x, micro_y, duration=0.05)
                
                # Perform click
                pyautogui.click(button=button)
            
            # Post-click delay
            self.human_delay(0.2, 0.5)
            
            # Record click
            self.last_click_time = time.time()
            self.click_history.append({
                'timestamp': self.last_click_time,
                'position': pyautogui.position(),
                'button': button,
                'clicks': clicks
            })
            
            print(f"🖱️ Human click performed at {pyautogui.position()}")
            return True
            
        except Exception as e:
            print(f"❌ Human click error: {e}")
            return False

    def find_and_click_element(self, selectors, click_type='call'):
        """🎯 Find and Click Trading Element"""
        try:
            print(f"🎯 Looking for {click_type} button...")
            
            # Try to find element using multiple selectors
            element = None
            used_selector = None
            
            for selector in selectors:
                try:
                    element = self.browser.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.is_displayed() and element.is_enabled():
                        used_selector = selector
                        break
                except:
                    continue
            
            if not element:
                print(f"❌ {click_type} button not found")
                return False
            
            # Get element position and size
            location = element.location
            size = element.size
            
            # Calculate click position (center with randomness)
            click_x = location['x'] + size['width'] // 2
            click_y = location['y'] + size['height'] // 2
            
            # Add browser window offset
            window_pos = self.browser.driver.get_window_position()
            click_x += window_pos['x']
            click_y += window_pos['y']
            
            # Add browser chrome offset (approximate)
            click_y += 80  # Browser toolbar height
            
            print(f"🎯 Found {click_type} button at ({click_x}, {click_y}) using selector: {used_selector}")
            
            # Perform human-like click
            success = self.human_click(click_x, click_y)
            
            if success:
                print(f"✅ {click_type} button clicked successfully")
                return True
            else:
                print(f"❌ Failed to click {click_type} button")
                return False
                
        except Exception as e:
            print(f"❌ Element click error: {e}")
            return False

    def execute_call_trade(self):
        """📈 Execute CALL Trade"""
        try:
            print("📈 Executing CALL trade...")
            
            call_selectors = [
                '.call-btn', '[class*="call"]', '.buy-btn', '.higher-btn', 
                '.up-btn', '[data-direction="call"]', '.green-btn',
                '.call-button', '.higher-button', '.buy-button'
            ]
            
            return self.find_and_click_element(call_selectors, 'CALL')
            
        except Exception as e:
            print(f"❌ CALL trade error: {e}")
            return False

    def execute_put_trade(self):
        """📉 Execute PUT Trade"""
        try:
            print("📉 Executing PUT trade...")
            
            put_selectors = [
                '.put-btn', '[class*="put"]', '.sell-btn', '.lower-btn',
                '.down-btn', '[data-direction="put"]', '.red-btn',
                '.put-button', '.lower-button', '.sell-button'
            ]
            
            return self.find_and_click_element(put_selectors, 'PUT')
            
        except Exception as e:
            print(f"❌ PUT trade error: {e}")
            return False

    def set_trade_amount(self, amount):
        """💰 Set Trade Amount"""
        try:
            print(f"💰 Setting trade amount: ${amount}")
            
            amount_selectors = [
                '.amount-input', '[class*="amount"]', '.investment-input',
                '.trade-amount', '.bet-amount', 'input[type="number"]'
            ]
            
            # Find amount input
            amount_input = None
            for selector in amount_selectors:
                try:
                    amount_input = self.browser.driver.find_element(By.CSS_SELECTOR, selector)
                    if amount_input and amount_input.is_displayed():
                        break
                except:
                    continue
            
            if not amount_input:
                print("❌ Amount input not found")
                return False
            
            # Clear and set amount
            amount_input.clear()
            self.human_delay(0.2, 0.4)
            
            # Type amount with human-like delays
            amount_str = str(amount)
            for char in amount_str:
                amount_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            self.human_delay(0.3, 0.6)
            
            print(f"✅ Trade amount set to ${amount}")
            return True
            
        except Exception as e:
            print(f"❌ Set amount error: {e}")
            return False

    def execute_trade(self, direction, amount=None):
        """🚀 Execute Complete Trade"""
        try:
            print(f"🚀 Executing {direction} trade...")
            
            # Set amount if specified
            if amount:
                if not self.set_trade_amount(amount):
                    print("⚠️ Failed to set amount, continuing with current amount")
            
            # Wait for market to be ready
            self.human_delay(0.5, 1.0)
            
            # Execute trade based on direction
            if direction.upper() == 'CALL':
                success = self.execute_call_trade()
            elif direction.upper() == 'PUT':
                success = self.execute_put_trade()
            else:
                print(f"❌ Invalid trade direction: {direction}")
                return False
            
            if success:
                print(f"✅ {direction} trade executed successfully")
                
                # Record trade
                trade_record = {
                    'timestamp': time.time(),
                    'direction': direction.upper(),
                    'amount': amount,
                    'success': True
                }
                
                return trade_record
            else:
                print(f"❌ {direction} trade execution failed")
                return False
                
        except Exception as e:
            print(f"❌ Trade execution error: {e}")
            return False

    def emergency_stop(self):
        """🛑 Emergency Stop All Actions"""
        try:
            print("🛑 Emergency stop activated")
            
            # Move mouse to safe position
            pyautogui.moveTo(100, 100, duration=0.1)
            
            # Clear any pending actions
            self.click_history.clear()
            
            print("✅ Emergency stop completed")
            return True
            
        except Exception as e:
            print(f"❌ Emergency stop error: {e}")
            return False

    def get_click_statistics(self):
        """📊 Get Click Statistics"""
        try:
            if not self.click_history:
                return {"total_clicks": 0, "last_click": None}
            
            recent_clicks = [c for c in self.click_history if time.time() - c['timestamp'] < 3600]  # Last hour
            
            return {
                "total_clicks": len(self.click_history),
                "recent_clicks": len(recent_clicks),
                "last_click": self.click_history[-1] if self.click_history else None,
                "average_interval": self.calculate_average_interval()
            }
            
        except Exception as e:
            print(f"❌ Statistics error: {e}")
            return {}

    def calculate_average_interval(self):
        """📈 Calculate Average Click Interval"""
        try:
            if len(self.click_history) < 2:
                return 0
            
            intervals = []
            for i in range(1, len(self.click_history)):
                interval = self.click_history[i]['timestamp'] - self.click_history[i-1]['timestamp']
                intervals.append(interval)
            
            return sum(intervals) / len(intervals)
            
        except Exception as e:
            print(f"❌ Interval calculation error: {e}")
            return 0

# Test function
def test_human_clicker():
    """🧪 Test Human Clicker"""
    print("🧪 Testing Human Clicker...")
    
    from browser_core import StealthBrowserCore
    
    browser = StealthBrowserCore()
    if browser.create_stealth_browser() and browser.connect_to_quotex():
        clicker = HumanClicker(browser)
        
        # Test mouse movement
        print("🧪 Testing mouse movement...")
        clicker.human_move_to(500, 300)
        clicker.human_delay(1, 2)
        
        # Test clicking
        print("🧪 Testing human click...")
        clicker.human_click(600, 400)
        
        # Test trade execution (without actually trading)
        print("🧪 Testing trade execution...")
        # clicker.execute_trade('CALL', 10)  # Uncomment to test actual trading
        
        browser.close_browser()
    
    print("🧪 Human Clicker test completed")

if __name__ == "__main__":
    test_human_clicker()
