"""
🧪 VIP BIG BANG - Extension UI Test
Test the new Extension Data UI components
"""

import sys
import asyncio
from pathlib import Path
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

# Add paths
sys.path.append(str(Path(__file__).parent))

# Import UI components
from ui.components.extension_data_widget import ExtensionDataWidget
from ui.extension_data_manager import ExtensionDataManager, ExtensionDataConnector

class ExtensionUITestWindow(QMainWindow):
    """
    🧪 Test window for Extension UI components
    """
    
    def __init__(self):
        super().__init__()
        
        # Window setup
        self.setWindowTitle("🧪 VIP BIG BANG - Extension UI Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Setup UI
        self._setup_ui()
        
        # Setup Extension Data Manager
        self._setup_extension_manager()
        
        # Start test data simulation
        self._start_test_simulation()
        
    def _setup_ui(self):
        """Setup test UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("🧪 Extension UI Components Test")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #3B82F6; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Test controls
        controls_layout = QHBoxLayout()
        
        self.test_connection_btn = QPushButton("🔌 Test Connection")
        self.test_connection_btn.clicked.connect(self._test_connection)
        controls_layout.addWidget(self.test_connection_btn)
        
        self.test_data_btn = QPushButton("📊 Send Test Data")
        self.test_data_btn.clicked.connect(self._send_test_data)
        controls_layout.addWidget(self.test_data_btn)
        
        self.clear_data_btn = QPushButton("🗑️ Clear Data")
        self.clear_data_btn.clicked.connect(self._clear_data)
        controls_layout.addWidget(self.clear_data_btn)
        
        layout.addLayout(controls_layout)
        
        # Extension Data Widget
        self.extension_widget = ExtensionDataWidget()
        layout.addWidget(self.extension_widget)
        
        # Status display
        self.status_label = QLabel("Status: Ready for testing")
        self.status_label.setStyleSheet("color: #6B7280; font-size: 14px; padding: 10px;")
        layout.addWidget(self.status_label)
        
        # Test data display
        self.test_data_display = QTextEdit()
        self.test_data_display.setMaximumHeight(200)
        self.test_data_display.setStyleSheet("""
            background: #1F2937;
            color: #F3F4F6;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 10px;
            font-family: 'Courier New', monospace;
        """)
        layout.addWidget(self.test_data_display)
        
        layout.addStretch()
        
    def _setup_extension_manager(self):
        """Setup Extension Data Manager for testing"""
        try:
            self.data_manager = ExtensionDataManager()
            
            # Register extension widget
            self.data_manager.register_ui_component(self.extension_widget)
            
            # Connect signals
            self.data_manager.data_received.connect(self._on_data_received)
            self.data_manager.connection_status_changed.connect(self._on_connection_changed)
            
            # Start listening
            self.data_manager.start_listening()
            
            self.status_label.setText("Status: Extension Data Manager initialized")
            self._log("✅ Extension Data Manager initialized")
            
        except Exception as e:
            self.status_label.setText(f"Status: Error - {e}")
            self._log(f"❌ Error setting up Extension Data Manager: {e}")
            
    def _start_test_simulation(self):
        """Start test data simulation"""
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self._simulate_extension_data)
        self.test_counter = 0
        
    def _test_connection(self):
        """Test connection to desktop robot"""
        self._log("🔌 Testing connection to desktop robot...")
        self.status_label.setText("Status: Testing connection...")
        
        # Simulate connection test
        QTimer.singleShot(2000, self._connection_test_result)
        
    def _connection_test_result(self):
        """Show connection test result"""
        if hasattr(self, 'data_manager') and self.data_manager.get_connection_status():
            self.status_label.setText("Status: ✅ Connected to desktop robot")
            self._log("✅ Connection test successful")
        else:
            self.status_label.setText("Status: ❌ Connection failed")
            self._log("❌ Connection test failed")
            
    def _send_test_data(self):
        """Send test data to extension widget"""
        self._log("📊 Sending test data...")
        
        # Create test data
        test_data = {
            'timestamp': '2025-06-16T05:20:00.000000',
            'url': 'https://qxbroker.com/en/trade',
            'balance': '$125.50',
            'currentAsset': 'EUR/USD OTC',
            'currentPrice': None,
            'payout': 'Your payout: 1.88 $',
            'pageTitle': 'Live trading | Quotex',
            'extractionCount': self.test_counter + 100,
            'source': 'REAL_CHROME_EXTENSION'
        }
        
        # Update widget
        self.extension_widget.update_data(test_data)
        
        # Log data
        self._log(f"📊 Test data sent: Balance={test_data['balance']}, Asset={test_data['currentAsset']}")
        
        self.test_counter += 1
        
    def _clear_data(self):
        """Clear all data"""
        self._log("🗑️ Clearing data...")
        
        # Reset extension widget
        clear_data = {
            'balance': '$0.00',
            'currentAsset': None,
            'payout': 'N/A',
            'extractionCount': 0,
            'source': 'REAL_CHROME_EXTENSION'
        }
        
        self.extension_widget.update_data(clear_data)
        self.test_data_display.clear()
        self.test_counter = 0
        
        self.status_label.setText("Status: Data cleared")
        
    def _simulate_extension_data(self):
        """Simulate real extension data"""
        import random
        
        # Simulate realistic data
        balances = ['$0.85', '$125.50', '$89.23', '$156.78', '$203.45']
        assets = ['EUR/USD OTC', 'GBP/USD OTC', 'USD/JPY OTC', 'AUD/USD OTC', 'USD/CAD OTC']
        payouts = ['Your payout: 1.88 $', 'Your payout: 1.85 $', 'Your payout: 1.90 $']
        
        test_data = {
            'timestamp': '2025-06-16T05:20:00.000000',
            'url': 'https://qxbroker.com/en/trade',
            'balance': random.choice(balances),
            'currentAsset': random.choice(assets),
            'currentPrice': None,
            'payout': random.choice(payouts),
            'pageTitle': 'Live trading | Quotex',
            'extractionCount': self.test_counter + 150,
            'source': 'REAL_CHROME_EXTENSION'
        }
        
        self.extension_widget.update_data(test_data)
        self.test_counter += 1
        
    def _on_data_received(self, data):
        """Handle data received from extension"""
        self._log(f"📨 Data received: {data.get('source', 'Unknown')} - Balance: {data.get('balance', 'N/A')}")
        
    def _on_connection_changed(self, connected):
        """Handle connection status change"""
        status = "🟢 Connected" if connected else "🔴 Disconnected"
        self._log(f"🔌 Connection status: {status}")
        self.status_label.setText(f"Status: {status}")
        
    def _log(self, message):
        """Log message to display"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.test_data_display.append(log_message)
        print(log_message)
        
    def closeEvent(self, event):
        """Handle window close"""
        self._log("👋 Closing Extension UI Test")
        event.accept()


def main():
    """Main function"""
    print("🧪 VIP BIG BANG - Extension UI Test")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Apply dark theme
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.WindowText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Base, QColor(25, 25, 25))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(0, 0, 0))
    palette.setColor(QPalette.ColorRole.ToolTipText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Text, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.BrightText, QColor(255, 0, 0))
    palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(0, 0, 0))
    app.setPalette(palette)
    
    # Create and show test window
    window = ExtensionUITestWindow()
    window.show()
    
    print("✅ Extension UI Test window opened")
    print("🔌 Testing Extension Data components...")
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
