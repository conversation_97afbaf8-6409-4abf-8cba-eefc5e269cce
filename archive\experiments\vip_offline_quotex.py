#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 VIP BIG BANG - Offline Quotex Simulator
🧠 شبیه‌ساز کامل Quotex برای استفاده آفلاین در ایران
📈 بدون نیاز به اینترنت - کاملاً محلی
🎮 Gaming-style UI with Real Trading Experience
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
import threading
import time
import json
from datetime import datetime, timedelta

class VIPOfflineQuotex:
    """🎯 VIP BIG BANG Offline Quotex Simulator"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 VIP BIG BANG - Offline Quotex Trading")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#0F0F23')
        self.root.state('zoomed')
        
        # Trading data
        self.balance = 1000.00
        self.demo_balance = 10000.00
        self.current_mode = "DEMO"  # DEMO or LIVE
        self.current_asset = "EUR/USD OTC"
        self.current_price = 1.07500
        self.trade_amount = 10
        self.trade_duration = 60  # seconds
        
        # Price history for realistic movement
        self.price_history = []
        self.generate_initial_price_history()
        
        # Active trades
        self.active_trades = []
        self.trade_history = []
        
        # Assets list
        self.assets = {
            "EUR/USD OTC": {"price": 1.07500, "volatility": 0.0001},
            "GBP/USD OTC": {"price": 1.26800, "volatility": 0.0002},
            "USD/JPY OTC": {"price": 149.750, "volatility": 0.01},
            "AUD/USD OTC": {"price": 0.65200, "volatility": 0.0001},
            "USD/CAD OTC": {"price": 1.36500, "volatility": 0.0001},
            "BTC/USD OTC": {"price": 43250.0, "volatility": 50.0},
            "ETH/USD OTC": {"price": 2650.0, "volatility": 20.0},
            "Gold OTC": {"price": 2025.50, "volatility": 2.0},
            "Silver OTC": {"price": 24.85, "volatility": 0.1},
            "Oil OTC": {"price": 78.50, "volatility": 1.0}
        }
        
        # Analysis data
        self.analysis_data = {
            "momentum": {"value": "Rising", "color": "#10B981", "confidence": 85, "icon": "📈"},
            "heatmap": {"value": "High Buy", "color": "#F59E0B", "confidence": 78, "icon": "🔥"},
            "buyer_seller": {"value": "65% Buy", "color": "#10B981", "confidence": 82, "icon": "⚖️"},
            "live_signals": {"value": "CALL", "color": "#10B981", "confidence": 90, "icon": "📡"},
            "brothers_can": {"value": "Active", "color": "#8B5CF6", "confidence": 75, "icon": "🤝"},
            "strong_level": {"value": "1.0732", "color": "#EF4444", "confidence": 88, "icon": "🎯"},
            "confirm_mode": {"value": "ON", "color": "#10B981", "confidence": 95, "icon": "✅"},
            "economic_news": {"value": "Medium", "color": "#6366F1", "confidence": 70, "icon": "📰"}
        }
        
        self.setup_ui()
        self.start_price_updates()
        self.start_analysis_updates()
        self.check_trades()
    
    def generate_initial_price_history(self):
        """Generate initial price history"""
        base_price = self.current_price
        for i in range(100):
            change = random.uniform(-0.0002, 0.0002)
            base_price += change
            self.price_history.append(base_price)
    
    def setup_ui(self):
        """Setup main UI"""
        # Main container
        main_container = tk.Frame(self.root, bg='#0F0F23')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header
        self.create_header(main_container)
        
        # Main content
        content_frame = tk.Frame(main_container, bg='#0F0F23')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Left panel
        left_panel = tk.Frame(content_frame, bg='#0F0F23', width=350)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_panel.pack_propagate(False)
        
        # Center panel (Quotex)
        center_panel = tk.Frame(content_frame, bg='#1A1A2E', relief=tk.RAISED, bd=3)
        center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Right panel
        right_panel = tk.Frame(content_frame, bg='#0F0F23', width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        right_panel.pack_propagate(False)
        
        # Create panels
        self.create_left_panel(left_panel)
        self.create_quotex_panel(center_panel)
        self.create_right_panel(right_panel)
        
        # Bottom panel
        self.create_bottom_panel(main_container)
    
    def create_header(self, parent):
        """Create header"""
        header = tk.Frame(parent, bg='#1A1A2E', height=80, relief=tk.RAISED, bd=2)
        header.pack(fill=tk.X, pady=(0, 10))
        header.pack_propagate(False)
        
        # Title
        title_frame = tk.Frame(header, bg='#1A1A2E')
        title_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)
        
        title = tk.Label(title_frame, text="🎯 VIP BIG BANG - Offline Quotex", 
                        font=("Arial", 24, "bold"), fg="#00D4FF", bg="#1A1A2E")
        title.pack(anchor=tk.W, pady=(15, 0))
        
        subtitle = tk.Label(title_frame, text="Professional Trading Simulator - No Internet Required", 
                           font=("Arial", 12), fg="#A0AEC0", bg="#1A1A2E")
        subtitle.pack(anchor=tk.W)
        
        # Status
        status_frame = tk.Frame(header, bg='#1A1A2E')
        status_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=15)
        
        # Mode indicator
        mode_color = "#43E97B" if self.current_mode == "DEMO" else "#F59E0B"
        mode_status = tk.Label(status_frame, text=f"🎮 {self.current_mode} MODE", 
                              font=("Arial", 12, "bold"), fg="white", bg=mode_color, 
                              padx=15, pady=8, relief=tk.RAISED, bd=2)
        mode_status.pack(side=tk.LEFT, padx=(0, 10))
        
        # Balance
        balance = self.demo_balance if self.current_mode == "DEMO" else self.balance
        balance_status = tk.Label(status_frame, text=f"💰 ${balance:.2f}", 
                                 font=("Arial", 12, "bold"), fg="white", bg="#00D4FF", 
                                 padx=15, pady=8, relief=tk.RAISED, bd=2)
        balance_status.pack(side=tk.LEFT, padx=(0, 10))
        self.balance_label = balance_status
        
        # Connection
        conn_status = tk.Label(status_frame, text="🟢 OFFLINE READY", 
                              font=("Arial", 12, "bold"), fg="white", bg="#8B5CF6", 
                              padx=15, pady=8, relief=tk.RAISED, bd=2)
        conn_status.pack(side=tk.LEFT)
    
    def create_left_panel(self, parent):
        """Create left analysis panel"""
        title = tk.Label(parent, text="📊 Live Analysis Engine", 
                        font=("Arial", 16, "bold"), fg="#00D4FF", bg="#0F0F23")
        title.pack(pady=(0, 15))
        
        boxes = [
            ("momentum", "Momentum"),
            ("heatmap", "Heatmap & PulseBar"),
            ("buyer_seller", "Buyer/Seller Power"),
            ("live_signals", "Live Signals")
        ]
        
        for key, title in boxes:
            self.create_analysis_box(parent, key, title)
    
    def create_right_panel(self, parent):
        """Create right analysis panel"""
        title = tk.Label(parent, text="🎯 Advanced Systems", 
                        font=("Arial", 16, "bold"), fg="#00D4FF", bg="#0F0F23")
        title.pack(pady=(0, 15))
        
        boxes = [
            ("brothers_can", "Brothers Can"),
            ("strong_level", "Strong Level"),
            ("confirm_mode", "Confirm Mode"),
            ("economic_news", "Economic News")
        ]
        
        for key, title in boxes:
            self.create_analysis_box(parent, key, title)
    
    def create_analysis_box(self, parent, data_key, title):
        """Create analysis box"""
        data = self.analysis_data[data_key]
        
        box = tk.Frame(parent, bg='#16213E', relief=tk.RAISED, bd=3,
                      highlightbackground=data["color"], highlightthickness=2)
        box.pack(fill=tk.X, pady=(0, 15), padx=5)
        
        # Header
        header = tk.Frame(box, bg='#16213E')
        header.pack(fill=tk.X, padx=15, pady=(15, 10))
        
        icon = tk.Label(header, text=data["icon"], font=("Arial", 20), bg="#16213E")
        icon.pack(side=tk.LEFT)
        
        title_label = tk.Label(header, text=title, font=("Arial", 12, "bold"), 
                              fg="#E8E8E8", bg="#16213E")
        title_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Value
        value = tk.Label(box, text=data["value"], font=("Arial", 16, "bold"), 
                        fg=data["color"], bg="#16213E")
        value.pack(pady=(0, 10))
        
        # Confidence
        conf_frame = tk.Frame(box, bg='#16213E')
        conf_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        conf_label = tk.Label(conf_frame, text=f"Confidence: {data['confidence']}%", 
                             font=("Arial", 10), fg="#A0AEC0", bg="#16213E")
        conf_label.pack()
        
        progress = ttk.Progressbar(conf_frame, length=250, mode='determinate', 
                                  value=data['confidence'])
        progress.pack(pady=(5, 0))
        
        # Store references
        setattr(self, f"{data_key}_value", value)
        setattr(self, f"{data_key}_conf", conf_label)
        setattr(self, f"{data_key}_progress", progress)

    def create_quotex_panel(self, parent):
        """Create main Quotex trading panel"""
        # Header
        header = tk.Frame(parent, bg='#1A1A2E', height=60)
        header.pack(fill=tk.X)
        header.pack_propagate(False)

        # Title
        title = tk.Label(header, text="📊 QUOTEX TRADING PLATFORM",
                        font=("Arial", 18, "bold"), fg="#00D4FF", bg="#1A1A2E")
        title.pack(side=tk.LEFT, padx=20, pady=15)

        # Mode switcher
        mode_frame = tk.Frame(header, bg='#1A1A2E')
        mode_frame.pack(side=tk.RIGHT, padx=20, pady=10)

        demo_btn = tk.Button(mode_frame, text="🎮 DEMO", font=("Arial", 10, "bold"),
                            bg="#43E97B" if self.current_mode == "DEMO" else "#4A5568",
                            fg="white", relief=tk.FLAT, padx=15, pady=5,
                            command=lambda: self.switch_mode("DEMO"))
        demo_btn.pack(side=tk.LEFT, padx=(0, 5))

        live_btn = tk.Button(mode_frame, text="💰 LIVE", font=("Arial", 10, "bold"),
                            bg="#F59E0B" if self.current_mode == "LIVE" else "#4A5568",
                            fg="white", relief=tk.FLAT, padx=15, pady=5,
                            command=lambda: self.switch_mode("LIVE"))
        live_btn.pack(side=tk.LEFT)

        self.demo_btn = demo_btn
        self.live_btn = live_btn

        # Main trading area
        trading_area = tk.Frame(parent, bg='#0F172A')
        trading_area.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # Asset selector
        asset_frame = tk.Frame(trading_area, bg='#16213E', height=50)
        asset_frame.pack(fill=tk.X, pady=(0, 10))
        asset_frame.pack_propagate(False)

        tk.Label(asset_frame, text="Asset:", font=("Arial", 12, "bold"),
                fg="#E8E8E8", bg="#16213E").pack(side=tk.LEFT, padx=15, pady=15)

        self.asset_var = tk.StringVar(value=self.current_asset)
        asset_combo = ttk.Combobox(asset_frame, textvariable=self.asset_var,
                                  values=list(self.assets.keys()), width=20, state="readonly")
        asset_combo.pack(side=tk.LEFT, padx=(0, 20), pady=15)
        asset_combo.bind('<<ComboboxSelected>>', self.change_asset)

        # Current price
        self.price_label = tk.Label(asset_frame, text=f"{self.current_price:.5f}",
                                   font=("Arial", 18, "bold"), fg="#43E97B", bg="#16213E")
        self.price_label.pack(side=tk.RIGHT, padx=20, pady=15)

        # Chart area
        chart_frame = tk.Frame(trading_area, bg='#0f1419', relief=tk.SUNKEN, bd=2)
        chart_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.chart_canvas = tk.Canvas(chart_frame, bg='#0f1419', highlightthickness=0)
        self.chart_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Trading controls
        controls_frame = tk.Frame(trading_area, bg='#0F172A', height=120)
        controls_frame.pack(fill=tk.X)
        controls_frame.pack_propagate(False)

        # Amount and duration
        settings_frame = tk.Frame(controls_frame, bg='#0F172A')
        settings_frame.pack(side=tk.LEFT, padx=20, pady=20)

        # Amount
        tk.Label(settings_frame, text="Amount ($):", font=("Arial", 12, "bold"),
                fg="#E8E8E8", bg="#0F172A").pack()
        self.amount_var = tk.StringVar(value=str(self.trade_amount))
        amount_entry = tk.Entry(settings_frame, textvariable=self.amount_var,
                               font=("Arial", 14), width=10, justify=tk.CENTER)
        amount_entry.pack(pady=(5, 15))

        # Duration
        tk.Label(settings_frame, text="Duration (sec):", font=("Arial", 12, "bold"),
                fg="#E8E8E8", bg="#0F172A").pack()
        self.duration_var = tk.StringVar(value=str(self.trade_duration))
        duration_combo = ttk.Combobox(settings_frame, textvariable=self.duration_var,
                                     values=["30", "60", "120", "300", "600"], width=8, state="readonly")
        duration_combo.pack(pady=(5, 0))

        # Trading buttons
        buttons_frame = tk.Frame(controls_frame, bg='#0F172A')
        buttons_frame.pack(side=tk.RIGHT, padx=20, pady=20)

        # HIGHER button
        higher_btn = tk.Button(buttons_frame, text="📈 HIGHER", font=("Arial", 18, "bold"),
                              bg="#43E97B", fg="white", relief=tk.RAISED, bd=4,
                              padx=40, pady=20, command=lambda: self.place_trade("HIGHER"))
        higher_btn.pack(side=tk.LEFT, padx=(0, 15))

        # LOWER button
        lower_btn = tk.Button(buttons_frame, text="📉 LOWER", font=("Arial", 18, "bold"),
                             bg="#EF4444", fg="white", relief=tk.RAISED, bd=4,
                             padx=40, pady=20, command=lambda: self.place_trade("LOWER"))
        lower_btn.pack(side=tk.LEFT)

        # Draw initial chart
        self.draw_chart()

    def create_bottom_panel(self, parent):
        """Create bottom panel with trades and indicators"""
        bottom_panel = tk.Frame(parent, bg='#1A1A2E', height=120, relief=tk.RAISED, bd=2)
        bottom_panel.pack(fill=tk.X, pady=(15, 0))
        bottom_panel.pack_propagate(False)

        # Left side - Active trades
        trades_frame = tk.Frame(bottom_panel, bg='#1A1A2E')
        trades_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=15)

        trades_title = tk.Label(trades_frame, text="🎯 Active Trades",
                               font=("Arial", 14, "bold"), fg="#00D4FF", bg="#1A1A2E")
        trades_title.pack(anchor=tk.W)

        self.trades_text = tk.Text(trades_frame, height=4, bg='#16213E', fg="#E8E8E8",
                                  font=("Arial", 10), relief=tk.SUNKEN, bd=1)
        self.trades_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # Right side - Indicators
        indicators_frame = tk.Frame(bottom_panel, bg='#1A1A2E')
        indicators_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20, pady=15)

        indicators_title = tk.Label(indicators_frame, text="📊 Live Indicators",
                                   font=("Arial", 14, "bold"), fg="#00D4FF", bg="#1A1A2E")
        indicators_title.pack()

        # Indicators
        indicators = [
            ("RSI: 65.2 📈", "#43E97B"),
            ("MACD: Bullish 🔥", "#F59E0B"),
            ("Volume: High ⚡", "#8B5CF6")
        ]

        for text, color in indicators:
            indicator = tk.Label(indicators_frame, text=text, font=("Arial", 11, "bold"),
                               fg=color, bg="#1A1A2E")
            indicator.pack(pady=2)

    def switch_mode(self, mode):
        """Switch between DEMO and LIVE mode"""
        self.current_mode = mode

        # Update buttons
        if mode == "DEMO":
            self.demo_btn.config(bg="#43E97B")
            self.live_btn.config(bg="#4A5568")
        else:
            self.demo_btn.config(bg="#4A5568")
            self.live_btn.config(bg="#F59E0B")

        # Update balance display
        balance = self.demo_balance if mode == "DEMO" else self.balance
        self.balance_label.config(text=f"💰 ${balance:.2f}")

        print(f"🔄 Switched to {mode} mode")

    def change_asset(self, event=None):
        """Change trading asset"""
        new_asset = self.asset_var.get()
        if new_asset in self.assets:
            self.current_asset = new_asset
            self.current_price = self.assets[new_asset]["price"]
            self.price_label.config(text=f"{self.current_price:.5f}")
            self.generate_initial_price_history()
            self.draw_chart()
            print(f"📈 Changed asset to {new_asset}")

    def place_trade(self, direction):
        """Place a trade"""
        try:
            amount = float(self.amount_var.get())
            duration = int(self.duration_var.get())

            # Check balance
            current_balance = self.demo_balance if self.current_mode == "DEMO" else self.balance
            if amount > current_balance:
                messagebox.showerror("Insufficient Balance",
                                   f"Not enough balance. Current: ${current_balance:.2f}")
                return

            # Create trade
            trade = {
                "id": len(self.active_trades) + 1,
                "direction": direction,
                "amount": amount,
                "asset": self.current_asset,
                "entry_price": self.current_price,
                "duration": duration,
                "start_time": datetime.now(),
                "end_time": datetime.now() + timedelta(seconds=duration),
                "mode": self.current_mode
            }

            self.active_trades.append(trade)

            # Deduct balance
            if self.current_mode == "DEMO":
                self.demo_balance -= amount
            else:
                self.balance -= amount

            # Update balance display
            balance = self.demo_balance if self.current_mode == "DEMO" else self.balance
            self.balance_label.config(text=f"💰 ${balance:.2f}")

            # Update trades display
            self.update_trades_display()

            print(f"🎯 Trade placed: {direction} ${amount} on {self.current_asset}")

            # Show confirmation
            messagebox.showinfo("Trade Placed",
                              f"✅ {direction} trade placed!\n\n"
                              f"Amount: ${amount}\n"
                              f"Asset: {self.current_asset}\n"
                              f"Duration: {duration}s\n"
                              f"Entry Price: {self.current_price:.5f}")

        except ValueError:
            messagebox.showerror("Invalid Input", "Please enter valid amount and duration")

    def update_trades_display(self):
        """Update active trades display"""
        self.trades_text.delete(1.0, tk.END)

        if not self.active_trades:
            self.trades_text.insert(tk.END, "No active trades")
            return

        for trade in self.active_trades:
            remaining = (trade["end_time"] - datetime.now()).total_seconds()
            if remaining > 0:
                profit_loss = (self.current_price - trade["entry_price"]) * 1000  # Simplified calculation
                if trade["direction"] == "LOWER":
                    profit_loss = -profit_loss

                status = "🟢" if profit_loss > 0 else "🔴"
                self.trades_text.insert(tk.END,
                    f"{status} {trade['direction']} ${trade['amount']} - {remaining:.0f}s left\n")

    def check_trades(self):
        """Check and close expired trades"""
        current_time = datetime.now()
        completed_trades = []

        for trade in self.active_trades[:]:
            if current_time >= trade["end_time"]:
                # Calculate result
                price_diff = self.current_price - trade["entry_price"]
                if trade["direction"] == "LOWER":
                    price_diff = -price_diff

                # Determine win/loss (simplified)
                won = price_diff > 0
                payout = trade["amount"] * 1.8 if won else 0  # 80% payout

                # Add to balance if won
                if won:
                    if trade["mode"] == "DEMO":
                        self.demo_balance += payout
                    else:
                        self.balance += payout

                # Move to history
                trade["result"] = "WIN" if won else "LOSS"
                trade["payout"] = payout
                trade["close_price"] = self.current_price
                self.trade_history.append(trade)
                completed_trades.append(trade)

                print(f"🎯 Trade completed: {trade['result']} - ${payout:.2f}")

        # Remove completed trades
        for trade in completed_trades:
            self.active_trades.remove(trade)

        # Update displays
        if completed_trades:
            balance = self.demo_balance if self.current_mode == "DEMO" else self.balance
            self.balance_label.config(text=f"💰 ${balance:.2f}")
            self.update_trades_display()

        # Schedule next check
        self.root.after(1000, self.check_trades)

    def draw_chart(self):
        """Draw trading chart"""
        if not hasattr(self, 'chart_canvas'):
            return

        # Clear canvas
        self.chart_canvas.delete("all")

        # Get canvas dimensions
        self.chart_canvas.update()
        width = self.chart_canvas.winfo_width()
        height = self.chart_canvas.winfo_height()

        if width <= 1 or height <= 1:
            self.root.after(100, self.draw_chart)
            return

        # Draw grid
        for i in range(0, width, 50):
            self.chart_canvas.create_line(i, 0, i, height, fill="#2d3748", width=1)
        for i in range(0, height, 30):
            self.chart_canvas.create_line(0, i, width, i, fill="#2d3748", width=1)

        # Draw price history
        if len(self.price_history) < 2:
            return

        # Calculate price range
        min_price = min(self.price_history[-50:])
        max_price = max(self.price_history[-50:])
        price_range = max_price - min_price
        if price_range == 0:
            price_range = 0.001

        # Draw candlesticks
        candle_width = max(3, width // 60)
        candle_spacing = max(5, width // 50)

        history_to_show = self.price_history[-50:]

        for i, price in enumerate(history_to_show):
            if i == 0:
                continue

            x = i * candle_spacing + 20

            # Generate OHLC data
            prev_price = history_to_show[i-1]
            open_price = prev_price
            close_price = price
            high_price = max(open_price, close_price) + random.uniform(0, price_range * 0.01)
            low_price = min(open_price, close_price) - random.uniform(0, price_range * 0.01)

            # Scale to canvas
            y_open = height - ((open_price - min_price) / price_range) * (height - 40) - 20
            y_close = height - ((close_price - min_price) / price_range) * (height - 40) - 20
            y_high = height - ((high_price - min_price) / price_range) * (height - 40) - 20
            y_low = height - ((low_price - min_price) / price_range) * (height - 40) - 20

            # Candle color
            color = "#43E97B" if close_price > open_price else "#EF4444"

            # Draw wick
            self.chart_canvas.create_line(x + candle_width//2, y_high,
                                         x + candle_width//2, y_low,
                                         fill=color, width=1)

            # Draw body
            self.chart_canvas.create_rectangle(x, min(y_open, y_close),
                                              x + candle_width, max(y_open, y_close),
                                              fill=color, outline=color)

        # Draw current price line
        current_y = height - ((self.current_price - min_price) / price_range) * (height - 40) - 20
        self.chart_canvas.create_line(0, current_y, width, current_y,
                                     fill="#00D4FF", width=2, dash=(5, 5))

        # Price labels
        self.chart_canvas.create_text(width - 80, current_y - 15,
                                     text=f"{self.current_price:.5f}",
                                     fill="#00D4FF", font=("Arial", 10, "bold"))

    def start_price_updates(self):
        """Start price updates"""
        def update_price():
            while True:
                try:
                    # Get asset volatility
                    volatility = self.assets[self.current_asset]["volatility"]

                    # Generate realistic price movement
                    change = random.uniform(-volatility, volatility)
                    self.current_price += change

                    # Update asset price
                    self.assets[self.current_asset]["price"] = self.current_price

                    # Add to history
                    self.price_history.append(self.current_price)
                    if len(self.price_history) > 200:
                        self.price_history.pop(0)

                    # Update UI
                    self.root.after(0, self.update_price_display)

                    time.sleep(1)  # Update every second
                except:
                    break

        # Start price update thread
        price_thread = threading.Thread(target=update_price, daemon=True)
        price_thread.start()

    def update_price_display(self):
        """Update price display"""
        if hasattr(self, 'price_label'):
            self.price_label.config(text=f"{self.current_price:.5f}")

        # Redraw chart every 3 seconds
        if hasattr(self, 'chart_canvas'):
            self.draw_chart()

    def start_analysis_updates(self):
        """Start analysis updates"""
        def update_analysis():
            for key, data in self.analysis_data.items():
                # Random confidence change
                change = random.randint(-3, 3)
                new_confidence = max(60, min(98, data["confidence"] + change))
                data["confidence"] = new_confidence

                # Update UI elements
                if hasattr(self, f"{key}_conf"):
                    conf_label = getattr(self, f"{key}_conf")
                    conf_label.config(text=f"Confidence: {new_confidence}%")

                if hasattr(self, f"{key}_progress"):
                    progress = getattr(self, f"{key}_progress")
                    progress.config(value=new_confidence)

        def analysis_loop():
            while True:
                try:
                    self.root.after(0, update_analysis)
                    time.sleep(5)  # Update every 5 seconds
                except:
                    break

        # Start analysis thread
        analysis_thread = threading.Thread(target=analysis_loop, daemon=True)
        analysis_thread.start()

    def run(self):
        """Run the application"""
        print("🎯 VIP BIG BANG Offline Quotex Started")
        print("💎 Professional offline trading simulator")
        print("📊 Real-time analysis with 8 advanced modules")
        print("🎮 Full trading experience without internet")
        print("\n" + "="*70)
        print("🎯 OFFLINE QUOTEX FEATURES:")
        print("  ✅ 10 Trading Assets (Forex, Crypto, Commodities)")
        print("  ✅ Real-time price simulation")
        print("  ✅ DEMO and LIVE trading modes")
        print("  ✅ Professional chart with candlesticks")
        print("  ✅ Active trades management")
        print("  ✅ 8 Analysis modules with live updates")
        print("  ✅ Realistic trading experience")
        print("  ✅ No internet connection required")
        print("  ✅ Perfect for Iran users")
        print("="*70)

        self.root.mainloop()


def main():
    """Main function"""
    try:
        app = VIPOfflineQuotex()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
