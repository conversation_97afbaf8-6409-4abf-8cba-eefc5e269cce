#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VIP BIG BANG - Quantum Stealth Pro Trader
Advanced 20 OTC Auto-Detection & Trading
Undetectable by Quotex with Quantum Stealth
No Unicode Issues - Professional Version
"""

import sys
import os
import time
import json
import random
from datetime import datetime
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWebEngineWidgets import QWebEngineView

class VIPQuantumStealthPro(QMainWindow):
    """VIP BIG BANG Quantum Stealth Pro Trader"""
    
    # Advanced Signals
    otc_detected = Signal(list)
    quantum_analysis_complete = Signal(dict)
    stealth_trade_executed = Signal(dict)
    
    def __init__(self):
        super().__init__()
        
        # Advanced Window Setup
        self.setWindowTitle("VIP BIG BANG - Quantum Stealth Pro Trader v8.0")
        self.setGeometry(15, 15, 2100, 1300)
        self.setMinimumSize(1900, 1100)
        
        # Quantum Stealth State
        self.quantum_state = {
            'engine_active': False,
            'stealth_level': 10,
            'anti_detection': True,
            'human_simulation': True,
            'quantum_tunneling': False,
            'invisibility_cloak': False,
            'behavioral_mimicry': True,
            'detection_avoidance': True
        }
        
        # Auto Trading State
        self.trading_state = {
            'auto_scan_active': False,
            'auto_trade_active': False,
            'otc_pairs_detected': [],
            'current_analysis': {},
            'active_trades': {},
            'trade_queue': [],
            'stealth_mode': True,
            'connected': False,
            'trader_installed': False
        }
        
        # 20 OTC Pairs Target List
        self.target_otc_pairs = [
            "EUR/USD OTC", "GBP/USD OTC", "USD/JPY OTC", "AUD/USD OTC", "USD/CAD OTC",
            "EUR/GBP OTC", "GBP/JPY OTC", "EUR/JPY OTC", "AUD/JPY OTC", "USD/CHF OTC",
            "EUR/CHF OTC", "GBP/CHF OTC", "AUD/CHF OTC", "CAD/JPY OTC", "CHF/JPY OTC",
            "NZD/USD OTC", "USD/SEK OTC", "USD/NOK OTC", "USD/DKK OTC", "EUR/AUD OTC"
        ]
        
        # Performance Analytics
        self.analytics = {
            'total_scans': 0,
            'pairs_detected': 0,
            'trades_executed': 0,
            'stealth_success_rate': 100.0,
            'detection_incidents': 0,
            'quantum_accuracy': 0.0,
            'profit_total': 0.0,
            'win_rate': 0.0
        }
        
        # Timers for real-time updates
        self.scan_timer = QTimer()
        self.analysis_timer = QTimer()
        self.stealth_timer = QTimer()
        self.update_timer = QTimer()
        self.time_timer = QTimer()
        
        # Initialize
        self._setup_quantum_ui()
        self._apply_quantum_styling()
        self._initialize_quantum_systems()
        self._start_real_time_systems()
        
        print("VIP BIG BANG Quantum Stealth Pro Trader v8.0 initialized")
        print("Quantum engine ready")
        print("Stealth protocols active")
        print("20 OTC auto-detection ready")
        print("Advanced anti-detection systems online")
    
    def _setup_quantum_ui(self):
        """Setup quantum UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(25)
        
        # Quantum Header
        header = self._create_quantum_header()
        main_layout.addWidget(header)
        
        # Main Content
        content_layout = QHBoxLayout()
        content_layout.setSpacing(25)
        
        # Left Panel - Quantum Controls
        left_panel = self._create_quantum_control_panel()
        content_layout.addWidget(left_panel)
        
        # Center Panel - Quotex with Quantum Integration
        center_panel = self._create_quantum_quotex_panel()
        content_layout.addWidget(center_panel, 3)
        
        # Right Panel - Stealth Trading Monitor
        right_panel = self._create_stealth_trading_panel()
        content_layout.addWidget(right_panel)
        
        main_layout.addLayout(content_layout)
        
        # Bottom Panel - 20 OTC Pairs Monitor
        bottom_panel = self._create_otc_pairs_monitor()
        main_layout.addWidget(bottom_panel)
        
        # Quantum Status Bar
        self._setup_quantum_status_bar()
    
    def _create_quantum_header(self):
        """Create quantum header"""
        header = QFrame()
        header.setObjectName("quantum-header")
        header.setFixedHeight(110)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(35, 20, 35, 20)
        
        # Quantum Logo & Info
        logo_layout = QVBoxLayout()
        
        logo_label = QLabel("VIP BIG BANG")
        logo_label.setObjectName("quantum-logo")
        logo_layout.addWidget(logo_label)
        
        subtitle_label = QLabel("Quantum Stealth Pro Trader v8.0")
        subtitle_label.setObjectName("quantum-subtitle")
        logo_layout.addWidget(subtitle_label)
        
        quantum_info = QLabel("Quantum State: Superposition | Stealth: Maximum | Anti-Detection: Active")
        quantum_info.setObjectName("quantum-info")
        logo_layout.addWidget(quantum_info)
        
        layout.addLayout(logo_layout)
        
        layout.addStretch()
        
        # Master Controls
        controls_layout = QGridLayout()
        
        # Row 1
        self.quantum_engine_btn = QPushButton("Activate Quantum Engine")
        self.quantum_engine_btn.setObjectName("quantum-master-btn")
        self.quantum_engine_btn.setCheckable(True)
        self.quantum_engine_btn.clicked.connect(self._toggle_quantum_engine)
        controls_layout.addWidget(self.quantum_engine_btn, 0, 0)
        
        self.stealth_system_btn = QPushButton("Enable Stealth System")
        self.stealth_system_btn.setObjectName("stealth-master-btn")
        self.stealth_system_btn.setCheckable(True)
        self.stealth_system_btn.clicked.connect(self._toggle_stealth_system)
        controls_layout.addWidget(self.stealth_system_btn, 0, 1)
        
        # Row 2
        self.auto_scan_btn = QPushButton("Start Auto OTC Scan")
        self.auto_scan_btn.setObjectName("scan-master-btn")
        self.auto_scan_btn.setCheckable(True)
        self.auto_scan_btn.clicked.connect(self._toggle_auto_scan)
        controls_layout.addWidget(self.auto_scan_btn, 1, 0)
        
        self.auto_trade_btn = QPushButton("Enable Auto Trading")
        self.auto_trade_btn.setObjectName("trade-master-btn")
        self.auto_trade_btn.setCheckable(True)
        self.auto_trade_btn.clicked.connect(self._toggle_auto_trading)
        controls_layout.addWidget(self.auto_trade_btn, 1, 1)
        
        layout.addLayout(controls_layout)
        
        layout.addStretch()
        
        # Status Indicators
        status_layout = QVBoxLayout()
        
        self.quantum_status = QLabel("Quantum: OFFLINE")
        self.quantum_status.setObjectName("quantum-status")
        status_layout.addWidget(self.quantum_status)
        
        self.stealth_status = QLabel("Stealth: INACTIVE")
        self.stealth_status.setObjectName("stealth-status")
        status_layout.addWidget(self.stealth_status)
        
        self.detection_risk = QLabel("Detection Risk: NONE")
        self.detection_risk.setObjectName("detection-status")
        status_layout.addWidget(self.detection_risk)
        
        self.live_time = QLabel()
        self.live_time.setObjectName("live-time")
        status_layout.addWidget(self.live_time)
        
        layout.addLayout(status_layout)
        
        return header
    
    def _create_quantum_control_panel(self):
        """Create quantum control panel"""
        panel = QFrame()
        panel.setObjectName("quantum-control-panel")
        panel.setFixedWidth(380)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        
        # Title
        title = QLabel("Quantum Control Center")
        title.setObjectName("quantum-panel-title")
        layout.addWidget(title)
        
        # Quantum Engine Status
        quantum_group = QGroupBox("Quantum Engine Status")
        quantum_group.setObjectName("quantum-group")
        quantum_layout = QVBoxLayout(quantum_group)
        
        self.quantum_power_label = QLabel("Quantum Power: 0%")
        self.quantum_power_label.setObjectName("quantum-info-label")
        quantum_layout.addWidget(self.quantum_power_label)
        
        self.coherence_time_label = QLabel("Coherence Time: 0ms")
        self.coherence_time_label.setObjectName("quantum-info-label")
        quantum_layout.addWidget(self.coherence_time_label)
        
        self.entanglement_label = QLabel("Entanglement: INACTIVE")
        self.entanglement_label.setObjectName("quantum-info-label")
        quantum_layout.addWidget(self.entanglement_label)
        
        # Quantum Controls
        quantum_controls = QHBoxLayout()
        
        self.tunneling_btn = QPushButton("Quantum Tunneling")
        self.tunneling_btn.setObjectName("quantum-feature-btn")
        self.tunneling_btn.setCheckable(True)
        self.tunneling_btn.clicked.connect(self._toggle_quantum_tunneling)
        quantum_controls.addWidget(self.tunneling_btn)
        
        self.superposition_btn = QPushButton("Superposition")
        self.superposition_btn.setObjectName("quantum-feature-btn")
        self.superposition_btn.setCheckable(True)
        self.superposition_btn.clicked.connect(self._toggle_superposition)
        quantum_controls.addWidget(self.superposition_btn)
        
        quantum_layout.addLayout(quantum_controls)
        layout.addWidget(quantum_group)
        
        # Stealth System Status
        stealth_group = QGroupBox("Stealth System Status")
        stealth_group.setObjectName("quantum-group")
        stealth_layout = QVBoxLayout(stealth_group)
        
        self.stealth_level_label = QLabel("Stealth Level: 10/10")
        self.stealth_level_label.setObjectName("quantum-info-label")
        stealth_layout.addWidget(self.stealth_level_label)
        
        self.anti_detection_label = QLabel("Anti-Detection: ACTIVE")
        self.anti_detection_label.setObjectName("quantum-info-label")
        stealth_layout.addWidget(self.anti_detection_label)
        
        self.human_behavior_label = QLabel("Human Behavior: 98%")
        self.human_behavior_label.setObjectName("quantum-info-label")
        stealth_layout.addWidget(self.human_behavior_label)
        
        # Stealth Controls
        stealth_controls = QHBoxLayout()
        
        self.invisibility_btn = QPushButton("Invisibility")
        self.invisibility_btn.setObjectName("stealth-feature-btn")
        self.invisibility_btn.setCheckable(True)
        self.invisibility_btn.clicked.connect(self._toggle_invisibility)
        stealth_controls.addWidget(self.invisibility_btn)
        
        self.mimicry_btn = QPushButton("Behavior Mimicry")
        self.mimicry_btn.setObjectName("stealth-feature-btn")
        self.mimicry_btn.setCheckable(True)
        self.mimicry_btn.setChecked(True)
        self.mimicry_btn.clicked.connect(self._toggle_behavior_mimicry)
        stealth_controls.addWidget(self.mimicry_btn)
        
        stealth_layout.addLayout(stealth_controls)
        layout.addWidget(stealth_group)
        
        # OTC Scanner Status
        scanner_group = QGroupBox("OTC Scanner Status")
        scanner_group.setObjectName("quantum-group")
        scanner_layout = QVBoxLayout(scanner_group)
        
        self.scan_status_label = QLabel("Scanner: STANDBY")
        self.scan_status_label.setObjectName("quantum-info-label")
        scanner_layout.addWidget(self.scan_status_label)
        
        self.pairs_detected_label = QLabel("Pairs Detected: 0/20")
        self.pairs_detected_label.setObjectName("quantum-info-label")
        scanner_layout.addWidget(self.pairs_detected_label)
        
        self.scan_progress_label = QLabel("Scan Progress: 0%")
        self.scan_progress_label.setObjectName("quantum-info-label")
        scanner_layout.addWidget(self.scan_progress_label)
        
        # Scanner Controls
        scanner_controls = QHBoxLayout()
        
        self.deep_scan_btn = QPushButton("Deep Scan")
        self.deep_scan_btn.setObjectName("scanner-control-btn")
        self.deep_scan_btn.clicked.connect(self._perform_deep_scan)
        scanner_controls.addWidget(self.deep_scan_btn)
        
        self.pattern_analysis_btn = QPushButton("Pattern Analysis")
        self.pattern_analysis_btn.setObjectName("scanner-control-btn")
        self.pattern_analysis_btn.setCheckable(True)
        self.pattern_analysis_btn.clicked.connect(self._toggle_pattern_analysis)
        scanner_controls.addWidget(self.pattern_analysis_btn)
        
        scanner_layout.addLayout(scanner_controls)
        layout.addWidget(scanner_group)
        
        layout.addStretch()
        
        return panel

    def _create_quantum_quotex_panel(self):
        """Create quantum Quotex panel"""
        panel = QFrame()
        panel.setObjectName("quantum-quotex-panel")

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # Quantum Connection Controls
        controls_layout = QHBoxLayout()

        self.quantum_connect_btn = QPushButton("Quantum Connect to Quotex")
        self.quantum_connect_btn.setObjectName("quantum-connect-btn")
        self.quantum_connect_btn.clicked.connect(self._quantum_connect_quotex)
        controls_layout.addWidget(self.quantum_connect_btn)

        self.install_stealth_trader_btn = QPushButton("Install Stealth Trader Pro")
        self.install_stealth_trader_btn.setObjectName("quantum-install-btn")
        self.install_stealth_trader_btn.clicked.connect(self._install_stealth_trader)
        controls_layout.addWidget(self.install_stealth_trader_btn)

        self.test_stealth_btn = QPushButton("Test Stealth Connection")
        self.test_stealth_btn.setObjectName("quantum-test-btn")
        self.test_stealth_btn.clicked.connect(self._test_stealth_connection)
        controls_layout.addWidget(self.test_stealth_btn)

        controls_layout.addStretch()

        self.emergency_stop_btn = QPushButton("EMERGENCY STOP")
        self.emergency_stop_btn.setObjectName("emergency-stop-btn")
        self.emergency_stop_btn.clicked.connect(self._emergency_stop_all)
        controls_layout.addWidget(self.emergency_stop_btn)

        layout.addLayout(controls_layout)

        # Quantum Status Bar
        status_layout = QHBoxLayout()

        self.connection_status = QLabel("Connection: OFFLINE")
        self.connection_status.setObjectName("connection-status")
        status_layout.addWidget(self.connection_status)

        self.trader_status = QLabel("Trader: NOT INSTALLED")
        self.trader_status.setObjectName("trader-status")
        status_layout.addWidget(self.trader_status)

        self.stealth_connection_status = QLabel("Stealth: INACTIVE")
        self.stealth_connection_status.setObjectName("stealth-connection-status")
        status_layout.addWidget(self.stealth_connection_status)

        status_layout.addStretch()

        layout.addLayout(status_layout)

        # Quantum Web View
        self.quantum_web_view = QWebEngineView()
        self.quantum_web_view.setObjectName("quantum-webview")
        self.quantum_web_view.setMinimumHeight(750)
        layout.addWidget(self.quantum_web_view)

        # Load Quotex with quantum stealth
        self.quantum_web_view.setUrl(QUrl("https://quotex.io/en/trade"))
        self.quantum_web_view.loadFinished.connect(self._on_quantum_quotex_loaded)

        return panel

    def _create_stealth_trading_panel(self):
        """Create stealth trading panel"""
        panel = QFrame()
        panel.setObjectName("stealth-trading-panel")
        panel.setFixedWidth(380)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # Title
        title = QLabel("Stealth Trading Monitor")
        title.setObjectName("quantum-panel-title")
        layout.addWidget(title)

        # Trading Status
        trading_group = QGroupBox("Auto Trading Status")
        trading_group.setObjectName("quantum-group")
        trading_layout = QVBoxLayout(trading_group)

        self.auto_trading_status = QLabel("Auto Trading: INACTIVE")
        self.auto_trading_status.setObjectName("quantum-info-label")
        trading_layout.addWidget(self.auto_trading_status)

        self.active_trades_label = QLabel("Active Trades: 0")
        self.active_trades_label.setObjectName("quantum-info-label")
        trading_layout.addWidget(self.active_trades_label)

        self.trade_queue_label = QLabel("Trade Queue: 0")
        self.trade_queue_label.setObjectName("quantum-info-label")
        trading_layout.addWidget(self.trade_queue_label)

        self.last_trade_label = QLabel("Last Trade: None")
        self.last_trade_label.setObjectName("quantum-info-label")
        trading_layout.addWidget(self.last_trade_label)

        layout.addWidget(trading_group)

        # Performance Analytics
        performance_group = QGroupBox("Performance Analytics")
        performance_group.setObjectName("quantum-group")
        performance_layout = QVBoxLayout(performance_group)

        self.total_trades_label = QLabel("Total Trades: 0")
        self.total_trades_label.setObjectName("quantum-info-label")
        performance_layout.addWidget(self.total_trades_label)

        self.quantum_accuracy_label = QLabel("Quantum Accuracy: 0%")
        self.quantum_accuracy_label.setObjectName("quantum-info-label")
        performance_layout.addWidget(self.quantum_accuracy_label)

        self.stealth_success_label = QLabel("Stealth Success: 100%")
        self.stealth_success_label.setObjectName("quantum-info-label")
        performance_layout.addWidget(self.stealth_success_label)

        self.detection_incidents_label = QLabel("Detection Incidents: 0")
        self.detection_incidents_label.setObjectName("quantum-info-label")
        performance_layout.addWidget(self.detection_incidents_label)

        self.profit_label = QLabel("Total Profit: $0.00")
        self.profit_label.setObjectName("quantum-info-label")
        performance_layout.addWidget(self.profit_label)

        self.win_rate_label = QLabel("Win Rate: 0%")
        self.win_rate_label.setObjectName("quantum-info-label")
        performance_layout.addWidget(self.win_rate_label)

        layout.addWidget(performance_group)

        # Trading Configuration
        config_group = QGroupBox("Trading Configuration")
        config_group.setObjectName("quantum-group")
        config_layout = QVBoxLayout(config_group)

        # Trade Amount
        amount_layout = QHBoxLayout()
        amount_layout.addWidget(QLabel("Trade Amount:"))
        self.trade_amount_spin = QSpinBox()
        self.trade_amount_spin.setObjectName("quantum-spinbox")
        self.trade_amount_spin.setRange(1, 1000)
        self.trade_amount_spin.setValue(10)
        self.trade_amount_spin.setSuffix(" $")
        amount_layout.addWidget(self.trade_amount_spin)
        config_layout.addLayout(amount_layout)

        # Trade Duration
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("Trade Duration:"))
        self.trade_duration_combo = QComboBox()
        self.trade_duration_combo.setObjectName("quantum-combo")
        self.trade_duration_combo.addItems(["5s", "15s", "30s", "1m", "5m"])
        self.trade_duration_combo.setCurrentText("5s")
        duration_layout.addWidget(self.trade_duration_combo)
        config_layout.addLayout(duration_layout)

        # Max Simultaneous Trades
        max_trades_layout = QHBoxLayout()
        max_trades_layout.addWidget(QLabel("Max Trades:"))
        self.max_trades_spin = QSpinBox()
        self.max_trades_spin.setObjectName("quantum-spinbox")
        self.max_trades_spin.setRange(1, 20)
        self.max_trades_spin.setValue(5)
        max_trades_layout.addWidget(self.max_trades_spin)
        config_layout.addLayout(max_trades_layout)

        # Signal Strength Threshold
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("Signal Threshold:"))
        self.signal_threshold_spin = QSpinBox()
        self.signal_threshold_spin.setObjectName("quantum-spinbox")
        self.signal_threshold_spin.setRange(50, 95)
        self.signal_threshold_spin.setValue(75)
        self.signal_threshold_spin.setSuffix("%")
        threshold_layout.addWidget(self.signal_threshold_spin)
        config_layout.addLayout(threshold_layout)

        layout.addWidget(config_group)

        # Emergency Controls
        emergency_group = QGroupBox("Emergency Controls")
        emergency_group.setObjectName("quantum-group")
        emergency_layout = QVBoxLayout(emergency_group)

        self.pause_trading_btn = QPushButton("Pause All Trading")
        self.pause_trading_btn.setObjectName("emergency-control-btn")
        self.pause_trading_btn.clicked.connect(self._pause_all_trading)
        emergency_layout.addWidget(self.pause_trading_btn)

        self.close_all_trades_btn = QPushButton("Close All Trades")
        self.close_all_trades_btn.setObjectName("emergency-control-btn")
        self.close_all_trades_btn.clicked.connect(self._close_all_trades)
        emergency_layout.addWidget(self.close_all_trades_btn)

        layout.addWidget(emergency_group)

        layout.addStretch()

        return panel

    def _create_otc_pairs_monitor(self):
        """Create OTC pairs monitor"""
        panel = QFrame()
        panel.setObjectName("otc-monitor-panel")
        panel.setFixedHeight(220)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # Title and Controls
        title_layout = QHBoxLayout()

        title = QLabel("20 OTC Pairs Real-time Monitor & Analysis")
        title.setObjectName("quantum-panel-title")
        title_layout.addWidget(title)

        title_layout.addStretch()

        self.auto_refresh_btn = QPushButton("Auto Refresh: ON")
        self.auto_refresh_btn.setObjectName("auto-refresh-btn")
        self.auto_refresh_btn.setCheckable(True)
        self.auto_refresh_btn.setChecked(True)
        self.auto_refresh_btn.clicked.connect(self._toggle_auto_refresh)
        title_layout.addWidget(self.auto_refresh_btn)

        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.setObjectName("select-all-btn")
        self.select_all_btn.clicked.connect(self._select_all_pairs)
        title_layout.addWidget(self.select_all_btn)

        layout.addLayout(title_layout)

        # OTC Pairs Grid
        scroll_area = QScrollArea()
        scroll_area.setObjectName("otc-scroll-area")
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        scroll_layout.setSpacing(12)

        # Create OTC pair widgets
        self.otc_widgets = {}
        for i, pair in enumerate(self.target_otc_pairs):
            row = i // 5
            col = i % 5

            otc_widget = self._create_otc_pair_widget(pair)
            scroll_layout.addWidget(otc_widget, row, col)
            self.otc_widgets[pair] = otc_widget

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        return panel

    def _create_otc_pair_widget(self, pair_name):
        """Create individual OTC pair widget"""
        widget = QFrame()
        widget.setObjectName("otc-pair-widget")
        widget.setFixedSize(220, 140)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # Pair name
        name_label = QLabel(pair_name.replace(" OTC", ""))
        name_label.setObjectName("otc-pair-name")
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(name_label)

        # Status
        status_label = QLabel("SCANNING...")
        status_label.setObjectName("otc-pair-status")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(status_label)

        # Analysis result
        analysis_label = QLabel("Analysis: 0%")
        analysis_label.setObjectName("otc-pair-analysis")
        analysis_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(analysis_label)

        # Signal
        signal_label = QLabel("Signal: WAIT")
        signal_label.setObjectName("otc-pair-signal")
        signal_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(signal_label)

        # Auto trade checkbox
        auto_trade_cb = QCheckBox("Auto Trade")
        auto_trade_cb.setObjectName("otc-auto-trade-cb")
        auto_trade_cb.setChecked(True)
        layout.addWidget(auto_trade_cb)

        # Store references in widget
        setattr(widget, 'status_label', status_label)
        setattr(widget, 'analysis_label', analysis_label)
        setattr(widget, 'signal_label', signal_label)
        setattr(widget, 'auto_trade_cb', auto_trade_cb)
        setattr(widget, 'pair_name', pair_name)

        return widget
