#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG System Launcher
منوی انتخاب سیستم‌های مختلف VIP BIG BANG
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path

# Fix Unicode encoding for Windows console
if sys.platform == "win32":
    try:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except:
        pass

class VIPSystemLauncher:
    """منوی انتخاب سیستم‌های VIP BIG BANG"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 VIP BIG BANG System Launcher")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        
        # Available systems
        self.systems = {
            "🚀 Main System": {
                "file": "main.py",
                "description": "سیستم اصلی VIP BIG BANG Enterprise",
                "features": ["15s Analysis", "5s Trading", "Enterprise Security"]
            },
            "🎯 Real Quotex": {
                "file": "vip_real_quotex_main.py", 
                "description": "اتصال واقعی به Quotex با WebView",
                "features": ["Real Quotex Connection", "Gaming UI", "Live Trading"]
            },
            "🔧 Auto Extension": {
                "file": "vip_auto_extension_quotex.py",
                "description": "نصب خودکار Chrome Extension",
                "features": ["Auto Extension Install", "Chrome Integration", "Stealth Mode"]
            },
            "💎 Complete System": {
                "file": "vip_big_bang_complete.py",
                "description": "سیستم کامل با همه قابلیت‌ها",
                "features": ["All-in-One", "Quantum Mode", "Professional UI"]
            },
            "🧠 Complete Analyzers": {
                "file": "vip_complete_with_all_analyzers.py",
                "description": "سیستم کامل با 10 اندیکاتور اصلی",
                "features": ["10 Core Analyzers", "Advanced Analysis", "Signal Generation"]
            },
            "📊 Working Dashboard": {
                "file": "vip_working_quotex_dashboard.py",
                "description": "داشبورد کاری با اتصال Quotex",
                "features": ["Live Dashboard", "Real-time Data", "Trading Controls"]
            },
            "⚡ Ultimate Dashboard": {
                "file": "vip_big_bang_ultimate_dashboard.py",
                "description": "داشبورد نهایی با قابلیت‌های پیشرفته",
                "features": ["Ultimate Features", "Multi-OTC", "Quantum Analysis"]
            },
            "🎮 Cartoon UI": {
                "file": "vip_cartoon_ultimate.py",
                "description": "رابط کاربری کارتونی و گیمینگ",
                "features": ["Gaming Design", "Cartoon Style", "Modern UI"]
            },
            "🏢 Professional UI": {
                "file": "vip_professional_ui.py",
                "description": "رابط کاربری حرفه‌ای",
                "features": ["Professional Design", "Business Style", "Clean UI"]
            },
            "⚛️ Quantum System": {
                "file": "vip_quantum_ultimate_system.py",
                "description": "سیستم کوانتوم با قابلیت‌های پیشرفته",
                "features": ["Quantum Engine", "Ultra-fast", "Advanced AI"]
            }
        }
        
        self.setup_ui()
    
    def setup_ui(self):
        """راه‌اندازی رابط کاربری"""
        # Header
        header_frame = tk.Frame(self.root, bg='#1a1a1a')
        header_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = tk.Label(header_frame, 
                              text="🚀 VIP BIG BANG System Launcher",
                              font=("Arial", 24, "bold"),
                              fg="#00D4FF", bg="#1a1a1a")
        title_label.pack()
        
        subtitle_label = tk.Label(header_frame,
                                 text="انتخاب سیستم مورد نظر برای اجرا",
                                 font=("Arial", 14),
                                 fg="#E8E8E8", bg="#1a1a1a")
        subtitle_label.pack(pady=(5, 0))
        
        # Main content
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Systems list
        self.create_systems_list(main_frame)
        
        # Control buttons
        self.create_control_buttons()
    
    def create_systems_list(self, parent):
        """ایجاد لیست سیستم‌ها"""
        # Scrollable frame
        canvas = tk.Canvas(parent, bg='#1a1a1a', highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#1a1a1a')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Create system cards
        for i, (name, info) in enumerate(self.systems.items()):
            self.create_system_card(scrollable_frame, name, info, i)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_system_card(self, parent, name, info, index):
        """ایجاد کارت سیستم"""
        # Card frame
        card_frame = tk.Frame(parent, bg='#2a2a2a', relief=tk.RAISED, bd=2)
        card_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Card content
        content_frame = tk.Frame(card_frame, bg='#2a2a2a')
        content_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # System name
        name_label = tk.Label(content_frame, text=name,
                             font=("Arial", 16, "bold"),
                             fg="#00D4FF", bg="#2a2a2a")
        name_label.pack(anchor=tk.W)
        
        # Description
        desc_label = tk.Label(content_frame, text=info["description"],
                             font=("Arial", 12),
                             fg="#E8E8E8", bg="#2a2a2a")
        desc_label.pack(anchor=tk.W, pady=(5, 10))
        
        # Features
        features_frame = tk.Frame(content_frame, bg='#2a2a2a')
        features_frame.pack(fill=tk.X)
        
        for feature in info["features"]:
            feature_label = tk.Label(features_frame, text=f"• {feature}",
                                   font=("Arial", 10),
                                   fg="#43E97B", bg="#2a2a2a")
            feature_label.pack(anchor=tk.W)
        
        # Launch button
        launch_btn = tk.Button(content_frame, text="🚀 Launch",
                              font=("Arial", 12, "bold"),
                              bg="#00D4FF", fg="#000000",
                              relief=tk.RAISED, bd=3,
                              padx=20, pady=5,
                              command=lambda: self.launch_system(info["file"]))
        launch_btn.pack(anchor=tk.E, pady=(10, 0))
    
    def create_control_buttons(self):
        """ایجاد دکمه‌های کنترل"""
        control_frame = tk.Frame(self.root, bg='#1a1a1a')
        control_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Exit button
        exit_btn = tk.Button(control_frame, text="❌ Exit",
                            font=("Arial", 14, "bold"),
                            bg="#EF4444", fg="white",
                            relief=tk.RAISED, bd=3,
                            padx=30, pady=10,
                            command=self.root.quit)
        exit_btn.pack(side=tk.RIGHT)
        
        # Refresh button
        refresh_btn = tk.Button(control_frame, text="🔄 Refresh",
                               font=("Arial", 14, "bold"),
                               bg="#F59E0B", fg="white",
                               relief=tk.RAISED, bd=3,
                               padx=30, pady=10,
                               command=self.refresh_systems)
        refresh_btn.pack(side=tk.RIGHT, padx=(0, 10))
    
    def launch_system(self, filename):
        """اجرای سیستم انتخاب شده"""
        try:
            if not os.path.exists(filename):
                messagebox.showerror("Error", f"File not found: {filename}")
                return
            
            print(f"Launching: {filename}")
            subprocess.Popen([sys.executable, filename])
            messagebox.showinfo("Success", f"System launched: {filename}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch system: {e}")
    
    def refresh_systems(self):
        """بروزرسانی لیست سیستم‌ها"""
        messagebox.showinfo("Info", "Systems list refreshed!")
    
    def run(self):
        """اجرای launcher"""
        print("🚀 VIP BIG BANG System Launcher")
        print("Select your desired system to launch")
        print("-" * 50)
        
        self.root.mainloop()

def main():
    """تابع اصلی"""
    launcher = VIPSystemLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
