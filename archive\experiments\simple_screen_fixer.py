"""
📐 SIMPLE SCREEN FIXER
🖥️ FIXES WINDOW SIZE FOR ANY SCREEN
🎯 WORKS WITH ALL RESOLUTIONS
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QMessageBox
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont

class SimpleScreenFixer:
    """
    📐 SIMPLE SCREEN FIXER
    🖥️ Automatically fixes window size for any screen
    """
    
    def __init__(self):
        self.app = QApplication.instance()
        if not self.app:
            self.app = QApplication(sys.argv)
        
        # Get screen information
        self.screen = self.app.primaryScreen()
        self.screen_geometry = self.screen.geometry()
        self.available_geometry = self.screen.availableGeometry()
        
        print(f"📺 Screen Size: {self.screen_geometry.width()}x{self.screen_geometry.height()}")
        print(f"📐 Available Size: {self.available_geometry.width()}x{self.available_geometry.height()}")
        
        # Calculate optimal window size
        self.optimal_width = int(self.available_geometry.width() * 0.85)
        self.optimal_height = int(self.available_geometry.height() * 0.85)
        
        # Ensure minimum size
        self.optimal_width = max(self.optimal_width, 1000)
        self.optimal_height = max(self.optimal_height, 700)
        
        print(f"🎯 Optimal Window Size: {self.optimal_width}x{self.optimal_height}")
    
    def fix_window_size(self, window):
        """📐 Fix window size to fit screen perfectly"""
        try:
            # Set optimal size
            window.resize(self.optimal_width, self.optimal_height)
            
            # Center window
            self.center_window(window)
            
            print(f"✅ Window fixed: {self.optimal_width}x{self.optimal_height}")
            return True
            
        except Exception as e:
            print(f"❌ Fix window error: {e}")
            return False
    
    def center_window(self, window):
        """🎯 Center window on screen"""
        # Get window frame geometry
        window_geometry = window.frameGeometry()
        
        # Get screen center
        screen_center = self.available_geometry.center()
        
        # Move window center to screen center
        window_geometry.moveCenter(screen_center)
        
        # Move window to calculated position
        window.move(window_geometry.topLeft())
        
        print(f"📍 Window centered at: {window_geometry.topLeft().x()}, {window_geometry.topLeft().y()}")
    
    def get_screen_info(self):
        """📊 Get screen information"""
        return {
            "screen_size": f"{self.screen_geometry.width()}x{self.screen_geometry.height()}",
            "available_size": f"{self.available_geometry.width()}x{self.available_geometry.height()}",
            "optimal_size": f"{self.optimal_width}x{self.optimal_height}",
            "dpi": self.screen.logicalDotsPerInch()
        }

class ScreenFixerTestWindow(QMainWindow):
    """🧪 Test window for screen fixer"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📐 VIP BIG BANG - Screen Size Fixer")
        
        # Initialize screen fixer
        self.screen_fixer = SimpleScreenFixer()
        
        # Setup UI
        self.setup_ui()
        
        # Apply screen fix
        self.apply_screen_fix()
    
    def setup_ui(self):
        """🎨 Setup UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("📐 VIP BIG BANG Screen Size Fixer")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #2196F3; margin: 20px;")
        layout.addWidget(title)
        
        # Screen info
        screen_info = self.screen_fixer.get_screen_info()
        
        info_layout = QVBoxLayout()
        
        screen_label = QLabel(f"📺 Screen Size: {screen_info['screen_size']}")
        screen_label.setFont(QFont("Arial", 12))
        screen_label.setStyleSheet("color: #333; margin: 5px;")
        info_layout.addWidget(screen_label)
        
        available_label = QLabel(f"📐 Available Size: {screen_info['available_size']}")
        available_label.setFont(QFont("Arial", 12))
        available_label.setStyleSheet("color: #333; margin: 5px;")
        info_layout.addWidget(available_label)
        
        optimal_label = QLabel(f"🎯 Optimal Window: {screen_info['optimal_size']}")
        optimal_label.setFont(QFont("Arial", 12))
        optimal_label.setStyleSheet("color: #4CAF50; margin: 5px; font-weight: bold;")
        info_layout.addWidget(optimal_label)
        
        dpi_label = QLabel(f"🔍 DPI: {screen_info['dpi']:.0f}")
        dpi_label.setFont(QFont("Arial", 12))
        dpi_label.setStyleSheet("color: #333; margin: 5px;")
        info_layout.addWidget(dpi_label)
        
        layout.addLayout(info_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        fix_button = QPushButton("📐 Fix Window Size")
        fix_button.clicked.connect(self.apply_screen_fix)
        fix_button.setFont(QFont("Arial", 12, QFont.Bold))
        fix_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(fix_button)
        
        center_button = QPushButton("🎯 Center Window")
        center_button.clicked.connect(self.center_window)
        center_button.setFont(QFont("Arial", 12, QFont.Bold))
        center_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)
        button_layout.addWidget(center_button)
        
        layout.addLayout(button_layout)
        
        # Status
        self.status_label = QLabel("✅ Screen fixer ready!")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 11))
        self.status_label.setStyleSheet("color: #4CAF50; margin: 10px; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # Instructions
        instructions = QLabel("""
📋 Instructions:
1️⃣ Click "📐 Fix Window Size" to resize window optimally
2️⃣ Click "🎯 Center Window" to center on screen
3️⃣ Window will fit your screen perfectly!

✅ Works with any screen size and resolution
🖥️ Supports 4K, 2K, Full HD, and all other sizes
        """)
        instructions.setFont(QFont("Arial", 10))
        instructions.setStyleSheet("color: #666; margin: 20px; line-height: 1.5;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
    
    def apply_screen_fix(self):
        """📐 Apply screen fix"""
        try:
            success = self.screen_fixer.fix_window_size(self)
            
            if success:
                self.status_label.setText("✅ Window size fixed perfectly!")
                self.status_label.setStyleSheet("color: #4CAF50; margin: 10px; font-weight: bold;")
                
                # Show success message
                QMessageBox.information(self, "✅ Success", 
                    f"Window size fixed successfully!\n\n"
                    f"📐 Size: {self.screen_fixer.optimal_width}x{self.screen_fixer.optimal_height}\n"
                    f"🎯 Centered on screen\n"
                    f"✅ Perfect fit for your display!")
            else:
                self.status_label.setText("❌ Failed to fix window size")
                self.status_label.setStyleSheet("color: #f44336; margin: 10px; font-weight: bold;")
                
        except Exception as e:
            self.status_label.setText(f"❌ Error: {e}")
            self.status_label.setStyleSheet("color: #f44336; margin: 10px; font-weight: bold;")
    
    def center_window(self):
        """🎯 Center window"""
        try:
            self.screen_fixer.center_window(self)
            self.status_label.setText("🎯 Window centered!")
            self.status_label.setStyleSheet("color: #4CAF50; margin: 10px; font-weight: bold;")
            
        except Exception as e:
            self.status_label.setText(f"❌ Center error: {e}")
            self.status_label.setStyleSheet("color: #f44336; margin: 10px; font-weight: bold;")

def main():
    """🚀 Main function"""
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    # Create and show test window
    window = ScreenFixerTestWindow()
    window.show()
    
    print("🚀 Screen Fixer Test Window launched!")
    print("📐 Test the screen fixing functionality")
    
    return app.exec()

if __name__ == "__main__":
    main()
