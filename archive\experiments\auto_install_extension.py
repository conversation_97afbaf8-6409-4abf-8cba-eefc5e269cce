#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VIP BIG BANG - Auto Extension Installer
نصب خودکار و اجباری اکستنشن Chrome
"""

import os
import sys
import subprocess
import shutil
import winreg
import time
import json
from pathlib import Path

class AutoExtensionInstaller:
    """
    🚀 نصب‌کننده خودکار و اجباری اکستنشن
    """
    
    def __init__(self):
        self.extension_dir = Path("chrome_extension")
        self.temp_dir = Path("temp_extension")
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
    def find_chrome(self):
        """پیدا کردن Chrome"""
        for path in self.chrome_paths:
            if os.path.exists(path):
                return path
        return None
    
    def create_temp_extension(self):
        """ایجاد اکستنشن موقت"""
        try:
            # Remove old temp
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
            
            # Copy extension to temp
            shutil.copytree(self.extension_dir, self.temp_dir)
            
            # Modify manifest for auto-install
            manifest_path = self.temp_dir / "manifest.json"
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            # Add auto-install permissions
            manifest["permissions"].extend([
                "management",
                "tabs",
                "activeTab",
                "storage",
                "unlimitedStorage"
            ])
            
            # Make permissions unique
            manifest["permissions"] = list(set(manifest["permissions"]))
            
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, indent=2)
            
            print(f"✅ Temp extension created: {self.temp_dir}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create temp extension: {e}")
            return False
    
    def kill_chrome_processes(self):
        """بستن تمام پروسه‌های Chrome"""
        try:
            subprocess.run(["taskkill", "/f", "/im", "chrome.exe"], 
                         capture_output=True, check=False)
            time.sleep(2)
            print("✅ Chrome processes killed")
        except:
            pass
    
    def launch_chrome_with_extension(self):
        """اجرای Chrome با اکستنشن"""
        try:
            chrome_exe = self.find_chrome()
            if not chrome_exe:
                return False
            
            extension_path = os.path.abspath(self.temp_dir)
            
            # Kill existing Chrome
            self.kill_chrome_processes()
            
            # Chrome arguments for forced extension loading
            chrome_args = [
                chrome_exe,
                f"--load-extension={extension_path}",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
                "--enable-experimental-extension-apis",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--disable-features=VizDisplayCompositor",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-mode",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--force-dev-mode-highlighting",
                "--enable-logging",
                "--log-level=0",
                "https://qxbroker.com/en/trade"
            ]
            
            print("🚀 Launching Chrome with forced extension loading...")
            process = subprocess.Popen(chrome_args, shell=False)
            
            # Wait for Chrome to start
            time.sleep(5)
            
            print("✅ Chrome launched with extension!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to launch Chrome: {e}")
            return False
    
    def create_registry_entry(self):
        """ایجاد ورودی رجیستری برای نصب اجباری"""
        try:
            extension_path = os.path.abspath(self.temp_dir)
            
            # Registry path for Chrome extensions
            reg_path = r"SOFTWARE\Policies\Google\Chrome\ExtensionInstallForcelist"
            
            try:
                # Create registry key
                key = winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
                
                # Add extension to force install list
                winreg.SetValueEx(key, "1", 0, winreg.REG_SZ, f"{extension_path}")
                
                winreg.CloseKey(key)
                print("✅ Registry entry created for forced installation")
                return True
                
            except PermissionError:
                print("⚠️ Registry modification requires admin rights")
                return False
                
        except Exception as e:
            print(f"❌ Registry entry failed: {e}")
            return False
    
    def create_startup_script(self):
        """ایجاد اسکریپت راه‌اندازی"""
        startup_script = f'''@echo off
echo 🚀 VIP BIG BANG Auto Extension Loader
echo.

set EXTENSION_PATH={os.path.abspath(self.temp_dir)}
set CHROME_PATH={self.find_chrome()}

echo Extension Path: %EXTENSION_PATH%
echo Chrome Path: %CHROME_PATH%
echo.

echo Killing existing Chrome processes...
taskkill /f /im chrome.exe >nul 2>&1

echo Starting Chrome with VIP BIG BANG extension...
start "" "%CHROME_PATH%" --load-extension="%EXTENSION_PATH%" --disable-extensions-file-access-check --no-first-run --no-default-browser-check "https://qxbroker.com/en/trade"

echo.
echo ✅ Chrome started with VIP BIG BANG extension!
echo 📋 Extension should be automatically loaded
echo.
echo 🔧 If extension is not working:
echo 1. Go to chrome://extensions/
echo 2. Enable "Developer mode"
echo 3. Check if VIP BIG BANG is enabled
echo.
pause
'''
        
        with open("start_chrome_with_extension.bat", "w", encoding="utf-8") as f:
            f.write(startup_script)
        
        print("✅ Startup script created: start_chrome_with_extension.bat")
        return True
    
    def auto_install_extension(self):
        """نصب خودکار کامل اکستنشن"""
        print("="*60)
        print("🚀 VIP BIG BANG Auto Extension Installer")
        print("="*60)
        
        # Step 1: Check Chrome
        chrome_path = self.find_chrome()
        if not chrome_path:
            print("❌ Chrome not found! Please install Google Chrome first.")
            return False
        print(f"✅ Chrome found: {chrome_path}")
        
        # Step 2: Check extension files
        if not self.extension_dir.exists():
            print("❌ Extension directory not found!")
            return False
        print("✅ Extension files found")
        
        # Step 3: Create temp extension
        if not self.create_temp_extension():
            print("❌ Failed to create temp extension")
            return False
        
        # Step 4: Create startup script
        self.create_startup_script()
        
        # Step 5: Try registry installation (optional)
        self.create_registry_entry()
        
        # Step 6: Launch Chrome with extension
        success = self.launch_chrome_with_extension()
        
        print("\n" + "="*60)
        if success:
            print("✅ AUTO INSTALLATION COMPLETED!")
            print("\n📋 What happened:")
            print("1. ✅ Chrome processes killed")
            print("2. ✅ Temp extension created")
            print("3. ✅ Chrome launched with extension")
            print("4. ✅ Extension should be loaded automatically")
            
            print("\n🎯 Extension Status:")
            print("• Extension should be visible in chrome://extensions/")
            print("• VIP BIG BANG should be enabled automatically")
            print("• Quotex page should detect the extension")
            
            print("\n🔧 If extension is not working:")
            print("1. Run: start_chrome_with_extension.bat")
            print("2. Go to chrome://extensions/")
            print("3. Enable 'Developer mode'")
            print("4. Make sure VIP BIG BANG is enabled")
            
        else:
            print("❌ AUTO INSTALLATION FAILED")
            print("\n🔧 Manual steps:")
            print("1. Run: start_chrome_with_extension.bat")
            print("2. Follow the instructions in the script")
        
        print("="*60)
        return success

def main():
    """اجرای نصب خودکار"""
    installer = AutoExtensionInstaller()
    installer.auto_install_extension()

if __name__ == "__main__":
    main()
