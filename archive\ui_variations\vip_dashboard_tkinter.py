#!/usr/bin/env python3
"""
🚀 VIP BIG BANG Dashboard - Tkinter Version
Professional trading dashboard with gaming-style design
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
import sys
import os
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import new components
try:
    from ui.components.multi_otc_analyzer import MultiOTCAnalyzer
    from ui.components.trading_systems_manager import TradingSystemsManager
    from ui.components.live_quotex_chart import LiveQuotexChart
    ADVANCED_COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"Advanced components not available: {e}")
    ADVANCED_COMPONENTS_AVAILABLE = False

class VIPDashboard:
    """VIP BIG BANG Professional Dashboard using Tkinter"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 VIP BIG BANG - Professional Trading Dashboard")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1F2937')
        
        # Dashboard state
        self.current_asset = "EUR/USD"
        self.current_timeframe = "15s"
        self.balance = 1000.0
        self.is_connected = False
        self.autotrade_enabled = False
        
        # Analysis data
        self.analysis_data = {
            "momentum": 85,
            "heatmap": "Strong",
            "buyer_seller": 67,
            "live_signals": "BUY",
            "brothers_can": "Active",
            "strong_level": 1.0732,
            "confirm_mode": "ON",
            "economic_news": "High"
        }
        
        # Setup UI
        self._setup_styles()
        self._create_ui()
        self._start_updates()
    
    def _setup_styles(self):
        """Setup custom styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Header.TFrame', background='#2D1B69')
        style.configure('Panel.TFrame', background='#374151')
        style.configure('Title.TLabel', background='#1F2937', foreground='#8B5CF6', font=('Arial', 16, 'bold'))
        style.configure('Value.TLabel', background='#1F2937', foreground='#FFFFFF', font=('Arial', 12, 'bold'))
        style.configure('Status.TLabel', background='#1F2937', foreground='#10B981', font=('Arial', 10, 'bold'))
    
    def _create_ui(self):
        """Create the main UI"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#1F2937')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # Header
        self._create_header(main_frame)
        
        # Content area
        content_frame = tk.Frame(main_frame, bg='#1F2937')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        
        # Left panel
        self._create_left_panel(content_frame)
        
        # Center panel
        self._create_center_panel(content_frame)
        
        # Right panel
        self._create_right_panel(content_frame)
        
        # Footer
        self._create_footer(main_frame)
    
    def _create_header(self, parent):
        """Create header section"""
        header_frame = tk.Frame(parent, bg='#2D1B69', relief=tk.RAISED, bd=2)
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Left section
        left_frame = tk.Frame(header_frame, bg='#2D1B69')
        left_frame.pack(side=tk.LEFT, padx=20, pady=15)
        
        # Logo and title
        logo_label = tk.Label(left_frame, text="🚀", font=('Arial', 32), bg='#2D1B69', fg='#8B5CF6')
        logo_label.pack(side=tk.LEFT)
        
        title_frame = tk.Frame(left_frame, bg='#2D1B69')
        title_frame.pack(side=tk.LEFT, padx=(10, 0))
        
        title_label = tk.Label(title_frame, text="VIP BIG BANG", font=('Arial', 20, 'bold'), bg='#2D1B69', fg='#8B5CF6')
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(title_frame, text="Professional Trading Dashboard", font=('Arial', 12), bg='#2D1B69', fg='#9CA3AF')
        subtitle_label.pack(anchor=tk.W)
        
        # Right section
        right_frame = tk.Frame(header_frame, bg='#2D1B69')
        right_frame.pack(side=tk.RIGHT, padx=20, pady=15)
        
        # Status indicators
        self.connection_label = tk.Label(right_frame, text="🔴 Disconnected", font=('Arial', 10, 'bold'), 
                                       bg='#EF4444', fg='white', padx=10, pady=5)
        self.connection_label.pack(side=tk.RIGHT, padx=(0, 10))
        
        self.balance_label = tk.Label(right_frame, text=f"💰 ${self.balance:.2f}", font=('Arial', 12, 'bold'), 
                                    bg='#2D1B69', fg='#8B5CF6')
        self.balance_label.pack(side=tk.RIGHT, padx=(0, 10))
    
    def _create_left_panel(self, parent):
        """Create left analysis panel"""
        left_frame = tk.Frame(parent, bg='#374151', relief=tk.RAISED, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        left_frame.configure(width=250)
        left_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(left_frame, text="📊 Analysis Modules", font=('Arial', 14, 'bold'), 
                             bg='#374151', fg='#8B5CF6')
        title_label.pack(pady=(15, 20))
        
        # Analysis boxes
        self.analysis_boxes = {}
        
        # Create 2x4 grid
        grid_frame = tk.Frame(left_frame, bg='#374151')
        grid_frame.pack(padx=15, pady=10)
        
        analyses = [
            ("⚡", "Momentum", "momentum", "#8B5CF6"),
            ("🔥", "Heatmap", "heatmap", "#EC4899"),
            ("⚖️", "Buyer/Seller", "buyer_seller", "#60A5FA"),
            ("📡", "Live Signals", "live_signals", "#10B981"),
            ("🤝", "Brothers Can", "brothers_can", "#F59E0B"),
            ("🎯", "Strong Level", "strong_level", "#EF4444"),
            ("✅", "Confirm Mode", "confirm_mode", "#8B5CF6"),
            ("📰", "Economic News", "economic_news", "#6366F1")
        ]
        
        for i, (icon, title, key, color) in enumerate(analyses):
            row = i // 2
            col = i % 2
            
            box = self._create_analysis_box(grid_frame, icon, title, key, color)
            box.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')
            
            self.analysis_boxes[key] = box
    
    def _create_analysis_box(self, parent, icon, title, key, color):
        """Create individual analysis box"""
        box_frame = tk.Frame(parent, bg='#4B5563', relief=tk.RAISED, bd=2, width=110, height=110)
        box_frame.pack_propagate(False)
        
        # Icon
        icon_label = tk.Label(box_frame, text=icon, font=('Arial', 20), bg='#4B5563', fg=color)
        icon_label.pack(pady=(10, 5))
        
        # Title
        title_label = tk.Label(box_frame, text=title, font=('Arial', 8, 'bold'), bg='#4B5563', fg='white')
        title_label.pack()
        
        # Value
        value_text = str(self.analysis_data.get(key, "N/A"))
        if key == "buyer_seller" or key == "momentum":
            value_text += "%"
        
        value_label = tk.Label(box_frame, text=value_text, font=('Arial', 10, 'bold'), bg='#4B5563', fg=color)
        value_label.pack(pady=(5, 10))
        
        # Store value label for updates
        box_frame.value_label = value_label
        box_frame.color = color
        
        return box_frame
    
    def _create_center_panel(self, parent):
        """Create center chart panel"""
        center_frame = tk.Frame(parent, bg='#1F2937', relief=tk.RAISED, bd=2)
        center_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))
        
        # Chart title
        title_label = tk.Label(center_frame, text=f"📈 Real-time Chart - {self.current_asset} ({self.current_timeframe})", 
                             font=('Arial', 14, 'bold'), bg='#1F2937', fg='white')
        title_label.pack(pady=(15, 10))
        
        # Chart area (placeholder)
        chart_frame = tk.Frame(center_frame, bg='#0F172A', relief=tk.SUNKEN, bd=2)
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        chart_label = tk.Label(chart_frame, text="📊 Live Candlestick Chart\n\nReal-time Price Data\nMA6 Overlay\nVortex Indicator\nVolume Bars", 
                             font=('Arial', 16), bg='#0F172A', fg='#9CA3AF', justify=tk.CENTER)
        chart_label.pack(expand=True)
        
        # Indicators panel
        indicators_frame = tk.Frame(center_frame, bg='#374151', relief=tk.RAISED, bd=1)
        indicators_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        indicators_title = tk.Label(indicators_frame, text="📊 Technical Indicators", font=('Arial', 12, 'bold'), 
                                   bg='#374151', fg='#8B5CF6')
        indicators_title.pack(pady=(10, 5))
        
        # Indicator values
        indicators_info = tk.Frame(indicators_frame, bg='#374151')
        indicators_info.pack(pady=(0, 10))
        
        self.ma6_label = tk.Label(indicators_info, text="MA6: 1.07325", font=('Arial', 11, 'bold'), 
                                bg='#374151', fg='#8B5CF6')
        self.ma6_label.pack(side=tk.LEFT, padx=20)
        
        self.vortex_label = tk.Label(indicators_info, text="Vortex: VI+ 1.02 | VI- 0.98", font=('Arial', 11, 'bold'), 
                                   bg='#374151', fg='#60A5FA')
        self.vortex_label.pack(side=tk.LEFT, padx=20)
        
        self.volume_label = tk.Label(indicators_info, text="Volume: High", font=('Arial', 11, 'bold'), 
                                   bg='#374151', fg='#EC4899')
        self.volume_label.pack(side=tk.LEFT, padx=20)
    
    def _create_right_panel(self, parent):
        """Create right control panel"""
        right_frame = tk.Frame(parent, bg='#374151', relief=tk.RAISED, bd=2)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y)
        right_frame.configure(width=220)
        right_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(right_frame, text="🎛️ Trading Controls", font=('Arial', 14, 'bold'), 
                             bg='#374151', fg='#60A5FA')
        title_label.pack(pady=(15, 20))
        
        # Trading buttons
        buttons_frame = tk.Frame(right_frame, bg='#374151')
        buttons_frame.pack(padx=15, pady=10)
        
        buy_btn = tk.Button(buttons_frame, text="📈 BUY", font=('Arial', 14, 'bold'), 
                          bg='#10B981', fg='white', relief=tk.RAISED, bd=3,
                          command=self._place_buy_order, width=15, height=2)
        buy_btn.pack(pady=(0, 10))
        
        sell_btn = tk.Button(buttons_frame, text="📉 SELL", font=('Arial', 14, 'bold'), 
                           bg='#EF4444', fg='white', relief=tk.RAISED, bd=3,
                           command=self._place_sell_order, width=15, height=2)
        sell_btn.pack(pady=(0, 20))
        
        # AutoTrade section
        autotrade_frame = tk.Frame(right_frame, bg='#4B5563', relief=tk.RAISED, bd=2)
        autotrade_frame.pack(fill=tk.X, padx=15, pady=10)
        
        autotrade_title = tk.Label(autotrade_frame, text="🤖 AutoTrade Status", font=('Arial', 11, 'bold'), 
                                 bg='#4B5563', fg='#8B5CF6')
        autotrade_title.pack(pady=(10, 5))
        
        self.autotrade_status = tk.Label(autotrade_frame, text="🔴 OFF", font=('Arial', 12, 'bold'), 
                                       bg='#4B5563', fg='#EF4444')
        self.autotrade_status.pack(pady=(0, 5))
        
        autotrade_toggle = tk.Button(autotrade_frame, text="Enable AutoTrade", font=('Arial', 10), 
                                   bg='#8B5CF6', fg='white', command=self._toggle_autotrade)
        autotrade_toggle.pack(pady=(0, 10))
        
        # Account info
        account_frame = tk.Frame(right_frame, bg='#4B5563', relief=tk.RAISED, bd=2)
        account_frame.pack(fill=tk.X, padx=15, pady=10)
        
        account_title = tk.Label(account_frame, text="💼 Account Info", font=('Arial', 11, 'bold'), 
                               bg='#4B5563', fg='#8B5CF6')
        account_title.pack(pady=(10, 5))
        
        mode_label = tk.Label(account_frame, text="Mode: DEMO", font=('Arial', 10), 
                            bg='#4B5563', fg='#10B981')
        mode_label.pack()
        
        self.account_balance = tk.Label(account_frame, text=f"Balance: ${self.balance:.2f}", font=('Arial', 10), 
                                      bg='#4B5563', fg='white')
        self.account_balance.pack(pady=(0, 10))
        
        # Emergency stop
        emergency_btn = tk.Button(right_frame, text="🚨 EMERGENCY STOP", font=('Arial', 12, 'bold'), 
                                bg='#DC2626', fg='white', relief=tk.RAISED, bd=3,
                                command=self._emergency_stop, width=18, height=3)
        emergency_btn.pack(padx=15, pady=20)
    
    def _create_footer(self, parent):
        """Create footer section"""
        footer_frame = tk.Frame(parent, bg='#374151', relief=tk.RAISED, bd=1)
        footer_frame.pack(fill=tk.X, pady=(15, 0))
        
        # System status
        self.system_status = tk.Label(footer_frame, text="🟢 System: Online", font=('Arial', 10, 'bold'), 
                                    bg='#374151', fg='#10B981')
        self.system_status.pack(side=tk.LEFT, padx=20, pady=10)
        
        # Performance
        self.performance_label = tk.Label(footer_frame, text="⚡ Analysis: 0.2s | 🎯 Accuracy: 87%", 
                                        font=('Arial', 9), bg='#374151', fg='#9CA3AF')
        self.performance_label.pack(side=tk.LEFT, expand=True)
        
        # Time
        self.time_label = tk.Label(footer_frame, text="🕐 12:34:56", font=('Arial', 10, 'bold'), 
                                 bg='#374151', fg='#8B5CF6')
        self.time_label.pack(side=tk.RIGHT, padx=20, pady=10)
    
    def _start_updates(self):
        """Start real-time updates"""
        self._update_time()
        self._update_data()
        
        # Schedule next updates
        self.root.after(1000, self._update_time)  # Update time every second
        self.root.after(3000, self._update_data)  # Update data every 3 seconds
    
    def _update_time(self):
        """Update current time"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.config(text=f"🕐 {current_time}")
        self.root.after(1000, self._update_time)
    
    def _update_data(self):
        """Update analysis data"""
        # Simulate data changes
        self.analysis_data["momentum"] = random.randint(70, 95)
        self.analysis_data["heatmap"] = random.choice(["Weak", "Moderate", "Strong", "Very Strong"])
        self.analysis_data["buyer_seller"] = random.randint(30, 80)
        self.analysis_data["live_signals"] = random.choice(["BUY", "SELL", "HOLD"])
        
        # Update analysis boxes
        for key, box in self.analysis_boxes.items():
            value = self.analysis_data.get(key, "N/A")
            if key in ["buyer_seller", "momentum"]:
                value_text = f"{value}%"
            else:
                value_text = str(value)
            
            box.value_label.config(text=value_text)
        
        # Update connection status randomly
        if random.choice([True, False]):
            self.is_connected = not self.is_connected
            if self.is_connected:
                self.connection_label.config(text="🟢 Connected", bg='#10B981')
                self.system_status.config(text="🟢 System: Online", fg='#10B981')
            else:
                self.connection_label.config(text="🔴 Disconnected", bg='#EF4444')
                self.system_status.config(text="🔴 System: Offline", fg='#EF4444')
        
        # Schedule next update
        self.root.after(3000, self._update_data)
    
    def _place_buy_order(self):
        """Place BUY order"""
        messagebox.showinfo("Order Placed", f"🟢 BUY order placed for {self.current_asset}\nVolume: $10")
        print(f"🟢 BUY order: {self.current_asset}")
    
    def _place_sell_order(self):
        """Place SELL order"""
        messagebox.showinfo("Order Placed", f"🔴 SELL order placed for {self.current_asset}\nVolume: $10")
        print(f"🔴 SELL order: {self.current_asset}")
    
    def _toggle_autotrade(self):
        """Toggle AutoTrade"""
        self.autotrade_enabled = not self.autotrade_enabled
        if self.autotrade_enabled:
            self.autotrade_status.config(text="🟢 ON", fg='#10B981')
            messagebox.showinfo("AutoTrade", "🤖 AutoTrade enabled")
        else:
            self.autotrade_status.config(text="🔴 OFF", fg='#EF4444')
            messagebox.showinfo("AutoTrade", "⏹️ AutoTrade disabled")
    
    def _emergency_stop(self):
        """Emergency stop"""
        result = messagebox.askyesno("Emergency Stop", 
                                   "🚨 Are you sure you want to activate emergency stop?\n\nThis will halt all trading activities.")
        if result:
            messagebox.showwarning("Emergency Stop", "🚨 EMERGENCY STOP ACTIVATED!\nAll trading has been halted.")
            print("🚨 EMERGENCY STOP activated")
    
    def run(self):
        """Run the dashboard"""
        print("🚀 VIP BIG BANG Dashboard Started")
        print("✨ Professional UI with gaming-style design")
        print("🎯 Real-time updates and responsive layout")
        print("📊 Analysis modules and trading controls")
        print("\n" + "="*50)
        print("Dashboard Features:")
        print("• Real-time price updates")
        print("• 8 analysis modules")
        print("• Professional trading controls")
        print("• AutoTrade functionality")
        print("• Emergency stop system")
        print("• Gaming-style visual design")
        print("="*50)
        
        self.root.mainloop()


def main():
    """Main function"""
    dashboard = VIPDashboard()
    dashboard.run()


if __name__ == "__main__":
    main()
