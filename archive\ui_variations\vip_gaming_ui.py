"""
🎮 VIP BIG BANG - Ultimate Gaming UI
بهترین رابط کاربری گیمینگ کارتونی برای تریدینگ
"""

import sys
import math
import random
import time
from datetime import datetime
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class GamingColors:
    """رنگ‌های Gaming"""
    NEON_CYAN = "#00FFFF"
    NEON_MAGENTA = "#FF10F0"
    NEON_GREEN = "#39FF14"
    NEON_YELLOW = "#FFFF00"
    NEON_ORANGE = "#FF6600"
    NEON_RED = "#FF0040"

    DARK_BG = "#0A0A0F"
    PANEL_BG = "#1A1A2E"
    CARD_BG = "#16213E"

    GOLD = "#FFD700"
    SILVER = "#C0C0C0"

class GamingButton(QPushButton):
    """دکمه Gaming با افکت‌های نئونی"""

    def __init__(self, text="", color=GamingColors.NEON_CYAN, parent=None):
        super().__init__(text, parent)
        self.color = color
        self.is_glowing = False
        self.setup_style()
        self.setup_animations()

    def setup_style(self):
        """تنظیم استایل Gaming"""
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 255, 255, 0.2),
                    stop:1 rgba(0, 255, 255, 0.1));
                border: 2px solid {self.color};
                border-radius: 15px;
                color: {self.color};
                font-family: 'Arial Black', Arial, sans-serif;
                font-size: 12px;
                font-weight: bold;
                padding: 10px 20px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            QPushButton:hover {{
                background: {self.color};
                color: #0A0A0F;
                border: 3px solid {self.color};
            }}
            QPushButton:pressed {{
                background: rgba(255, 255, 255, 0.3);
                transform: scale(0.98);
            }}
        """)

    def setup_animations(self):
        """تنظیم انیمیشن‌ها"""
        self.glow_timer = QTimer()
        self.glow_timer.timeout.connect(self.toggle_glow)

    def start_glow(self):
        """شروع افکت گلو"""
        self.glow_timer.start(1000)

    def stop_glow(self):
        """توقف افکت گلو"""
        self.glow_timer.stop()
        self.is_glowing = False
        self.update_glow()

    def toggle_glow(self):
        """تغییر حالت گلو"""
        self.is_glowing = not self.is_glowing
        self.update_glow()

    def update_glow(self):
        """به‌روزرسانی افکت گلو"""
        if self.is_glowing:
            self.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(0, 255, 255, 0.4),
                        stop:1 rgba(0, 255, 255, 0.2));
                    border: 3px solid {self.color};
                    border-radius: 15px;
                    color: {self.color};
                    font-family: 'Arial Black', Arial, sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                    padding: 10px 20px;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }}
            """)
        else:
            self.setup_style()

class GamingCard(QFrame):
    """کارت Gaming با افکت شیشه‌ای"""

    def __init__(self, title="", icon="", color=GamingColors.NEON_CYAN, parent=None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self.color = color

        self.setup_ui()
        self.setup_style()

    def setup_ui(self):
        """تنظیم UI کارت"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)

        # Header
        header_layout = QHBoxLayout()

        # Icon
        if self.icon:
            icon_label = QLabel(self.icon)
            icon_label.setStyleSheet(f"""
                font-size: 24px;
                color: {self.color};
            """)
            header_layout.addWidget(icon_label)

        # Title
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                font-family: 'Arial Black', Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: {self.color};
                text-transform: uppercase;
                letter-spacing: 1px;
            """)
            header_layout.addWidget(title_label)

        header_layout.addStretch()
        layout.addLayout(header_layout)

        # Content area
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        layout.addWidget(self.content_widget)

    def setup_style(self):
        """تنظیم استایل کارت"""
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(26, 26, 46, 0.9),
                    stop:1 rgba(22, 33, 62, 0.9));
                border: 2px solid rgba(0, 255, 255, 0.3);
                border-radius: 20px;
            }}
            QFrame:hover {{
                border: 2px solid rgba(0, 255, 255, 0.6);
            }}
        """)

    def add_content(self, widget):
        """افزودن محتوا به کارت"""
        self.content_layout.addWidget(widget)

class GamingProgressBar(QProgressBar):
    """نوار پیشرفت Gaming"""

    def __init__(self, color=GamingColors.NEON_CYAN, parent=None):
        super().__init__(parent)
        self.color = color
        self.setup_style()

    def setup_style(self):
        """تنظیم استایل نوار پیشرفت"""
        self.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {self.color};
                border-radius: 10px;
                background: rgba(0, 0, 0, 0.5);
                text-align: center;
                color: white;
                font-weight: bold;
                font-size: 11px;
                height: 25px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.color},
                    stop:0.5 rgba(255, 255, 255, 0.8),
                    stop:1 {self.color});
                border-radius: 8px;
            }}
        """)

class GamingHUD(QWidget):
    """HUD Gaming برای نمایش اطلاعات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(400, 300)

        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(50)  # 20 FPS

        self.radar_angle = 0
        self.signals = []

        # Generate random signals
        for _ in range(5):
            self.signals.append({
                "x": random.uniform(50, 350),
                "y": random.uniform(50, 250),
                "type": random.choice(["CALL", "PUT"]),
                "strength": random.uniform(0.5, 1.0)
            })

    def update_animation(self):
        """به‌روزرسانی انیمیشن"""
        self.radar_angle += 2
        if self.radar_angle >= 360:
            self.radar_angle = 0

        # Update signals
        for signal in self.signals:
            signal["x"] += random.uniform(-1, 1)
            signal["y"] += random.uniform(-1, 1)

            # Keep in bounds
            signal["x"] = max(50, min(350, signal["x"]))
            signal["y"] = max(50, min(250, signal["y"]))

        self.update()

    def paintEvent(self, event):
        """رسم HUD"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Dark background
        painter.fillRect(self.rect(), QColor(10, 10, 15, 200))

        # Draw radar
        self.draw_radar(painter)

        # Draw signals
        self.draw_signals(painter)

        # Draw stats
        self.draw_stats(painter)

    def draw_radar(self, painter):
        """رسم رادار"""
        center_x, center_y = 100, 100
        radius = 70

        # Radar circles
        painter.setPen(QPen(QColor(0, 255, 255, 100), 1))
        for r in range(20, radius + 1, 20):
            painter.drawEllipse(center_x - r, center_y - r, r * 2, r * 2)

        # Radar lines
        painter.setPen(QPen(QColor(0, 255, 255, 80), 1))
        for angle in range(0, 360, 45):
            end_x = center_x + radius * math.cos(math.radians(angle))
            end_y = center_y + radius * math.sin(math.radians(angle))
            painter.drawLine(center_x, center_y, end_x, end_y)

        # Radar sweep
        sweep_x = center_x + radius * math.cos(math.radians(self.radar_angle))
        sweep_y = center_y + radius * math.sin(math.radians(self.radar_angle))

        painter.setPen(QPen(QColor(0, 255, 0), 3))
        painter.drawLine(center_x, center_y, sweep_x, sweep_y)

    def draw_signals(self, painter):
        """رسم سیگنال‌ها"""
        for signal in self.signals:
            if signal["type"] == "CALL":
                color = QColor(0, 255, 0)  # Green
            else:
                color = QColor(255, 0, 0)  # Red

            # Signal dot
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color, 2))
            size = int(signal["strength"] * 8)
            painter.drawEllipse(signal["x"] - size//2, signal["y"] - size//2, size, size)

            # Signal pulse
            pulse_size = size + int(math.sin(time.time() * 5) * 5)
            painter.setPen(QPen(color, 1))
            painter.setBrush(QBrush(Qt.BrushStyle.NoBrush))
            painter.drawEllipse(signal["x"] - pulse_size//2, signal["y"] - pulse_size//2, pulse_size, pulse_size)

    def draw_stats(self, painter):
        """رسم آمار"""
        # Health bar
        painter.setPen(QPen(QColor(0, 255, 0), 2))
        painter.drawRect(220, 50, 150, 15)
        health_width = int(150 * 0.85)
        painter.fillRect(222, 52, health_width, 11, QColor(0, 255, 0, 150))

        painter.setPen(QPen(QColor(255, 255, 255), 1))
        painter.setFont(QFont("Arial", 8))
        painter.drawText(225, 45, "PROFIT: 85%")

        # Mana bar
        painter.setPen(QPen(QColor(0, 128, 255), 2))
        painter.drawRect(220, 80, 150, 15)
        mana_width = int(150 * 0.67)
        painter.fillRect(222, 82, mana_width, 11, QColor(0, 128, 255, 150))

        painter.drawText(225, 75, "BALANCE: 67%")

        # Energy bar
        painter.setPen(QPen(QColor(255, 255, 0), 2))
        painter.drawRect(220, 110, 150, 15)
        energy_width = int(150 * 0.92)
        painter.fillRect(222, 112, energy_width, 11, QColor(255, 255, 0, 150))

        painter.drawText(225, 105, "ENERGY: 92%")

class VIPGamingMainWindow(QMainWindow):
    """پنجره اصلی Gaming VIP BIG BANG"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎮 VIP BIG BANG - Ultimate Gaming Trader")
        self.setGeometry(50, 50, 1600, 1000)

        # Gaming data
        self.balance = 3250.89
        self.winrate = 94.7
        self.trades = 127
        self.current_signal = "CALL"
        self.confidence = 87.3

        self.setup_gaming_ui()
        self.setup_gaming_style()
        self.start_gaming_updates()

    def setup_gaming_ui(self):
        """تنظیم UI Gaming"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Gaming Header
        self.create_gaming_header(main_layout)

        # Gaming Content
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)

        # Left Panel
        self.create_left_panel(content_layout)

        # Center Area
        self.create_center_area(content_layout)

        # Right Panel
        self.create_right_panel(content_layout)

        main_layout.addLayout(content_layout)

        # Gaming Footer
        self.create_gaming_footer(main_layout)

    def create_gaming_header(self, layout):
        """ایجاد هدر Gaming"""
        header = QFrame()
        header.setFixedHeight(100)
        header.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 255, 255, 0.2),
                    stop:0.5 rgba(255, 16, 240, 0.2),
                    stop:1 rgba(0, 255, 255, 0.2));
                border-bottom: 3px solid {GamingColors.NEON_CYAN};
            }}
        """)

        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(30, 20, 30, 20)

        # Gaming Logo
        logo_layout = QHBoxLayout()

        logo = QLabel("🚀")
        logo.setStyleSheet(f"""
            font-size: 48px;
            color: {GamingColors.NEON_CYAN};
        """)
        logo_layout.addWidget(logo)

        # Gaming Title
        title_layout = QVBoxLayout()
        title_layout.setSpacing(0)

        title = QLabel("VIP BIG BANG")
        title.setStyleSheet(f"""
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 32px;
            font-weight: 900;
            color: {GamingColors.NEON_CYAN};
            text-transform: uppercase;
            letter-spacing: 3px;
        """)
        title_layout.addWidget(title)

        subtitle = QLabel("ULTIMATE GAMING TRADER")
        subtitle.setStyleSheet(f"""
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            color: {GamingColors.NEON_MAGENTA};
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
        """)
        title_layout.addWidget(subtitle)

        logo_layout.addLayout(title_layout)
        header_layout.addLayout(logo_layout)

        header_layout.addStretch()

        # Gaming Status HUD
        self.create_status_hud(header_layout)

        layout.addWidget(header)

    def create_status_hud(self, layout):
        """ایجاد HUD وضعیت"""
        hud_layout = QHBoxLayout()

        # Status indicators
        status_items = [
            ("🟢", "ONLINE", GamingColors.NEON_GREEN),
            ("🤖", "AUTO", GamingColors.NEON_CYAN),
            ("⚡", "FAST", GamingColors.NEON_YELLOW),
            ("🛡️", "SECURE", GamingColors.NEON_MAGENTA)
        ]

        for icon, text, color in status_items:
            status_widget = QWidget()
            status_layout = QVBoxLayout(status_widget)
            status_layout.setContentsMargins(10, 5, 10, 5)
            status_layout.setSpacing(2)

            icon_label = QLabel(icon)
            icon_label.setStyleSheet(f"""
                font-size: 20px;
                color: {color};
            """)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            status_layout.addWidget(icon_label)

            text_label = QLabel(text)
            text_label.setStyleSheet(f"""
                font-family: 'Arial', monospace;
                font-size: 10px;
                color: {color};
                font-weight: bold;
                text-align: center;
            """)
            text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            status_layout.addWidget(text_label)

            hud_layout.addWidget(status_widget)

        # Live clock
        self.time_label = QLabel()
        self.time_label.setStyleSheet(f"""
            font-family: 'Arial', monospace;
            font-size: 16px;
            color: {GamingColors.NEON_CYAN};
            font-weight: bold;
        """)
        hud_layout.addWidget(self.time_label)

        layout.addLayout(hud_layout)

    def create_left_panel(self, layout):
        """پنل چپ Gaming"""
        panel = QWidget()
        panel.setFixedWidth(320)

        panel_layout = QVBoxLayout(panel)
        panel_layout.setSpacing(15)

        # Trading Mode Card
        mode_card = GamingCard("TRADING MODE", "🎮", GamingColors.NEON_CYAN)

        mode_content = QWidget()
        mode_layout = QVBoxLayout(mode_content)

        modes = [
            ("🔥 ULTRA AGGRESSIVE", GamingColors.NEON_RED),
            ("🛡️ HYPER CONSERVATIVE", GamingColors.NEON_GREEN),
            ("⚡ QUANTUM SCALPING", GamingColors.NEON_MAGENTA),
            ("🧠 NEURAL ADAPTIVE", GamingColors.NEON_ORANGE)
        ]

        for mode_text, color in modes:
            btn = GamingButton(mode_text, color)
            btn.clicked.connect(lambda checked, m=mode_text: self.set_trading_mode(m))
            mode_layout.addWidget(btn)

        mode_card.add_content(mode_content)
        panel_layout.addWidget(mode_card)

        # AutoTrade Card
        auto_card = GamingCard("AUTOTRADE", "🤖", GamingColors.NEON_GREEN)

        auto_content = QWidget()
        auto_layout = QVBoxLayout(auto_content)

        # AutoTrade status
        auto_status = QLabel("🟢 ACTIVE")
        auto_status.setStyleSheet(f"""
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 16px;
            color: {GamingColors.NEON_GREEN};
            font-weight: bold;
            text-align: center;
        """)
        auto_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        auto_layout.addWidget(auto_status)

        # Control buttons
        control_layout = QHBoxLayout()

        self.start_btn = GamingButton("START", GamingColors.NEON_GREEN)
        self.start_btn.clicked.connect(self.start_autotrade)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = GamingButton("STOP", GamingColors.NEON_RED)
        self.stop_btn.clicked.connect(self.stop_autotrade)
        control_layout.addWidget(self.stop_btn)

        auto_layout.addLayout(control_layout)

        auto_card.add_content(auto_content)
        panel_layout.addWidget(auto_card)

        # Performance Card
        perf_card = GamingCard("PERFORMANCE", "🏆", GamingColors.GOLD)

        perf_content = QWidget()
        perf_layout = QVBoxLayout(perf_content)

        # Win rate
        winrate_label = QLabel(f"WIN RATE: {self.winrate}%")
        winrate_label.setStyleSheet(f"""
            font-family: 'Arial', Arial, sans-serif;
            font-size: 12px;
            color: {GamingColors.GOLD};
            font-weight: bold;
        """)
        perf_layout.addWidget(winrate_label)

        self.winrate_bar = GamingProgressBar(GamingColors.GOLD)
        self.winrate_bar.setValue(int(self.winrate))
        perf_layout.addWidget(self.winrate_bar)

        # Balance
        balance_label = QLabel(f"BALANCE: ${self.balance:,.2f}")
        balance_label.setStyleSheet(f"""
            font-family: 'Arial', monospace;
            font-size: 14px;
            color: {GamingColors.NEON_CYAN};
            font-weight: bold;
        """)
        perf_layout.addWidget(balance_label)

        perf_card.add_content(perf_content)
        panel_layout.addWidget(perf_card)

        panel_layout.addStretch()
        layout.addWidget(panel)

    def create_center_area(self, layout):
        """منطقه مرکزی Gaming"""
        center_widget = QWidget()
        center_layout = QVBoxLayout(center_widget)
        center_layout.setSpacing(20)

        # Gaming Chart Area
        chart_card = GamingCard("BATTLE CHART", "📈", GamingColors.NEON_CYAN)
        chart_card.setMinimumHeight(400)

        # Gaming HUD
        self.gaming_hud = GamingHUD()
        chart_card.add_content(self.gaming_hud)

        center_layout.addWidget(chart_card)

        # Signal Analysis
        signal_card = GamingCard("COMBAT SIGNAL", "🎯", GamingColors.NEON_MAGENTA)
        signal_card.setFixedHeight(200)

        signal_content = QWidget()
        signal_layout = QHBoxLayout(signal_content)

        # Direction display
        self.direction_widget = QWidget()
        direction_layout = QVBoxLayout(self.direction_widget)

        direction_label = QLabel("DIRECTION")
        direction_label.setStyleSheet(f"""
            font-family: 'Arial', Arial, sans-serif;
            font-size: 12px;
            color: {GamingColors.NEON_CYAN};
            text-align: center;
        """)
        direction_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        direction_layout.addWidget(direction_label)

        self.direction_display = QLabel(self.current_signal)
        self.direction_display.setStyleSheet(f"""
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 24px;
            font-weight: bold;
            color: {GamingColors.NEON_GREEN if self.current_signal == 'CALL' else GamingColors.NEON_RED};
            text-align: center;
            background: rgba(57, 255, 20, 0.2);
            border: 2px solid {GamingColors.NEON_GREEN if self.current_signal == 'CALL' else GamingColors.NEON_RED};
            border-radius: 15px;
            padding: 10px;
        """)
        self.direction_display.setAlignment(Qt.AlignmentFlag.AlignCenter)
        direction_layout.addWidget(self.direction_display)

        signal_layout.addWidget(self.direction_widget)

        # Confidence display
        confidence_widget = QWidget()
        confidence_layout = QVBoxLayout(confidence_widget)

        conf_label = QLabel("CONFIDENCE")
        conf_label.setStyleSheet(f"""
            font-family: 'Arial', Arial, sans-serif;
            font-size: 12px;
            color: {GamingColors.NEON_CYAN};
            text-align: center;
        """)
        conf_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        confidence_layout.addWidget(conf_label)

        self.confidence_bar = GamingProgressBar(GamingColors.NEON_CYAN)
        self.confidence_bar.setValue(int(self.confidence))
        confidence_layout.addWidget(self.confidence_bar)

        conf_value = QLabel(f"{self.confidence}%")
        conf_value.setStyleSheet(f"""
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: {GamingColors.NEON_CYAN};
            text-align: center;
        """)
        conf_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        confidence_layout.addWidget(conf_value)

        signal_layout.addWidget(confidence_widget)

        # Action buttons
        action_widget = QWidget()
        action_layout = QVBoxLayout(action_widget)

        execute_btn = GamingButton("🚀 EXECUTE", GamingColors.NEON_GREEN)
        execute_btn.clicked.connect(self.execute_signal)
        execute_btn.start_glow()
        action_layout.addWidget(execute_btn)

        reject_btn = GamingButton("❌ REJECT", GamingColors.NEON_RED)
        reject_btn.clicked.connect(self.reject_signal)
        action_layout.addWidget(reject_btn)

        signal_layout.addWidget(action_widget)

        signal_card.add_content(signal_content)
        center_layout.addWidget(signal_card)

        layout.addWidget(center_widget, 2)

    def create_right_panel(self, layout):
        """پنل راست Gaming"""
        panel = QWidget()
        panel.setFixedWidth(320)

        panel_layout = QVBoxLayout(panel)
        panel_layout.setSpacing(15)

        # Battle Stats
        stats_card = GamingCard("BATTLE STATS", "⚔️", GamingColors.NEON_ORANGE)

        stats_content = QWidget()
        stats_layout = QVBoxLayout(stats_content)

        battle_stats = [
            ("KILLS", "120", GamingColors.NEON_GREEN),
            ("DEATHS", "7", GamingColors.NEON_RED),
            ("K/D RATIO", "17.1", GamingColors.GOLD),
            ("STREAK", "23", GamingColors.NEON_MAGENTA)
        ]

        for stat_name, value, color in battle_stats:
            stat_layout = QHBoxLayout()

            name_label = QLabel(stat_name)
            name_label.setStyleSheet(f"""
                font-family: 'Arial', monospace;
                font-size: 11px;
                color: {color};
                font-weight: bold;
            """)
            stat_layout.addWidget(name_label)

            stat_layout.addStretch()

            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                font-family: 'Arial Black', Arial, sans-serif;
                font-size: 14px;
                color: {color};
                font-weight: bold;
            """)
            stat_layout.addWidget(value_label)

            stats_layout.addLayout(stat_layout)

        stats_card.add_content(stats_content)
        panel_layout.addWidget(stats_card)

        # Equipment Card
        equipment_card = GamingCard("EQUIPMENT", "🎒", GamingColors.NEON_MAGENTA)

        equipment_content = QWidget()
        equipment_layout = QVBoxLayout(equipment_content)

        equipment_items = [
            "🗡️ Neural Sword (Lv.10)",
            "🛡️ Quantum Shield (Lv.8)",
            "⚡ Speed Boost (3x)",
            "💎 Profit Gem (Active)"
        ]

        for item in equipment_items:
            item_label = QLabel(item)
            item_label.setStyleSheet(f"""
                font-family: 'Arial', Arial, sans-serif;
                font-size: 11px;
                color: {GamingColors.NEON_MAGENTA};
                font-weight: bold;
                padding: 5px;
                background: rgba(144, 19, 254, 0.1);
                border: 1px solid rgba(144, 19, 254, 0.3);
                border-radius: 8px;
                margin: 2px;
            """)
            equipment_layout.addWidget(item_label)

        equipment_card.add_content(equipment_content)
        panel_layout.addWidget(equipment_card)

        # Recent Trades
        trades_card = GamingCard("RECENT BATTLES", "📈", GamingColors.NEON_YELLOW)

        trades_content = QWidget()
        trades_layout = QVBoxLayout(trades_content)

        recent_trades = [
            ("14:30 CALL 60s", "✅ +$42.50", GamingColors.NEON_GREEN),
            ("14:28 PUT 30s", "✅ +$25.50", GamingColors.NEON_GREEN),
            ("14:26 CALL 15s", "✅ +$12.75", GamingColors.NEON_GREEN),
            ("14:24 PUT 60s", "❌ -$50.00", GamingColors.NEON_RED),
            ("14:22 CALL 30s", "✅ +$25.50", GamingColors.NEON_GREEN)
        ]

        for trade_time, result, color in recent_trades:
            trade_layout = QHBoxLayout()

            time_label = QLabel(trade_time)
            time_label.setStyleSheet(f"""
                font-family: 'Arial', monospace;
                font-size: 10px;
                color: {GamingColors.NEON_CYAN};
                font-weight: bold;
            """)
            trade_layout.addWidget(time_label)

            trade_layout.addStretch()

            result_label = QLabel(result)
            result_label.setStyleSheet(f"""
                font-family: 'Arial', Arial, sans-serif;
                font-size: 10px;
                color: {color};
                font-weight: bold;
            """)
            trade_layout.addWidget(result_label)

            trades_layout.addLayout(trade_layout)

        trades_card.add_content(trades_content)
        panel_layout.addWidget(trades_card)

        panel_layout.addStretch()
        layout.addWidget(panel)

    def create_gaming_footer(self, layout):
        """ایجاد فوتر Gaming"""
        footer = QFrame()
        footer.setFixedHeight(80)
        footer.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 255, 255, 0.1),
                    stop:1 rgba(255, 16, 240, 0.1));
                border-top: 2px solid {GamingColors.NEON_CYAN};
            }}
        """)

        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(30, 15, 30, 15)

        # System status
        status_items = [
            ("🟢 ONLINE", GamingColors.NEON_GREEN),
            ("⚡ FAST MODE", GamingColors.NEON_YELLOW),
            ("🛡️ SECURED", GamingColors.NEON_MAGENTA),
            ("📊 OPTIMAL", GamingColors.NEON_CYAN),
            ("🎯 READY", GamingColors.NEON_ORANGE)
        ]

        for status_text, color in status_items:
            status_label = QLabel(status_text)
            status_label.setStyleSheet(f"""
                font-family: 'Arial', Arial, sans-serif;
                font-size: 12px;
                color: {color};
                font-weight: bold;
                text-transform: uppercase;
            """)
            footer_layout.addWidget(status_label)

            if status_text != status_items[-1][0]:  # Not last item
                footer_layout.addWidget(QLabel("|"))

        layout.addWidget(footer)

    def setup_gaming_style(self):
        """تنظیم استایل Gaming"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {GamingColors.DARK_BG},
                    stop:0.3 {GamingColors.PANEL_BG},
                    stop:0.7 {GamingColors.CARD_BG},
                    stop:1 {GamingColors.DARK_BG});
                color: {GamingColors.NEON_CYAN};
                font-family: 'Arial', sans-serif;
            }}
            QLabel {{
                color: white;
            }}
        """)

    def start_gaming_updates(self):
        """شروع به‌روزرسانی‌های Gaming"""
        # Time update timer
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

        # Data update timer
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.update_gaming_data)
        self.data_timer.start(3000)  # Every 3 seconds

        self.update_time()

    def update_time(self):
        """به‌روزرسانی زمان"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"⏰ {current_time}")

    def update_gaming_data(self):
        """به‌روزرسانی داده‌های Gaming"""
        # Simulate new signal
        self.current_signal = random.choice(["CALL", "PUT"])
        self.confidence = random.uniform(75, 95)

        # Update UI
        color = GamingColors.NEON_GREEN if self.current_signal == 'CALL' else GamingColors.NEON_RED
        self.direction_display.setText(self.current_signal)
        self.direction_display.setStyleSheet(f"""
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 24px;
            font-weight: bold;
            color: {color};
            text-align: center;
            background: rgba(57, 255, 20, 0.2);
            border: 2px solid {color};
            border-radius: 15px;
            padding: 10px;
        """)

        self.confidence_bar.setValue(int(self.confidence))

    def set_trading_mode(self, mode):
        """تنظیم حالت تریدینگ"""
        QMessageBox.information(self, "🎮 Gaming Mode",
            f"🎯 {mode} ACTIVATED! 🎯\n\n"
            "🔥 SYSTEM RECONFIGURING...\n"
            "⚡ NEURAL NETWORKS ADAPTING...\n"
            "🌌 QUANTUM PROCESSORS REALIGNING...\n"
            "🛡️ SECURITY PROTOCOLS UPDATING...")

    def start_autotrade(self):
        """شروع AutoTrade"""
        self.start_btn.start_glow()
        QMessageBox.information(self, "🤖 AutoTrade", "🟢 AUTOTRADE ACTIVATED!\n\n🚀 READY FOR BATTLE!")

    def stop_autotrade(self):
        """توقف AutoTrade"""
        self.start_btn.stop_glow()
        QMessageBox.information(self, "🤖 AutoTrade", "🔴 AUTOTRADE STOPPED!\n\n⏸️ BATTLE PAUSED!")

    def execute_signal(self):
        """اجرای سیگنال"""
        QMessageBox.information(self, "🎯 Signal Executed",
            f"🚀 {self.current_signal} SIGNAL EXECUTED!\n\n"
            f"🎯 Confidence: {self.confidence:.1f}%\n"
            f"💰 Expected Profit: $42.50\n"
            f"⏰ Duration: 60 seconds")

    def reject_signal(self):
        """رد سیگنال"""
        QMessageBox.information(self, "❌ Signal Rejected", "❌ SIGNAL REJECTED!\n\n🔍 SCANNING FOR BETTER OPPORTUNITY...")

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set application style
    app.setStyle('Fusion')

    # Create and show gaming window
    window = VIPGamingMainWindow()
    window.show()

    sys.exit(app.exec())