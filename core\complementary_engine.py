"""
VIP BIG BANG Enterprise - Complementary Analysis Engine
Manager for all complementary analyses and trade filters
"""

import pandas as pd  # type: ignore
from typing import Dict, Optional, Any
import logging
from datetime import datetime

# Import our type definitions
# from .types import (...)

from .heatmap_pulsebar import HeatmapPulseBarAnalyzer
from .economic_news_filter import Economic<PERSON>ewsFilter
from .otc_mode_detector import OTCModeDetector
from .live_signal_scanner import LiveSignalScanner
from .confirm_mode import ConfirmMode
from .brothers_can_pattern import BrothersCanPattern
from .complementary_analyzers import (
    ActiveAnalysesPanel, AutoTradeConditionsCheck, 
    AccountSafety, ManualConfirm
)

class ComplementaryEngine:
    """
    Complementary Analysis Engine - VIP BIG BANG
    Manages all 10 complementary analyses for trade filtering and confirmation
    """
    
    def __init__(self, settings: Any):
        self.settings = settings
        self.logger = logging.getLogger("ComplementaryEngine")
        
        # Initialize all complementary analyzers
        self.analyzers = {
            'heatmap_pulsebar': HeatmapPulseBarAnalyzer(settings),
            'economic_news_filter': EconomicNewsFilter(settings),
            'otc_mode_detector': OTCModeDetector(settings),
            'live_signal_scanner': LiveSignalScanner(settings),
            'confirm_mode': ConfirmMode(settings),
            'brothers_can_pattern': BrothersCanPattern(settings),
            'active_analyses_panel': ActiveAnalysesPanel(settings),
            'autotrade_conditions_check': AutoTradeConditionsCheck(settings),
            'account_safety': AccountSafety(settings),
            'manual_confirm': ManualConfirm(settings)
        }
        
        # Filter weights for final decision
        self.filter_weights = {
            'economic_news_filter': 0.20,  # High weight for news
            'account_safety': 0.20,        # High weight for safety
            'otc_mode_detector': 0.15,     # Medium weight for OTC
            'confirm_mode': 0.15,          # Medium weight for confirmation
            'autotrade_conditions_check': 0.10,  # Medium weight for conditions
            'live_signal_scanner': 0.10,   # Medium weight for scanner
            'manual_confirm': 0.05,        # Low weight for manual
            'heatmap_pulsebar': 0.03,      # Low weight for visual
            'brothers_can_pattern': 0.01,  # Very low weight for pattern
            'active_analyses_panel': 0.01  # Very low weight for panel
        }
        
        self.logger.info("Complementary Analysis Engine initialized with 10 filters")
    
    def run_all_complementary_analyses(self, data: pd.DataFrame,
                                     primary_analysis_results: Dict[str, Any],
                                     account_data: Optional[Dict[str, Any]] = None,
                                     market_conditions: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run all complementary analyses"""
        
        complementary_results = {}
        
        try:
            # 1. Heatmap & PulseBar
            complementary_results['heatmap_pulsebar'] = self.analyzers['heatmap_pulsebar'].analyze(data)
            
            # 2. Economic News Filter
            complementary_results['economic_news_filter'] = self.analyzers['economic_news_filter'].analyze(data)
            
            # 3. OTC Mode Detector
            complementary_results['otc_mode_detector'] = self.analyzers['otc_mode_detector'].analyze(data)
            
            # 4. Live Signal Scanner (needs primary results)
            complementary_results['live_signal_scanner'] = self.analyzers['live_signal_scanner'].analyze(primary_analysis_results)
            
            # 5. Brothers Can Pattern
            complementary_results['brothers_can_pattern'] = self.analyzers['brothers_can_pattern'].analyze(data)
            
            # 6. Active Analyses Panel (needs primary results)
            complementary_results['active_analyses_panel'] = self.analyzers['active_analyses_panel'].analyze(primary_analysis_results)
            
            # 7. AutoTrade Conditions Check
            performance_data = account_data.get('performance', {}) if account_data else {}
            complementary_results['autotrade_conditions_check'] = self.analyzers['autotrade_conditions_check'].analyze(
                account_data, performance_data
            )
            
            # 8. Account Safety
            complementary_results['account_safety'] = self.analyzers['account_safety'].analyze(account_data)
            
            # 9. Manual Confirm
            complementary_results['manual_confirm'] = self.analyzers['manual_confirm'].analyze(
                primary_analysis_results, market_conditions
            )
            
            # 10. Confirm Mode (needs all results)
            all_results = {**primary_analysis_results, **complementary_results}
            signal_data = complementary_results.get('live_signal_scanner', {})
            complementary_results['confirm_mode'] = self.analyzers['confirm_mode'].analyze(
                all_results, signal_data, account_data
            )
            
        except Exception as e:
            self.logger.error(f"Error running complementary analyses: {e}")
        
        return complementary_results
    
    def calculate_final_trading_decision(self, primary_results: Dict[str, Any],
                                       complementary_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate final trading decision based on all analyses"""
        
        # Collect all blocking factors
        blocking_factors = []
        warning_factors = []
        
        # Check each filter
        for filter_name, result in complementary_results.items():
            if isinstance(result, dict):
                # Economic News Filter
                if filter_name == 'economic_news_filter':
                    if not result.get('allow_trading', True):
                        blocking_factors.append(f"News: {result.get('reason', 'Unknown')}")
                
                # Account Safety
                elif filter_name == 'account_safety':
                    if not result.get('allow_trading', True):
                        blocking_factors.append(f"Safety: {result.get('safety_level', 'Unknown')}")
                
                # AutoTrade Conditions
                elif filter_name == 'autotrade_conditions_check':
                    if not result.get('allow_autotrade', True):
                        blocking_factors.append("AutoTrade conditions not met")
                
                # Confirm Mode
                elif filter_name == 'confirm_mode':
                    if not result.get('allow_trading', True):
                        blocking_factors.append("Confirmation required")
                    elif result.get('requires_confirmation', False):
                        warning_factors.append("Manual confirmation recommended")
                
                # Manual Confirm
                elif filter_name == 'manual_confirm':
                    if result.get('requires_manual', False):
                        warning_factors.append("Manual confirmation required")
                
                # OTC Mode
                elif filter_name == 'otc_mode_detector':
                    if result.get('is_otc_mode', False):
                        warning_factors.append("OTC mode detected - higher risk")
        
        # Calculate weighted filter score
        filter_score = 0.0
        total_weight = 0.0
        
        for filter_name, weight in self.filter_weights.items():
            if filter_name in complementary_results:
                result = complementary_results[filter_name]
                if isinstance(result, dict) and 'score' in result:
                    filter_score += result['score'] * weight
                    total_weight += weight
        
        if total_weight > 0:
            filter_score = filter_score / total_weight
        
        # Calculate primary analysis score
        primary_scores = []
        for result in primary_results.values():
            if isinstance(result, dict) and 'score' in result:
                primary_scores.append(result['score'])
        
        primary_score = sum(primary_scores) / len(primary_scores) if primary_scores else 0.5
        
        # Ultra strict scoring for 95% win rate (80% primary, 20% filters)
        final_score = (primary_score * 0.8) + (filter_score * 0.2)

        # Additional ultra-strict validation
        if final_score < 0.95:
            blocking_factors.append("Score below ultra-strict threshold (95%)")

        # Check alignment of primary indicators
        primary_directions = []
        for result in primary_results.values():
            if isinstance(result, dict) and 'direction' in result:
                primary_directions.append(result['direction'])

        if primary_directions:
            main_direction = max(set(primary_directions), key=primary_directions.count)
            alignment = primary_directions.count(main_direction) / len(primary_directions)

            if alignment < 0.95:  # 95% alignment required
                blocking_factors.append(f"Alignment {alignment:.1%} below 95% requirement")
        
        # Determine final decision
        if blocking_factors:
            final_decision = 'BLOCKED'
            allow_trading = False
            confidence = 0.0
        elif len(warning_factors) >= 3:
            final_decision = 'HIGH_RISK'
            allow_trading = False
            confidence = 0.3
        elif warning_factors:
            final_decision = 'CAUTION'
            allow_trading = True
            confidence = 0.6
        else:
            final_decision = 'ALLOWED'
            allow_trading = True
            confidence = filter_score
        
        # Get signal direction from live scanner
        signal_scanner = complementary_results.get('live_signal_scanner', {})
        direction = signal_scanner.get('direction', 'NEUTRAL')
        
        return {
            'final_score': final_score,
            'primary_score': primary_score,
            'filter_score': filter_score,
            'direction': direction,
            'confidence': confidence,
            'final_decision': final_decision,
            'allow_trading': allow_trading,
            'blocking_factors': blocking_factors,
            'warning_factors': warning_factors,
            'signal_quality': signal_scanner.get('quality_check', {}),
            'details': f"Decision: {final_decision} - Score: {final_score:.2f}"
        }
    
    def get_trading_summary(self, primary_results: Dict[str, Any],
                          complementary_results: Dict[str, Any],
                          final_decision: Dict[str, Any]) -> Dict[str, Any]:
        """Get comprehensive trading summary"""
        
        # Count active primary analyses
        active_primary = sum(1 for r in primary_results.values() 
                           if isinstance(r, dict) and r.get('direction') != 'NEUTRAL')
        
        # Get key metrics
        signal_scanner = complementary_results.get('live_signal_scanner', {})
        alignment = signal_scanner.get('alignment_data', {})
        
        # Get visual data
        heatmap = complementary_results.get('heatmap_pulsebar', {})
        visual_box = heatmap.get('visual_box', {})
        
        return {
            'timestamp': datetime.now(),
            'primary_analyses': {
                'total': len(primary_results),
                'active': active_primary,
                'alignment_percentage': alignment.get('alignment_percentage', 0) * 100
            },
            'complementary_filters': {
                'total': len(complementary_results),
                'blocking': len(final_decision['blocking_factors']),
                'warnings': len(final_decision['warning_factors'])
            },
            'final_recommendation': {
                'decision': final_decision['final_decision'],
                'direction': final_decision['direction'],
                'confidence': final_decision['confidence'],
                'score': final_decision['final_score']
            },
            'visual_indicators': {
                'heatmap_color': visual_box.get('primary_color', 'GRAY'),
                'pulse_intensity': visual_box.get('combined_intensity', 0),
                'signal_box_text': signal_scanner.get('signal_box', {}).get('display_text', 'No Signal')
            },
            'risk_assessment': {
                'risk_level': 'HIGH' if final_decision['blocking_factors'] else 'MEDIUM' if final_decision['warning_factors'] else 'LOW',
                'otc_mode': complementary_results.get('otc_mode_detector', {}).get('is_otc_mode', False),
                'news_risk': complementary_results.get('economic_news_filter', {}).get('risk_level', 'LOW')
            }
        }
