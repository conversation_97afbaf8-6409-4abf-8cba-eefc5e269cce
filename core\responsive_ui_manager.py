"""
🖥️ RESPONSIVE UI MANAGER
📐 HANDLES DIFFERENT SCREEN SIZES AND RESOLUTIONS
🎛️ PROVIDES UI SCALING AND LAYOUT MANAGEMENT
"""

import logging
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QSettings, QSize
from PySide6.QtGui import QScreen

class ResponsiveUIManager:
    """
    🖥️ RESPONSIVE UI MANAGER
    📐 Manages UI scaling and responsive design
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ResponsiveUIManager")

        # Settings for persistent UI preferences
        self.settings = QSettings("VIP_BIG_BANG", "UISettings")

        # Get ALL screen information
        self.app = QApplication.instance()
        self.primary_screen = self.app.primaryScreen()
        self.all_screens = self.app.screens()

        # Primary screen details
        self.screen_geometry = self.primary_screen.geometry()
        self.screen_size = self.screen_geometry.size()
        self.available_geometry = self.primary_screen.availableGeometry()
        self.available_size = self.available_geometry.size()

        # DPI and scaling information
        self.device_pixel_ratio = self.primary_screen.devicePixelRatio()
        self.logical_dpi = self.primary_screen.logicalDotsPerInch()
        self.physical_dpi = self.primary_screen.physicalDotsPerInch()

        # Enhanced UI Scale factors based on DPI
        self.scale_factors = {
            "tiny": 0.6,
            "small": 0.8,
            "normal": 1.0,
            "large": 1.25,
            "xlarge": 1.5,
            "xxlarge": 1.75,
            "huge": 2.0
        }

        # Dynamic base window size based on screen
        self.calculate_optimal_window_size()

        self.logger.info("🖥️ Advanced Responsive UI Manager initialized")
        self.logger.info(f"📺 Screen: {self.screen_size.width()}x{self.screen_size.height()}")
        self.logger.info(f"📐 Available: {self.available_size.width()}x{self.available_size.height()}")
        self.logger.info(f"🔍 DPI: Logical={self.logical_dpi:.1f}, Physical={self.physical_dpi:.1f}")
        self.logger.info(f"📊 Device Pixel Ratio: {self.device_pixel_ratio}")
        self.logger.info(f"🖥️ Screens detected: {len(self.all_screens)}")

    def calculate_optimal_window_size(self):
        """📐 Calculate optimal window size based on screen"""
        # Use 80% of available screen space as base
        optimal_width = int(self.available_size.width() * 0.8)
        optimal_height = int(self.available_size.height() * 0.8)

        # Ensure minimum size
        optimal_width = max(optimal_width, 1000)
        optimal_height = max(optimal_height, 700)

        # Ensure maximum size for very large screens
        optimal_width = min(optimal_width, 1600)
        optimal_height = min(optimal_height, 1200)

        self.base_window_size = QSize(optimal_width, optimal_height)
        self.logger.info(f"📐 Optimal window size calculated: {optimal_width}x{optimal_height}")
    
    def detect_screen_type(self):
        """📺 Advanced screen detection with DPI awareness"""
        width = self.screen_size.width()
        height = self.screen_size.height()
        dpi = self.logical_dpi
        pixel_ratio = self.device_pixel_ratio

        # Calculate effective resolution (considering DPI scaling)
        effective_width = width * pixel_ratio
        effective_height = height * pixel_ratio

        self.logger.info(f"📊 Screen Analysis:")
        self.logger.info(f"   Physical: {width}x{height}")
        self.logger.info(f"   Effective: {effective_width:.0f}x{effective_height:.0f}")
        self.logger.info(f"   DPI: {dpi:.1f}, Ratio: {pixel_ratio}")

        # Enhanced detection with DPI consideration
        if effective_width >= 3840 and effective_height >= 2160:  # 4K+
            if dpi >= 150:  # High DPI 4K
                return "4k_hidpi", "xlarge"
            else:  # Standard DPI 4K
                return "4k", "large"
        elif effective_width >= 2560 and effective_height >= 1440:  # 2K/QHD
            if dpi >= 120:  # High DPI 2K
                return "2k_hidpi", "large"
            else:  # Standard DPI 2K
                return "2k", "normal"
        elif effective_width >= 1920 and effective_height >= 1080:  # Full HD
            if dpi >= 120:  # High DPI Full HD
                return "fullhd_hidpi", "normal"
            else:  # Standard DPI Full HD
                return "fullhd", "normal"
        elif effective_width >= 1366 and effective_height >= 768:   # HD
            return "hd", "small"
        else:  # Lower resolution
            return "low", "tiny"
    
    def get_recommended_scale(self):
        """📐 Get recommended scale factor"""
        screen_type, recommended_scale = self.detect_screen_type()
        
        # Check saved preference
        saved_scale = self.settings.value("ui_scale", recommended_scale)
        
        return saved_scale if saved_scale in self.scale_factors else recommended_scale
    
    def get_scaled_size(self, base_size: QSize, scale: str = None):
        """📏 Get scaled size based on scale factor"""
        if scale is None:
            scale = self.get_recommended_scale()
        
        scale_factor = self.scale_factors.get(scale, 1.0)
        
        scaled_width = int(base_size.width() * scale_factor)
        scaled_height = int(base_size.height() * scale_factor)
        
        return QSize(scaled_width, scaled_height)
    
    def get_window_size(self, scale: str = None):
        """🪟 Get appropriate window size"""
        return self.get_scaled_size(self.base_window_size, scale)
    
    def get_font_size(self, base_font_size: int = 12, scale: str = None):
        """🔤 Get scaled font size"""
        if scale is None:
            scale = self.get_recommended_scale()
        
        scale_factor = self.scale_factors.get(scale, 1.0)
        return int(base_font_size * scale_factor)
    
    def get_spacing(self, base_spacing: int = 10, scale: str = None):
        """📏 Get scaled spacing"""
        if scale is None:
            scale = self.get_recommended_scale()
        
        scale_factor = self.scale_factors.get(scale, 1.0)
        return int(base_spacing * scale_factor)
    
    def get_button_size(self, base_width: int = 140, base_height: int = 35, scale: str = None):
        """🔘 Get scaled button size"""
        if scale is None:
            scale = self.get_recommended_scale()
        
        scale_factor = self.scale_factors.get(scale, 1.0)
        
        return {
            "width": int(base_width * scale_factor),
            "height": int(base_height * scale_factor)
        }
    
    def get_responsive_stylesheet(self, scale: str = None):
        """🎨 Get responsive stylesheet"""
        if scale is None:
            scale = self.get_recommended_scale()
        
        font_size = self.get_font_size(12, scale)
        button_font_size = self.get_font_size(11, scale)
        spacing = self.get_spacing(10, scale)
        button_size = self.get_button_size(140, 35, scale)
        
        return f"""
        /* Responsive Stylesheet - Scale: {scale} */
        
        QMainWindow {{
            font-size: {font_size}px;
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        
        QLabel {{
            font-size: {font_size}px;
            padding: {spacing//2}px;
        }}
        
        QPushButton {{
            font-size: {button_font_size}px;
            min-width: {button_size['width']}px;
            min-height: {button_size['height']}px;
            padding: {spacing}px {spacing*2}px;
            margin: {spacing//2}px;
            border-radius: {spacing//2}px;
            font-weight: bold;
        }}
        
        QGroupBox {{
            font-size: {font_size}px;
            font-weight: bold;
            padding-top: {spacing*2}px;
            margin-top: {spacing}px;
        }}
        
        QGroupBox::title {{
            padding: 0 {spacing}px 0 {spacing}px;
        }}
        
        QSpinBox, QComboBox {{
            font-size: {font_size}px;
            min-height: {button_size['height']-10}px;
            padding: {spacing//2}px;
            border-radius: {spacing//3}px;
        }}
        
        QTextEdit {{
            font-size: {font_size-1}px;
            padding: {spacing}px;
        }}
        
        QTabWidget::pane {{
            border: 1px solid #444;
            border-radius: {spacing//2}px;
        }}
        
        QTabBar::tab {{
            font-size: {font_size}px;
            padding: {spacing}px {spacing*2}px;
            margin: 2px;
            border-radius: {spacing//3}px;
        }}
        """
    
    def save_scale_preference(self, scale: str):
        """💾 Save scale preference"""
        self.settings.setValue("ui_scale", scale)
        self.logger.info(f"💾 UI scale preference saved: {scale}")
    
    def get_scale_info(self):
        """ℹ️ Get scale information"""
        screen_type, recommended = self.detect_screen_type()
        current_scale = self.get_recommended_scale()
        
        return {
            "screen_type": screen_type,
            "screen_size": f"{self.screen_size.width()}x{self.screen_size.height()}",
            "recommended_scale": recommended,
            "current_scale": current_scale,
            "available_scales": list(self.scale_factors.keys()),
            "scale_factors": self.scale_factors
        }
    
    def center_window(self, window):
        """🎯 Center window on screen"""
        window_geometry = window.frameGeometry()
        screen_center = self.screen_geometry.center()
        window_geometry.moveCenter(screen_center)
        window.move(window_geometry.topLeft())
    
    def fit_to_screen(self, window, margin_percent: float = 0.1):
        """📐 Fit window to screen with margin"""
        margin_width = int(self.screen_size.width() * margin_percent)
        margin_height = int(self.screen_size.height() * margin_percent)
        
        max_width = self.screen_size.width() - (margin_width * 2)
        max_height = self.screen_size.height() - (margin_height * 2)
        
        current_size = window.size()
        
        if current_size.width() > max_width or current_size.height() > max_height:
            new_width = min(current_size.width(), max_width)
            new_height = min(current_size.height(), max_height)
            
            window.resize(new_width, new_height)
            self.center_window(window)
            
            self.logger.info(f"📐 Window fitted to screen: {new_width}x{new_height}")
    
    def apply_responsive_design(self, window, scale: str = None):
        """🎨 Apply responsive design to window with perfect screen fitting"""
        if scale is None:
            scale = self.get_recommended_scale()

        # Get optimal window size for this screen
        optimal_size = self.get_optimal_window_size(scale)

        # Set window size
        window.resize(optimal_size)

        # Apply stylesheet
        stylesheet = self.get_responsive_stylesheet(scale)
        window.setStyleSheet(stylesheet)

        # Perfect screen fitting
        self.perfect_screen_fit(window)

        # Center window
        self.center_window(window)

        self.logger.info(f"🎨 Perfect responsive design applied - Scale: {scale}")
        self.logger.info(f"📐 Window size: {optimal_size.width()}x{optimal_size.height()}")

        return scale

    def get_optimal_window_size(self, scale: str = None):
        """📐 Get optimal window size for current screen"""
        if scale is None:
            scale = self.get_recommended_scale()

        # Start with available screen space
        available_width = self.available_size.width()
        available_height = self.available_size.height()

        # Apply scale factor
        scale_factor = self.scale_factors.get(scale, 1.0)

        # Calculate optimal size (85% of available space)
        optimal_width = int(available_width * 0.85 * scale_factor)
        optimal_height = int(available_height * 0.85 * scale_factor)

        # Ensure it fits on screen
        optimal_width = min(optimal_width, available_width - 50)
        optimal_height = min(optimal_height, available_height - 50)

        # Ensure minimum size
        optimal_width = max(optimal_width, 800)
        optimal_height = max(optimal_height, 600)

        return QSize(optimal_width, optimal_height)

    def perfect_screen_fit(self, window):
        """📐 Perfect screen fitting with margin"""
        # Get window size
        window_size = window.size()

        # Check if window is larger than available space
        if (window_size.width() > self.available_size.width() or
            window_size.height() > self.available_size.height()):

            # Resize to fit perfectly
            new_width = min(window_size.width(), self.available_size.width() - 50)
            new_height = min(window_size.height(), self.available_size.height() - 50)

            window.resize(new_width, new_height)
            self.logger.info(f"📐 Window resized to fit screen: {new_width}x{new_height}")

        # Position window in available geometry
        available_rect = self.available_geometry
        window_rect = window.frameGeometry()

        # Center in available space
        x = available_rect.x() + (available_rect.width() - window_rect.width()) // 2
        y = available_rect.y() + (available_rect.height() - window_rect.height()) // 2

        window.move(x, y)
        self.logger.info(f"📍 Window positioned at: {x}, {y}")

    def get_detailed_screen_info(self):
        """📊 Get detailed screen information"""
        screen_info = {
            "primary_screen": {
                "geometry": f"{self.screen_geometry.width()}x{self.screen_geometry.height()}",
                "available": f"{self.available_geometry.width()}x{self.available_geometry.height()}",
                "dpi": {
                    "logical": self.logical_dpi,
                    "physical": self.physical_dpi,
                    "device_pixel_ratio": self.device_pixel_ratio
                }
            },
            "all_screens": []
        }

        for i, screen in enumerate(self.all_screens):
            screen_info["all_screens"].append({
                "index": i,
                "name": screen.name(),
                "geometry": f"{screen.geometry().width()}x{screen.geometry().height()}",
                "available": f"{screen.availableGeometry().width()}x{screen.availableGeometry().height()}",
                "dpi": screen.logicalDotsPerInch(),
                "is_primary": screen == self.primary_screen
            })

        return screen_info
